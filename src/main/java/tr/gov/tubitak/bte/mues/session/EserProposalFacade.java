package tr.gov.tubitak.bte.mues.session;

import java.util.Date;
import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Named;

import tr.gov.tubitak.bte.mues.model.EserProposal;
import tr.gov.tubitak.bte.mues.model.EserProposalHareket;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

/**
 *
 * ali.kelle
 */
@RequestScoped
@Named
public class EserProposalFacade extends AbstractFacade<EserProposal> {

    public EserProposalFacade() {
        super(EserProposal.class);
    }

    public List<EserProposal> findByNameAndMuseumDirectorate(final Integer mudurlukId) {
        return this.getEM().createNamedQuery("EserProposal.findByNameAndMuseumDirectorate", EserProposal.class).setParameter("id", mudurlukId).getResultList();
    }

    public EserProposal getLastEserProposalOfPersonelId(final Integer userId) {
        final List<EserProposal> eserProposals = this.getEM().createNamedQuery("EserProposal.getLastEserProposalOfPersonelId", EserProposal.class).setParameter("userId", userId).getResultList();
        if (eserProposals.isEmpty()) {
            return null;
        }
        return eserProposals.get(0);
    }

    public EserProposal getLastEserProposalOfResearcherId(final Integer userId) {
        final List<EserProposal> eserProposals = this.getEM().createNamedQuery("EserProposal.getLastEserProposalOfResearcherId", EserProposal.class).setParameter("userId", userId).getResultList();
        if (eserProposals.isEmpty()) {
            return null;
        }
        return eserProposals.get(0);
    }

    public List<EserProposal> findByEserProposalIdAndSearchParameters(final Integer eserProposalId, final Date startDate, final Date endDate) {
        return this.em.createNamedQuery("EserProposal.findByEserProposalIdAndSearchParameters", EserProposal.class)
                      .setParameter("eserProposalId", eserProposalId)
                      .setParameter("startDate", startDate)
                      .setParameter("endDate", endDate)
                      .getResultList();
    }

    public List<EserProposal> findAllByApplicationType(final ApplicationType applicationType, final List<Integer> list) {
        return this.em.createNamedQuery("EserProposal.findAllByApplicationTypeAndPermission", EserProposal.class)
                      .setParameter("applicationType", Integer.valueOf(applicationType.getCode()))
                      .setParameter("mudurluks", list)

                      .getResultList();
    }

    public List<EserProposal> findByEserProposalAndSearchParameters(final Date startDate, final Date endDate) {
        return this.em.createNamedQuery("EserProposal.findByEserProposalAndSearchParameters", EserProposal.class).setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
    }

    //
    /**
     * Eser hareketi gosterme ile ayni sebepten bu sekilde duzeltilmistir.
     */
    @Override
    public EserProposal findEagerById(final Integer id) {
        final EserProposal eser = super.findEagerById(id);
        if (eser != null) {
            eser.getEserHarekets().addAll(this.getEM().createNamedQuery("EserProposalHareket.findEagerByEserId", EserProposalHareket.class).setParameter("id", id).getResultList());
        }
        return eser;
    }

}