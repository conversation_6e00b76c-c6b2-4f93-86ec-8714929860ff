package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
*
 */
@Entity
@Table(name = "EP_ESER_FOTOGRAF")
public class EserProposalFotograf extends EserFotografSuper {

    private static final long serialVersionUID = -1255653912454192664L;

    @JoinColumn(name = "ILGILI_YUZ", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MuesPick              ilgiliYuz;

    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalFotograf() {
        this.setAnaFotograf(false);
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public MuesPick getIlgiliYuz() {
        return this.ilgiliYuz;
    }

    public void setIlgiliYuz(final MuesPick ilgiliYuz) {
        this.ilgiliYuz = ilgiliYuz;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
