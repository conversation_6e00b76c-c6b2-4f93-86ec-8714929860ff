/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.text.Collator;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.enterprise.context.SessionScoped;
import javax.enterprise.event.Observes;
import javax.enterprise.inject.Produces;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.event.SessionVariablesChangeEvent;
import tr.gov.tubitak.bte.mues.model.Announcement;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.model.mapping.MudurlukIzin;
import tr.gov.tubitak.bte.mues.session.KullaniciFacade;
import tr.gov.tubitak.bte.mues.session.MudurlukFacade;
import tr.gov.tubitak.bte.mues.util.CurrentUser;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

/**
*
 *
 */
@Named
@SessionScoped
public class SessionBean implements Serializable {

    private static final long           serialVersionUID             = -3129041154458141238L;

    private static final Logger         logger                       = LoggerFactory.getLogger(SessionBean.class);

    private Locale                      locale;

    private static final String         PAGINATOR_TEMPLATE           = "{CurrentPageReport} {FirstPageLink} {PreviousPageLink} {PageLinks} {NextPageLink} {LastPageLink} {RowsPerPageDropdown}";

    private static final String         CURRENT_PAGE_REPORT_TEMPLATE = "{currentPage} / {totalPages}";

    private static final String         ROWS_PER_PAGE_TEMPLATE       = "10, 25, 50";

    private static final int            ROWS_PER_PAGE_SIZE           = 15;

    private static final int            QUERY_DELAY                  = 750;

    @Inject
    private AbstractParameters          params;

    @Inject
    private transient KullaniciFacade   kullaniciFacade;

    @Inject
    private transient MudurlukFacade    mudurlukFacade;

    private Kullanici                   currentUser;

    private String                      paginatorTemplate;

    private String                      currentPageReportTemplate;

    private String                      rowsPerPageTemplate;

    private int                         rowsPerPageSize;

    private int                         queryDelay;
    
    //TODO: ROlleri arasinda multiple var olan var mi Seklinde duzenleme yapilacak 
    private boolean                     hasDirectorate;

    private Mudurluk                    directorate;

    private Map<String, List<Mudurluk>> permissionBasedMudurluks;

    private final List<Mudurluk>        permittedMudurlukList        = new ArrayList<>();

    private List<Announcement>          announcements;

    private int                         tabIndex;

    private List<Mudurluk>              permissionBasedDirectorates;

    private Boolean                     reviewPermission;

    private String                      generalDataProtectionRegulationApproved;

    private String                      hiddenInput;

    private ApplicationType             applicationType;

    public SessionBean() {
        // default constructor
    }

    @PreDestroy
    private void destructor() {
        this.params.getLoginMap().remove(this.currentUser.getKullaniciAdi());
    }

    @PostConstruct
    private void init() {
        this.locale = new Locale("tr", "TR");
        Locale.setDefault(this.locale);
        final Collator collator = Collator.getInstance(this.locale);
        collator.setStrength(Collator.SECONDARY);
        collator.setDecomposition(Collator.CANONICAL_DECOMPOSITION);

        this.currentUser = this.kullaniciFacade.findCurrentUser();
        this.setApplicationType(ApplicationType.parse(this.params.get(ApplicationType.class.getSimpleName())));

        // application tipi degistirilmeden kullanicinin diger baska bir modulde login olmasini saglama.
        if ((this.currentUser.getPersonelView() != null) && !this.getApplicationType().getCode().equals(String.valueOf(this.currentUser.getPersonelView().getApplicationType()))) {
            final PersonelView personelView = this.currentUser.getPersonelView();
            personelView.setApplicationType(Integer.valueOf(this.getApplicationType().getCode()));
            personelView.setMudurluk(null);
            personelView.setMudurlukAd(null);
            this.kullaniciFacade.update(personelView);
        }

        this.permissionBasedMudurluks = this.prepareAllPermittedMudurluks();
        this.permissionBasedDirectorates = this.fetchMudurlukListByPermission("mudurluk:listele");

        this.announcements = this.kullaniciFacade.findAnnouncements(this.currentUser);

        this.generalDataProtectionRegulationApproved = this.kullaniciFacade.ifNotKVKKApprovedReturnPath(this.currentUser);

        if (SecurityUtils.getSubject().hasRole("SUPERUSER")
            || SecurityUtils.getSubject().hasRole("sistem_merkez_sorumlusu")
            || SecurityUtils.getSubject().hasRole("KVMGM_merkez_yoneticisi")
            || SecurityUtils.getSubject().hasRole("muzeler_daire_baskani")
            || SecurityUtils.getSubject().hasRole("ktb_merkez_yoneticisi")) {

            this.hasDirectorate = false;

            this.permittedMudurlukList.add(new Mudurluk());
            this.permittedMudurlukList.get(0).setAd("(Tüm Müzeler)");
        } else {
            final Collection<List<Mudurluk>> values = this.permissionBasedMudurluks.values();
            for (final List<Mudurluk> list : values) {

                if (!this.permittedMudurlukList.contains(list.get(0))) {
                    this.permittedMudurlukList.add(list.get(0));
                }
            }

            if (this.currentUser.getPersonel() != null) {
                if (this.currentUser.getPersonel().getMudurluk() != null) {
                    this.hasDirectorate = true;
                }
            } else {
                this.hasDirectorate = false;
            }
        }

        this.paginatorTemplate = this.toAcceptedFormat("dataTable.paginatorTemplate", SessionBean.PAGINATOR_TEMPLATE);
        this.currentPageReportTemplate = this.toAcceptedFormat("dataTable.currentPageReportTemplate", SessionBean.CURRENT_PAGE_REPORT_TEMPLATE);
        this.rowsPerPageTemplate = this.toAcceptedFormat("dataTable.rowsPerPageTemplate", SessionBean.ROWS_PER_PAGE_TEMPLATE);
        this.rowsPerPageSize = this.toInt("dataTable.rowsPerPageSize", SessionBean.ROWS_PER_PAGE_SIZE);
        this.queryDelay = this.toInt("autocomplete.query.delay", SessionBean.QUERY_DELAY);
    }

    public void refreshSessionVariables(final @Observes SessionVariablesChangeEvent event) {
        if (logger.isInfoEnabled()) {
            logger.info("[refreshSessionVariables] : {}", event.getClass().getName());
        }
        this.init();
    }
    
    public boolean isMues() {
        if(this.getApplicationType().name().equals("ENVANTER"))
            return true;
        return false;
    }

    public Map<String, List<Mudurluk>> prepareAllPermittedMudurluks() {
        String permissionName = "";
        final List<Mudurluk> allMudurlukList = this.mudurlukFacade.findActive();
        final Map<Integer, Mudurluk> mudurlukRegistry = allMudurlukList.stream().collect(Collectors.toMap(Mudurluk::getId, x -> x));
        final List<MudurlukIzin> museumBasedPermissions = this.mudurlukFacade.fetchAllPermittedMudurluks();
        final Map<String, List<Mudurluk>> mudurlukPermissionMap = new HashMap<>();

        for (final MudurlukIzin each : museumBasedPermissions) {
            final List<Mudurluk> permissionBasedList = mudurlukPermissionMap.computeIfAbsent(each.getIzinKod(), x -> new ArrayList<>());

            if (each.getMudurlukId() == null) {
                permissionBasedList.addAll(allMudurlukList);
                permissionName = each.getIzinKod();

            } else {
                // bütün müzelerde tanımlı olan üst yetki varsa özel müze için eklenmesine gerek yok. sql ile yapılabilirse daha iyi olur
                if (!permissionName.equals(each.getIzinKod()) && (mudurlukRegistry.get(each.getMudurlukId()) != null)) {
                    permissionBasedList.add(mudurlukRegistry.get(each.getMudurlukId()));
                    permissionName = "";
                }
            }
        }
        return mudurlukPermissionMap;
    }

    /***
     * Gelen appType ve permission'a gore yetkili olunan muze isimlerini doner
     * 
     * @param appType Application tipi (mues, lab vb.)
     * @param permission izin
     * @return mudurluk listesi
     */
    @SuppressWarnings("unchecked")
    public List<Mudurluk> fetchMudurlukByApplicationTypeAndPermission(final ApplicationType appType, final String permission) {
        List<Mudurluk> mudurlukList = null;

        if (appType.getCode().equals(this.getApplicationType().getCode())) {
            mudurlukList = this.fetchByPermission(permission);
        } else if (this.getApplicationType().equals(ApplicationType.LABORATUVAR)) {

            // native query
            final List<Integer> permittedMudurlukIdList = this.fetchByPermission(permission).stream().map(Mudurluk::getId).collect(Collectors.toList());

            mudurlukList = this.kullaniciFacade.getEM()
                                               .createNativeQuery("SELECT * FROM MUZE_MUDURLUGU m WHERE m.LAB_DIRECTORATE_ID IN ( :permittedMudurlukIdList ) order by m.ad ", Mudurluk.class)
                                               .setParameter("permittedMudurlukIdList", MuesUtil.toIds(permittedMudurlukIdList))
                                               .getResultList();
        }

        return mudurlukList;
    }

    public List<Mudurluk> filterByNameAndPermission(final String permission, final String query) {
        return Optional.ofNullable(this.fetchMudurlukListByPermission(permission))
                       .orElse(Collections.emptyList())
                       .stream()
                       .filter(x -> x.getAd().toLowerCase().contains(query.toLowerCase()) || Optional.ofNullable(x.getAciklama()).orElse("").toLowerCase().contains(query.toLowerCase()))
                       .collect(Collectors.toList());
    }

    public Mudurluk filterByPermission(final String permission) {
        final List<Mudurluk> filterByPermission = this.fetchByPermission(permission);
        if (!filterByPermission.isEmpty()) {
            return filterByPermission.get(0);
        }
        return null;
    }

    public List<Mudurluk> fetchByPermission(final String permission) {
        return Optional.ofNullable(this.fetchMudurlukListByPermission(permission)).orElse(Collections.emptyList());
    }

    public List<Mudurluk> fetchMudurlukListByPermission(final String permission) {
        return this.permissionBasedMudurluks.get(permission);
    }

    public boolean isPermittedMuseum(final String permission, final int musuemId) {
        final List<Mudurluk> list = this.permissionBasedMudurluks.get(permission);
        if (list == null) {
            return false;
        }
        return list.stream().anyMatch(x -> x.getId() == musuemId);
    }

    public boolean checkDirectorate(final Mudurluk directorate) {
        if (this.hasDirectorate) {
            for (final Mudurluk each : this.permissionBasedDirectorates) {
                if (each.equals(directorate)) {
                    return true;
                }
            }
        } else {
            return true;
        }
        return false;
    }

    // utilities ..............................................................

    private String toAcceptedFormat(final String key, final String defaultValue) {
        final String value = this.params.get(key);

        if (MuesUtil.checkFormat(value)) {
            return value;

        } else {
            return defaultValue;
        }
    }

    private Integer toInt(final String key, final int defaultValue) {
        try {
            return Integer.parseInt(this.params.get(key));

        } catch (final NumberFormatException nfe) {
            logger.debug("Bağli Birim Changed{}", nfe.getMessage());
            return defaultValue;
        }
    }

    /* This method's sole purpose is to enforce this rule: 'a muze_mudur_yardimcisi with an authorization document can review an artifact'. 
     * This method returns true for all other cases, which should be taken care by the rol-izni structure.
     * Also this method does not check the user's mudurluk */
    private boolean fixReviewPermission() {
        
    	if (!this.hasDirectorate) {
            return true;
        }
    	
        boolean allow = true;
        
        for (final KullaniciBirimRol each : this.currentUser.getKullaniciBirimRols()) {
            
        	if ("muze_mudur_yardimcisi".equals(each.getRol().getKod()) && (each.getAprovePermitDocPath() == null)) {
                allow = false;
            }
            if ("muze_muduru".equals(each.getRol().getKod())) {
                allow = true;
            }
        }
        return allow;
    }

    // getters ................................................................

    @Produces
    @Named
    @SessionScoped
    @CurrentUser
    public Kullanici getCurrentUser() {
        return this.currentUser;
    }

    public Locale getLocale() {
        return this.locale == null ? MuesUtil.LOCALE_TR : this.locale;
    }

    public String getLanguage() {
        return this.locale == null ? "tr" : this.locale.getLanguage();
    }

    public String getPaginatorTemplate() {
        return this.paginatorTemplate;
    }

    public boolean getHasDirectorate() {
        return this.hasDirectorate;
    }

    public String getCurrentPageReportTemplate() {
        return this.currentPageReportTemplate;
    }

    public String getRowsPerPageTemplate() {
        return this.rowsPerPageTemplate;
    }

    public int getRowsPerPageSize() {
        return this.rowsPerPageSize;
    }

    public int getQueryDelay() {
        return this.queryDelay;
    }

    public Date getCurrentDate() {
        return new Date();
    }

    public String getTodaysDate() {
        final Date today = new Date();
        final SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        return sdf.format(today);
    }

    public String getUserPhoto() {
        String photoPath = "user";
        if ((this.currentUser.getPersonelView() != null) && (this.currentUser.getPersonelView().getFotografPath() != null)) {
            photoPath = this.currentUser.getPersonelView().getFotografPath();
        }
        return photoPath;
    }

    /**
     * @deprecated should replaced with permitions personel directory should be removed.
     * 
     */
    @Deprecated
    public Mudurluk getDirectorate() {
        return this.directorate;
    }

    public List<Mudurluk> getPermittedMudurlukList() {
        return this.permittedMudurlukList;
    }

    public List<Announcement> getAnnouncements() {
        return this.announcements;
    }

    public void setAnnouncements(final List<Announcement> announcements) {
        this.announcements = announcements;
    }

    public int getTabIndex() {
        return this.tabIndex;
    }

    public void setTabIndex(final int tabIndex) {
        this.tabIndex = tabIndex;
    }

    public List<Mudurluk> getPermissionBasedDirectorates() {
        return this.permissionBasedDirectorates;
    }

    public void setPermissionBasedDirectorates(final List<Mudurluk> permissionBasedDirectorates) {
        this.permissionBasedDirectorates = permissionBasedDirectorates;
    }

    public boolean getReviewPermission() {
        if (this.reviewPermission == null) {
            this.reviewPermission = this.fixReviewPermission();
        }
        return this.reviewPermission;
    }

    public String getHiddenInput() {
        return this.hiddenInput;
    }

    public void setHiddenInput(final String hiddenInput) {
        this.hiddenInput = hiddenInput;
    }

    public ApplicationType getApplicationType() {
        return this.applicationType;
    }

    public void setApplicationType(final ApplicationType applicationType) {
        this.applicationType = applicationType;
    }

    public String getGeneralDataProtectionRegulationApproved() {
        return this.generalDataProtectionRegulationApproved;
    }

    public void setGeneralDataProtectionRegulationApproved(final String generalDataProtectionRegulationApproved) {
        this.generalDataProtectionRegulationApproved = generalDataProtectionRegulationApproved;
    }

}
