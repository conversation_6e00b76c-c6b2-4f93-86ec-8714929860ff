package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Entity
@Table(name = "Malzeme")
@NamedQuery(name = "Malzeme.findEagerById", query = "SELECT m FROM Malzeme m JOIN FETCH m.grup WHERE m.id = :id")
@NamedQuery(name = "Malzeme.findAll", query = "SELECT m FROM Malzeme m JOIN FETCH m.grup mg ORDER BY m.silinmis, m.aktif DESC, mg.ad")
@NamedQuery(name = "Malzeme.findByNameAndAciklama", query = "SELECT m FROM Malzeme m WHERE m.aktif = true AND m.silinmis = false AND (m.ad LIKE :str OR m.aciklama LIKE :str) ORDER BY m.ad, m.aciklama")
@NamedQuery(name = "Malzeme.findActive", query = "SELECT m FROM Malzeme m WHERE m.aktif = true AND m.silinmis = false ORDER BY m.ad")
@NamedNativeQuery(name = "Malzeme.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_SUSLEME_TEKNIGI WHERE SILINMIS = 0 AND MALZEME_ID = :id) + "
                                                                 + "(SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_YAPIM_TEKNIGI WHERE SILINMIS = 0 AND MALZEME_ID = :id) + "
                                                                 + "(SELECT case when count(1) > 0 then 1 else 0 end FROM MALZEME_SUSLEME_TEKNIGI WHERE SILINMIS = 0 AND MALZEME_ID = :id) + "
                                                                 + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_DeterminationColour WHERE SILINMIS = 0 AND malzemeId = :id) + "
                                                                 + "(SELECT case when count(1) > 0 then 1 else 0 end FROM MALZEME_YAPIM_TEKNIGI WHERE SILINMIS = 0 AND MALZEME_ID = :id)")

public class Malzeme extends AbstractEntity implements DeleteValidatable {

    private static final long                     serialVersionUID = 7431416600070239342L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                                ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                                deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                                aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String                                fotografPath;

    @Column(name = "period")
    private Integer                               period;

    @JoinColumn(name = "MALZEME_GRUBU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MalzemeGrubu                          grup;

    @OneToMany(mappedBy = "malzeme")
    private Collection<MalzemeYapimTeknigi>       malzemeYapimTeknigiCollection;

    @OneToMany(mappedBy = "malzeme")
    private Collection<MalzemeSuslemeTeknigi>     malzemeSuslemeTeknigiCollection;

    @OneToMany(mappedBy = "malzeme")
    private Collection<EserMalzemeYapimTeknigi>   eserMalzemeYapimTeknigiCollection;

    @OneToMany(mappedBy = "malzeme")
    private Collection<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigiCollection;

    public Malzeme() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public MalzemeGrubu getGrup() {
        return this.grup;
    }

    public void setGrup(final MalzemeGrubu grup) {
        this.grup = grup;
    }

    public Collection<EserMalzemeYapimTeknigi> getEserMalzemeYapimTeknigiCollection() {
        return this.eserMalzemeYapimTeknigiCollection;
    }

    public void setEserMalzemeYapimTeknigiCollection(final Collection<EserMalzemeYapimTeknigi> eserMalzemeYapimTeknigiCollection) {
        this.eserMalzemeYapimTeknigiCollection = eserMalzemeYapimTeknigiCollection;
    }

    public Collection<MalzemeSuslemeTeknigi> getMalzemeSuslemeTeknigiCollection() {
        return this.malzemeSuslemeTeknigiCollection;
    }

    public void setMalzemeSuslemeTeknigiCollection(final Collection<MalzemeSuslemeTeknigi> malzemeSuslemeTeknigiCollection) {
        this.malzemeSuslemeTeknigiCollection = malzemeSuslemeTeknigiCollection;
    }

    public Collection<EserMalzemeSuslemeTeknigi> getEserMalzemeSuslemeTeknigiCollection() {
        return this.eserMalzemeSuslemeTeknigiCollection;
    }

    public void setEserMalzemeSuslemeTeknigiCollection(final Collection<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigiCollection) {
        this.eserMalzemeSuslemeTeknigiCollection = eserMalzemeSuslemeTeknigiCollection;
    }

    public Collection<MalzemeYapimTeknigi> getMalzemeYapimTeknigiCollection() {
        return this.malzemeYapimTeknigiCollection;
    }

    public void setMalzemeYapimTeknigiCollection(final Collection<MalzemeYapimTeknigi> malzemeYapimTeknigiCollection) {
        this.malzemeYapimTeknigiCollection = malzemeYapimTeknigiCollection;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(final Integer period) {
        this.period = period;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
