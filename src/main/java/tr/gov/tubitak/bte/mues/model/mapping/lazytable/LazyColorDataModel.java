package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.RenkController;
import tr.gov.tubitak.bte.mues.model.Renk;
import tr.gov.tubitak.bte.mues.session.RenkLazyLoadFacade;

public class LazyColorDataModel extends LazyDataModel<Renk> {

    private static final long        serialVersionUID = -4739489425974372483L;

    private static final Logger      logger           = LoggerFactory.getLogger(LazyColorDataModel.class);

    private final RenkLazyLoadFacade renkFacade;

    private final RenkController     renkController;

    private List<Renk>               data;

    public LazyColorDataModel(final RenkLazyLoadFacade renkFacade, final RenkController renkController) {
        this.renkFacade = renkFacade;
        this.renkController = renkController;
    }

    @Override
    public List<Renk> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        this.data = this.renkFacade.fetchData(first, pageSize, sortBy, filterBy);
        logger.debug("filters: {} _ multiSortMeta Length, {}", sortBy, filterBy);

        return this.data;
    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
        return this.renkFacade.count(filterBy);
    }

    @Override
    public Renk getRowData(final String rowKey) {
        for (final Renk row : this.renkController.getSelectionList()) {
            if (row.getId().toString().equals(rowKey)) {
                return row;
            }
        }
        for (final Renk each : this.data) {
            if (each.getId().equals(Integer.valueOf(rowKey))) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final Renk renk) {
        return renk.getId().toString();
    }

}
