package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.mapping.AuditView;
import tr.gov.tubitak.bte.mues.session.AuditViewFacade;

public class LazyAuditViewDataModel extends LazyDataModel<AuditView>
{

    private static final long     serialVersionUID = -4739489425974372483L;

    private static final Logger   logger           = LoggerFactory.getLogger(LazyAuditViewDataModel.class);

    private final AuditViewFacade auditViewFacade;

    private List<AuditView>       data;

    public LazyAuditViewDataModel(final AuditViewFacade auditViewFacade) {
        this.auditViewFacade = auditViewFacade;
    }

    @Override
    public List<AuditView> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        if (sortBy.isEmpty()) {
            final SortMeta newSortMeta = SortMeta.builder().field("id").order(SortOrder.DESCENDING).build();
            sortBy.put("id", newSortMeta);
        } 
        this.data = this.auditViewFacade.fetchData(first, pageSize, sortBy, filterBy);
        logger.debug("filters: {} _ multiSortMeta Length,  {}", sortBy, filterBy);
        return this.data;
    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
        return this.auditViewFacade.count(filterBy);
    }

    @Override
    public AuditView getRowData(final String rowKey) {
        for (final AuditView each : this.data) {
            if (each.getId().equals(BigInteger.valueOf(Long.valueOf(rowKey)))) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final AuditView auditView) {
        return auditView.getId().toString();
    }

}
