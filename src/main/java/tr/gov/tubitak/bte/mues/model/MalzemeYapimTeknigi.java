package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "MALZEME_YAPIM_TEKNIGI")
@NamedQuery(name = "MalzemeYapimTeknigi.findEagerById", query = "SELECT x FROM MalzemeYapimTeknigi x JOIN FETCH x.malzeme JOIN FETCH x.yapimTeknigi WHERE x.id = :id")
@NamedQuery(name = "MalzemeYapimTeknigi.findAll", query = "SELECT x FROM MalzemeYapimTeknigi x JOIN FETCH x.yapimTeknigi JOIN FETCH x.malzeme xm ORDER BY x.silinmis, x.aktif DESC, xm.ad")
@NamedQuery(name = "MalzemeYapimTeknigi.findActive", query = "SELECT x FROM MalzemeYapimTeknigi x LEFT JOIN FETCH x.malzeme m LEFT JOIN FETCH x.yapimTeknigi yt WHERE x.aktif = true AND x.silinmis = false ORDER BY x.yapimTeknigi.ad")
@NamedQuery(name = "MalzemeYapimTeknigi.findActiveGroupByMalzeme", query = "SELECT x FROM MalzemeYapimTeknigi x WHERE x.aktif = true AND x.silinmis = false GROUP BY x.malzeme")
@NamedNativeQuery(name = "MalzemeYapimTeknigi.validateBeforeDelete", query = "SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_YAPIM_TEKNIGI WHERE SILINMIS = 0 AND ESER_YAPIM_TEKNIGI_ID = :id")
public class MalzemeYapimTeknigi extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 2988942386319231033L;

    @JoinColumn(name = "MALZEME_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Malzeme           malzeme;

    @JoinColumn(name = "YAPIM_TEKNIGI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private YapimTeknigi      yapimTeknigi;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public MalzemeYapimTeknigi() {
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Malzeme getMalzeme() {
        return this.malzeme;
    }

    public void setMalzeme(final Malzeme malzeme) {
        this.malzeme = malzeme;
    }

    public YapimTeknigi getYapimTeknigi() {
        return this.yapimTeknigi;
    }

    public void setYapimTeknigi(final YapimTeknigi yapimTeknigi) {
        this.yapimTeknigi = yapimTeknigi;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.malzeme).flatMap(malzeme -> Optional.of("{Malzeme: " + malzeme.getAd())).toString()
               + Optional.ofNullable(this.yapimTeknigi).flatMap(yapimTeknigi -> Optional.of(", YapimTeknigi: " + yapimTeknigi.getAd()));

    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.malzeme.getAd()).orElse(this.yapimTeknigi.getAd());
    }

}
