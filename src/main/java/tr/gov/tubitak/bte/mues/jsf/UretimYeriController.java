package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.UretimYeri;
import tr.gov.tubitak.bte.mues.session.UretimYeriFacade;

@Named
@ViewScoped
public class UretimYeriController extends AbstractController<UretimYeri> {

    private static final long serialVersionUID = 2215886861273694263L;

    @Inject
    private UretimYeriFacade  facade;

    public UretimYeriController() {
        super(UretimYeri.class);
    }

    public List<UretimYeri> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public UretimYeriFacade getFacade() {
        return this.facade;
    }

}
