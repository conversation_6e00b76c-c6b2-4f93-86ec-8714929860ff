package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
 * <AUTHOR>
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "MuzeMudurluguIl.findEagerById", query = "SELECT m FROM MuzeMudurluguIl m LEFT JOIN FETCH m.mudurluk LEFT JOIN FETCH m.il WHERE m.id = :id")
@NamedQuery(name = "MuzeMudurluguIl.findAll", query = "SELECT m FROM MuzeMudurluguIl m LEFT JOIN FETCH m.mudurluk LEFT JOIN FETCH m.il")
@NamedQuery(name = "MuzeMudurluguIl.findActive", query = "SELECT m FROM MuzeMudurluguIl m WHERE m.aktif = true AND m.silinmis = false")

public abstract class MuzeMudurluguIlSuper extends AbstractEntity {

    private static final long serialVersionUID = 1035253373119984286L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                il;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    protected MuzeMudurluguIlSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

}
