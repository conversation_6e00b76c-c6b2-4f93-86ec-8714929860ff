package tr.gov.tubitak.bte.mues.model;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Version;
import javax.validation.constraints.Size;

import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.ValidEser;
import tr.gov.tubitak.bte.mues.constraint.validator.EserGroup;

/**
 *
*
 */
@Entity
@Table(name = "ESER")

@NamedQuery(name = "Eser.findAll", query = "SELECT e FROM Eser e ORDER BY e.silinmis, e.aktif DESC, e.id")
@NamedQuery(name = "Eser.findActive", query = "SELECT e FROM Eser e WHERE e.aktif = true AND e.silinmis = false ORDER BY e.id")
@NamedQuery(name = "Eser.findEagerById", query = "SELECT e FROM Eser e "
                                                 + "LEFT JOIN FETCH e.eserHarekets h "
                                                 + "LEFT JOIN FETCH h.kazi "
                                                 + "LEFT JOIN FETCH e.eserDepos d  LEFT JOIN FETCH d.alanKonumu dk LEFT JOIN FETCH dk.alan da LEFT JOIN FETCH da.bina db LEFT JOIN FETCH db.bagliBirim dbb LEFT JOIN FETCH dbb.mudurluk "
                                                 + "LEFT JOIN FETCH e.cag c LEFT JOIN FETCH c.kronoloji "
                                                 + "LEFT JOIN FETCH e.donem do LEFT JOIN FETCH do.cag dc LEFT JOIN FETCH dc.kronoloji "
                                                 + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi LEFT JOIN FETCH yt.renks "
                                                 + "LEFT JOIN FETCH e.eserMalzemeSuslemeTeknigis st LEFT JOIN FETCH st.malzeme LEFT JOIN FETCH st.suslemeTeknigi LEFT JOIN FETCH st.renks "
                                                 + "LEFT JOIN FETCH e.transcriptions t LEFT JOIN FETCH t.yaziDili LEFT JOIN FETCH t.yaziTipi LEFT JOIN FETCH t.monogram "
                                                 + "LEFT JOIN FETCH e.eserMeasures m LEFT JOIN FETCH m.measure mm LEFT JOIN FETCH mm.type LEFT JOIN FETCH m.annotation "
                                                 + "LEFT JOIN FETCH e.eserKeywords k LEFT JOIN FETCH k.keyword "
                                                 + "LEFT JOIN FETCH e.eserAltTur at LEFT JOIN FETCH at.eserTur "
                                                 + "LEFT JOIN FETCH e.eserStils s LEFT JOIN FETCH s.stil "
                                                 + "LEFT JOIN FETCH e.eserAtolyes a LEFT JOIN FETCH a.atolye "
                                                 + "LEFT JOIN FETCH e.eserYayinLiteraturs ly LEFT JOIN FETCH ly.literatur "
                                                 + "LEFT JOIN FETCH e.eserKaynakLiteraturs lk LEFT JOIN FETCH lk.literatur "
                                                 + "LEFT JOIN FETCH e.iliskilendirme i LEFT JOIN FETCH i.tur LEFT JOIN FETCH i.groupedArtifacts ia LEFT JOIN FETCH ia.eserFotografs LEFT JOIN FETCH ia.eserHarekets "
                                                 + "LEFT JOIN FETCH e.eserFotografs f LEFT JOIN FETCH f.ilgiliYuz LEFT JOIN FETCH f.annotations "
                                                 + "LEFT JOIN FETCH e.anaFotograf "
                                                 + "LEFT JOIN FETCH e.eserZimmets z LEFT JOIN FETCH z.zimmetPersonel "
                                                 + "LEFT JOIN FETCH e.eserCizims "
                                                 + "LEFT JOIN FETCH e.kondisyonDurumu "
                                                 + "LEFT JOIN FETCH e.sikkeDarpYonu "
                                                 + "LEFT JOIN FETCH e.yazmaBasmaSecimi "
                                                 + "LEFT JOIN FETCH e.islamiGayriSecimi "
                                                 + "LEFT JOIN FETCH e.elisiDokumaSecimi "
                                                 + "LEFT JOIN FETCH e.hukumdar "
                                                 + "LEFT JOIN FETCH e.uygarlik "
                                                 + "LEFT JOIN FETCH e.yaptiranVip "
                                                 + "LEFT JOIN FETCH e.bagislayanVip "
                                                 + "LEFT JOIN FETCH e.yapanVip "
                                                 + "LEFT JOIN FETCH e.kullanacakVip "
                                                 + "LEFT JOIN FETCH e.kullananVip "
                                                 + "LEFT JOIN FETCH e.tasinirMalYonKod "
                                                 + "LEFT JOIN FETCH e.uretimYeri "
                                                 + "LEFT JOIN FETCH e.uretimBolgesi "
                                                 + "LEFT JOIN FETCH e.darpYeri "
                                                 + "LEFT JOIN FETCH e.eserSerhs "
                                                 + "LEFT JOIN FETCH e.createdBy "
                                                 + "LEFT JOIN FETCH e.updatedBy "
                                                 + "LEFT JOIN FETCH e.deletedBy "
                                                 + "LEFT JOIN FETCH e.eserOneriPersonel "
                                                 + "LEFT JOIN FETCH e.eserOneriResearcher "
                                                 + "LEFT JOIN FETCH e.videos "
                                                 + "WHERE e.id = :id")
@NamedQuery(name = "Eser.findEagerByPermanentId", query = "SELECT e FROM Eser e "
                                                          + "LEFT JOIN FETCH e.eserHarekets h "
                                                          + "LEFT JOIN FETCH h.kazi "
                                                          + "LEFT JOIN FETCH e.eserDepos d  LEFT JOIN FETCH d.alanKonumu dk LEFT JOIN FETCH dk.alan da LEFT JOIN FETCH da.bina db LEFT JOIN FETCH db.bagliBirim dbb LEFT JOIN FETCH dbb.mudurluk "
                                                          + "LEFT JOIN FETCH e.cag c LEFT JOIN FETCH c.kronoloji "
                                                          + "LEFT JOIN FETCH e.donem do LEFT JOIN FETCH do.cag dc LEFT JOIN FETCH dc.kronoloji "
                                                          + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi LEFT JOIN FETCH yt.renks "
                                                          + "LEFT JOIN FETCH e.eserMalzemeSuslemeTeknigis st LEFT JOIN FETCH st.malzeme LEFT JOIN FETCH st.suslemeTeknigi LEFT JOIN FETCH st.renks "
                                                          + "LEFT JOIN FETCH e.transcriptions t LEFT JOIN FETCH t.yaziDili LEFT JOIN FETCH t.yaziTipi LEFT JOIN FETCH t.monogram "
                                                          + "LEFT JOIN FETCH e.eserMeasures m LEFT JOIN FETCH m.measure mm LEFT JOIN FETCH mm.type "
                                                          + "LEFT JOIN FETCH e.eserKeywords k LEFT JOIN FETCH k.keyword "
                                                          + "LEFT JOIN FETCH e.eserAltTur at LEFT JOIN FETCH at.eserTur "
                                                          + "LEFT JOIN FETCH e.eserStils s LEFT JOIN FETCH s.stil "
                                                          + "LEFT JOIN FETCH e.eserAtolyes a LEFT JOIN FETCH a.atolye "
                                                          + "LEFT JOIN FETCH e.eserYayinLiteraturs ly LEFT JOIN FETCH ly.literatur "
                                                          + "LEFT JOIN FETCH e.eserKaynakLiteraturs lk LEFT JOIN FETCH lk.literatur "
                                                          + "LEFT JOIN FETCH e.iliskilendirme i LEFT JOIN FETCH i.tur LEFT JOIN FETCH i.groupedArtifacts ia LEFT JOIN FETCH ia.eserFotografs LEFT JOIN FETCH ia.eserHarekets "
                                                          + "LEFT JOIN FETCH e.eserFotografs f LEFT JOIN FETCH f.ilgiliYuz "
                                                          + "LEFT JOIN FETCH e.eserZimmets z LEFT JOIN FETCH z.zimmetPersonel "
                                                          + "LEFT JOIN FETCH e.eserCizims "
                                                          + "LEFT JOIN FETCH e.kondisyonDurumu "
                                                          + "LEFT JOIN FETCH e.sikkeDarpYonu "
                                                          + "LEFT JOIN FETCH e.yazmaBasmaSecimi "
                                                          + "LEFT JOIN FETCH e.islamiGayriSecimi "
                                                          + "LEFT JOIN FETCH e.elisiDokumaSecimi "
                                                          + "LEFT JOIN FETCH e.hukumdar "
                                                          + "LEFT JOIN FETCH e.uygarlik "
                                                          + "LEFT JOIN FETCH e.yaptiranVip "
                                                          + "LEFT JOIN FETCH e.bagislayanVip "
                                                          + "LEFT JOIN FETCH e.yapanVip "
                                                          + "LEFT JOIN FETCH e.kullanacakVip "
                                                          + "LEFT JOIN FETCH e.kullananVip "
                                                          + "LEFT JOIN FETCH e.tasinirMalYonKod "
                                                          + "LEFT JOIN FETCH e.uretimYeri "
                                                          + "LEFT JOIN FETCH e.uretimBolgesi "
                                                          + "LEFT JOIN FETCH e.darpYeri "
                                                          + "LEFT JOIN FETCH e.eserSerhs "
                                                          + "LEFT JOIN FETCH e.createdBy "
                                                          + "LEFT JOIN FETCH e.updatedBy "
                                                          + "LEFT JOIN FETCH e.deletedBy "
                                                          + "LEFT JOIN FETCH e.videos "
                                                          + "WHERE e.permanentId = :permanentId")
@NamedQuery(name = "Eser.reportByAlanKonum", query = "SELECT DISTINCT e FROM Eser e "
                                                     + "LEFT JOIN FETCH e.eserHarekets h LEFT JOIN FETCH h.arastirma LEFT JOIN FETCH h.eserGelisSekli LEFT JOIN FETCH h.kazi "
                                                     + "LEFT JOIN FETCH e.eserDepos d LEFT JOIN FETCH d.alanKonumu k LEFT JOIN FETCH k.alan a LEFT JOIN FETCH a.bina b LEFT JOIN FETCH b.bagliBirim bb LEFT JOIN FETCH bb.mudurluk m "
                                                     + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi "
                                                     + "LEFT JOIN FETCH e.anaFotograf ef "
                                                     + "WHERE e.aktif = true AND e.silinmis = false AND e.permanentId is not NULL AND "
                                                     + "k.id = :konumId "
                                                     + "ORDER BY e.id DESC")
@NamedQuery(name = "Eser.reportByAlan", query = "SELECT DISTINCT e FROM Eser e "
                                                + "LEFT JOIN FETCH e.eserHarekets h LEFT JOIN FETCH h.arastirma LEFT JOIN FETCH h.eserGelisSekli LEFT JOIN FETCH h.kazi "
                                                + "LEFT JOIN FETCH e.eserDepos d LEFT JOIN FETCH d.alanKonumu k LEFT JOIN FETCH k.alan a LEFT JOIN FETCH a.bina b LEFT JOIN FETCH b.bagliBirim bb LEFT JOIN FETCH bb.mudurluk m "
                                                + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi "
                                                + "LEFT JOIN FETCH e.anaFotograf ef "
                                                + "WHERE e.aktif = true AND e.silinmis = false AND e.permanentId is not NULL AND "
                                                + "a.id = :alanId "
                                                + "ORDER BY e.id DESC")
@NamedQuery(name = "Eser.reportByBina", query = "SELECT DISTINCT e FROM Eser e "
                                                + "LEFT JOIN FETCH e.eserHarekets h LEFT JOIN FETCH h.arastirma LEFT JOIN FETCH h.eserGelisSekli LEFT JOIN FETCH h.kazi "
                                                + "LEFT JOIN FETCH e.eserDepos d LEFT JOIN FETCH d.alanKonumu k LEFT JOIN FETCH k.alan a LEFT JOIN FETCH a.bina b LEFT JOIN FETCH b.bagliBirim bb LEFT JOIN FETCH bb.mudurluk m "
                                                + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi "
                                                + "LEFT JOIN FETCH e.anaFotograf ef "
                                                + "WHERE e.aktif = true AND e.silinmis = false AND e.permanentId is not NULL AND "
                                                + " b.id = :binaId "
                                                + "ORDER BY e.id DESC")
@NamedQuery(name = "Eser.reportByBagliBirim", query = "SELECT DISTINCT e FROM Eser e "
                                                      + "LEFT JOIN FETCH e.eserHarekets h LEFT JOIN FETCH h.arastirma LEFT JOIN FETCH h.eserGelisSekli LEFT JOIN FETCH h.kazi "
                                                      + "LEFT JOIN FETCH e.eserDepos d LEFT JOIN FETCH d.alanKonumu k LEFT JOIN FETCH k.alan a LEFT JOIN FETCH a.bina b LEFT JOIN FETCH b.bagliBirim bb LEFT JOIN FETCH bb.mudurluk m "
                                                      + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi "
                                                      + "LEFT JOIN FETCH e.anaFotograf ef "
                                                      + "WHERE e.aktif = true AND e.silinmis = false AND e.permanentId is not NULL AND "
                                                      + "bb.id = :bagliBirimId "
                                                      + "ORDER BY e.id DESC")
@NamedQuery(name = "Eser.reportByMuzeMudurlugu", query = "SELECT DISTINCT e FROM Eser e "
                                                         + "LEFT JOIN FETCH e.eserHarekets h LEFT JOIN FETCH h.arastirma LEFT JOIN FETCH h.eserGelisSekli LEFT JOIN FETCH h.kazi "
                                                         + "LEFT JOIN FETCH e.eserDepos d LEFT JOIN FETCH d.alanKonumu k LEFT JOIN FETCH k.alan a LEFT JOIN FETCH a.bina b LEFT JOIN FETCH b.bagliBirim bb LEFT JOIN FETCH bb.mudurluk m "
                                                         + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi "
                                                         + "LEFT JOIN FETCH e.anaFotograf ef "
                                                         + "WHERE e.aktif = true AND e.silinmis = false AND e.permanentId is not NULL AND "
                                                         + "m.id = :mudurlukId "
                                                         + "ORDER BY e.id DESC")
@NamedQuery(name = "Eser.findByNameAndMuseumDirectorate", query = "SELECT e FROM Eser e LEFT JOIN FETCH e.eserAltTur WHERE e.id IN ("
                                                                  + "SELECT ed.eser.id FROM EserDepo ed JOIN ed.alanKonumu ak JOIN ak.alan a JOIN a.bina b JOIN b.bagliBirim bb JOIN bb.mudurluk m "
                                                                  + "WHERE m.id = :id) AND e.aktif = true AND e.silinmis = false AND e.versiyon = 1 AND e.permanentId is not NULL")
@NamedQuery(name = "Eser.findByEnvanterNoAndMuseumDirectorate", query = "SELECT e FROM Eser e LEFT JOIN FETCH e.eserAltTur WHERE e.id IN ("
                                                                        + "SELECT ed.eser.id FROM EserDepo ed JOIN ed.alanKonumu ak JOIN ak.alan a JOIN a.bina b JOIN b.bagliBirim bb JOIN bb.mudurluk m "
                                                                        + "WHERE m.id = :id) AND e.envanterNo = :envNo AND e.aktif = true AND e.silinmis = false")
@NamedQuery(name = "Eser.findByNameAndMuzeMudurluguExcludeIds", query = "SELECT DISTINCT e FROM Eser e LEFT JOIN FETCH e.anaFotograf f "
                                                                        + "JOIN e.eserDepos d JOIN d.alanKonumu k JOIN k.alan a JOIN a.bina b JOIN b.bagliBirim bb JOIN bb.mudurluk m "
                                                                        + "WHERE e.aktif = true AND e.silinmis = false AND e.versiyon = 1 AND e.id NOT IN :ids AND bb.mudurluk in :muzeler "
                                                                        + "AND (e.eserOzelAdi LIKE :ad OR e.permanentId LIKE :ad) ORDER BY e.id DESC")
@NamedQuery(name = "Eser.findApprovedArtifactByPermanentId", query = "SELECT e FROM Eser e WHERE e.permanentId is not NULL AND e.permanentId = :permanentId ")
@NamedNativeQuery(name = "Eser.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM SecurityImplementation s WHERE s.eserId = :id)")

@ValidEser(groups = EserGroup.class)
public class Eser extends EserSuper implements DeleteValidatable
{

    private static final long              serialVersionUID = 5834980331509052387L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserFotograf>              eserFotografs;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(mappedBy = "eser", fetch = FetchType.LAZY)
    @Where(clause = " ANA_FOTOGRAF = 1 ")
    private Set<EserFotograf>              anaFotograf;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserMeasure>               eserMeasures;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserCizim>                 eserCizims;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<Transcription>             transcriptions;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserKeyword>               eserKeywords;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserDepo>                  eserDepos;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserMalzemeYapimTeknigi>   eserMalzemeYapimTeknigis;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigis;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserStil>                  eserStils;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserAtolye>                eserAtolyes;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserKaynakLiteratur>       eserKaynakLiteraturs;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserYayinLiteratur>        eserYayinLiteraturs;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserSerh>                  eserSerhs;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserMedia>                 videos;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "KONDISYON_DURUMU", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                       kondisyonDurumu;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "SIKKE_DARP_YONU", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                       sikkeDarpYonu;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "YAZMA_BASMA_SECIMI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                       yazmaBasmaSecimi;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ISLAMI_GAYRI_SECIMI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                       islamiGayriSecimi;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ELISI_DOKUMA_SECIMI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                       elisiDokumaSecimi;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "DUZENLEME_KULLANICI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Kullanici                      updatedBy;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "SILME_KULLANICI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Kullanici                      deletedBy;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "YARATMA_KULLANICI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Kullanici                      createdBy;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ONAYLAYAN_KULLANICI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Kullanici                      onaylayanKullanici;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserHareket>               eserHarekets;

    @JoinColumn(name = "eserOneriPersonel", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView                   eserOneriPersonel;

    @JoinColumn(name = "researcherId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Researcher                     eserOneriResearcher;

    @Column(name = "eserOneriYapanKisiOzet", length = 100)
    private String                         eserOneriYapanKisiOzet;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "file3DPath", length = 250)
    private String                         file3DPath;

    @Column(name = "objectId")
    private Integer                        objectId;

    @Column(name = "researcherSuggestion")
    private Boolean                        researcherSuggestion;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mudurlukId")
    private Mudurluk                       mudurluk;

    @Version
    private int                            version;

    /**
     * Instantiates a new eser.
     */

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public List<EserMeasure> getListFromEserMeasure(final Set<EserMeasure> eserMeasures) {
        return new ArrayList<>(eserMeasures);
    }

    public List<EserMalzemeYapimTeknigi> getListFromEserMalzemeYapimTeknigis(final Set<EserMalzemeYapimTeknigi> eserMalzemeYapimTeknigis) {
        return new ArrayList<>(eserMalzemeYapimTeknigis);
    }

    public Eser() {
        // empty constructor for generation of model
    }
    // getters and setters ....................................................

    public MuesPick getSikkeDarpYonu() {
        return this.sikkeDarpYonu;
    }

    public void setSikkeDarpYonu(final MuesPick sikkeDarpYonu) {
        this.sikkeDarpYonu = sikkeDarpYonu;
    }

    public MuesPick getYazmaBasmaSecimi() {
        return this.yazmaBasmaSecimi;
    }

    public void setYazmaBasmaSecimi(final MuesPick yazmaBasmaSecimi) {
        this.yazmaBasmaSecimi = yazmaBasmaSecimi;
    }

    public MuesPick getIslamiGayriSecimi() {
        return this.islamiGayriSecimi;
    }

    public void setIslamiGayriSecimi(final MuesPick islamiGayriSecimi) {
        this.islamiGayriSecimi = islamiGayriSecimi;
    }

    public MuesPick getElisiDokumaSecimi() {
        return this.elisiDokumaSecimi;
    }

    public void setElisiDokumaSecimi(final MuesPick elisiDokumaSecimi) {
        this.elisiDokumaSecimi = elisiDokumaSecimi;
    }

    public Kullanici getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(final Kullanici updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Kullanici getDeletedBy() {
        return this.deletedBy;
    }

    public void setDeletedBy(final Kullanici deletedBy) {
        this.deletedBy = deletedBy;
    }

    public Kullanici getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(final Kullanici createdBy) {
        this.createdBy = createdBy;
    }

    public MuesPick getKondisyonDurumu() {
        return this.kondisyonDurumu;
    }

    public void setKondisyonDurumu(final MuesPick kondisyonDurumu) {
        this.kondisyonDurumu = kondisyonDurumu;
    }

    public void setOnaylayanKullanici(final Kullanici onaylayanKullanici) {
        this.onaylayanKullanici = onaylayanKullanici;
    }

    public Kullanici getOnaylayanKullanici() {
        return this.onaylayanKullanici;
    }

    public Set<EserDepo> getEserDepos() {
        return this.eserDepos;
    }

    public void setEserDepos(final Set<EserDepo> eserDepos) {
        this.eserDepos = eserDepos;
    }

    public EserDepo getEserDepo() {
        return Optional.ofNullable(this.eserDepos).orElse(Collections.emptySet()).stream().filter(EserDepo::getAktif).findFirst().orElse(null);
    }

    public void setEserDepo(final EserDepo eserDepo) {
        if (this.eserDepos == null) {
            this.eserDepos = new LinkedHashSet<>();
        } else {
            this.eserDepos.clear();
        }
        this.eserDepos.add(eserDepo);
    }

    public Set<EserMeasure> getEserMeasures() {
        return this.eserMeasures;
    }

    public void setEserMeasures(final Set<EserMeasure> eserMeasures) {
        this.eserMeasures = eserMeasures;
    }

    public Set<EserCizim> getEserCizims() {
        return this.eserCizims;
    }

    public void setEserCizims(final Set<EserCizim> eserCizims) {
        this.eserCizims = eserCizims;
    }

    public Set<Transcription> getTranscriptions() {
        return this.transcriptions;
    }

    public void setTranscriptions(final Set<Transcription> transcriptions) {
        this.transcriptions = transcriptions;
    }

    public Set<EserMalzemeYapimTeknigi> getEserMalzemeYapimTeknigis() {
        return this.eserMalzemeYapimTeknigis;
    }

    public void setEserMalzemeYapimTeknigis(final Set<EserMalzemeYapimTeknigi> eserMalzemeYapimTeknigis) {
        this.eserMalzemeYapimTeknigis = eserMalzemeYapimTeknigis;
    }

    public Set<EserMalzemeSuslemeTeknigi> getEserMalzemeSuslemeTeknigis() {
        return this.eserMalzemeSuslemeTeknigis;
    }

    public void setEserMalzemeSuslemeTeknigis(final Set<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigis) {
        this.eserMalzemeSuslemeTeknigis = eserMalzemeSuslemeTeknigis;
    }

    public Set<EserStil> getEserStils() {
        if (this.eserStils == null) {
            this.eserStils = new LinkedHashSet<>();
        }
        return this.eserStils;
    }

    public void setEserStils(final Set<EserStil> eserStils) {
        this.eserStils = eserStils;
    }

    public Set<EserAtolye> getEserAtolyes() {
        return this.eserAtolyes;
    }

    public void setEserAtolyes(final Set<EserAtolye> eserAtolyes) {
        this.eserAtolyes = eserAtolyes;
    }

    public Set<EserKaynakLiteratur> getEserKaynakLiteraturs() {
        return this.eserKaynakLiteraturs;
    }

    public void setEserKaynakLiteraturs(final Set<EserKaynakLiteratur> eserKaynakLiteraturs) {
        this.eserKaynakLiteraturs = eserKaynakLiteraturs;
    }

    public Set<EserYayinLiteratur> getEserYayinLiteraturs() {
        return this.eserYayinLiteraturs;
    }

    public void setEserYayinLiteraturs(final Set<EserYayinLiteratur> eserYayinLiteraturs) {
        this.eserYayinLiteraturs = eserYayinLiteraturs;
    }

    public Set<EserSerh> getEserSerhs() {
        return this.eserSerhs;
    }

    public void setEserSerhs(final Set<EserSerh> eserSerhs) {
        this.eserSerhs = eserSerhs;
    }

    public Set<EserKeyword> getEserKeywords() {
        return this.eserKeywords;
    }

    public void setEserKeywords(final Set<EserKeyword> eserKeywords) {
        this.eserKeywords = eserKeywords;
    }

    public Set<EserFotograf> getEserFotografs() {
        if (this.eserFotografs == null) {
            this.eserFotografs = new LinkedHashSet<>();
        }
        return this.eserFotografs;
    }

    public Set<EserMedia> getVideos() {
        return this.videos;
    }

    public void setVideos(final Set<EserMedia> videos) {
        this.videos = videos;
    }

    public void setEserFotografs(final Set<EserFotograf> eserFotografs) {
        this.eserFotografs = eserFotografs;
    }

    public Set<EserFotograf> getAnaFotograf() {
        return this.anaFotograf;
    }

    public void setAnaFotograf(final Set<EserFotograf> anaFotograf) {
        this.anaFotograf = anaFotograf;
    }

    public Set<EserHareket> getEserHarekets() {
        return this.eserHarekets;
    }

    public void setEserHarekets(final Set<EserHareket> eserHarekets) {
        this.eserHarekets = eserHarekets;
    }

    public PersonelView getEserOneriPersonel() {
        return this.eserOneriPersonel;
    }

    public void setEserOneriPersonel(final PersonelView eserOneriPersonel) {
        this.eserOneriPersonel = eserOneriPersonel;
    }

    public String getEserOneriYapanKisiOzet() {
        return this.eserOneriYapanKisiOzet;
    }

    public void setEserOneriYapanKisiOzet(final String eserOneriYapanKisiOzet) {
        this.eserOneriYapanKisiOzet = eserOneriYapanKisiOzet;
    }

    public Researcher getEserOneriResearcher() {
        return this.eserOneriResearcher;
    }

    public void setEserOneriResearcher(final Researcher eserOneriResearcher) {
        this.eserOneriResearcher = eserOneriResearcher;
    }

    public String getMalzemeTitles() {
        final StringJoiner sj = new StringJoiner(", ");
        if ((this.getEserMalzemeYapimTeknigis() != null)) {
            this.getEserMalzemeYapimTeknigis().stream().forEach(x -> sj.add(x.getMalzeme().getAd()));
        } else {
            return null;
        }
        return sj.toString();
    }

    public Integer getObjectId() {
        return this.objectId;
    }

    public void setObjectId(final Integer objectId) {
        this.objectId = objectId;
    }

    public String getFile3DPath() {
        return this.file3DPath;
    }

    public void setFile3DPath(final String file3dPath) {
        this.file3DPath = file3dPath;
    }

    public String getEserKonumu() {
        final StringJoiner sj = new StringJoiner(" > ");
        if ((this.getEserDepo() != null)
            && (this.getEserDepo().getAlanKonumu() != null)
            && (this.getEserDepo().getAlanKonumu().getAlan() != null)
            && (this.getEserDepo().getAlanKonumu().getAlan().getBina() != null)
            && (this.getEserDepo().getAlanKonumu().getAlan().getBina().getBagliBirim() != null)
            && (this.getEserDepo().getAlanKonumu().getAlan().getBina().getBagliBirim().getMudurluk() != null)) {
            sj.add(this.getEserDepo().getAlanKonumu().getAlan().getBina().getBagliBirim().getMudurluk().getAd());
            sj.add(this.getEserDepo().getAlanKonumu().getAlan().getBina().getBagliBirim().getAd());
            sj.add(this.getEserDepo().getAlanKonumu().getAlan().getBina().getAd());
            sj.add(this.getEserDepo().getAlanKonumu().getAlan().getAd());
            sj.add(this.getEserDepo().getAlanKonumu().getAd());
        } else {
            return null;
        }
        return sj.toString();
    }

    public String getZimmetPersonelTitles() {
        return this.getEserZimmets().stream().map(x -> x.getZimmetPersonel().getTitle()).limit(5).collect(Collectors.joining(", ", "", (this.getEserZimmets().size() > 5 ? ", ..." : "")));
    }

    public Boolean getResearcherSuggestion() {
        return this.researcherSuggestion;
    }

    public void setResearcherSuggestion(final Boolean researcherSuggestion) {
        this.researcherSuggestion = researcherSuggestion;
    }

    public int getVersion() {
        return this.version;
    }

    public void setVersion(final int version) {
        this.version = version;
    }

}
