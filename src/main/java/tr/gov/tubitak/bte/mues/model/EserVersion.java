package tr.gov.tubitak.bte.mues.model;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum EserVersion {

    /** default: null */
    DEFAULT(0, "Default", "fa fa-window-minimize", true, true, MailEnum.DEFAULT),

    /** Onaylandı: 1 */
    APPROVED(1, "Onayland<PERSON>", "fa fa-check green", false, true, MailEnum.ARTIFACT_CREATE_APPROVED),

    // create part: 2, 4, 5, 6, 7, 25, 28, 32
    /** Taslak eser: 2 */
    DRAFT(2, "Geçici Kayıt", "fa fa-file-text-o gray", true, true, MailEnum.DEFAULT),

    /** Normal güncelleme: 4 */
    UPDATE_NORMAL(4, "Normal Güncelleme Geçici Kayıt", "fa fa-file-text yellow", true, true, MailEnum.DEFAULT),

    /** <PERSON><PERSON>l<PERSON> güncelleme: 5 */
    UPDATE_CERTIFIED(5, "Onaylı Güncelleme Geçici Kayıt", "fa fa-file-text orange", true, true, MailEnum.DEFAULT),

    /** Eser ilişkilendirme: 6 */
    ILISKILENDIRME(6, "İlişkilendirme Geçici Kayıt", "fa fa-file-text fuchsia", true, true, MailEnum.DEFAULT),

    /** Eser birleştirme: 7 */
    BIRLESTIRME(7, "Birleştirme Geçici Kayıt", "fa fa-file-text purple", true, true, MailEnum.DEFAULT),

    /** Komisyondan gelen taslak eser: 32 */
    DRAFT_FROM_COMMISSION(32, "Komisyondan Gelen Geçici Kayıt", "fa fa-file-text-o blue", true, true, MailEnum.DEFAULT),

    // pending part: 3, 8, 9, 10, 11, 26, 29
    /** Taslak eser onaya gönderildi: 3 */
    DRAFT_PENDING_REVIEW(3, "Taslak Eser Onayda", "fa fa-pause hardblue", false, false, MailEnum.DEFAULT),

    /** Normal güncelleme onaya gönderildi: 8 */
    UPDATE_NORMAL_PENDING_REVIEW(8, "Normal Güncelleme Onayda", "fa fa-pause yellow", false, false, MailEnum.DEFAULT),

    /** onaylı güncelleme onaya gönderildi: 9 */
    UPDATE_CERTIFIED_PENDING_REVIEW(9, "Onaylı Güncelleme Onayda", "fa fa-pause orange", false, false, MailEnum.DEFAULT),

    /** Eser ilişkilendirme onaya gönderildi: 10 */
    ILISKILENDIRME_PENDING_REVIEW(10, "İlişkilendirme Onayda", "fa fa-pause fuchsia", false, false, MailEnum.DEFAULT),

    /** Eser birleştirme onaya gönderildi: 11 */
    BIRLESTIRME_PENDING_REVIEW(11, "Birleştirme Onayda", "fa fa-pause purple", false, false, MailEnum.DEFAULT),

    /** Araştırmacı Katkısı için uzman güncellemesi: 25 */
    RESEARCHER_SUGGESTION(25, "Araştırmacı Katkı Geçici Kayıt", "fa fa-file-text blue", true, true, MailEnum.DEFAULT),

    /** Araştırmacı Katkısı için uzman ONAYLI güncellemesi: 28 */
    RESEARCHER_SUGGESTION_CERTIFIED(28, "Araştırmacı Katkı Onaylı Güncelleme Geçici Kayıt", "fa fa-file-text blue", true, true, MailEnum.DEFAULT),

    /** Araştırmacı Katkısı için güncelleme onaya gönderildi: 26 */
    RESEARCHER_SUGGESTION_PENDING_REVIEW(26, "Araştırmacı Katkı Onayda", "fa fa-file-text blue", false, false, MailEnum.DEFAULT),

    /** Araştırmacı Katkısı için ONAYLI güncelleme onaya gönderildi: 29 */
    RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW(29, "Araştırmacı Katkı Onaylı Güncelleme Onayda", "fa fa-file-text blue", false, false, MailEnum.DEFAULT),

    /** Araştırmacı Katkısı için güncelleme geri gönderildi: -26 */
    RESEARCHER_SUGGESTION_REJECTED(-26, "Araştırmacı Katkı Geri Gönderildi", "fa fa-undo blue", true, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Araştırmacı Katkısı için ONAYLI güncelleme geri gönderildi: -29 */
    RESEARCHER_SUGGESTION_CERTIFIED_REJECTED(-29, "Araştırmacı Katkı Onaylı Güncelleme Geri Gönderildi", "fa fa-undo blue", true, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Araştırmacı Katkısı için güncelleme geri gönderildi: 27 */
    RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW(27, "Geri Gönderilen Araştırmacı Katkı Onayda", "fa fa-undo blue", false, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Araştırmacı Katkısı için güncelleme geri gönderildi: 30 */
    RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW(30, "Geri Gönderilen Araştırmacı Katkı Onaylı Güncelleme Onayda", "fa fa-undo blue", false, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    // reject part: -3, -8, -9, -10, -11, -26, -29
    /** Taslak eser geri gönderildi: -3 */
    DRAFT_REJECTED(-3, "Taslak Eser Geri Gönderildi", "fa fa-undo hardblue", true, true, MailEnum.ARTIFACT_CREATE_REJECTED),

    /** Normal güncelleme geri gönderildi: -8 */
    UPDATE_NORMAL_REJECTED(-8, "Normal Güncelleme Geri Gönderildi", "fa fa-undo yellow", true, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Onaylı güncelleme geri gönderildi: -9 */
    UPDATE_CERTIFIED_REJECTED(-9, "Onaylı Güncelleme Geri Gönderildi", "fa fa-undo orange", true, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Eser ilişkilendirme geri gönderildi: -10 */
    ILISKILENDIRME_REJECTED(-10, "İlişkilendirme Geri Gönderildi", "fa fa-undo fuchsia", true, true, MailEnum.ARTIFACT_GROUP_REJECTED),

    /** Eser birleştirme geri gönderildi: -11 */
    BIRLESTIRME_REJECTED(-11, "Birleştirme Geri Gönderildi", "fa fa-undo purple", true, true, MailEnum.ARTIFACT_JOIN_REJECTED),

    // reject resubmit part: 12, 13, 14, 15, 16, 27, 30
    /** Geri gönderilen taslak eser onayda: 12 */
    DRAFT_REJECTED_PENDING_REVIEW(12, "Geri Gönderilen Taslak Eser Onayda", "fa fa-undo hardblue", false, true, MailEnum.ARTIFACT_CREATE_REJECTED),

    /** Geri gönderilen normal güncelleme onayda: 13 */
    UPDATE_NORMAL_REJECTED_PENDING_REVIEW(13, "Geri Gönderilen Normal Güncelleme Onayda", "fa fa-undo yellow", false, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Geri gönderilen onaylı güncelleme onayda: 14 */
    UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW(14, "Geri Gönderilen Onaylı Güncelleme Onayda", "fa fa-undo orange", false, true, MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Geri gönderilen ilişkilendirme onayda: 15 */
    ILISKILENDIRME_REJECTED_PENDING_REVIEW(15, "Geri Gönderilen İlişkilendirme Onayda", "fa fa-undo fuchsia", false, true, MailEnum.ARTIFACT_GROUP_REJECTED),

    /** Geri gönderilen birleştirme onayda: 16 */
    BIRLESTIRME_REJECTED_PENDING_REVIEW(16, "Geri Gönderilen Birleştirme Onayda", "fa fa-undo purple", false, true, MailEnum.ARTIFACT_JOIN_REJECTED),

    // Zimmet Onayı
    /** Eser zimmet müdür onayı: 17 */
    LIABILITY_PENDING_REVIEW(17, "Eser Zimmeti Onayda", "fa fa-info-circle hardblue", false, true, MailEnum.ARTIFACT_UPDATE_APPROVED),

    // Zimmet Onayı
    /** Eser zimmetten dusme müdür onayı: 18 */
    LIABILITY_DROP_PENDING_REVIEW(18, "Eser Zimmetten Düşme Onayda", "fa fa-info-circle hardblue", false, true, MailEnum.ARTIFACT_UPDATE_APPROVED),

    // Müzeler Arası Eser Devir
    /** Müzeler arası eser devir müdür onayı bekliyor: 20 */
    MUSEUM_TRANSFER_PENDING_REVIEW(20, "Müzeler Arası Devir - Devreden Müze Onayı", "fa fa-undo purple", false, false,  MailEnum.ARTIFACT_UPDATE_APPROVED),

    /** Müzeler arası eser devir müdür reddi: 21 */
    MUSEUM_TRANSFER_REJECTED(21, "Müzeler Arası Devir - Devreden Müzede Reddedildi", "fa fa-undo purple", false, false,  MailEnum.ARTIFACT_UPDATE_REJECTED),

    /** Müzeler arası eser devir alan müdür değerlendirmesi: 22 */
    MUSEUM_TRANSFER_RECEIVER_PENDING_REVIEW(22, "Müzeler Arası Devir - Devralan Müze Onayı", "fa fa-undo purple", false, false,  MailEnum.ARTIFACT_UPDATE_APPROVED),

    /** Müzeler arası eser devir alan müdür onayı: 23 */
    MUSEUM_TRANSFER_RECEIVER_APPROVED(23, "Müzeler Arası Devir - Devralan Müzede Onaylandı", "fa fa-undo purple",false, true,  MailEnum.ARTIFACT_UPDATE_APPROVED),

    /** Müzeler arası eser devir alan müdür reddi: 24 */
    MUSEUM_TRANSFER_RECEIVER_REJECTED(24, "Müzeler Arası Devir - Devralan Müzede Reddedildi", "fa fa-undo purple", false, false,  MailEnum.ARTIFACT_UPDATE_REJECTED),

    ;

    private final int                              code;

    private final String                           label;

    private final String                           icon;

    private final boolean                          modifiable;

    private final boolean                          regular;

    private EserVersion                            next;

    private EserVersion                            prev;

    private EserVersion                            self;

    private MailEnum                               mail;

    private static final Map<Integer, EserVersion> map = new HashMap<>();

    private static final List<Integer>             createList;

    private static final List<Integer>             pendingList;

    private static final List<Integer>             pendingListWithoutLiability;

    private static final List<Integer>             bulkPendingList;

    private static final List<Integer>             rejectList;

    private static final List<Integer>             certifiedList;

    static {
        for (final EserVersion each : EserVersion.values()) {
            map.put(each.getCode(), each);
        }

        createList = Arrays.asList(DRAFT.code,
                                   UPDATE_NORMAL.code,
                                   UPDATE_CERTIFIED.code,
                                  
                                   ILISKILENDIRME.code,
                                   BIRLESTIRME.code,
                                   RESEARCHER_SUGGESTION.code,
                                   RESEARCHER_SUGGESTION_CERTIFIED.code,
                                   DRAFT_FROM_COMMISSION.code);

        pendingList = Arrays.asList(DRAFT_PENDING_REVIEW.code,
                                    UPDATE_NORMAL_PENDING_REVIEW.code,
                                    UPDATE_CERTIFIED_PENDING_REVIEW.code,
                                    ILISKILENDIRME_PENDING_REVIEW.code,
                                    BIRLESTIRME_PENDING_REVIEW.code,
                                    RESEARCHER_SUGGESTION_PENDING_REVIEW.code,
                                    RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW.code,
                                    DRAFT_REJECTED_PENDING_REVIEW.code,
                                    UPDATE_NORMAL_REJECTED_PENDING_REVIEW.code,
                                    UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW.code,
                                    ILISKILENDIRME_REJECTED_PENDING_REVIEW.code,
                                    BIRLESTIRME_REJECTED_PENDING_REVIEW.code,
                                    LIABILITY_DROP_PENDING_REVIEW.code,
                                    LIABILITY_PENDING_REVIEW.code,
                                    RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW.code,
                                    RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW.code);

        pendingListWithoutLiability = Arrays.asList(DRAFT_PENDING_REVIEW.code,
                                                    UPDATE_NORMAL_PENDING_REVIEW.code,
                                                    UPDATE_CERTIFIED_PENDING_REVIEW.code,
                                                    ILISKILENDIRME_PENDING_REVIEW.code,
                                                    BIRLESTIRME_PENDING_REVIEW.code,
                                                    RESEARCHER_SUGGESTION_PENDING_REVIEW.code,
                                                    RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW.code,
                                                    DRAFT_REJECTED_PENDING_REVIEW.code,
                                                    UPDATE_NORMAL_REJECTED_PENDING_REVIEW.code,
                                                    UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW.code,
                                                    ILISKILENDIRME_REJECTED_PENDING_REVIEW.code,
                                                    BIRLESTIRME_REJECTED_PENDING_REVIEW.code,
                                                    RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW.code,
                                                    RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW.code);

        bulkPendingList = Arrays.asList(LIABILITY_PENDING_REVIEW.code, LIABILITY_DROP_PENDING_REVIEW.code);

        rejectList = Arrays.asList(DRAFT_REJECTED.code,
                                   UPDATE_NORMAL_REJECTED.code,
                                   UPDATE_CERTIFIED_REJECTED.code,
                                   ILISKILENDIRME_REJECTED.code,
                                   BIRLESTIRME_REJECTED.code,
                                   RESEARCHER_SUGGESTION_REJECTED.code,
                                   RESEARCHER_SUGGESTION_CERTIFIED_REJECTED.code);

        certifiedList = Arrays.asList(UPDATE_CERTIFIED.code, UPDATE_CERTIFIED_REJECTED.code, RESEARCHER_SUGGESTION_CERTIFIED.code, RESEARCHER_SUGGESTION_CERTIFIED_REJECTED.code);

        // initial

        DRAFT.prev = DRAFT;
        DRAFT.self = DRAFT;
        DRAFT.next = DRAFT_PENDING_REVIEW;

        UPDATE_NORMAL.prev = UPDATE_NORMAL;
        UPDATE_NORMAL.self = UPDATE_NORMAL;
        UPDATE_NORMAL.next = UPDATE_NORMAL_PENDING_REVIEW;

        UPDATE_CERTIFIED.prev = UPDATE_CERTIFIED;
        UPDATE_CERTIFIED.self = UPDATE_CERTIFIED;
        UPDATE_CERTIFIED.next = UPDATE_CERTIFIED_PENDING_REVIEW;

        BIRLESTIRME.prev = BIRLESTIRME;
        BIRLESTIRME.self = BIRLESTIRME;
        BIRLESTIRME.next = BIRLESTIRME_PENDING_REVIEW;

        ILISKILENDIRME.prev = ILISKILENDIRME;
        ILISKILENDIRME.self = ILISKILENDIRME;
        ILISKILENDIRME.next = ILISKILENDIRME_PENDING_REVIEW;

        DRAFT_FROM_COMMISSION.prev = DRAFT_FROM_COMMISSION;
        DRAFT_FROM_COMMISSION.self = DRAFT_FROM_COMMISSION;
        DRAFT_FROM_COMMISSION.next = DRAFT_PENDING_REVIEW;

        RESEARCHER_SUGGESTION.prev = RESEARCHER_SUGGESTION;
        RESEARCHER_SUGGESTION.self = RESEARCHER_SUGGESTION;
        RESEARCHER_SUGGESTION.next = RESEARCHER_SUGGESTION_PENDING_REVIEW;

        RESEARCHER_SUGGESTION_CERTIFIED.prev = RESEARCHER_SUGGESTION_CERTIFIED;
        RESEARCHER_SUGGESTION_CERTIFIED.self = RESEARCHER_SUGGESTION_CERTIFIED;
        RESEARCHER_SUGGESTION_CERTIFIED.next = RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW;

        // pending

        DRAFT_PENDING_REVIEW.prev = DRAFT_REJECTED;
        DRAFT_PENDING_REVIEW.self = DRAFT_PENDING_REVIEW;
        DRAFT_PENDING_REVIEW.next = APPROVED;

        UPDATE_NORMAL_PENDING_REVIEW.prev = UPDATE_NORMAL_REJECTED;
        UPDATE_NORMAL_PENDING_REVIEW.self = UPDATE_NORMAL_PENDING_REVIEW;
        UPDATE_NORMAL_PENDING_REVIEW.next = APPROVED;

        UPDATE_CERTIFIED_PENDING_REVIEW.prev = UPDATE_CERTIFIED_REJECTED;
        UPDATE_CERTIFIED_PENDING_REVIEW.self = UPDATE_CERTIFIED_PENDING_REVIEW;
        UPDATE_CERTIFIED_PENDING_REVIEW.next = APPROVED;

        BIRLESTIRME_PENDING_REVIEW.prev = BIRLESTIRME_REJECTED;
        BIRLESTIRME_PENDING_REVIEW.self = BIRLESTIRME_PENDING_REVIEW;
        BIRLESTIRME_PENDING_REVIEW.next = APPROVED;

        ILISKILENDIRME_PENDING_REVIEW.prev = ILISKILENDIRME_REJECTED;
        ILISKILENDIRME_PENDING_REVIEW.self = ILISKILENDIRME_PENDING_REVIEW;
        ILISKILENDIRME_PENDING_REVIEW.next = APPROVED;

        LIABILITY_PENDING_REVIEW.prev = APPROVED;
        LIABILITY_PENDING_REVIEW.self = LIABILITY_PENDING_REVIEW;
        LIABILITY_PENDING_REVIEW.next = APPROVED;

        LIABILITY_DROP_PENDING_REVIEW.prev = APPROVED;
        LIABILITY_DROP_PENDING_REVIEW.self = LIABILITY_DROP_PENDING_REVIEW;
        LIABILITY_DROP_PENDING_REVIEW.next = APPROVED;

        RESEARCHER_SUGGESTION_PENDING_REVIEW.prev = RESEARCHER_SUGGESTION_REJECTED;
        RESEARCHER_SUGGESTION_PENDING_REVIEW.self = RESEARCHER_SUGGESTION_PENDING_REVIEW;
        RESEARCHER_SUGGESTION_PENDING_REVIEW.next = APPROVED;

        RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW.prev = RESEARCHER_SUGGESTION_CERTIFIED_REJECTED;
        RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW.self = RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW;
        RESEARCHER_SUGGESTION_CERTIFIED_PENDING_REVIEW.next = APPROVED;

        // rejected & pending review

        DRAFT_REJECTED.prev = DRAFT_REJECTED;
        DRAFT_REJECTED.self = DRAFT_REJECTED;
        DRAFT_REJECTED.next = DRAFT_REJECTED_PENDING_REVIEW;

        DRAFT_REJECTED_PENDING_REVIEW.prev = DRAFT_REJECTED;
        DRAFT_REJECTED_PENDING_REVIEW.self = DRAFT_REJECTED_PENDING_REVIEW;
        DRAFT_REJECTED_PENDING_REVIEW.next = APPROVED;

        UPDATE_NORMAL_REJECTED.prev = UPDATE_NORMAL_REJECTED;
        UPDATE_NORMAL_REJECTED.self = UPDATE_NORMAL_REJECTED;
        UPDATE_NORMAL_REJECTED.next = UPDATE_NORMAL_REJECTED_PENDING_REVIEW;

        UPDATE_NORMAL_REJECTED_PENDING_REVIEW.prev = UPDATE_NORMAL_REJECTED;
        UPDATE_NORMAL_REJECTED_PENDING_REVIEW.self = UPDATE_NORMAL_REJECTED_PENDING_REVIEW;
        UPDATE_NORMAL_REJECTED_PENDING_REVIEW.next = APPROVED;

        UPDATE_CERTIFIED_REJECTED.prev = UPDATE_CERTIFIED_REJECTED;
        UPDATE_CERTIFIED_REJECTED.self = UPDATE_CERTIFIED_REJECTED;
        UPDATE_CERTIFIED_REJECTED.next = UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW;

        UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW.prev = UPDATE_CERTIFIED_REJECTED;
        UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW.self = UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW;
        UPDATE_CERTIFIED_REJECTED_PENDING_REVIEW.next = APPROVED;

        BIRLESTIRME_REJECTED.prev = BIRLESTIRME_REJECTED;
        BIRLESTIRME_REJECTED.self = BIRLESTIRME_REJECTED;
        BIRLESTIRME_REJECTED.next = BIRLESTIRME_REJECTED_PENDING_REVIEW;

        BIRLESTIRME_REJECTED_PENDING_REVIEW.prev = BIRLESTIRME_REJECTED;
        BIRLESTIRME_REJECTED_PENDING_REVIEW.self = BIRLESTIRME_REJECTED_PENDING_REVIEW;
        BIRLESTIRME_REJECTED_PENDING_REVIEW.next = APPROVED;

        ILISKILENDIRME_REJECTED.prev = ILISKILENDIRME_REJECTED;
        ILISKILENDIRME_REJECTED.self = ILISKILENDIRME_REJECTED;
        ILISKILENDIRME_REJECTED.next = ILISKILENDIRME_REJECTED_PENDING_REVIEW;

        ILISKILENDIRME_REJECTED_PENDING_REVIEW.prev = ILISKILENDIRME_REJECTED;
        ILISKILENDIRME_REJECTED_PENDING_REVIEW.self = ILISKILENDIRME_REJECTED_PENDING_REVIEW;
        ILISKILENDIRME_REJECTED_PENDING_REVIEW.next = APPROVED;

        RESEARCHER_SUGGESTION_REJECTED.prev = RESEARCHER_SUGGESTION_REJECTED;
        RESEARCHER_SUGGESTION_REJECTED.self = RESEARCHER_SUGGESTION_REJECTED;
        RESEARCHER_SUGGESTION_REJECTED.next = RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW;

        RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW.prev = RESEARCHER_SUGGESTION_REJECTED;
        RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW.self = RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW;
        RESEARCHER_SUGGESTION_REJECTED_PENDING_REVIEW.next = APPROVED;

        RESEARCHER_SUGGESTION_CERTIFIED_REJECTED.prev = RESEARCHER_SUGGESTION_CERTIFIED_REJECTED;
        RESEARCHER_SUGGESTION_CERTIFIED_REJECTED.self = RESEARCHER_SUGGESTION_CERTIFIED_REJECTED;
        RESEARCHER_SUGGESTION_CERTIFIED_REJECTED.next = RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW;

        RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW.prev = RESEARCHER_SUGGESTION_CERTIFIED_REJECTED;
        RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW.self = RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW;
        RESEARCHER_SUGGESTION_CERTIFIED_REJECTED_PENDING_REVIEW.next = APPROVED;

        // Museum Transfer

        MUSEUM_TRANSFER_PENDING_REVIEW.prev = MUSEUM_TRANSFER_REJECTED;
        MUSEUM_TRANSFER_PENDING_REVIEW.self = MUSEUM_TRANSFER_PENDING_REVIEW;
        MUSEUM_TRANSFER_PENDING_REVIEW.next = MUSEUM_TRANSFER_RECEIVER_PENDING_REVIEW;

        MUSEUM_TRANSFER_RECEIVER_PENDING_REVIEW.prev = MUSEUM_TRANSFER_RECEIVER_REJECTED;
        MUSEUM_TRANSFER_RECEIVER_PENDING_REVIEW.self = MUSEUM_TRANSFER_RECEIVER_PENDING_REVIEW;
        MUSEUM_TRANSFER_RECEIVER_PENDING_REVIEW.next = MUSEUM_TRANSFER_RECEIVER_APPROVED;

        MUSEUM_TRANSFER_RECEIVER_APPROVED.self = MUSEUM_TRANSFER_RECEIVER_APPROVED;
        MUSEUM_TRANSFER_RECEIVER_APPROVED.next = DRAFT_PENDING_REVIEW;
    }

    private EserVersion(final int code, final String label, final String icon, final boolean modifiable, final boolean regular, final MailEnum mail) {
        this.code = code;
        this.label = label;
        this.icon = icon;
        this.modifiable = modifiable;
        this.regular = regular;
        this.mail = mail;
    }

    public static EserVersion parse(final Integer number) {
        return map.getOrDefault(number, DEFAULT);
    }

    // getters ................................................................

    public int getCode() {
        return this.code;
    }

    public String getLabel() {
        return this.label;
    }

    public String getIcon() {
        return this.icon;
    }

    public EserVersion getNext() {
        return this.next;
    }

    public EserVersion getPrev() {
        return this.prev;
    }

    public boolean isModifiable() {
        return this.modifiable;
    }

    public boolean isRegular() {
        return this.regular;
    }

    public MailEnum getMail() {
        return this.mail;
    }

    public EserVersion getSelf() {
        return this.self;
    }

    public static List<Integer> getCreatelist() {
        return createList;
    }

    public static List<Integer> getPendinglist() {
        return pendingList;
    }

    public static List<Integer> getPendinglistWithoutLiability() {
        return pendingListWithoutLiability;
    }

    public static List<Integer> getRejectlist() {
        return rejectList;
    }

    public static List<Integer> getBulkPendinglist() {
        return bulkPendingList;
    }

    public static List<Integer> getCertifiedList() {
        return certifiedList;
    }

}
