package tr.gov.tubitak.bte.mues.search;

import java.util.List;
import java.util.stream.Collectors;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * Çoklu seçilebilir liste tabanlı kriterleri tutan sınıf..
 */
@Entity
@DiscriminatorValue("5")
public class MultiValuedListCriterion extends TextCriterion {

    @Transient
    private List<String>      values;

    private static final long serialVersionUID = 1979157639797295200L;

    /**
     * Yapıcı metot.
     */
    public MultiValuedListCriterion() {
    }

    public MultiValuedListCriterion(final Integer id) {
        super(id);
    }

    @Override
    @Transient
    public String getSql() {

        if (((this.getValues() == null) || (this.getValues().isEmpty()))) {

            return super.getSql();
        }

        // solr query cümleciği oluşturuluyor.
        final StringBuilder sb = new StringBuilder();

        if (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.ISNULL)) {
            sb.append(SearchConstants.LEFT_PARANTHESIS + SearchConstants.ALL_QUERY + SearchConstants.SPACE_LITERAL);
            sb.append("-");
        }

        sb.append(MuesUtil.surroundWithParanthesis(this.getValues()
                                                       .stream()
                                                       .map(x -> this.getMetadata().getName()
                                                                 + SearchConstants.SOLREQUALSSIGN
                                                                 + this.getComparisonOperatorEnum().getPrefix(this)
                                                                 + x
                                                                 + this.getComparisonOperatorEnum().getSuffix(this))
                                                       .collect(Collectors.joining(" OR "))));

        if (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.ISNULL)) {
            sb.append(SearchConstants.RIGHT_PARANTHESIS);
        }

        return sb.toString();
    }

    public List<String> getValues() {
        return this.values;
    }

    public void setValues(final List<String> values) {
        this.values = values;
    }

}
