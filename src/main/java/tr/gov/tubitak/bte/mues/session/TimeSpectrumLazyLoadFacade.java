package tr.gov.tubitak.bte.mues.session;

import java.util.Map;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.SortMeta;

import tr.gov.tubitak.bte.mues.model.mapping.TimeSpectrum;
import tr.gov.tubitak.bte.mues.search.SearchConstants;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class TimeSpectrumLazyLoadFacade extends LazyLoadFromDBFacade<TimeSpectrum> {

    private static final long serialVersionUID = 3344360888176915961L;

    @Inject
    protected EntityManager   em;

    public TimeSpectrumLazyLoadFacade() {
        super(TimeSpectrum.class);
        this.setCustomQuery(SearchConstants.EMPTY);
    }

    @Override
    protected String sortClause(final Map<String, SortMeta> multiSortMeta) {

        final StringBuilder str = new StringBuilder();

        if ((multiSortMeta != null) && !multiSortMeta.isEmpty()) {

            return super.sortClause(multiSortMeta);

        } else {
            str.append("kronolojiAdi");
            str.insert(0, " ORDER BY ");
            return str.toString();
        }
    }

    @Override
    protected String appendLike(final String param, final FilterMeta filterMeta) {
        if ("globalFilter".equals(param)) {
            return "(LOWER(o.kronolojiAdi) LIKE LOWER(:globalFilter) OR LOWER(o.cagAdi) LIKE LOWER(:globalFilter) OR LOWER(o.donemAdi) LIKE "
                   + "LOWER(:globalFilter) OR LOWER(o.uygarlikAdi) LIKE LOWER(:globalFilter) OR LOWER(o.hukumdarAdi) LIKE LOWER(:globalFilter))";
        }
        return super.appendLike(param, filterMeta);
    }

    @Override
    protected EntityManager getEM() {
        return this.em;
    }

}
