/***********************************************************
 * ApplicationType.java - mues <PERSON>si
 *
 * Kullanılan JRE: 1.8.0_91
 *
 * halis.yilboga - 19.03.2021
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2012©
 ***********************************************************/

package tr.gov.tubitak.bte.mues.util.enums;

public enum ApplicationType {

    ENVANTER(Values.ENVANTER),

    MUZEBILGISISTEMI(Values.MUZEBILGISISTEMI),

    PERSONEL(Values.PERSONEL),

    LABORATUVAR(Values.LABORATUVAR),

    KAM(Values.KAM),

    OMK(Values.OMK);

    String code;

    private ApplicationType(final String code) {
        this.code = code;
    }

    public static ApplicationType parse(final String code) {
        for (final ApplicationType each : ApplicationType.values()) {
            if (code.equals(each.name())) {
                return each;
            }
        }

        return ApplicationType.ENVANTER;
    }

    public String getCode() {
        return this.code;
    }
    // discrimator degerine code girilmedigi icin bu workaround uygulanmistir.
    public static class Values {
        private Values() {
            // intentially blank
        }

        public static final String ENVANTER         = "0";
        public static final String KAM              = "0";
        public static final String OMK              = "4";
        public static final String MUZEBILGISISTEMI = "1";
        public static final String PERSONEL         = "2";
        public static final String LABORATUVAR      = "3";
    }

}
