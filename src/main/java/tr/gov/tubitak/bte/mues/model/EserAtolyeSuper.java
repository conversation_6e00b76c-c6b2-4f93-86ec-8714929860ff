package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@MappedSuperclass
@NamedQuery(name = "EserAtolye.findEagerById", query = "SELECT x FROM EserAtolye x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.atolye WHERE x.id = :id")
@NamedQuery(name = "EserAtolye.findAll", query = "SELECT a FROM EserAtolye a")
@NamedQuery(name = "EserAtolye.findActive", query = "SELECT a FROM EserAtolye a WHERE a.aktif = true AND a.silinmis = false")
public class EserAtolyeSuper extends AbstractEntity {

    private static final long serialVersionUID = 5262644200337308054L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "atolyeId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Atolye            atolye;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public EserAtolyeSuper() {
    }

    // getters and setters ....................................................

    public Atolye getAtolye() {
        return this.atolye;
    }

    public void setAtolye(final Atolye atolye) {
        this.atolye = atolye;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.atolye.getAd()).orElse("" + this.getId());
    }

}
