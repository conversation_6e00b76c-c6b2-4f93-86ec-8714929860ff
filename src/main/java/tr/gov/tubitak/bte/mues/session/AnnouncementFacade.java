package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.persistence.NoResultException;

import tr.gov.tubitak.bte.mues.model.Announcement;
import tr.gov.tubitak.bte.mues.model.Kullanici;

/**
 *
*
 */
@RequestScoped
public class AnnouncementFacade extends AbstractFacade<Announcement> {

    public AnnouncementFacade() {
        super(Announcement.class);
    }

    public List<Announcement> findAll(final List<String> rolList) {
        return this.em.createNamedQuery("Announcement.findAll", Announcement.class).setParameter("rolList", rolList).getResultList();

    }

    public List<String> findKullaniciRolsCodeByKullanici(final Ku<PERSON><PERSON> kullanici) {
        return this.em.createNamedQuery("KullaniciBirimRol.findKullaniciRolsCodeByKullanici", String.class).setParameter("id", kullanici.getId()).getResultList();
    }

    public Announcement findByMaxId() {
        try {
            return this.em.createNamedQuery("Announcement.findByMaxId", Announcement.class).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }
}
