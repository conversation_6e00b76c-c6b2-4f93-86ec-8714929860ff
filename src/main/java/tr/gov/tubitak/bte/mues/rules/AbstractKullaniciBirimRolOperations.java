package tr.gov.tubitak.bte.mues.rules;

import java.io.Serializable;
import java.util.List;

import javax.enterprise.context.Dependent;
import javax.faces.application.FacesMessage;
import javax.inject.Inject;

import tr.gov.tubitak.bte.mues.jsf.KullaniciBirimRolController;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.session.KullaniciBirimRolFacade;
import tr.gov.tubitak.bte.mues.session.KullaniciFacade;
import tr.gov.tubitak.bte.mues.session.PersonelFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Dependent
public abstract class AbstractKullaniciBirimRolOperations implements Serializable {

    private static final long             serialVersionUID = 5320423568128809746L;

    

    @Inject
    protected KullaniciBirimRolFacade     facade;

    @Inject
    protected PersonelFacade              personelFacade;

    @Inject
    protected KullaniciFacade             kullaniciFacade;

    protected AbstractKullaniciBirimRolOperations() {
    	//blank constructor
    }

    public KullaniciBirimRolFacade getFacade() {
        return this.facade;
    }
    //abstract methods
    public abstract void changePersonelMudurluk(final List<AbstractEntity> entities, final KullaniciBirimRol kullaniciBirimRol, Personel personel, int from); 
    public abstract List<PersonelView> filterByFullNameFromPersonelPool(final String query);

    public String delete(final KullaniciBirimRol model, String deleteDescription) {
    	
        final DBOperationResult result = this.getFacade().virtualDeleteOperation(true, model, deleteDescription);
        
        if (result.isSuccess()) {
            final List<KullaniciBirimRol> yetkiler = this.facade.fetchKbrByUser(model.getKullanici());
            String message = "Yetki başarıyla kaldırıldı";

            // kullanici tarafindan rolun silindigini belirtmek için ilgili field'in set edilmesi
            model.setIsDeletedByUser(true);

            message += this.afterDelete(model, yetkiler);
            MuesUtil.showMessage(message);

        } else {
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
        }
        
        return null;
    }

    public Boolean modulSpecificCreationCheckAndDoCreationalDetails(KullaniciBirimRol kullaniciBirimRol, final List<AbstractEntity> entities, int from, Personel personel) {
        
        if (from == KullaniciBirimRolController.TYPE_PERSONEL) {
            final Kullanici kullanici = this.getFacade().create(personel);
            kullaniciBirimRol.setKullanici(kullanici);
            entities.add(kullanici);
        }

        this.changePersonelMudurluk(entities, kullaniciBirimRol,personel,from );
        entities.add(kullaniciBirimRol);
        return Boolean.TRUE;
    }

    
    public String afterDelete(final KullaniciBirimRol model, final List<KullaniciBirimRol> yetkiler) {
        String message = "";
        final Personel personel1 = this.personelFacade.findById(model.getKullanici().getPersonelView().getId());

        if (personel1 != null) {

            if (yetkiler.isEmpty()) {

                message = "Kullanıcı, yetkisi olmadığı için personel havuzuna geri gönderilmiştir.";

                if (this.kullaniciFacade.virtualDeleteOperation(true, model.getKullanici(), message).isSuccess()) {
                    personel1.setMudurluk(null);
                    personel1.setMudurlukAd(null);
                    this.personelFacade.update(personel1);

                } else {
                    message = "Kullanıcının, olusturdugu, güncellediği veya sildiği eser  bulunuduğu için kullanıcının bütün yetkileri silinemiyor."
                              + " Kullanıcıya yeni yetkisini verdikten sonra eski yetkiyi silebilirsiniz.";
                    this.getFacade().virtualDeleteOperation(false, model, message);
                }

            } else {
                if (yetkiler.get(yetkiler.size() - 1).getMudurluk() != null) {
                    personel1.setMudurluk(yetkiler.get(yetkiler.size() - 1).getMudurluk()); // get the last one
                    personel1.setMudurlukAd(yetkiler.get(yetkiler.size() - 1).getMudurluk().getAd());
                }
                this.personelFacade.update(personel1);
            }
        }
        return message;
    }
   
}
