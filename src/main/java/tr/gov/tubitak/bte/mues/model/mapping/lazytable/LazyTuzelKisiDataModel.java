package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.JpaLazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.util.SerializableSupplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.TuzelKisiController;
import tr.gov.tubitak.bte.mues.model.TuzelKisi;

public class LazyTuzelKisiDataModel extends JpaLazyDataModel<TuzelKisi> {

    private static final long         serialVersionUID = -8472871088513579940L;

    private List<TuzelKisi>           data;

    private static final Logger       logger           = LoggerFactory.getLogger(LazyTuzelKisiDataModel.class);

    private final TuzelKisiController tuzelKisiController;

    public LazyTuzelKisiDataModel(final Class<TuzelKisi> entityClass, final SerializableSupplier<EntityManager> entityManager, final TuzelKisiController tuzelKisiController) {
        super(entityClass, entityManager, "id");
        this.tuzelKisiController = tuzelKisiController;
    }

    @Override
    public List<TuzelKisi> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        final EntityManager em = this.entityManager.get();

        final CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<TuzelKisi> cq = cb.createQuery(this.entityClass);
        final Root<TuzelKisi> root = cq.from(this.entityClass);
        root.fetch("il", JoinType.LEFT);
        root.fetch("ilce", JoinType.LEFT);
        // Order By 'AD' initally
        cq.orderBy(cb.asc(root.get("ticariUnvan")));

        cq = cq.select(root);

        this.applyFilters(cb, cq, root, filterBy);
        this.applySort(cb, cq, root, sortBy);
        final TypedQuery<TuzelKisi> query = em.createQuery(cq);
        query.setFirstResult(first);
        query.setMaxResults(pageSize);

        logger.debug("filters: {} _ multiSortMeta Length, {}", sortBy, filterBy);

        this.data = query.getResultList();
        return this.data;
    }

    @Override
    public TuzelKisi getRowData(final String rowKey) {

        for (final TuzelKisi row : this.tuzelKisiController.getSelectionList()) {
            if (row.getId().toString().equals(rowKey)) {
                return row;
            }
        }
        for (final TuzelKisi each : this.data) {
            if (each.getId().equals(Integer.valueOf(rowKey))) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final TuzelKisi tuzelKisi) {
        return tuzelKisi.getId().toString();
    }

}
