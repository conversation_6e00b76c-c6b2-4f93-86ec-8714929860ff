package tr.gov.tubitak.bte.mues.util;

import java.util.Date;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.SignatureException;
import io.jsonwebtoken.UnsupportedJwtException;
import tr.gov.tubitak.bte.mues.model.Kullanici;

public class JWTGuard extends AccessControlFilter {

    private static final String SECRET_KEY    = "TuBiTaK*b760-MUES.jwt-secret";

    private static final long   EXPIRATION_MS = 600000;                                // 10 minutes

    private static final Logger logger        = LoggerFactory.getLogger(JWTGuard.class);

    public String generateJwtToken(final Kullanici user) {
        return this.generateTokenFromUsername(user.getTitle());
    }

    public String generateTokenFromUsername(final String username) {
        return Jwts.builder().setSubject(username).setIssuedAt(new Date()).setExpiration(new Date((new Date()).getTime() + EXPIRATION_MS)).signWith(SignatureAlgorithm.HS256, SECRET_KEY).compact();
    }

    public String getUserNameFromJwtToken(final String token) {
        return Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(token).getBody().getSubject();
    }

    @Override
    protected boolean isAccessAllowed(final ServletRequest request, final ServletResponse arg1, final Object arg2) throws Exception {

        boolean accessAllowed = false;

        if (SecurityUtils.getSubject().isAuthenticated()) {
            return Boolean.TRUE;
        }

        final String token = request.getParameter("t");

        if (token == null) {
            return Boolean.FALSE;
        }

        final String username = Jwts.parser().setSigningKey(JWTGuard.SECRET_KEY).parseClaimsJws(token).getBody().getSubject();
        final JWTAuthenticationToken jwtAuthToken = new JWTAuthenticationToken(username, token);

        accessAllowed = this.validateJwtToken(token);

        if (accessAllowed) {
            SecurityUtils.getSubject().login(jwtAuthToken);
        }

        return accessAllowed;
    }

    @Override
    protected boolean onAccessDenied(final ServletRequest arg0, final ServletResponse arg1) throws Exception {
        final HttpServletResponse response = (HttpServletResponse) arg1;
        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
        return false;
    }

    public boolean validateJwtToken(final String authToken) {
        try {
            Jwts.parser().setSigningKey(JWTGuard.SECRET_KEY).parseClaimsJws(authToken);
            logger.info("JWT validated token: {}", authToken);
            return true;
        } catch (final SignatureException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
        } catch (final MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
        } catch (final ExpiredJwtException e) {
            logger.error("JWT token is expired: {}", e.getMessage());
        } catch (final UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
        } catch (final IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
        }

        return false;
    }
}
