package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.PersonelYabanciDil;
import tr.gov.tubitak.bte.mues.session.PersonelYabanciDilFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
 * <AUTHOR>
 */
@Named
@ViewScoped
public class PersonelYabanciDilController extends AbstractController<PersonelYabanciDil> {

    private static final long        serialVersionUID = -4790994858737662781L;

    @Inject
    private PersonelYabanciDilFacade facade;

    @Inject
    private PersonelController       personelController;

    public PersonelYabanciDilController() {
        super(PersonelYabanciDil.class);
    }

    public List<PersonelYabanciDil> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    public List<Dil> filterByFullNameAndAciklamaPreventDuplicate(final String query) {

        final List<Dil> dilList = new ArrayList<>();

        if (this.personelController.getModel().getPersonelYabanciDil() != null) {
            for (final PersonelYabanciDil ehs : this.personelController.getModel().getPersonelYabanciDil()) {
                dilList.add(ehs.getDil());
            }
        }

        return this.getFacade().filterByFullNameAndAciklamaPreventDuplicate(query, MuesUtil.toIds(dilList));
    }

    // getters and setters ....................................................

    @Override
    public PersonelYabanciDilFacade getFacade() {
        return this.facade;
    }

}
