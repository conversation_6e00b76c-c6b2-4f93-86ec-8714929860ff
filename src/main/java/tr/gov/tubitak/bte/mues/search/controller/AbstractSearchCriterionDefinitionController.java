package tr.gov.tubitak.bte.mues.search.controller;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.enterprise.context.Dependent;
import javax.faces.application.FacesMessage;
import javax.faces.event.AjaxBehaviorEvent;
import javax.inject.Inject;

import org.apache.commons.collections4.CollectionUtils;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.ToggleSelectEvent;

import tr.gov.tubitak.bte.mues.jsf.AbstractController;
import tr.gov.tubitak.bte.mues.jsf.SessionBean;
import tr.gov.tubitak.bte.mues.model.Identifiable;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.search.AbstractCriterion;
import tr.gov.tubitak.bte.mues.search.AbstractSearchController;
import tr.gov.tubitak.bte.mues.search.CriterionEnum;
import tr.gov.tubitak.bte.mues.search.ICriterion;
import tr.gov.tubitak.bte.mues.search.SearchCriterionDefinition;
import tr.gov.tubitak.bte.mues.search.SearchDefinitionColumns;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Dependent
public class AbstractSearchCriterionDefinitionController<T extends Identifiable<?>> extends AbstractController<SearchCriterionDefinition> {

    private static final long               serialVersionUID     = 8279594678575644645L;

    protected AbstractSearchController<T>   searchController;

    @Inject
    private SolrSearcher                    solrSearcher;

    @Inject
    private SearchCriterionDefinitionFacade searchCriterionDefinitionFacade;

    @Inject
    private SessionBean                     sessionBean;

    private Set<Metadata>                   list;

    private List<Metadata>                  previousSelectedList = new LinkedList<>();

    private List<SearchDefinitionColumns>   columns;

    private List<SearchDefinitionColumns>   childColumns;

    private Boolean                         paginatorActive      = true;

    private boolean                         changeRangeActivated;

    public AbstractSearchCriterionDefinitionController() {
        super(SearchCriterionDefinition.class);
    }

    @Override
    public DBOperationResult create() {

        this.getModel().setLogicalOperator(this.searchController.getLogicalOperatorValue());
        this.getModel().setUser(this.sessionBean.getCurrentUser());
        final Set<ICriterion> criterions = new LinkedHashSet<>();
        for (final ICriterion criterion : this.searchController.getCriteriaList()) {
            criterions.add(criterion);
        }

        this.getModel().setCriteriaList(criterions);

        for (final ICriterion iCriterion : this.searchController.getCriteriaList()) {
            iCriterion.setSearchCriterionDefinition(this.getModel());
            ((AbstractCriterion) iCriterion).setId(null);
        }

        if (this.getColumns() != null) {

            for (final SearchDefinitionColumns searchDefinitionColumns : this.getColumns()) {
                searchDefinitionColumns.setId(null);
            }

            this.getModel().setColumns(new LinkedHashSet<>(this.getColumns()));

            for (final SearchDefinitionColumns column : this.getColumns()) {
                column.setId(null);
                column.setSearchCriterionDefinition(this.getModel());
            }
        }

        return super.create();
    }

    public void restoreQuery(final SearchCriterionDefinition searchCriterionDefinition) {

        this.setModel(this.searchCriterionDefinitionFacade.findEagerById(searchCriterionDefinition.getId()));

        final Set<ICriterion> criteriaList2 = this.getModel().getCriteriaList();

        // tıklanan aramanın parametreleri geliyor
        this.searchController.getCriteriaList().clear();
        this.searchController.getSelectedCriteriaList().clear();

        for (final ICriterion sc : criteriaList2) {
            // parent child durumuna göre ayarlama yapılacak
            ((AbstractCriterion) sc).setId(0);
            this.searchController.getCriteriaList().add(sc);
            this.searchController.resetCriterionFields();
        }
        this.previousSelectedList = new LinkedList<>();
        this.searchController.setSelectedList(new LinkedList<>());
        for (final SearchDefinitionColumns searchDefinitionColumns : this.getModel().getColumns()) {
            this.previousSelectedList.add(searchDefinitionColumns.getMetadata());
            this.searchController.getSelectedList().add(searchDefinitionColumns.getMetadata());
        }

        this.createDynamicColumns();
    }

    @Override
    public void delete() {
        final DBOperationResult result = this.getFacade().delete(this.getModel());
        this.setDeleteDescription(null);
        if (result.isSuccess()) {

            if (this.filteredValues != null) {

                this.filteredValues.remove(this.getModel());

            }
            this.items.remove(this.getModel());

            MuesUtil.showMessage("Kayıt başarıyla silindi.", FacesMessage.SEVERITY_INFO);
            this.logger.info("[delete] : Kayıt başarıyla silindi.");

        } else {
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
        }

    }

    public void actionViewColumnToggleAction(final ToggleSelectEvent toggleEventParam) {
        this.logger.info("[actionViewColumnToggleAction] :{}", toggleEventParam);
        this.createDynamicColumns();
    }

    public void actionViewColumnSelectionChange(final AjaxBehaviorEvent toggleEventParam) {

        final LinkedList<Metadata> tempList = new LinkedList<>(CollectionUtils.disjunction(this.searchController.getSelectedList(), this.previousSelectedList));

        if (this.previousSelectedList.size() > this.searchController.getSelectedList().size()) {
            this.previousSelectedList.remove(tempList.get(0));
        } else {
            this.previousSelectedList.add(tempList.get(0));
        }
        this.searchController.setSelectedList(this.previousSelectedList);
        this.createDynamicColumns();
        this.logger.debug("[actionViewColumnSelectionChange] :{}", toggleEventParam);

    }

    // Handle operations
    public void handleReportSelect(final SelectEvent<?> event) {
        final SearchCriterionDefinition selected = (SearchCriterionDefinition) event.getObject();

        this.restoreQuery(selected);

        this.searchController.makeDetailedSearch();
        this.setPaginatorActive(true);
    }

    @Override
    public AbstractFacade<SearchCriterionDefinition> getFacade() {
        return this.searchCriterionDefinitionFacade;
    }

    public Set<Metadata> getList() {

        if (this.list == null) {

            final Map<String, Metadata> metaDataMap = this.solrSearcher.getMetaDataMap();
            this.list = new LinkedHashSet<>();

            for (final Metadata metadata : metaDataMap.values()) {

                this.list.add(metadata);
            }
        }

        return this.list;
    }

    public Set<Metadata> getParentList() {
        return this.getList().stream().filter(x -> !x.isChildField()).collect(Collectors.toSet());
    }

    public Set<Metadata> getChildList() {
        return this.getList().stream().filter(x -> x.isChildField()).collect(Collectors.toSet());
    }

    public List<Metadata> getPreviousSelectedList() {
        return this.previousSelectedList;
    }

    public void setPreviousSelectedList(final List<Metadata> previousSelectedList) {
        this.previousSelectedList = previousSelectedList;
    }

    public List<SearchDefinitionColumns> getColumns() {
        return this.columns;
    }

    public List<SearchDefinitionColumns> getChildColumns() {
        return this.childColumns;
    }

    public void createDynamicColumns() {
        this.columns = new ArrayList<>();
        this.childColumns = new ArrayList<>();
        this.changeRangeActivated = false;

        for (final Metadata columnKey : this.searchController.getSelectedList()) {
            if (CriterionEnum.DATE.getCode().equals(columnKey.getDataTypeId()) || CriterionEnum.MULTI_DATE.getCode().equals(columnKey.getDataTypeId())) {
                this.changeRangeActivated = true;
            }
            if (columnKey.isChildField()) {
                this.childColumns.add(new SearchDefinitionColumns(columnKey));
            } else {
                this.columns.add(new SearchDefinitionColumns(columnKey));
            }
        }
    }

    public Boolean getPaginatorActive() {
        return this.paginatorActive;
    }

    public void setPaginatorActive(final Boolean paginatorActive) {
        this.paginatorActive = paginatorActive;
    }

    public long getPaginatorSize() {
        if (Boolean.FALSE.equals(this.paginatorActive)) {// full page

            return this.searchController.getSolrSearcher().getTotalCount();
        }

        return this.sessionBean.getRowsPerPageSize();
    }

    public boolean getChangeRangeActivated() {
        return this.changeRangeActivated;
    }

    public void setChangeRangeActivated(final boolean changeRangeActivated) {
        this.changeRangeActivated = changeRangeActivated;
    }

}
