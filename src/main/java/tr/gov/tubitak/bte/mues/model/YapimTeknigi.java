package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Entity
@Table(name = "YapimTeknigi")
@NamedQuery(name = "YapimTeknigi.findEagerById", query = "SELECT y FROM YapimTeknigi y WHERE y.id = :id")
@NamedQuery(name = "YapimTeknigi.findAll", query = "SELECT y FROM YapimTeknigi y ORDER BY y.silinmis, y.aktif DESC, y.ad")
@NamedQuery(name = "YapimTeknigi.findActive", query = "SELECT y FROM YapimTeknigi y WHERE y.aktif = true AND y.silinmis = false ORDER BY y.ad")
@NamedQuery(name = "YapimTeknigi.findByNameAndAciklama", query = "SELECT y FROM YapimTeknigi y WHERE y.aktif = true AND y.silinmis = false AND (y.ad LIKE :str OR y.aciklama LIKE :str) ORDER BY y.ad, y.aciklama")
@NamedQuery(name = "YapimTeknigi.findByNameAndAciklamaAndMalzeme", query = "SELECT x.yapimTeknigi FROM MalzemeYapimTeknigi x WHERE x.aktif = true AND x.silinmis = false AND x.yapimTeknigi.aktif = true AND x.yapimTeknigi.silinmis = false AND x.malzeme = :malzeme AND (x.yapimTeknigi.ad LIKE :str OR x.yapimTeknigi.aciklama LIKE :str) ORDER BY x.yapimTeknigi.ad")
@NamedNativeQuery(name = "YapimTeknigi.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM MALZEME_YAPIM_TEKNIGI WHERE SILINMIS = 0 AND YAPIM_TEKNIGI_ID = :id)")
public class YapimTeknigi extends AbstractEntity implements DeleteValidatable {

    private static final long                   serialVersionUID = 2616567229997182990L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                              ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                              deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                              aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String                              fotografPath;

    @OneToMany(mappedBy = "yapimTeknigi")
    private Collection<MalzemeYapimTeknigi>     malzemeYapimTeknigiCollection;

    @OneToMany(mappedBy = "yapimTeknigi")
    private Collection<EserMalzemeYapimTeknigi> eserMalzemeYapimTeknigiCollection;

    public YapimTeknigi() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public Collection<EserMalzemeYapimTeknigi> getEserMalzemeYapimTeknigiCollection() {
        return this.eserMalzemeYapimTeknigiCollection;
    }

    public void setEserMalzemeYapimTeknigiCollection(final Collection<EserMalzemeYapimTeknigi> eserMalzemeYapimTeknigiCollection) {
        this.eserMalzemeYapimTeknigiCollection = eserMalzemeYapimTeknigiCollection;
    }

    public Collection<MalzemeYapimTeknigi> getMalzemeYapimTeknigiCollection() {
        return this.malzemeYapimTeknigiCollection;
    }

    public void setMalzemeYapimTeknigiCollection(final Collection<MalzemeYapimTeknigi> malzemeYapimTeknigiCollection) {
        this.malzemeYapimTeknigiCollection = malzemeYapimTeknigiCollection;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
