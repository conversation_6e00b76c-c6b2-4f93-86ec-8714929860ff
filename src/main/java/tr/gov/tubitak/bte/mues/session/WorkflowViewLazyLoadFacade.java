package tr.gov.tubitak.bte.mues.session;

import java.util.Map;

import javax.el.ValueExpression;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.datatable.feature.FilterFeature;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.MatchMode;
import org.primefaces.model.SortMeta;

import tr.gov.tubitak.bte.mues.model.mapping.WorkflowView;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
 * <AUTHOR>
 */
@ViewScoped
public class WorkflowViewLazyLoadFacade extends LazyLoadFromDBFacade<WorkflowView> {

	private static final long   serialVersionUID = 7695684279522170997L;

	private static final String ASSET_ID         = "assetID";
	
    private static final String ENVANTERNO       = "eserEnvanterNo";

    @Inject
    protected EntityManager     em;

    public WorkflowViewLazyLoadFacade() {
        super(WorkflowView.class);
    }

    @Override
    protected String constructFilterClause(final Map<String, FilterMeta> filters) {

        if ((filters != null) && !filters.isEmpty() && filters.containsKey(ASSET_ID) && (filters.get(ASSET_ID).getFilterValue() != null)) {
            this.changeFilterForAssetID(filters, ASSET_ID);
        }

        return super.constructFilterClause(filters);
    }

    @Override
    protected String sortClause(final Map<String, SortMeta> sortBy) {

        final StringBuilder str = new StringBuilder();
        if ((sortBy != null) && !sortBy.isEmpty()) {
            for (final String sortKey : sortBy.keySet()) {

                if (sortKey.contains(ENVANTERNO)) {
                    this.changeSortForOldAssetID(sortBy, sortKey);

                }

            }

            return super.sortClause(sortBy);
        } else {
            str.append("workflowId");
            str.insert(0, " ORDER BY ");
            return str.toString();
        }
    }

    @Override
    protected String appendLike(final String param, final FilterMeta filterMeta) {
        if ("globalFilter".equals(param)) {
            return "(LOWER(o.muzeMudurlukAd) LIKE LOWER(:globalFilter) OR LOWER(o.modifierAd) " + "LIKE LOWER(:globalFilter) OR LOWER(o.review) LIKE LOWER(:globalFilter))";
        }

        final StringBuilder str = new StringBuilder();
        if (param.contains("date")) {

            if (filterMeta.getMatchMode() == MatchMode.EQUALS) {
                str.append("o." + param).append("=:").append(param);
            } else {
                str.append("CONVERT(VARCHAR, o." + param + " , 120)").append(" LIKE :").append(param);
            }
            return str.toString();
        } else if (param.contains(ENVANTERNO)) {

           
                str.append("REPLACE(o." + param + ", ' ', '')").append(" LIKE REPLACE( :").append(param).append(", ' ', '') ");
            
            return str.toString();
        } else{
            return super.appendLike(param, filterMeta);
        }
    }

    @Override
    protected EntityManager getEM() {
        return this.em;
    }

    private void changeFilterForAssetID(final Map<String, FilterMeta> filterBy, final String key) {
        final String s = (String) filterBy.get(key).getFilterValue(); 
        final Integer assetIdFilter = MuesUtil.getUnWrappedAssetId(s); 

        if (assetIdFilter != null) {
            final FilterMeta filterMeta = FilterMeta.builder()
                                                     .field(filterBy.get(key).getField())
                                                     .filterBy(filterBy.get(key).getFilterBy())
                                                     .filterValue(assetIdFilter.toString())
                                                     .constraint(FilterFeature.FILTER_CONSTRAINTS.get(filterBy.get(key).getMatchMode()))
                                                     .matchMode(MatchMode.CONTAINS)
                                                     .build();
            final FilterMeta filterMeta2 = new FilterMeta(filterBy.get(key).getField(), filterBy.get(key).getColumnKey(), filterBy.get(key).getFilterBy(), MatchMode.CONTAINS, assetIdFilter.toString());

            
            filterBy.put(key, filterMeta);
        }
    }
 

    private void changeSortForOldAssetID(final Map<String, SortMeta> sortBy, final String key) {
        final SortMeta sortMeta = sortBy.get(key);

        final String sortField = sortMeta.getField();
        final String valueWithSortFunction = "dbo.udf_ExpandDigits(" + sortField + ", 3, '0')";

        final SortMeta newSortMeta = SortMeta.builder().field(valueWithSortFunction).order(sortMeta.getOrder()).function(sortMeta.getFunction()).build();

        sortBy.put(key, newSortMeta);
    }
}
