package tr.gov.tubitak.bte.mues.search;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.model.Metadata;

@Audited
@MappedSuperclass
public class SearchDefinitionColumnsSuper implements Serializable {

    private static final long         serialVersionUID = 1691480360993126153L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer                   id;

    @JoinColumn(name = "searchCriterionDefinitionId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private SearchCriterionDefinition searchCriterionDefinition;

    @JoinColumn(name = "metadataId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Metadata                  metadata;

    @Transient
    private String                    header;

    @Transient
    private String                    property;

    @Transient
    private String                    headerLongName;

    @Transient
    private Integer                   type;

    public SearchDefinitionColumnsSuper() {

    }

    public SearchDefinitionColumnsSuper(final Metadata metadata) {
        this.header = metadata.getIndexShortName();
        this.property = metadata.getName();
        this.headerLongName = metadata.getIndexFaceName();
        this.type = metadata.getDataTypeId();
        this.setMetadata(metadata);

    }

    public String getHeader() {
        return this.header;
    }

    public String getProperty() {
        return this.property;
    }

    public String getHeaderLongName() {
        return this.headerLongName;
    }

    public Integer getType() {
        return this.type;
    }

    public SearchCriterionDefinition getSearchCriterionDefinition() {
        return this.searchCriterionDefinition;
    }

    public void setSearchCriterionDefinition(final SearchCriterionDefinition searchCriterionDefinition) {
        this.searchCriterionDefinition = searchCriterionDefinition;
    }

    public Metadata getMetadata() {
        return this.metadata;
    }

    public void setMetadata(final Metadata metadata) {
        this.metadata = metadata;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(final Integer id) {
        this.id = id;
    }
}
