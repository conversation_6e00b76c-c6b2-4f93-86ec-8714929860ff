package tr.gov.tubitak.bte.mues.model.mapping;

import java.beans.Transient;
import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.Date;

import javax.persistence.Table;

import tr.gov.tubitak.bte.mues.model.EserVersion;

@Table(name = "WorkflowView")
public class WorkflowView implements Serializable {

    private static final long          serialVersionUID = -1246091245127551539L;

    private static final String        TR_M             = "TR.M.%s";

    private static final DecimalFormat df               = new DecimalFormat("000,000,000");

    private Integer                    workflowId;

    private String                     workflowRowKey;

    private Integer                    eserID;

    private Integer                    permanentId;

    private String                     assetID;

    private String                     eserEnvanterNo;

    private String                     tempID;

    private Integer                    anaFotografId;

    private String                     envanterFishiPath;

    private String                     file3DPath;

    private Boolean                    eserAktif;

    private Boolean                    eserSilinmis;

    private Integer                    initiatorId;

    private Integer                    modifierId;

    private Integer                    reviewerId;

    private Integer                    eserZimmetId;

    private Integer                    mudurlukId;

    private Integer                    reviewId;

    private String                     review;

    private String                     initiator;

    private String                     modifier;

    private String                     reviewer;

    private String                     devreden;

    private String                     devralan;

    private String                     muzeMudurluk;

    private String                     anaFotografPath;

    private Date                       dateCompleted;

    private Date                       dateInitiated;

    private Date                       dateModified;

    private Boolean                    hasPlyFile;

    private Integer                    objectId;
    
    private String                     alanMuzeMudurlugu;
    
    private String                     gonderenMuzeMudurlugu;
    
    private String                     eserAltTur;
    
    private String                     eserTur;
    
    private String                     tasinirMalYonKod;

    public WorkflowView() {
    }

    public WorkflowView(final Integer workflowId,
                        final String workflowRowKey,
                        final Integer eserID,
                        final Integer permanentId,
                        final String assetID,
                        final String eserEnvanterNo,
                        final String tempID,
                        final Integer anaFotografId,
                        final String envanterFishiPath,
                        final String file3DPath,
                        final Boolean eserAktif,
                        final Boolean eserSilinmis,
                        final Integer initiatorId,
                        final Integer modifierId,
                        final Integer reviewerId,
                        final Integer mudurlukId,
                        final Integer reviewId,
                        final Integer eserZimmetId,
                        final Date dateCompleted,
                        final Date dateInitiated,
                        final Date dateModified,
                        final String review,
                        final String initiator,
                        final String modifier,
                        final String reviewer,
                        final String devreden,
                        final String devralan,
                        final String muzeMudurluk,
                        final String anaFotografPath,
                        final Boolean hasPlyFile,
                        final Integer objectId,
                        final String alanMuzeMudurlugu,
                        final String gonderenMuzeMudurlugu,
                        final String eserAltTur,
                        final String eserTur,
                        final String tasinirMalYonKod

    ) {
        this.workflowId = workflowId;
        this.workflowRowKey = workflowRowKey;
        this.eserID = eserID;
        this.permanentId = permanentId;
        this.assetID = assetID;
        this.eserEnvanterNo = eserEnvanterNo;
        this.tempID = tempID;
        this.anaFotografId = anaFotografId;
        this.envanterFishiPath = envanterFishiPath;
        this.file3DPath = file3DPath;
        this.eserAktif = eserAktif;
        this.eserSilinmis = eserSilinmis;
        this.initiatorId = initiatorId;
        this.modifierId = modifierId;
        this.reviewerId = reviewerId;
        this.eserZimmetId = eserZimmetId;
        this.mudurlukId = mudurlukId;
        this.reviewId = reviewId;
        this.dateCompleted = dateCompleted;
        this.dateInitiated = dateInitiated;
        this.dateModified = dateModified;
        this.review = review;
        this.initiator = initiator;
        this.modifier = modifier;
        this.reviewer = reviewer;
        this.devreden = devreden;
        this.devralan = devralan;
        this.muzeMudurluk = muzeMudurluk;
        this.anaFotografPath = anaFotografPath;
        this.hasPlyFile = hasPlyFile;
        this.objectId = objectId;
        this.alanMuzeMudurlugu = alanMuzeMudurlugu;
        this.gonderenMuzeMudurlugu = gonderenMuzeMudurlugu;
        this.eserAltTur = eserAltTur;
        this.eserTur = eserTur;
        this.tasinirMalYonKod = tasinirMalYonKod;
    }

    @Transient
    public long compareDateForWarning() {
        if (this.dateInitiated != null) {
            return (new Date().getTime() - this.dateInitiated.getTime()) / (1000 * 60 * 60 * 24);
        }
        return 0;
    }

    // getters and setters ....................................................

    public Integer getEserID() {
        return this.eserID;
    }

    public void setEserID(final Integer eserID) {
        this.eserID = eserID;
    }
    
    public String getEserAltTur() {
        return eserAltTur;
    }

    public void setEserAltTur(String eserAltTur) {
        this.eserAltTur = eserAltTur;
    }

    public String getEserTur() {
        return eserTur;
    }

    public void setEserTur(String eserTur) {
        this.eserTur = eserTur;
    }

    public String getTasinirMalYonKod() {
        return tasinirMalYonKod;
    }

    public void setTasinirMalYonKod(String tasinirMalYonKod) {
        this.tasinirMalYonKod = tasinirMalYonKod;
    }

    public String getAlanMuzeMudurlugu() {
        return alanMuzeMudurlugu;
    }

    public void setAlanMuzeMudurlugu(String alanMuzeMudurlugu) {
        this.alanMuzeMudurlugu = alanMuzeMudurlugu;
    }

    public String getGonderenMuzeMudurlugu() {
        return gonderenMuzeMudurlugu;
    }

    public void setGonderenMuzeMudurlugu(String gonderenMuzeMudurlugu) {
        this.gonderenMuzeMudurlugu = gonderenMuzeMudurlugu;
    }

    public void setAssetID(final String assetID) {
        this.assetID = assetID;
    }

    public String getEserEnvanterNo() {
        return this.eserEnvanterNo;
    }

    public void setEserEnvanterNo(final String eserEnvanterNo) {
        this.eserEnvanterNo = eserEnvanterNo;
    }

    public String getTempID() {
        return this.tempID;
    }

    public void setTempID(final String tempID) {
        this.tempID = tempID;
    }

    public Integer getAnaFotografId() {
        return this.anaFotografId;
    }

    public void setAnaFotografId(final Integer anaFotografId) {
        this.anaFotografId = anaFotografId;
    }

    public String getEnvanterFishiPath() {
        return this.envanterFishiPath;
    }

    public void setEnvanterFishiPath(final String envanterFishiPath) {
        this.envanterFishiPath = envanterFishiPath;
    }

    public String getFile3DPath() {
        return this.file3DPath;
    }

    public void setFile3DPath(final String file3DPath) {
        this.file3DPath = file3DPath;
    }

    public Integer getInitiatorId() {
        return this.initiatorId;
    }

    public void setInitiatorId(final Integer initiatorId) {
        this.initiatorId = initiatorId;
    }

    public Integer getModifierId() {
        return this.modifierId;
    }

    public void setModifierId(final Integer modifierId) {
        this.modifierId = modifierId;
    }

    public Integer getReviewerId() {
        return this.reviewerId;
    }

    public void setReviewerId(final Integer reviewerId) {
        this.reviewerId = reviewerId;
    }

    public Integer getMudurlukId() {
        return this.mudurlukId;
    }

    public void setMudurlukId(final Integer mudurlukId) {
        this.mudurlukId = mudurlukId;
    }

    public Integer getReviewId() {
        return this.reviewId;
    }

    public void setReviewId(final Integer reviewId) {
        this.reviewId = reviewId;
    }

    public Integer getEserZimmetId() {
        return this.eserZimmetId;
    }

    public void setEserZimmetId(final Integer eserZimmetId) {
        this.eserZimmetId = eserZimmetId;
    }

    public String getReview() {
        return this.review;
    }

    public void setReview(final String review) {
        this.review = review;
    }

    public String getInitiator() {
        return this.initiator;
    }

    public void setInitiator(final String initiator) {
        this.initiator = initiator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public void setModifier(final String modifier) {
        this.modifier = modifier;
    }

    public String getReviewer() {
        return this.reviewer;
    }

    public void setReviewer(final String reviewer) {
        this.reviewer = reviewer;
    }

    public String getMuzeMudurluk() {
        return this.muzeMudurluk;
    }

    public void setMuzeMudurluk(final String muzeMudurluk) {
        this.muzeMudurluk = muzeMudurluk;
    }

    public String getAnaFotografPath() {
        return this.anaFotografPath;
    }

    public void setAnaFotografPath(final String anaFotografPath) {
        this.anaFotografPath = anaFotografPath;
    }

    public Date getDateCompleted() {
        return this.dateCompleted;
    }

    public void setDateCompleted(final Date dateCompleted) {
        this.dateCompleted = dateCompleted;
    }

    public Date getDateInitiated() {
        return this.dateInitiated;
    }

    public void setDateInitiated(final Date dateInitiated) {
        this.dateInitiated = dateInitiated;
    }

    public Date getDateModified() {
        return this.dateModified;
    }

    public void setDateModified(final Date dateModified) {
        this.dateModified = dateModified;
    }

    public EserVersion getReviewEnum() {
        return EserVersion.parse(this.reviewId);
    }

    public Integer getPermanentId() {
        return this.permanentId;
    }

    public void setPermanentId(final Integer permanentId) {
        this.permanentId = permanentId;
    }

    public String getAssetID() {
        return this.assetID;
    }

    public String getFormattedAssetID() {
        if (this.permanentId == null) {
            return null;
        }
        return String.format(TR_M, df.format(this.permanentId).replace(",", "."));
    }

    public String getDevreden() {
        return this.devreden;
    }

    public void setDevreden(final String devreden) {
        this.devreden = devreden;
    }

    public String getDevralan() {
        return this.devralan;
    }

    public void setDevralan(final String devralan) {
        this.devralan = devralan;
    }

    public Boolean getEserAktif() {
        return this.eserAktif;
    }

    public void setEserAktif(final Boolean eserAktif) {
        this.eserAktif = eserAktif;
    }

    public Boolean getEserSilinmis() {
        return this.eserSilinmis;
    }

    public void setEserSilinmis(final Boolean eserSilinmis) {
        this.eserSilinmis = eserSilinmis;
    }

    @Override
    public int hashCode() {
        if (this.workflowRowKey == null) {
            return super.hashCode();
        }

        return this.workflowRowKey.hashCode();
    }

    @Override
    public boolean equals(final Object object) {
        if (this == object) {
            return true;
        }
        if (object == null) {
            return false;
        }
        if (this.getClass() != object.getClass()) {
            return false;
        }

        return (this.workflowRowKey.equals(((WorkflowView) object).getWorkflowRowKey()));
    }

    public String getWorkflowRowKey() {
        return this.workflowRowKey;
    }

    public void setWorkflowRowKey(final String workflowRowKey) {
        this.workflowRowKey = workflowRowKey;
    }

    public Integer getWorkflowId() {
        return this.workflowId;
    }

    public void setWorkflowId(final Integer workflowId) {
        this.workflowId = workflowId;
    }

    public Boolean getHasPlyFile() {
        return this.hasPlyFile;
    }

    public void setHasPlyFile(final Boolean hasPlyFile) {
        this.hasPlyFile = hasPlyFile;
    }

    public Integer getObjectId() {
        return this.objectId;
    }

    public void setObjectId(final Integer objectId) {
        this.objectId = objectId;
    }

}
