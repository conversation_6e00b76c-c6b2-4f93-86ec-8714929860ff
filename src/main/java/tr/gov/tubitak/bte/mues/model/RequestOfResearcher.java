package tr.gov.tubitak.bte.mues.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "RequestOfResearcher")

@NamedQuery(name = "RequestOfResearcher.findEagerById", query = "SELECT distinct rr FROM RequestOfResearcher rr LEFT JOIN FETCH rr.mudurluk LEFT JOIN FETCH rr.researcher LEFT JOIN FETCH rr.artifact rra LEFT JOIN FETCH rra.artifact art LEFT JOIN FETCH art.eserAltTur arteat LEFT JOIN FETCH arteat.eserTur  WHERE rr.id = :id ")
@NamedQuery(name = "RequestOfResearcher.findByResearcher", query = "SELECT distinct rr FROM RequestOfResearcher rr LEFT JOIN FETCH rr.mudurluk LEFT JOIN FETCH rr.researcher r LEFT JOIN FETCH rr.artifact rra LEFT JOIN FETCH rra.artifact art LEFT JOIN FETCH art.cag c LEFT JOIN FETCH c.kronoloji LEFT JOIN FETCH art.tasinirMalYonKod LEFT JOIN FETCH art.eserAltTur arteat LEFT JOIN FETCH arteat.eserTur LEFT JOIN FETCH art.eserFotografs ef LEFT JOIN FETCH ef.ilgiliYuz WHERE rr.endDate > :date and rr.startDate <= :date and r= :researcher ORDER BY rr.id DESC")
@NamedQuery(name = "RequestOfResearcher.findByMudurluk", query = "SELECT distinct rr FROM RequestOfResearcher rr LEFT JOIN FETCH rr.mudurluk LEFT JOIN FETCH rr.researcher r LEFT JOIN FETCH rr.artifact rra LEFT JOIN FETCH rra.artifact art LEFT JOIN FETCH art.cag c LEFT JOIN FETCH c.kronoloji LEFT JOIN FETCH art.tasinirMalYonKod LEFT JOIN FETCH art.eserAltTur arteat LEFT JOIN FETCH arteat.eserTur LEFT JOIN FETCH art.eserFotografs ef LEFT JOIN FETCH ef.ilgiliYuz WHERE rr.mudurluk in :mudurlukList ORDER BY  rr.silinmis, rr.aktif desc, rr.id DESC")

public class RequestOfResearcher extends AbstractEntity implements EditPermissible {

    private static final long                               serialVersionUID = -3057538775464057461L;

    @JoinColumn(name = "muzeMudurluguId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk                                        mudurluk;

    @JoinColumn(name = "researcherId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Researcher                                      researcher;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "researcherRequest", fetch = FetchType.LAZY, orphanRemoval = true)
    private Set<ResearcherRequestArtifact>                  artifact;

    @Column(name = "requestDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                                            requestDate;

    @Column(name = "state")
    private Boolean                                         state;

    // calışma konusu
    @Size(max = 100)
    @Column(name = "workItem")
    private String                                          workItem;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "commitmentFilePath", length = 150)
    private String                                          commitmentFilePath;

    @Column(name = "startDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                                            startDate;

    @Column(name = "endDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                                            endDate;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                                          aciklama;

    private @Transient ArrayList<ResearcherRequestArtifact> list;

    public RequestOfResearcher() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Researcher getResearcher() {
        return this.researcher;
    }

    public void setResearcher(final Researcher researcher) {
        this.researcher = researcher;
    }

    public Set<ResearcherRequestArtifact> getArtifact() {
        if (this.artifact == null) {
            this.artifact = new LinkedHashSet<>();
        }
        return this.artifact;
    }

    public List<ResearcherRequestArtifact> getArtifacts() {
        if ((this.list == null) || this.list.isEmpty()) {
            this.list = new ArrayList<>(this.artifact);
        }
        return this.list;
    }

    public void setArtifact(final Set<ResearcherRequestArtifact> artifact) {
        this.artifact = artifact;
    }

    public Date getRequestDate() {
        return this.requestDate;
    }

    public void setRequestDate(final Date requestDate) {
        this.requestDate = requestDate;
    }

    public Boolean getState() {
        return this.state;
    }

    public void setState(final Boolean state) {
        this.state = state;
    }

    public String getWorkItem() {
        return this.workItem;
    }

    public void setWorkItem(final String workItem) {
        this.workItem = workItem;
    }

    public String getCommitmentFilePath() {
        return this.commitmentFilePath;
    }

    public void setCommitmentFilePath(final String commitmentFilePath) {
        this.commitmentFilePath = commitmentFilePath;
    }

    public Date getStartDate() {
        return this.startDate;
    }

    public void setStartDate(final Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return this.endDate;
    }

    public void setEndDate(final Date endDate) {
        this.endDate = endDate;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public Integer getUserIdentifier() {
        return this.getId();
    }

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }

}
