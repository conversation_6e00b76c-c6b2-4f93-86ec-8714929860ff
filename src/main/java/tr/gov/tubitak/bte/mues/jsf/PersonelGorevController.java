package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.PersonelGorev;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;
import tr.gov.tubitak.bte.mues.session.PersonelFacade;
import tr.gov.tubitak.bte.mues.session.PersonelGorevFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class PersonelGorevController extends AbstractController<PersonelGorev> implements SingleFileUploadable {

    private static final long   serialVersionUID = -4820119210961177317L;

    @Inject
    private PersonelGorevFacade facade;
    @Inject
    private PersonelFacade      personelFacade;

    @Inject
    private SessionBean         sessionBean;

    @Inject
    private FileUploadHelper    fileUploadHelper;

    private Mudurluk            personelMudurluk;

    private Mudurluk            mudurlukForEdit;

    private Personel            personel;

    public PersonelGorevController() {
        super(PersonelGorev.class);
    }

    @PostConstruct
    public void init() {

        if (this.sessionBean.fetchByPermission("personelgorev:listele").size() == 1) {
            this.setPersonelMudurluk(this.sessionBean.fetchByPermission("personelgorev:listele").get(0));
        }

        this.search();
    }

    public List<Personel> filterByFullNameAndAciklamaAndMuseumDirectorate(final String query) {
        return this.personelFacade.filterByFullNameAndAciklamaAndMuseumDirectorates(query, List.of(this.getPersonelMudurluk()));
    }

    public List<Personel> filterByFullNameAndAciklama(final String query) {
        return this.personelFacade.filterByFullNameAndAciklama(query);
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.mudurlukForEdit = null;
        this.getModel().setMudurluk(this.sessionBean.filterByPermission("personelgorev:listele"));
    }

    public void search() {
        if ((this.getPersonelMudurluk() == null) && (this.sessionBean.getPermissionBasedDirectorates() != null) && !this.sessionBean.getPermissionBasedDirectorates().isEmpty()) {
            this.items = this.facade.filterByPersonelAndMudurluks(this.sessionBean.getPermissionBasedDirectorates(), this.personel);
        } else {
            final List<Mudurluk> muzes = new ArrayList<>(1);
            muzes.add(this.getPersonelMudurluk());
            this.items = this.facade.filterByPersonelAndMudurluks(muzes, this.personel);
        }
    }

    public void reset() {
        this.setPersonelMudurluk(null);
        this.setPersonel(null);
        this.setItems(null);
    }

    @Override
    public void showDetail(final PersonelGorev item) {
        super.showDetail(item);
        this.personelMudurluk = this.getModel().getPersonel().getMudurluk();
        this.mudurlukForEdit = this.getModel().getMudurluk();
    }

    /**
     * Personel sayfasında kullanılmak üzere personel listesini döner. Yetki dahilinde görebileceği toplam görevlendirmeyi listelemek için kullanılır.
     */
    public void fetchCommissionPersonels() {
        this.items = this.facade.fetchCommissionPersonels(this.sessionBean.fetchMudurlukListByPermission("personel:listele"));
    }

    public void handleMudurlukChange(final SelectEvent<Mudurluk> event) {
        this.setPersonelMudurluk(event.getObject());
        this.setPersonel(null);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setGorevDocPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getGorevDocPath() != null) {
            this.getModel().setGorevDocPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getGorevDocPath(), FolderType.OTHER));
        }
    }

    public List<Personel> filterByNameAndMudurluk(final String query) {
        return this.facade.filterByNameAndMudurluk(query, this.getPersonelMudurluk());
    }

    // getters and setters ....................................................
    @Override
    public DBOperationResult create() {
        this.getModel().setMudurluk(this.mudurlukForEdit);
        return super.create();
    }

    @Override
    public DBOperationResult update() {
        this.getModel().setMudurluk(this.mudurlukForEdit);
        return super.update();
    }

    @Override
    public AbstractFacade<PersonelGorev> getFacade() {
        return this.facade;
    }

    @Override
    public List<PersonelGorev> getItems() {
        return this.items;
    }

    public Personel getPersonel() {
        return this.personel;
    }

    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public Mudurluk getMudurlukForEdit() {
        return this.mudurlukForEdit;
    }

    public void setMudurlukForEdit(final Mudurluk mudurlukForEdit) {
        this.mudurlukForEdit = mudurlukForEdit;
    }

    public Mudurluk getPersonelMudurluk() {
        return this.personelMudurluk;
    }

    public void setPersonelMudurluk(final Mudurluk personelMudurluk) {
        this.personelMudurluk = personelMudurluk;
    }

}
