package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;
import javax.json.Json;
import javax.json.JsonArrayBuilder;
import javax.json.JsonObject;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.slf4j.Logger;

import tr.gov.tubitak.bte.mues.model.VirtualKeyboardCharacter;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardLanguage;
import tr.gov.tubitak.bte.mues.search.ComparisonOperatorEnum;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardCharacterFacade;

@Named
@ViewScoped
public class VirtualKeyboardController extends AbstractController<VirtualKeyboardCharacter> {

    private static final long              serialVersionUID = 5315937344953322347L;

    @Inject
    private VirtualKeyboardCharacterFacade      facade;

    private Boolean                             isKeyboardSet;

    private Boolean                             isTranscriptionSelected;

    private Boolean                             isKeyboardActive;

    public VirtualKeyboardController() {
        super(VirtualKeyboardCharacter.class);
    }

    // bu kısım transkript alanında arama yapılması durumunda sanal klavyeye ait karakterlerin çekilmesi için eklendi
    public void handleLanguageChange(final SelectEvent<VirtualKeyboardLanguage> event) {
    	
        final VirtualKeyboardLanguage language = event.getObject();
        final List<VirtualKeyboardCharacter> letterList = this.facade.findByLanguageId(language.getId());

        final JsonArrayBuilder outerJa = Json.createArrayBuilder();

        for (int i = 0; i < letterList.size(); i++) {
            if ((i == 0) || ((i % 11) == 0)) {
                final JsonArrayBuilder ja = Json.createArrayBuilder();
                ja.add(letterList.get(i).getCharacter()).add(letterList.get(i).getCharacter()).add(1).add(0).add(true);
                outerJa.add(ja);
            } else {
                final JsonArrayBuilder ja = Json.createArrayBuilder();
                ja.add(letterList.get(i).getCharacter()).add(letterList.get(i).getCharacter()).add(1).add(0).add(false);
                outerJa.add(ja);
            }
        }

        if (!letterList.isEmpty()) {
            final JsonArrayBuilder ja = Json.createArrayBuilder();
            ja.add("Sil").add("8").add(8).add(9).add(true);
            outerJa.add(ja);
            ja.add("Boşluk").add("32").add(32).add(9).add(false);
            outerJa.add(ja);

            final JsonObject jo = Json.createObjectBuilder().add("layout", Json.createArrayBuilder().add(Json.createArrayBuilder().add(outerJa))).build();
            PrimeFaces.current().executeScript("setLanguage(" + jo + ");");

            this.setIsKeyboardSet(true);
        } else {
            this.setIsKeyboardSet(false);
        }

    }
    
    // Transkripsiyon seçiminin yanında koşul boş seçildiğinde ve hiç seçim olmadığında klavye gösterilmez
    public void handleConditionSelect(final SelectEvent<ComparisonOperatorEnum> event) {

        final ComparisonOperatorEnum comparisonOperatorEnum = event.getObject();
        if (!(comparisonOperatorEnum.name().equals("ISNULL") || comparisonOperatorEnum.name().equals("NOP"))) {
            this.isKeyboardActive = true;
        } else {
            this.isKeyboardActive = false;
        }

    }

    @Override
    public VirtualKeyboardCharacterFacade getFacade() {
        return this.facade;
    }

    public Boolean getIsKeyboardSet() {
        return this.isKeyboardSet;
    }

    public void setIsKeyboardSet(final Boolean isKeyboardSet) {
        this.isKeyboardSet = isKeyboardSet;
    }
    
    public Boolean getIsTranscriptionSelected() {
        return this.isTranscriptionSelected;
    }

    public void setIsTranscriptionSelected(final Boolean isTranscriptionSelected) {
        this.isTranscriptionSelected = isTranscriptionSelected;
    }

    public Boolean getIsKeyboardActive() {
        return this.isKeyboardActive;
    }

    public void setIsKeyboardActive(final Boolean isKeyboardActive) {
        this.isKeyboardActive = isKeyboardActive;
    }

}
