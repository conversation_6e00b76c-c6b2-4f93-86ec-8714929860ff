package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "OriginalPhotographRequest")
@NamedQuery(name = "OriginalPhotographRequest.findEagerById", query = "SELECT distinct opr FROM OriginalPhotographRequest opr LEFT JOIN FETCH opr.photo p LEFT JOIN FETCH p.eser WHERE opr.id = :id")
@NamedQuery(name = "OriginalPhotographRequest.findAll", query = "SELECT distinct opr FROM OriginalPhotographRequest opr LEFT JOIN FETCH opr.photo p LEFT JOIN FETCH p.eser eser  LEFT JOIN FETCH eser.cag c LEFT JOIN FETCH c.kronoloji LEFT JOIN FETCH eser.tasinirMalYonKod LEFT JOIN FETCH eser.eserAltTur arteat LEFT JOIN FETCH arteat.eserTur LEFT JOIN FETCH eser.eserFotografs ef LEFT JOIN FETCH ef.ilgiliYuz ORDER BY opr.id DESC")
@NamedQuery(name = "OriginalPhotographRequest.findByResearcher", query = "SELECT distinct opr FROM OriginalPhotographRequest opr LEFT JOIN FETCH opr.photo p LEFT JOIN FETCH p.eser eser  LEFT JOIN FETCH eser.cag c LEFT JOIN FETCH c.kronoloji LEFT JOIN FETCH eser.tasinirMalYonKod LEFT JOIN FETCH eser.eserAltTur arteat LEFT JOIN FETCH arteat.eserTur LEFT JOIN FETCH eser.eserFotografs ef LEFT JOIN FETCH ef.ilgiliYuz LEFT JOIN FETCH opr.researcherRequest rr LEFT JOIN FETCH rr.researcher r WHERE r.id =:researcher ORDER BY opr.id DESC")
@NamedQuery(name = "OriginalPhotographRequest.findByMudurluk", query = "SELECT distinct opr FROM OriginalPhotographRequest opr LEFT JOIN FETCH opr.photo p LEFT JOIN FETCH p.eser eser  LEFT JOIN FETCH eser.cag c LEFT JOIN FETCH c.kronoloji LEFT JOIN FETCH eser.tasinirMalYonKod LEFT JOIN FETCH eser.eserAltTur arteat LEFT JOIN FETCH arteat.eserTur LEFT JOIN FETCH eser.eserFotografs ef LEFT JOIN FETCH ef.ilgiliYuz LEFT JOIN FETCH opr.researcherRequest rr LEFT JOIN FETCH rr.researcher r WHERE rr.mudurluk in :mudurlukList ORDER BY opr.id DESC")
@NamedQuery(name = "OriginalPhotographRequest.findActive", query = "SELECT opr FROM OriginalPhotographRequest opr LEFT JOIN FETCH opr.photo p LEFT JOIN FETCH p.eser WHERE opr.aktif = true ORDER BY opr.id DESC")

public class OriginalPhotographRequest extends AbstractEntity implements EditPermissible {

    private static final long   serialVersionUID = -6700185692329545038L;

    @JoinColumn(name = "researcherRequestId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private RequestOfResearcher researcherRequest;

    @JoinColumn(name = "photoId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserFotograf        photo;

    @Column(name = "requestDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                requestDate;

    /*
    *-1 rejected
    * 0 new record to wait manager approvment
    * 1 approved by manager
    */
    @Column(name = "state")
    private Integer             state;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "    receiptFilePath", length = 150)
    private String              receiptFilePath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String              aciklama;

    @Size(max = 150)
    @Column(name = "review", length = 150)
    private String              review;

    public OriginalPhotographRequest() {
        // blank constructor
    }

    // getters and setters ....................................................

    public RequestOfResearcher getResearcherRequest() {
        return this.researcherRequest;
    }

    public void setResearcherRequest(final RequestOfResearcher researcherRequest) {
        this.researcherRequest = researcherRequest;
    }

    public EserFotograf getPhoto() {
        return this.photo;
    }

    public void setPhoto(final EserFotograf photo) {
        this.photo = photo;
    }

    public Date getRequestDate() {
        return this.requestDate;
    }

    public void setRequestDate(final Date requestDate) {
        this.requestDate = requestDate;
    }

    public Integer getState() {
        return this.state;
    }

    public void setState(final Integer state) {
        this.state = state;
    }

    public String getReceiptFilePath() {
        return this.receiptFilePath;
    }

    public void setReceiptFilePath(final String receiptFilePath) {
        this.receiptFilePath = receiptFilePath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public Integer getUserIdentifier() {
        return this.getId();
    }

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }

    public String getReview() {
        return this.review;
    }

    public void setReview(final String review) {
        this.review = review;
    }

}
