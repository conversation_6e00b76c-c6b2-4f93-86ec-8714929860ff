package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.GeneralDataProtectionRegulation;
import tr.gov.tubitak.bte.mues.session.GeneralDataProtectionRegulationFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class GeneralDataProtectionRegulationController extends AbstractController<GeneralDataProtectionRegulation> implements SingleFileUploadable {

    private static final long                     serialVersionUID = 818035325233734331L;

    @Inject
    private GeneralDataProtectionRegulationFacade facade;

    @Inject
    private FileUploadHelper                      fileUploadHelper;

    public GeneralDataProtectionRegulationController() {
        super(GeneralDataProtectionRegulation.class);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setDocumentPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getDocumentPath() != null) {
            this.getModel().setDocumentPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDocumentPath(), FolderType.OTHER));
        }
    }

    // getters and setters ....................................................

    @Override
    public List<GeneralDataProtectionRegulation> getItems() {
        if (this.items == null) {
            this.items = this.getFacade().findAll();
        }
        return this.items;
    }

    @Override
    public GeneralDataProtectionRegulationFacade getFacade() {
        return this.facade;
    }

    @Transactional
    @Override
    public DBOperationResult create() {
        this.facade.updateAllKVKKStatus(this.getModel().getId());
        return super.create();
    }

    @Transactional
    @Override
    public DBOperationResult update() {
        this.facade.updateAllKVKKStatus(this.getModel().getId());
        return super.update();
    }

}
