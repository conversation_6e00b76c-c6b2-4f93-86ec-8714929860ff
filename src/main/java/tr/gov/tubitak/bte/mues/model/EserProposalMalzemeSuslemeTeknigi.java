package tr.gov.tubitak.bte.mues.model;

import javax.persistence.AssociationOverride;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Entity
@Table(name = "EP_ESER_MALZEME_SUSLEME_TEKNIGI")
@AssociationOverride(name = "renks")
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public class EserProposalMalzemeSuslemeTeknigi extends EserMalzemeSuslemeTeknigiSuper implements DeleteValidatable {

    private static final long serialVersionUID = 2967247320571605844L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalMalzemeSuslemeTeknigi() {
        // Blank Contsructor
    }

    // getters and setters ....................................................
    
    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
