package tr.gov.tubitak.bte.mues.service;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.enterprise.concurrent.ManagedExecutorService;
import javax.enterprise.context.SessionScoped;
import javax.inject.Inject;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import tr.gov.tubitak.bte.mues.model.SolrLastIndexTimes;
import tr.gov.tubitak.bte.mues.search.AbstractSolrSearcherIndexingUtil;
import tr.gov.tubitak.bte.mues.session.SolrLastIndexTimesFacade;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

@SessionScoped
public class IndexingService implements Serializable {

    private static final long                  serialVersionUID   = 2771932696426735078L;

    private static final Logger                logger             = LogManager.getLogger(IndexingService.class);

    @Inject
    SolrLockService                            solrLockService;

    @Resource
    private ManagedExecutorService             executorService;

    private final Map<SolrEnum, AtomicBoolean> solrCoreSuccessMap = Arrays.stream(SolrEnum.values()).collect(Collectors.toConcurrentMap(solrEnum -> solrEnum, solrEnum -> new AtomicBoolean(false)));

    public Boolean isSuccess(final SolrEnum solrEnum) {
        return this.solrCoreSuccessMap.get(solrEnum).get();
    }

    public void setSuccess(final SolrEnum solrEnum, final Boolean success) {
        this.solrCoreSuccessMap.get(solrEnum).set(success);
    }

    public void indexToSolrAsync(final AbstractSolrSearcherIndexingUtil searcherIndexingUtil,
                                 final SolrLastIndexTimesFacade solrLastIndexTimesFacade,
                                 final SolrEnum solrEnum,
                                 final String incidentTypeEnumName) {

        try{
            CompletableFuture.runAsync(() -> {
                logger.info("Full record indexing completed: runAsync => thenRun");
                this.executeIndexing(searcherIndexingUtil, solrLastIndexTimesFacade, solrEnum, incidentTypeEnumName);
            }, this.executorService).thenRun(() -> {
                logger.info("Full record indexing completed: runAsync => thenRun");
                this.setSuccess(solrEnum, Boolean.TRUE);
            }).exceptionally(ex -> {
                logger.error("Full record Indexing failed:{} _ {}", ex.getMessage(), ex);
                this.setSuccess(solrEnum, Boolean.FALSE);
                return null;
            });
        } catch (RuntimeException e) {
            throw new RuntimeException(e);
        }
    }

    // @Transactional
    private void executeIndexing(final AbstractSolrSearcherIndexingUtil searcherIndexingUtil,
                                 final SolrLastIndexTimesFacade solrLastIndexTimesFacade,
                                 final SolrEnum solrEnum,
                                 final String incidentTypeEnumName) {
        logger.info("Async task started... {}", Thread.currentThread().getName());

        try {
            if (Boolean.FALSE.equals(this.solrLockService.getSolrSchedulerLock(solrEnum))) {
                logger.info("Lock acquired, starting indexing... {}", Thread.currentThread().getName());
                this.solrLockService.updateSolrSchedulerLock(solrEnum, Boolean.TRUE);

                searcherIndexingUtil.fullRecordToSolr(solrEnum, incidentTypeEnumName);
                logger.warn("Full record indexing completed {}", Thread.currentThread().getName());

                SolrLastIndexTimes solrLastIndexTimes = solrLastIndexTimesFacade.findBySolrCore(solrEnum);

                if (solrLastIndexTimes == null) {
                    solrLastIndexTimes = new SolrLastIndexTimes();
                    solrLastIndexTimes.setSolrCoreCode(solrEnum.getCode());
                }

                solrLastIndexTimes.setLastIndexTime(new Date());
                solrLastIndexTimesFacade.update(solrLastIndexTimes);


                logger.info("Releasing lock and deactivating request context " + Thread.currentThread().getName());
                logger.info("Solr indexing metadata updated " + Thread.currentThread().getName());

            } else {
                logger.warn("Skipping indexing: Another process holds the lock. " + Thread.currentThread().getName());
            }
        } catch (final Exception e) {
            logger.info("Releasing lock and deactivating request context " + Thread.currentThread().getName());
            logger.error(incidentTypeEnumName +" indexing got exception " + Thread.currentThread().getName(), e);
        } finally {
            this.solrLockService.updateSolrSchedulerLock(solrEnum, Boolean.FALSE);
        }
    }

}
