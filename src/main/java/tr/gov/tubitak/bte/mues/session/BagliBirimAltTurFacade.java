package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.BagliBirimAltTur;
import tr.gov.tubitak.bte.mues.model.BagliBirimTur;

/**
 *
 * 
 */
@RequestScoped
public class BagliBirimAltTurFacade extends AbstractFacade<BagliBirimAltTur> {

    public BagliBirimAltTurFacade() {
        super(BagliBirimAltTur.class);
    }

    public List<BagliBirimAltTur> filterByNameAndTur(final String value, final BagliBirimTur bagliBirimTur) {
        return this.em.createNamedQuery("BagliBirimAltTur.findByNameAndTur", BagliBirimAltTur.class)
                      .setParameter("ad", "%" + value + "%")
                      .setParameter("tur", bagliBirimTur)
                      .getResultList();
    }
    
    public List<BagliBirimAltTur> filterByNameAndTurPreventDuplication(final String value, final BagliBirimTur bagliBirimTur, List<BagliBirimAltTur> altTurs) {
        return this.em.createNamedQuery("BagliBirimAltTur.filterByNameAndTur", BagliBirimAltTur.class)
                      .setParameter("ad", "%" + value + "%")
                      .setParameter("tur", bagliBirimTur)
                      .setParameter("bagliBirimAltTur", altTurs)
                      .getResultList();
    }
}
