package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.PersonelGorev;

/**
 *
 */

@RequestScoped
public class PersonelGorevFacade extends AbstractFacade<PersonelGorev> {

    public PersonelGorevFacade() {
        super(PersonelGorev.class);
    }

    public List<PersonelGorev> filterByPersonelAndMudurluks(final List<Mudurluk> mudurlukList, final Personel personel) {
        return this.em.createNamedQuery("PersonelGorev.findByPersonelAndMudurluks", PersonelGorev.class).setParameter("museums", mudurlukList).setParameter("personel", personel).getResultList();
    }

    public List<PersonelGorev> fetchCommissionPersonels(final List<Mudurluk> mudurluk) {
        return this.em.createNamedQuery("PersonelGorev.findByMudurluk", PersonelGorev.class).setParameter("muze", mudurluk).getResultList();
    }

    public List<Personel> filterByNameAndMudurluk(final String value, final Mudurluk mudurluk) {
        return this.em.createNamedQuery("PersonelGorev.findByNameAndMudurluk", Personel.class).setParameter("ad", "%" + value + "%").setParameter("muze", mudurluk).getResultList();
    }

}
