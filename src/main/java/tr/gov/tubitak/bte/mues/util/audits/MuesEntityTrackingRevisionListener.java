package tr.gov.tubitak.bte.mues.util.audits;

import java.io.Serializable;

import org.apache.shiro.SecurityUtils;
import org.hibernate.envers.EntityTrackingRevisionListener;
import org.hibernate.envers.RevisionType;

import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

public class MuesEntityTrackingRevisionListener implements EntityTrackingRevisionListener {

    @Override
    public void newRevision(final Object revisionEntity) {

        final Audit revEntity = (Audit) revisionEntity;
        revEntity.setUserIp(MuesUtil.fetchUserIp());
        revEntity.setUserName(SecurityUtils.getSubject().getPrincipal().toString());
    }

    /*
     * (non-Javadoc)
     * @see org.hibernate.envers.EntityTrackingRevisionListener#entityChanged(java.lang.Class, java.lang.String, java.io.Serializable, org.hibernate.envers.RevisionType, java.lang.Object)
     */
    @Override
    @SuppressWarnings("rawtypes")
    public void entityChanged(final Class entityClass,
                              final String entityName,
                              final Serializable entityId,
                              final RevisionType revisionType,
                              final Object revisionEntity) {
        // either javax.persistence.Table or org.hibernate.annotations.Table

        final Audit revEntity = (Audit) revisionEntity;
        final String className = entityClass.getSimpleName();
        revEntity.setEntity(className);

        final Object deleteDescription = MuesUtil.getSessionMapParameter("deleteDescription");
        final Object primaryDeleteDescription = MuesUtil.getSessionMapParameter("deleteDescription" + className);
        
        AuditEvent auditEvent = null;
        if (primaryDeleteDescription != null) {
        	auditEvent = AuditEvent.parseByName(revEntity.getEntity() + AuditEvent.parseByCode(RevisionType.DEL.getRepresentation()).name());
            revEntity.setAciklama(auditEvent.getLabel() + " yapıldı. " + primaryDeleteDescription);
        } else if (deleteDescription != null) {
            auditEvent = AuditEvent.parseByName(revEntity.getEntity() + AuditEvent.parseByCode(RevisionType.DEL.getRepresentation()).name());
            revEntity.setAciklama(auditEvent.getLabel() + " yapıldı. " + deleteDescription);
            MuesUtil.removeSessionMapParameter("deleteDescription");
        } else {
            auditEvent = AuditEvent.parseByName(revEntity.getEntity() + AuditEvent.parseByCode(revisionType.getRepresentation()).name());     	
        	revEntity.setAciklama(auditEvent.getLabel() + " yapıldı.");
        }
        revEntity.setRevType(auditEvent.getCode());
    }

}
