package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.VirtualKeyboardCharacter;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class VirtualKeyboardCharacterFacade extends AbstractFacade<VirtualKeyboardCharacter> {

    public VirtualKeyboardCharacterFacade() {
        super(VirtualKeyboardCharacter.class);
    }

    public List<VirtualKeyboardCharacter> findByLanguageId(final int languageId) {
        return this.em.createNamedQuery("VirtualKeyboardCharacter.findByLanguageId", VirtualKeyboardCharacter.class).setParameter("languageId", languageId).getResultList();
    }

    public List<VirtualKeyboardCharacter> findByDilId(final int languageId) {
        return this.em.createNamedQuery("VirtualKeyboardCharacter.findByDilId", VirtualKeyboardCharacter.class).setParameter("dilId", languageId).getResultList();
    }

    public List<Object[]> getRawCharacterData(final int languageId) {
       
            return em.createNativeQuery("SELECT character,LATIN FROM Virtual_Keyboard_Character kc LEFT JOIN Virtual_Keyboard_Language kl on kc.language_Id = kl.ID  where dil_Id=:languageId").setParameter("languageId", languageId).getResultList();
          
    }
    
    public DBOperationResult deleteCharsByLanguage(final int languageId) {
        this.getEM().createNativeQuery("DELETE FROM Virtual_Keyboard_Character WHERE language_Id = :languageId ").setParameter("languageId", languageId).executeUpdate();
        return DBOperationResult.success();
    }

}
