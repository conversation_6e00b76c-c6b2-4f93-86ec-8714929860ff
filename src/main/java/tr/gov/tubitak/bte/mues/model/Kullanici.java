package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "KULLANICI")
@NamedQuery(name = "Kullanici.findByResearcher", query = "SELECT k from Kullanici k LEFT JOIN FETCH k.researcher r WHERE r.id = :researcherId")
@NamedQuery(name = "Kullanici.filterByExternalUserName", query = "SELECT DISTINCT(k) from Kullanici k WHERE k.researcher is not NULL and (REPLACE(concat(k.ad, k.soyad), ' ', '') LIKE :str) and k.silinmis = false AND k.aktif = true ORDER BY k.ad")
@NamedQuery(name = "Kullanici.findActiveByUsername", query = "SELECT k FROM Kullanici k LEFT JOIN FETCH k.personel p LEFT JOIN FETCH k.personelView pw LEFT JOIN FETCH k.researcher r LEFT JOIN FETCH p.mudurluk m LEFT JOIN FETCH k.kullaniciBirimRols kbr LEFT JOIN FETCH kbr.rol WHERE (m is null or  (m.aktif = true AND m.silinmis = false)) AND k.kullaniciAdi = :tckn")

@NamedNativeQuery(name = "Kullanici.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER WHERE SILINMIS = 0)")

public class Kullanici extends KullaniciSuper {

    private static final long serialVersionUID = -5384594000926869394L;

    @JoinColumn(name = "externalUserId", referencedColumnName = "ID")
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    private Researcher        researcher;

    public Kullanici() {
        // blank constructor
    }

    public Researcher getResearcher() {
        return this.researcher;
    }

    public void setResearcher(final Researcher researcher) {
        this.researcher = researcher;
    }

    @Override
    public Integer getUserIdentifier() {
        return (this.getPersonelView().getId() == null) ? this.getResearcher().getId() : this.getPersonelView().getId();
    }

}
