package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.EntityNotFoundException;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.common.SolrException;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.EserVersion;
import tr.gov.tubitak.bte.mues.model.PickSuper;
import tr.gov.tubitak.bte.mues.model.Workflow;
import tr.gov.tubitak.bte.mues.model.mapping.LabRestorationView;
import tr.gov.tubitak.bte.mues.search.SearchQualifier;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.search.controller.SearchController;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;
import tr.gov.tubitak.bte.mues.session.AuditFacade;
import tr.gov.tubitak.bte.mues.session.EserDepoFacade;
import tr.gov.tubitak.bte.mues.session.EserFacade;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;
import tr.gov.tubitak.bte.mues.util.MuesException;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.audits.Audit;

@Named
@ViewScoped
public class ArtifactViewingController extends AbstractController<Eser> {

    private static final String                NUMARALI_ESERI_GORUNTULEMISTIR = " numaralı eseri görüntülemiştir.";

    private static final String                KULLANICISI                    = " kullanıcısı ";

    private static final String                TIMESTAMP                      = "timestamp";

    private static final long                  serialVersionUID               = -2081078589892240140L;

    @Inject
    @SearchQualifier
    private SearchController                   searchController;

    @Inject
    private EserFacade                         facade;

    @Inject
    private AuditFacade                        auditFacade;

    @Inject
    private EserDepoFacade                     eserDepoFacade;

    @Inject
    private SessionBean                        sessionBean;

    @Inject
    private SolrSearcher                       solrSearcher;

    private transient List<Object[]>           eserHistoryList;

    private Integer                            permanentId;

    private ArtifactsSolrModel                 selectedArtifactSolrModelData;

    private boolean                            envanterDefteriPathChanged;

    private boolean                            eskiEnvanterNoChanged;

    private boolean                            tasinirMalYonKodChanged;

    private boolean                            tasinirIslemFisiNoChanged;

    private boolean                            webSiteChanged;

    private boolean                            eserAltTurEserTurChanged;

    private boolean                            eserAltTurChanged;

    private boolean                            eserOzelAdiChanged;

    private boolean                            aktifPasifChanged;

    private boolean                            yazmaBasmaSecimiChanged;

    private boolean                            elisiDokumaSecimiChanged;

    private boolean                            islamiGayriSecimiChanged;

    private boolean                            uniklikDurumuChanged;

    private boolean                            torenselDurumuChanged;

    private boolean                            kiymetChanged;

    private boolean                            kondisyonDurumuChanged;

    private boolean                            alanKonumuChanged;

    private boolean                            konumAciklamaChanged;

    private boolean                            hukumdarChanged;

    private boolean                            termStartChanged;

    private boolean                            termEndChanged;

    private boolean                            kronolojiAciklamaChanged;

    private boolean                            yapanVipChanged;

    private boolean                            yaptiranVipChanged;

    private boolean                            kullanacakVipChanged;

    private boolean                            kullananVipChanged;

    private boolean                            bagislayanVipChanged;

    private boolean                            eserFotografsChanged;

    private boolean                            eserVideosChanged;

    private boolean                            eserMeasuresChanged;

    private boolean                            eserZimmetsChanged;

    private boolean                            eserCizimChanged;

    private boolean                            eserHareketsChanged;

    private boolean                            transcriptionChanged;

    private boolean                            genelAciklamaChanged;

    private boolean                            eserKeywordsChanged;

    private boolean                            uretimBolgesiChanged;

    private boolean                            uretimYeriChanged;

    private boolean                            darpYeriChanged;

    private boolean                            uretimYiliChanged;

    private boolean                            sikkeDarpYonuChanged;

    private boolean                            eserMalzemeYapimTeknigisChanged;

    private boolean                            eserMalzemeSuslemeTeknigisChanged;

    private boolean                            eserStilsChanged;

    private boolean                            eserAtolyesChanged;

    private boolean                            eserKaynakLiteratursChanged;

    private boolean                            eserYayinLiteratursChanged;

    private boolean                            iliskilendirmeChanged;

    private boolean                            eserSerhsChanged;

    private Integer                            lastCompletedAlanKonumuId;

    private transient List<Object[]>           envanterNoHistoryList;

    private transient List<LabRestorationView> vouchersList;

    public ArtifactViewingController() {
        super(Eser.class);
    }

    @Override
    public void showDetail(final Integer id) {
        try {
            this.resetDifferences();
            this.setModel(this.getFacade().findEagerById(id));
            if (this.getModel() == null) {
                this.logger.info("[showDetail] : Eser bulunamadı : eserID: {}", id);
                PrimeFaces.current().executeScript("PF('growlMessageWidget').renderMessage({'summary':" + "'Kayıt Bulunamadı'" + ", 'detail':" + "'Eser ID: " + id + "'" + ", 'severity':'error'})");
            }
            this.setNewMode(false);
            this.setVouchersList(this.facade.fetchVouchersById(id));
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + KULLANICISI + this.getModel().getOneId() + NUMARALI_ESERI_GORUNTULEMISTIR;
            this.auditFacade.log(AuditEvent.EserGoruntuleme, msg);

        } catch (final MuesException e) {
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + KULLANICISI + this.getModel().getOneId() + " numaralı eseri görüntüleyememiştir.";
            this.auditFacade.log(AuditEvent.EserGoruntulemeHata, msg, e.getMessage());
        }
    }

    public void showDetail(final String id) {
        try {
            this.setModel(this.facade.findEagerByPermanentId(MuesUtil.getUnWrappedKamId(id)));
            this.setNewMode(false);
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + this.getModel().getOneId() + " numaralı yabancı eser bilgilerini görüntülemiştir.";
            this.auditFacade.log(AuditEvent.EserGoruntuleme, msg);

        } catch (final MuesException e) {
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + this.getModel().getOneId() + " numaralı yabancı eser bilgilerini görüntüleyememiştir.";
            this.auditFacade.log(AuditEvent.EserGoruntulemeHata, msg, e.getMessage());
        }
    }

    public void onRowSelectWithContextMenu(final SelectEvent<ArtifactsSolrModel> event) {
        this.showDetail(event.getObject().getId());
        this.selectedArtifactSolrModelData = event.getObject();
        this.searchController.getSelectionList().add(event.getObject());
    }

    public void showDetailWithChanges(final Integer id) {
        this.showDetail(id);
        this.findDifferences(this.getModel());
    }

    public void showDetailByPermanent(final Integer permanentId) {
        try {
            this.setModel(this.facade.findEagerByPermanentId(permanentId));
            if (this.getModel() == null) {
                this.logger.info("[showDetailByPermanent] : Eser bulunamadı : permanentID: {}", permanentId);
                PrimeFaces.current()
                          .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                         + "'Kayıt Bulunamadı'"
                                         + ", 'detail':"
                                         + "'Kultur Varligi ID: "
                                         + permanentId
                                         + "'"
                                         + ", 'severity':'error'})");
            }
            this.setNewMode(false);
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı Eser Sayımda Kültür Varlığı ID'si" + this.getModel().getOneId() + NUMARALI_ESERI_GORUNTULEMISTIR;
            this.auditFacade.log(AuditEvent.EserGoruntuleme, msg);
        } catch (final MuesException e) {
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi()
                               + " kullanıcısı Eser Sayımda Kültür Varlığı ID'si"
                               + this.getModel().getOneId()
                               + " numaralı eseri görüntüleyememiştir.";
            this.auditFacade.log(AuditEvent.EserGoruntulemeHata, msg, e.getMessage());
        }
    }

    public void showDetailFromSolr(final Integer id) {
        try {
            this.logger.debug("{}", this.solrSearcher.getQuery());

            this.setModel(this.solrSearcher.makeSearch(new SolrQuery("id:" + id)).getBeans(Eser.class).get(0));

        } catch (final SolrServerException | SolrException | IOException e) {

            MuesUtil.showMessage("Solr Sunucusuna Ulaşmada Hata", FacesMessage.SEVERITY_ERROR);
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Solr Sunucusuna Ulaşmada Hata'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine Başvurunuz. '"
                                     + ", 'severity':'error'})");
            this.logger.error("[solrLoad] : Hata : {}", e.getMessage(), e);
        }
    }

    public void listHistoryOfArtifact(final Integer id) {
        this.setEserHistoryList(this.getFacade().history(id));
    }

    public String redirectToEditPage() {
        if (this.getModel() == null) {
            // TODO: ilginc sekilde bu kisim bazen null gelmektedir. tam sebebini buluncaya kadar bu kisim kalabilir.

            MuesUtil.showMessage("Beklenmeyen hata olustu. Tekrar deneyin.", FacesMessage.SEVERITY_ERROR);
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Beklenmeyen hata olustu. Tekrar deneyin.'"
                                     + ", 'detail':"
                                     + "'Sayfayı tekrar yükleyerek yeniden deneyebilirsiniz. '"
                                     + ", 'severity':'error'})");
            return null;
        }

        final EserVersion mode = (EserVersion) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("mode");
        if (mode == null) {
            return "/eser/eser?faces-redirect=true&e=" + this.getModel().getId();
        }
        return "/eser/eser?faces-redirect=true&e=" + this.getModel().getId() + "&mode=" + mode.getCode();
    }

    public void showDetailHistory(final Object[] item) {
        this.setModel((this.facade.showDetailHistory(item)));
        this.setNewMode(false);

    }

    public void findDifferences(final Eser eser) {
        final Eser lastVersion = this.getLastCompletedEserByDirectorate(eser, this.facade.findByArtifact(eser));

        this.tasinirMalYonKodChanged = this.checkDiff(lastVersion.getTasinirMalYonKod().getAd(), this.getModel().getTasinirMalYonKod().getAd());
        this.eserAltTurEserTurChanged = this.checkDiff(lastVersion.getEserAltTur().getEserTur().getAd(), this.getModel().getEserAltTur().getEserTur().getAd());
        this.eserAltTurChanged = this.checkDiff(lastVersion.getEserAltTur().getAd(), this.getModel().getEserAltTur().getAd());
        this.envanterDefteriPathChanged = this.checkDiff(lastVersion.getEnvanterDefteriPath(), this.getModel().getEnvanterDefteriPath());
        this.eskiEnvanterNoChanged = this.checkDiff(lastVersion.getEnvanterNo(), this.getModel().getEnvanterNo());
        this.eserOzelAdiChanged = this.checkDiff(lastVersion.getEserOzelAdi(), this.getModel().getEserOzelAdi());
        this.tasinirIslemFisiNoChanged = this.checkDiff(lastVersion.getTasinirIslemFisiNo(), this.getModel().getTasinirIslemFisiNo());
        this.webSiteChanged = this.checkDiff(lastVersion.getWebSite(), this.getModel().getWebSite());
        this.aktifPasifChanged = this.checkDiff(lastVersion.getAktif(), this.getModel().getAktif());

        // MuesPicks
        this.yazmaBasmaSecimiChanged = this.checkDiff(lastVersion.getYazmaBasmaSecimi(), this.getModel().getYazmaBasmaSecimi());
        this.elisiDokumaSecimiChanged = this.checkDiff(lastVersion.getElisiDokumaSecimi(), this.getModel().getElisiDokumaSecimi());
        this.islamiGayriSecimiChanged = this.checkDiff(lastVersion.getIslamiGayriSecimi(), this.getModel().getIslamiGayriSecimi());
        this.kondisyonDurumuChanged = this.checkDiff(lastVersion.getKondisyonDurumu(), this.getModel().getKondisyonDurumu());

        this.uniklikDurumuChanged = this.checkDiff(lastVersion.getUniklikDurumu(), this.getModel().getUniklikDurumu());
        this.torenselDurumuChanged = this.checkDiff(lastVersion.getTorenselDurumu(), this.getModel().getTorenselDurumu());

        this.kiymetChanged = this.checkDiff(lastVersion.getKiymet(), this.getModel().getKiymet());
        this.alanKonumuChanged = this.checkDiff(this.lastCompletedAlanKonumuId, this.getModel().getEserDepo().getAlanKonumu().getId());
        if (lastVersion.getEserDepo() != null) {
            this.konumAciklamaChanged = this.checkDiff(lastVersion.getEserDepo().getAciklama(), this.getModel().getEserDepo().getAciklama());
        }

        this.hukumdarChanged = this.checkDiff(lastVersion.getHukumdar(), this.getModel().getHukumdar());
        this.termStartChanged = this.checkDiff(lastVersion.getTermStart(), this.getModel().getTermStart());
        this.termEndChanged = this.checkDiff(lastVersion.getTermEnd(), this.getModel().getTermEnd());
        this.kronolojiAciklamaChanged = this.checkDiff(lastVersion.getKronolojiAciklama(), this.getModel().getKronolojiAciklama());
        this.yapanVipChanged = this.checkDiff(lastVersion.getYapanVip(), this.getModel().getYapanVip());
        this.yaptiranVipChanged = this.checkDiff(lastVersion.getYaptiranVip(), this.getModel().getYaptiranVip());
        this.kullanacakVipChanged = this.checkDiff(lastVersion.getKullanacakVip(), this.getModel().getKullanacakVip());
        this.kullananVipChanged = this.checkDiff(lastVersion.getKullananVip(), this.getModel().getKullananVip());
        this.bagislayanVipChanged = this.checkDiff(lastVersion.getBagislayanVip(), this.getModel().getBagislayanVip());
        this.genelAciklamaChanged = this.checkDiff(lastVersion.getGenelAciklama(), this.getModel().getGenelAciklama());
        this.uretimBolgesiChanged = this.checkDiff(lastVersion.getUretimBolgesi(), this.getModel().getUretimBolgesi());
        this.uretimYeriChanged = this.checkDiff(lastVersion.getUretimYeri(), this.getModel().getUretimYeri());
        this.darpYeriChanged = this.checkDiff(lastVersion.getDarpYeri(), this.getModel().getDarpYeri());
        this.uretimYiliChanged = this.checkDiff(lastVersion.getUretimYili(), this.getModel().getUretimYili());
        this.sikkeDarpYonuChanged = this.checkDiff(lastVersion.getSikkeDarpYonu(), this.getModel().getSikkeDarpYonu());
        this.iliskilendirmeChanged = this.checkDiff(lastVersion.getIliskilendirme(), this.getModel().getIliskilendirme());

        // collections
        this.eserFotografsChanged = this.checkDiff(lastVersion.getEserFotografs(), this.getModel().getEserFotografs());
        this.eserVideosChanged = this.checkDiff(lastVersion.getVideos(), this.getModel().getVideos());
        this.eserMeasuresChanged = this.checkDiff(lastVersion.getEserMeasures(), this.getModel().getEserMeasures());
        this.eserZimmetsChanged = this.checkDiff(lastVersion.getEserZimmets(), this.getModel().getEserZimmets());
        this.eserCizimChanged = this.checkDiff(lastVersion.getEserCizims(), this.getModel().getEserCizims());
        this.eserHareketsChanged = this.checkDiff(lastVersion.getEserHarekets(), this.getModel().getEserHarekets());
        this.transcriptionChanged = this.checkDiff(lastVersion.getTranscriptions(), this.getModel().getTranscriptions());
        this.eserKeywordsChanged = this.checkDiff(lastVersion.getEserKeywords(), this.getModel().getEserKeywords());
        this.eserMalzemeYapimTeknigisChanged = this.checkDiff(lastVersion.getEserMalzemeYapimTeknigis(), this.getModel().getEserMalzemeYapimTeknigis());
        this.eserMalzemeSuslemeTeknigisChanged = this.checkDiff(lastVersion.getEserMalzemeSuslemeTeknigis(), this.getModel().getEserMalzemeSuslemeTeknigis());
        this.eserStilsChanged = this.checkDiff(lastVersion.getEserStils(), this.getModel().getEserStils());
        this.eserAtolyesChanged = this.checkDiff(lastVersion.getEserAtolyes(), this.getModel().getEserAtolyes());
        this.eserKaynakLiteratursChanged = this.checkDiff(lastVersion.getEserKaynakLiteraturs(), this.getModel().getEserKaynakLiteraturs());
        this.eserYayinLiteratursChanged = this.checkDiff(lastVersion.getEserYayinLiteraturs(), this.getModel().getEserYayinLiteraturs());
        this.eserSerhsChanged = this.checkDiff(lastVersion.getEserSerhs(), this.getModel().getEserSerhs());
    }

    /***
     * Checks any difference between 2 objects
     * 
     * @param obj1
     * @param obj2
     * @return diff
     */
    public boolean checkDiff(final Object obj1, final Object obj2) {
        try {
            return (((obj1 != null) && (obj2 != null) && !obj1.equals(obj2)) || (((obj1 != null) && (obj2 == null)) || ((obj1 == null) && (obj2 != null))));

        } catch (final EntityNotFoundException e) { // exception handling for MuesPick
            this.logger.info("[checkDiff] : {}", e.getMessage());
            if ((obj2 instanceof PickSuper) && !e.getMessage().split(" with id ")[1].equals((((PickSuper) obj2).getId().toString()))) {
                return true;
            }
        }
        return false;
    }

    /***
     * Checks any difference for collections considering empty and null values mean no diff
     */
    public boolean checkDiff(final Collection<?> c1, final Collection<?> c2) {
        return (((c1 != null) && (c2 != null) && !c1.equals(c2)) || ((this.hasItem(c1) && !this.hasItem(c2)) || (this.hasItem(c2) && !this.hasItem(c1))));
    }

    public boolean hasItem(final Collection<?> c) {
        return (c != null) && !c.isEmpty();
    }

    // Envers history Function
    @SuppressWarnings("unchecked")
    public Eser getLastCompletedEserByDirectorate(final Eser eser, final Workflow workflow) {
        List<Object[]> auditEserList = null;

        Eser lastCompletedEser = null;

        Audit lastApprovedRevision = null;
        /*  
            Eser ilk eklendiğinde, completed tarihi olmaz.  
            Bu durum eserin hiç onaylanmadığı, eserin değiştirilmesi için geri gönderildiği durumdur 
            Müdürün önüne DRAFT_PENDING_REVIEW versiyonunda ilk defa geliyor.   
            Eser versiyonun 3 olduğu durumdur.    
            Bir eser birkaç defa müdür tarafından reject edilse bile hep kıyaslama müdürün önüne gelen ilk duruma göre yapılmaktadır.   
            Müdürün ilk gördüğü eser durumu veritabanında versiyonu 3 olan ilk eserdir.    
        */
        /*  
            Bizim versiyon 5.1  
            In Hibernate Envers 5.x, the only traversal that is currently supported are those that are to-one mappings. 
            We intend to support the to-many association traversal in Hibernate Envers 6.0. 
        */

        /*
            Yukarıdaki denemede alınan hata :    
            Cag üzerinden kronoloji left joinle çekilmek istendiğinde                                   
            Property kronoloji of entity tr.gov.tubitak.bte.mues.model.Cag is not a valid association for queries:
        */

        if (workflow.getDateCompleted() == null) {
            auditEserList = AuditReaderFactory.get(this.getFacade().getEM())
                                              .createQuery()
                                              .forRevisionsOfEntity(Eser.class, false, true)
                                              .add(AuditEntity.id().eq(eser.getId()))
                                              .add(AuditEntity.property("versiyon").in(Arrays.asList(EserVersion.DRAFT_PENDING_REVIEW.getCode(), EserVersion.BIRLESTIRME_PENDING_REVIEW.getCode())))
                                              .addOrder(AuditEntity.revisionProperty(TIMESTAMP).asc())
                                              .setMaxResults(1)
                                              .getResultList();

        } else {
            /*  
                Eser daha önce müdür tarafından onaylandı. Sonra değişiklikler yapıldı  
                Eserin workflowdaki completed tarihine bakılarak onaylanan son eser çekilir.    
             */
            auditEserList = AuditReaderFactory.get(this.getFacade().getEM())
                                              .createQuery()
                                              .forRevisionsOfEntity(Eser.class, false, true)
                                              .add(AuditEntity.id().eq(eser.getId()))
                                              .add(AuditEntity.revisionProperty(TIMESTAMP).gt(workflow.getDateCompleted().getTime()))
                                              .addOrder(AuditEntity.revisionProperty(TIMESTAMP).asc())
                                              .setMaxResults(1)
                                              .getResultList();

        }

        lastCompletedEser = (Eser) auditEserList.get(0)[0];
        lastApprovedRevision = (Audit) auditEserList.get(0)[1];
        final Long lastApprovedRevisionNo = lastApprovedRevision.getId();
        this.lastCompletedAlanKonumuId = this.eserDepoFacade.findLastApprovedAlanKonumId(eser.getId(), lastApprovedRevisionNo);

        return lastCompletedEser;
    }

    public void resetDifferences() {
        this.envanterDefteriPathChanged = false;
        this.eskiEnvanterNoChanged = false;
        this.tasinirMalYonKodChanged = false;
        this.tasinirIslemFisiNoChanged = false;
        this.webSiteChanged = false;
        this.eserAltTurEserTurChanged = false;
        this.eserAltTurChanged = false;
        this.eserOzelAdiChanged = false;
        this.aktifPasifChanged = false;
        this.yazmaBasmaSecimiChanged = false;
        this.elisiDokumaSecimiChanged = false;
        this.islamiGayriSecimiChanged = false;
        this.uniklikDurumuChanged = false;
        this.torenselDurumuChanged = false;
        this.kiymetChanged = false;
        this.kondisyonDurumuChanged = false;
        this.konumAciklamaChanged = false;
        this.hukumdarChanged = false;
        this.termStartChanged = false;
        this.termEndChanged = false;
        this.kronolojiAciklamaChanged = false;
        this.yapanVipChanged = false;
        this.yaptiranVipChanged = false;
        this.kullanacakVipChanged = false;
        this.kullananVipChanged = false;
        this.bagislayanVipChanged = false;
        this.eserFotografsChanged = false;
        this.eserMeasuresChanged = false;
        this.eserZimmetsChanged = false;
        this.eserCizimChanged = false;
        this.eserHareketsChanged = false;
        this.transcriptionChanged = false;
        this.genelAciklamaChanged = false;
        this.eserKeywordsChanged = false;
        this.uretimBolgesiChanged = false;
        this.uretimYeriChanged = false;
        this.darpYeriChanged = false;
        this.uretimYiliChanged = false;
        this.sikkeDarpYonuChanged = false;
        this.eserMalzemeYapimTeknigisChanged = false;
        this.eserMalzemeSuslemeTeknigisChanged = false;
        this.eserStilsChanged = false;
        this.eserAtolyesChanged = false;
        this.eserKaynakLiteratursChanged = false;
        this.eserYayinLiteratursChanged = false;
        this.iliskilendirmeChanged = false;
        this.alanKonumuChanged = false;
        this.eserSerhsChanged = false;
    }

    public void listHistoryOfEnvanterNo() {
        if (this.getModel() != null) {
            final List<Object[]> auditList = this.facade.historyOfEnvanterNoByNativeQuery(this.getModel().getId());
            this.setEnvanterNoHistoryList(auditList);
        }
    }

    // getters and setters ....................................................

    public List<Object[]> getEserHistoryList() {
        return this.eserHistoryList;
    }

    public void setEserHistoryList(final List<Object[]> eserHistoryList) {
        this.eserHistoryList = eserHistoryList;
    }

    @Override
    public AbstractFacade<Eser> getFacade() {
        return this.facade;
    }

    public ArtifactsSolrModel getSelectedArtifactSolrModelData() {
        return this.selectedArtifactSolrModelData;
    }

    public void setSelectedArtifactSolrModelData(final ArtifactsSolrModel selectedArtifactSolrModelData) {
        this.selectedArtifactSolrModelData = selectedArtifactSolrModelData;
    }

    public Integer getPermanentId() {
        return this.permanentId;
    }

    public void setPermanentId(final Integer permanentId) {
        if (permanentId == null) {
            return;
        }
        if ((this.permanentId == null) || !Objects.equals(this.permanentId, permanentId)) {
            this.setModel(this.getFacade().findEagerById(permanentId));
            this.permanentId = permanentId;
            this.auditFacade.log(AuditEvent.EserGoruntuleme, this.sessionBean.getCurrentUser().getKullaniciAdi() + KULLANICISI + this.getModel().getOneId() + NUMARALI_ESERI_GORUNTULEMISTIR);
        }
    }

    public boolean isEnvanterDefteriPathChanged() {
        return this.envanterDefteriPathChanged;
    }

    public void setEnvanterDefteriPathChanged(final boolean envanterDefteriPathChanged) {
        this.envanterDefteriPathChanged = envanterDefteriPathChanged;
    }

    public boolean isEskiEnvanterNoChanged() {
        return this.eskiEnvanterNoChanged;
    }

    public void setEskiEnvanterNoChanged(final boolean eskiEnvanterNoChanged) {
        this.eskiEnvanterNoChanged = eskiEnvanterNoChanged;
    }

    public boolean isTasinirMalYonKodChanged() {
        return this.tasinirMalYonKodChanged;
    }

    public void setTasinirMalYonKodChanged(final boolean tasinirMalYonKodChanged) {
        this.tasinirMalYonKodChanged = tasinirMalYonKodChanged;
    }

    public boolean isTasinirIslemFisiNoChanged() {
        return this.tasinirIslemFisiNoChanged;
    }

    public void setTasinirIslemFisiNoChanged(final boolean tasinirIslemFisiNoChanged) {
        this.tasinirIslemFisiNoChanged = tasinirIslemFisiNoChanged;
    }

    public boolean isEserAltTurEserTurChanged() {
        return this.eserAltTurEserTurChanged;
    }

    public void setEserAltTurEserTurChanged(final boolean eserAltTurEserTurChanged) {
        this.eserAltTurEserTurChanged = eserAltTurEserTurChanged;
    }

    public boolean isEserAltTurChanged() {
        return this.eserAltTurChanged;
    }

    public void setEserAltTurChanged(final boolean eserAltTurChanged) {
        this.eserAltTurChanged = eserAltTurChanged;
    }

    public boolean isEserOzelAdiChanged() {
        return this.eserOzelAdiChanged;
    }

    public void setEserOzelAdiChanged(final boolean eserOzelAdiChanged) {
        this.eserOzelAdiChanged = eserOzelAdiChanged;
    }

    public boolean isAktifPasifChanged() {
        return this.aktifPasifChanged;
    }

    public void setAktifPasifChanged(final boolean aktifPasifChanged) {
        this.aktifPasifChanged = aktifPasifChanged;
    }

    public boolean isYazmaBasmaSecimiChanged() {
        return this.yazmaBasmaSecimiChanged;
    }

    public void setYazmaBasmaSecimiChanged(final boolean yazmaBasmaSecimiChanged) {
        this.yazmaBasmaSecimiChanged = yazmaBasmaSecimiChanged;
    }

    public boolean isElisiDokumaSecimiChanged() {
        return this.elisiDokumaSecimiChanged;
    }

    public void setElisiDokumaSecimiChanged(final boolean elisiDokumaSecimiChanged) {
        this.elisiDokumaSecimiChanged = elisiDokumaSecimiChanged;
    }

    public boolean isIslamiGayriSecimiChanged() {
        return this.islamiGayriSecimiChanged;
    }

    public void setIslamiGayriSecimiChanged(final boolean islamiGayriSecimiChanged) {
        this.islamiGayriSecimiChanged = islamiGayriSecimiChanged;
    }

    public boolean isUniklikDurumuChanged() {
        return this.uniklikDurumuChanged;
    }

    public void setUniklikDurumuChanged(final boolean uniklikDurumuChanged) {
        this.uniklikDurumuChanged = uniklikDurumuChanged;
    }

    public boolean isTorenselDurumuChanged() {
        return this.torenselDurumuChanged;
    }

    public void setTorenselDurumuChanged(final boolean torenselDurumuChanged) {
        this.torenselDurumuChanged = torenselDurumuChanged;
    }

    public boolean isKiymetChanged() {
        return this.kiymetChanged;
    }

    public void setKiymetChanged(final boolean kiymetChanged) {
        this.kiymetChanged = kiymetChanged;
    }

    public boolean isKondisyonDurumuChanged() {
        return this.kondisyonDurumuChanged;
    }

    public void setKondisyonDurumuChanged(final boolean kondisyonDurumuChanged) {
        this.kondisyonDurumuChanged = kondisyonDurumuChanged;
    }

    public boolean isAlanKonumuChanged() {
        return this.alanKonumuChanged;
    }

    public void setAlanKonumuChanged(final boolean alanKonumuChanged) {
        this.alanKonumuChanged = alanKonumuChanged;
    }

    public boolean isKonumAciklamaChanged() {
        return this.konumAciklamaChanged;
    }

    public void setKonumAciklamaChanged(final boolean konumAciklamaChanged) {
        this.konumAciklamaChanged = konumAciklamaChanged;
    }

    public boolean isHukumdarChanged() {
        return this.hukumdarChanged;
    }

    public void setHukumdarChanged(final boolean hukumdarChanged) {
        this.hukumdarChanged = hukumdarChanged;
    }

    public boolean isTermStartChanged() {
        return this.termStartChanged;
    }

    public void setTermStartChanged(final boolean termStartChanged) {
        this.termStartChanged = termStartChanged;
    }

    public boolean isTermEndChanged() {
        return this.termEndChanged;
    }

    public void setTermEndChanged(final boolean termEndChanged) {
        this.termEndChanged = termEndChanged;
    }

    public boolean isKronolojiAciklamaChanged() {
        return this.kronolojiAciklamaChanged;
    }

    public void setKronolojiAciklamaChanged(final boolean kronolojiAciklamaChanged) {
        this.kronolojiAciklamaChanged = kronolojiAciklamaChanged;
    }

    public boolean isYapanVipChanged() {
        return this.yapanVipChanged;
    }

    public void setYapanVipChanged(final boolean yapanVipChanged) {
        this.yapanVipChanged = yapanVipChanged;
    }

    public boolean isYaptiranVipChanged() {
        return this.yaptiranVipChanged;
    }

    public void setYaptiranVipChanged(final boolean yaptiranVipChanged) {
        this.yaptiranVipChanged = yaptiranVipChanged;
    }

    public boolean isKullanacakVipChanged() {
        return this.kullanacakVipChanged;
    }

    public void setKullanacakVipChanged(final boolean kullanacakVipChanged) {
        this.kullanacakVipChanged = kullanacakVipChanged;
    }

    public boolean isKullananVipChanged() {
        return this.kullananVipChanged;
    }

    public void setKullananVipChanged(final boolean kullananVipChanged) {
        this.kullananVipChanged = kullananVipChanged;
    }

    public boolean isBagislayanVipChanged() {
        return this.bagislayanVipChanged;
    }

    public void setBagislayanVipChanged(final boolean bagislayanVipChanged) {
        this.bagislayanVipChanged = bagislayanVipChanged;
    }

    public boolean isEserFotografsChanged() {
        return this.eserFotografsChanged;
    }

    public void setEserFotografsChanged(final boolean eserFotografsChanged) {
        this.eserFotografsChanged = eserFotografsChanged;
    }

    public boolean isEserMeasuresChanged() {
        return this.eserMeasuresChanged;
    }

    public void setEserMeasuresChanged(final boolean eserMeasuresChanged) {
        this.eserMeasuresChanged = eserMeasuresChanged;
    }

    public boolean isEserZimmetsChanged() {
        return this.eserZimmetsChanged;
    }

    public void setEserZimmetsChanged(final boolean eserZimmetsChanged) {
        this.eserZimmetsChanged = eserZimmetsChanged;
    }

    public boolean isEserCizimChanged() {
        return this.eserCizimChanged;
    }

    public void setEserCizimChanged(final boolean eserCizimChanged) {
        this.eserCizimChanged = eserCizimChanged;
    }

    public boolean isEserHareketsChanged() {
        return this.eserHareketsChanged;
    }

    public void setEserHareketsChanged(final boolean eserHareketsChanged) {
        this.eserHareketsChanged = eserHareketsChanged;
    }

    public boolean isTranscriptionChanged() {
        return this.transcriptionChanged;
    }

    public void setTranscriptionChanged(final boolean transcriptionChanged) {
        this.transcriptionChanged = transcriptionChanged;
    }

    public boolean isGenelAciklamaChanged() {
        return this.genelAciklamaChanged;
    }

    public void setGenelAciklamaChanged(final boolean genelAciklamaChanged) {
        this.genelAciklamaChanged = genelAciklamaChanged;
    }

    public boolean isEserKeywordsChanged() {
        return this.eserKeywordsChanged;
    }

    public void setEserKeywordsChanged(final boolean eserKeywordsChanged) {
        this.eserKeywordsChanged = eserKeywordsChanged;
    }

    public boolean isUretimBolgesiChanged() {
        return this.uretimBolgesiChanged;
    }

    public void setUretimBolgesiChanged(final boolean uretimBolgesiChanged) {
        this.uretimBolgesiChanged = uretimBolgesiChanged;
    }

    public boolean isUretimYeriChanged() {
        return this.uretimYeriChanged;
    }

    public void setUretimYeriChanged(final boolean uretimYeriChanged) {
        this.uretimYeriChanged = uretimYeriChanged;
    }

    public boolean isDarpYeriChanged() {
        return this.darpYeriChanged;
    }

    public void setDarpYeriChanged(final boolean darpYeriChanged) {
        this.darpYeriChanged = darpYeriChanged;
    }

    public boolean isUretimYiliChanged() {
        return this.uretimYiliChanged;
    }

    public void setUretimYiliChanged(final boolean uretimYiliChanged) {
        this.uretimYiliChanged = uretimYiliChanged;
    }

    public boolean isSikkeDarpYonuChanged() {
        return this.sikkeDarpYonuChanged;
    }

    public void setSikkeDarpYonuChanged(final boolean sikkeDarpYonuChanged) {
        this.sikkeDarpYonuChanged = sikkeDarpYonuChanged;
    }

    public boolean isEserMalzemeYapimTeknigisChanged() {
        return this.eserMalzemeYapimTeknigisChanged;
    }

    public void setEserMalzemeYapimTeknigisChanged(final boolean eserMalzemeYapimTeknigisChanged) {
        this.eserMalzemeYapimTeknigisChanged = eserMalzemeYapimTeknigisChanged;
    }

    public boolean isEserMalzemeSuslemeTeknigisChanged() {
        return this.eserMalzemeSuslemeTeknigisChanged;
    }

    public void setEserMalzemeSuslemeTeknigisChanged(final boolean eserMalzemeSuslemeTeknigisChanged) {
        this.eserMalzemeSuslemeTeknigisChanged = eserMalzemeSuslemeTeknigisChanged;
    }

    public boolean isEserStilsChanged() {
        return this.eserStilsChanged;
    }

    public void setEserStilsChanged(final boolean eserStilsChanged) {
        this.eserStilsChanged = eserStilsChanged;
    }

    public boolean isEserAtolyesChanged() {
        return this.eserAtolyesChanged;
    }

    public void setEserAtolyesChanged(final boolean eserAtolyesChanged) {
        this.eserAtolyesChanged = eserAtolyesChanged;
    }

    public boolean isEserKaynakLiteratursChanged() {
        return this.eserKaynakLiteratursChanged;
    }

    public void setEserKaynakLiteratursChanged(final boolean eserKaynakLiteratursChanged) {
        this.eserKaynakLiteratursChanged = eserKaynakLiteratursChanged;
    }

    public boolean isEserYayinLiteratursChanged() {
        return this.eserYayinLiteratursChanged;
    }

    public void setEserYayinLiteratursChanged(final boolean eserYayinLiteratursChanged) {
        this.eserYayinLiteratursChanged = eserYayinLiteratursChanged;
    }

    public boolean isIliskilendirmeChanged() {
        return this.iliskilendirmeChanged;
    }

    public void setIliskilendirmeChanged(final boolean iliskilendirmeChanged) {
        this.iliskilendirmeChanged = iliskilendirmeChanged;
    }

    public Integer getLastCompletedAlanKonumuId() {
        return this.lastCompletedAlanKonumuId;
    }

    public void setLastCompletedAlanKonumuId(final Integer lastCompletedAlanKonumuId) {
        this.lastCompletedAlanKonumuId = lastCompletedAlanKonumuId;
    }

    public boolean isEserSerhsChanged() {
        return this.eserSerhsChanged;
    }

    public void setEserSerhsChanged(final boolean eserSerhsChanged) {
        this.eserSerhsChanged = eserSerhsChanged;
    }

    public boolean isWebSiteChanged() {
        return this.webSiteChanged;
    }

    public void setWebSiteChanged(final boolean webSiteChanged) {
        this.webSiteChanged = webSiteChanged;
    }

    public List<Object[]> getEnvanterNoHistoryList() {
        return this.envanterNoHistoryList;
    }

    public void setEnvanterNoHistoryList(final List<Object[]> envanterNoHistoryList) {
        this.envanterNoHistoryList = envanterNoHistoryList;
    }

    public boolean isEserVideosChanged() {
        return this.eserVideosChanged;
    }

    public void setEserVideosChanged(final boolean eserVideosChanged) {
        this.eserVideosChanged = eserVideosChanged;
    }

    public List<LabRestorationView> getVouchersList() {
        return this.vouchersList;
    }

    public void setVouchersList(final List<LabRestorationView> vouchersList) {
        this.vouchersList = vouchersList;
    }

}
