package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.session.MalzemeFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class MalzemeController extends AbstractController<Malzeme> implements SingleFileUploadable {

    private static final long       serialVersionUID = -8962274757448225195L;

    @Inject
    private FileUploadHelper        fileUploadHelper;

    @Inject
    private MuesParameters          muesParameters;

    @Inject
    private MalzemeFacade           facade;

    private transient List<Malzeme> selectionList;

    private transient Integer       period           = Integer.valueOf(12);

    public MalzemeController() {
        super(Malzeme.class);
    }

    public List<Malzeme> filterByNameAndAciklama(final String query) {
        return this.facade.filterByNameAndAciklama(query);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.muesParameters.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
    }

    public void updatePeriods() {
        final List<AbstractEntity> entities = new ArrayList<>();
        this.getSelectionList().stream().forEach(x -> {
            x.setPeriod(this.getPeriod());
            entities.add(x);
        });
        if (this.facade.update(entities).isSuccess()) {
            MuesUtil.showMessage("Period(lar) Güncellendi", FacesMessage.SEVERITY_INFO);
            this.getSelectionList().clear();
        } else {
            MuesUtil.showMessage("Güncelleme Başarısız", FacesMessage.SEVERITY_INFO);
        }
    }

    // getters and setters ....................................................

    @Override
    public MalzemeFacade getFacade() {
        return this.facade;
    }

    public List<Malzeme> getSelectionList() {
        if ((this.selectionList == null)) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;

    }

    public void setSelectionList(final List<Malzeme> selectionList) {
        this.selectionList = selectionList;
    }

    public Integer getPeriod() {
        return this.period;
    }

    public void setPeriod(final Integer period) {
        this.period = period;
    }

}
