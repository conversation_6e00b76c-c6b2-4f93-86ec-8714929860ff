package tr.gov.tubitak.bte.mues.constraint;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import tr.gov.tubitak.bte.mues.constraint.validator.ValidEserHareketValidator;

@Target({ ElementType.TYPE, ElementType.ANNOTATION_TYPE })
@Constraint(validatedBy = { ValidEserHareketValidator.class })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidEserHareket {

    String message() default "{tr.gov.tubitak.bte.mues.constraint.ValidEserHareket}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
