package tr.gov.tubitak.bte.mues.constraint.validator;

import org.omnifaces.util.copier.Copier;

import tr.gov.tubitak.bte.mues.model.Alan;

public class AlanCopier implements Copier {
	@Override
    public Alan copy(final Object object) {
        final Alan original = (Alan) object;
        final Alan copy = new Alan();

        copy.setKod(original.getKod());
                
        return copy;
    }
}
