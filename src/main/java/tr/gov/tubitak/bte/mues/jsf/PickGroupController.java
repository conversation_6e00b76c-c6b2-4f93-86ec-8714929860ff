package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.PickGroup;
import tr.gov.tubitak.bte.mues.session.PickGroupFacade;

@Named
@ViewScoped
public class PickGroupController extends AbstractController<PickGroup> {

    private static final long  serialVersionUID = 5674238528961050396L;

    @Inject
    private PickGroupFacade facade;

    public PickGroupController() {
        super(PickGroup.class);
    }

    @Override
    public PickGroupFacade getFacade() {
        return this.facade;
    }

}
