package tr.gov.tubitak.bte.mues.search;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

import org.apache.poi.ss.formula.eval.NotImplementedException;

/**
 * Birleşik kriterleri tutan sınıf.
 */
@Entity
@DiscriminatorValue("0")
public class CompoundCriterion extends AbstractCriterion {

    private static final long   serialVersionUID = 6210102957784605499L;

    @Column(name = "logicalOperator")
    private LogicalOperatorEnum logicalOperator  = LogicalOperatorEnum.NULL;

    /**
     * Yapıcı metot.
     */
    public CompoundCriterion() {
    }

    /* (non-Javadoc)
     * @see tr.gov.tubitak.bte.ebelgem.search.ICriterion#setModel(tr.gov.tubitak.bte.ebelgem.search.detailed.CriterionModel)
     */
    @Override
    public ICriterion setModel(final CriterionModel model) {
        this.setRowId(model.getRowId());
        this.setChildren(model.getCriteriaList());
        this.logicalOperator = model.getOperator();
        return this;
    }

    public int getLogicalOperator() {
        return this.getLogicalOperatorEnum().getCode();
    }

    /**
     * Mantıksal operatörü döner.
     *
     * @return mantıksal operatör
     */
    @Transient
    public LogicalOperatorEnum getLogicalOperatorEnum() {
        if (this.logicalOperator != null) {
            return this.logicalOperator;
        }
        return LogicalOperatorEnum.NULL;
    }

    public void setLogicalOperator(final int logicalOperator) {
        this.logicalOperator = LogicalOperatorEnum.parse(logicalOperator);
    }

    public void setLogicalOperator(final LogicalOperatorEnum logicalOperator) {
        this.logicalOperator = logicalOperator;
    }

    @Override
    @Transient
    public String getText() {
        final List<ICriterion> children = this.getChildren();
        if (children.isEmpty()) {
            return "";
        }
        final StringBuilder sb = new StringBuilder();
        // TODO not işleminde sadece 1 tane seçili olmalı
        if (this.logicalOperator == LogicalOperatorEnum.NOT) {
            sb.append(this.logicalOperator.getText());
            sb.append(SearchConstants.SPACE_LITERAL);
            sb.append(SearchConstants.LEFT_PARANTHESIS);
            sb.append(children.get(0).getText());
            sb.append(SearchConstants.RIGHT_PARANTHESIS);
        } else {
            final String separator = SearchConstants.SPACE_LITERAL + this.logicalOperator.getText() + SearchConstants.SPACE_LITERAL;
            String delimeter = "";
            sb.append(SearchConstants.LEFT_PARANTHESIS);
            for (final ICriterion criterion : children) {
                sb.append(delimeter).append(criterion.getText());
                delimeter = separator;
            }
            sb.append(SearchConstants.RIGHT_PARANTHESIS);
        }
        return sb.toString();
    }

    /* (non-Javadoc)
     * @see tr.gov.tubitak.bte.ebelgem.search.ICriterion#getSql()
     */
    @Override
    @Transient
    public String getSql() {
        final List<ICriterion> children = this.getChildren();
        if (children.isEmpty()) {
            return "";
        }
        final StringBuilder sb = new StringBuilder();
        // TODO: not işleminde sadece 1 tane seçili olmalı
        if (this.logicalOperator == LogicalOperatorEnum.NOT) {
            sb.append(this.logicalOperator.getSql());
            sb.append(SearchConstants.SPACE_LITERAL);
            sb.append(SearchConstants.LEFT_PARANTHESIS);
            sb.append(children.get(0).getSql());
            sb.append(SearchConstants.RIGHT_PARANTHESIS);
        } else {
            String delimeter = "";
            sb.append(SearchConstants.LEFT_PARANTHESIS);
            final String separator = SearchConstants.SPACE_LITERAL + this.logicalOperator.getSql() + SearchConstants.SPACE_LITERAL;

            for (final ICriterion criterion : children) {
                final String sql = criterion.getSql();

                if (sql.length() > 0) {
                    sb.append(delimeter).append(sql);
                    delimeter = separator;
                }

            }
            sb.append(SearchConstants.RIGHT_PARANTHESIS);
        }
        return sb.toString();
    }

    @Override
    @Transient
    public boolean isValid() {
        // TODO: operasyonların kontrolü burda yapılabilir (yapılmaya da bilir).
        return true;
    }

    @Override
    public String getFacet() {

        throw new NotImplementedException("coumpund criteria için facet heniz tanımlanmadı.");
    }

    @Override
    public String getTitle() {
        // TODO: Auto-generated method stub
        return null;
    }
}
