package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
					   
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "AlanKonumu.findEagerById", query = "SELECT a FROM AlanKonumu a JOIN FETCH a.alanKonumuTur JOIN FETCH a.alan aa JOIN FETCH aa.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE a.id = :id")
@NamedQuery(name = "AlanKonumu.findByMuseumDirectorate", query = "SELECT a FROM AlanKonumu a JOIN FETCH a.alanKonumuTur JOIN FETCH a.alan aa JOIN FETCH aa.bina b JOIN FETCH b.bagliBirim bb WHERE bb.mudurluk.id = :id AND a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedQuery(name = "AlanKonumu.findTeshirSalonuByMuseumDirectorate", query = "SELECT a FROM AlanKonumu a JOIN FETCH a.alanKonumuTur JOIN FETCH a.alan aa JOIN FETCH aa.bina b JOIN FETCH b.bagliBirim bb WHERE bb.mudurluk.id = :id AND a.aktif = true AND a.silinmis = false AND a.kod LIKE 'TS%' ORDER BY a.ad")
@NamedQuery(name = "AlanKonumu.findAll", query = "SELECT a FROM AlanKonumu a JOIN FETCH a.alanKonumuTur JOIN FETCH a.alan aa JOIN FETCH aa.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk ORDER BY a.silinmis, a.aktif DESC, a.ad")
@NamedQuery(name = "AlanKonumu.findByMudurluk", query = "SELECT a FROM AlanKonumu a JOIN FETCH a.alanKonumuTur JOIN FETCH a.alan aa JOIN FETCH aa.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk m where bb.mudurluk in :muzeler ORDER BY a.silinmis, a.aktif DESC,m.ad ASC")
@NamedQuery(name = "AlanKonumu.findActive", query = "SELECT a FROM AlanKonumu a JOIN FETCH a.alanKonumuTur JOIN FETCH a.alan aa JOIN FETCH aa.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedQuery(name = "AlanKonumu.findByNameAndAciklamaAndAlan", query = "SELECT a FROM AlanKonumu a WHERE a.aktif = true AND a.silinmis = false AND (a.ad LIKE :str OR a.aciklama LIKE :str) AND a.alan = :alan ORDER BY a.ad")

public class AlanKonumuSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 8002475743152810876L;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "pdfPath", length = 250)
    private String            pdfPath;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "dwgPath", length = 250)
    private String            dwgPath;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ALAN_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Alan              alan;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ALAN_KONUMU_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private AlanKonumuTur     alanKonumuTur;

    public AlanKonumuSuper() {
        // default constructor
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getPdfPath() {
        return this.pdfPath;
    }

    public void setPdfPath(final String pdfPath) {
        this.pdfPath = pdfPath;
    }

    public String getDwgPath() {
        return this.dwgPath;
    }

    public void setDwgPath(final String dwgPath) {
        this.dwgPath = dwgPath;
    }

    public Alan getAlan() {
        return this.alan;
    }

    public void setAlan(final Alan alan) {
        this.alan = alan;
    }

    public AlanKonumuTur getAlanKonumuTur() {
        return this.alanKonumuTur;
    }

    public void setAlanKonumuTur(final AlanKonumuTur alanKonumuTur) {
        this.alanKonumuTur = alanKonumuTur;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
