package tr.gov.tubitak.bte.mues.model;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "YAZI_TIPI_DIL")
@NamedQuery(name = "YaziTipiDil.findEagerById", query = "SELECT y FROM YaziTipiDil y LEFT JOIN FETCH y.yaziTipi LEFT JOIN FETCH y.dil WHERE y.id = :id")
@NamedQuery(name = "YaziTipiDil.findAll", query = "SELECT y FROM YaziTipiDil y LEFT JOIN FETCH y.yaziTipi yt LEFT JOIN FETCH y.dil  ORDER BY y.silinmis, y.aktif DESC ,yt.ad ASC ")
@NamedQuery(name = "YaziTipiDil.findActive", query = "SELECT y FROM YaziTipiDil y LEFT JOIN FETCH y.yaziTipi LEFT JOIN FETCH y.dil WHERE y.aktif = true AND y.silinmis = false")
public class YaziTipiDil extends AbstractEntity {

    private static final long serialVersionUID = -8985897051092289380L;

    @JoinColumn(name = "YAZI_TIPI_ID", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private YaziTipi          yaziTipi;

    @JoinColumn(name = "DIL_ID", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Dil               dil;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public YaziTipiDil() {
    }

    // getters and setters ....................................................

    public YaziTipi getYaziTipi() {
        return this.yaziTipi;
    }

    public void setYaziTipi(final YaziTipi yaziTipi) {
        this.yaziTipi = yaziTipi;
    }

    public Dil getDil() {
        return this.dil;
    }

    public void setDil(final Dil dil) {
        this.dil = dil;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return "(" + Stream.of(this.yaziTipi.getAd(), this.dil.getAd()).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" ")) + ")";
    }

}
