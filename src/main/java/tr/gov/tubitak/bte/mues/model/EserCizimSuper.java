package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "EserCizim.findEagerById", query = "SELECT e FROM EserCizim e WHERE e.id = :id")
@NamedQuery(name = "EserCizim.findAll", query = "SELECT e FROM EserCizim e ORDER BY e.silinmis, e.aktif DESC")
@NamedQuery(name = "EserCizim.findActive", query = "SELECT e FROM EserCizim e WHERE e.aktif = true AND e.silinmis = false")

public class EserCizimSuper extends AbstractEntity {

    private static final long serialVersionUID = -1515398537489345482L;

    @Size(max = 50)
    @Column(name = "ad", length = 50)
    private String            ad;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "cizimPath", length = 250)
    private String            fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public EserCizimSuper() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
