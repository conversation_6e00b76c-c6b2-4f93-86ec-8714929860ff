package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.EserProposalCizim;
import tr.gov.tubitak.bte.mues.session.EserProposalCizimFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class EserProposalCizimController extends AbstractController<EserProposalCizim> implements SingleFileUploadable {

    private static final long       serialVersionUID = -128005660308802713L;

    @Inject
    private EserProposalCizimFacade facade;

    @Inject
    private FileUploadHelper        fileUploadHelper;

    public EserProposalCizimController() {
        super(EserProposalCizim.class);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setAd(MuesUtil.extractFileName(event.getFile().getFileName()));
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath((this.fileUploadHelper.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK)));
        }
    }

    public void makeFileRelatedOperations(final EserProposalCizim cizim) {
        this.setModel(cizim);
        this.writeToPermanentFolder();
    }

    public List<String> buildFilePathFromModel(final EserProposalCizim model) {
        return this.fileUploadHelper.constructMainCopyImagePath(model.getFotografPath(), FolderType.IMAGE_AK);

    }

    // getters and setters ....................................................

    @Override
    public EserProposalCizimFacade getFacade() {
        return this.facade;
    }

}
