package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ESER_DEPO")
public class EserDepo extends EserDepoSuper {

    private static final long serialVersionUID = -5646334757253460716L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public EserDepo() {
        // default constructor
    }

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    @Override
    public String toString() {
        Optional.ofNullable(this.getAlanKonumu().getAd()).orElse("" + this.getId());
        return Joiner.on(" ").skipNulls().join(this.eser.getTitle(), this.getAlanKonumu().getAd(), this.getAciklama());
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.getAlanKonumu().getAd()).orElse("" + this.getId());
    }

}
