package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Ilce;
import tr.gov.tubitak.bte.mues.model.Kazi;
import tr.gov.tubitak.bte.mues.session.IlceFacade;
import tr.gov.tubitak.bte.mues.session.KaziFacade;

@Named
@ViewScoped
public class KaziController extends AbstractController<Kazi> {

    private static final long serialVersionUID = -4790994858737662781L;

    @Inject
    private KaziFacade        facade;

    @Inject
    private IlceFacade        ilceFacade;

    public KaziController() {
        super(Kazi.class);
    }
    
    public List<Kazi> getActiveItems(){
        return this.facade.findActive();
    }

    public void handleIlChange(final SelectEvent event) {
        // this.getModel().setIl((Il) event.getObject());
        this.getModel().setIlce(null);
    }

    public List<Ilce> filterByNameAndIl(final String query) {
        return this.ilceFacade.filterByNameAndIl(query, this.getModel().getIl());
    }

    public List<Kazi> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }
    // getters and setters ....................................................

    @Override
    public KaziFacade getFacade() {
        return this.facade;
    }

}
