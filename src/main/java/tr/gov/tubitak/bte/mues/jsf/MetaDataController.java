
/**
 * 
 * Meta Data Controller
 * 
 * <AUTHOR>
 *
 */

package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.model.DualListModel;

import tr.gov.tubitak.bte.mues.configuration.MetadataFacade;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;

@Named
@ViewScoped
public class MetaDataController extends AbstractController<Metadata> {

    private static final long       serialVersionUID = -2272968981172922632L;

    @Inject
    private MetadataFacade          facade;

    @Inject
    private SolrSearcher            solrSearcher;

    private DualListModel<Metadata> metadataDualList;

    public MetaDataController() {
        super(Metadata.class);
    }

    public void initMetaDataDuallist() {

        // Countries
        final List<Metadata> metadataSource = this.facade.findAllMetadatas();
        final List<Metadata> metadataTarget = new ArrayList<>();

        this.metadataDualList = new DualListModel<>(metadataSource, metadataTarget);

    }

    // getters and setters ....................................................

    @Override
    public MetadataFacade getFacade() {
        return this.facade;
    }

    public DualListModel<Metadata> getMetadataDualList() {
        if (this.metadataDualList == null) {
            this.metadataDualList = new DualListModel<>();
        }
        return this.metadataDualList;
    }

    public void setMetadataDualList(final DualListModel<Metadata> metadataDualList) {
        this.metadataDualList = metadataDualList;
    }

    public String fetchMetadataIndexFaceName(final String metadataName) {
        return this.solrSearcher.getMetaDataMap().get(metadataName).getIndexFaceName();
    }

}
