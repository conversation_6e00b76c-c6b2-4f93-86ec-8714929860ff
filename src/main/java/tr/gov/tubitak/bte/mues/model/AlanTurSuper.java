package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
@NamedQuery(name = "AlanTur.findEagerById", query = "SELECT a FROM AlanTur a WHERE a.id = :id")
@NamedQuery(name = "AlanTur.findAll", query = "SELECT a FROM AlanTur a ORDER BY a.silinmis, a.aktif DESC, a.ad")
@NamedQuery(name = "AlanTur.findActive", query = "SELECT a FROM AlanTur a WHERE a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedNativeQuery(name = "AlanTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ALAN WHERE SILINMIS = 0 AND ALAN_TUR_ID = :id)")
@MappedSuperclass
public class AlanTurSuper extends AbstractEntity implements DeleteValidatable
{

    private static final long serialVersionUID = 2907570293100647646L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public AlanTurSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
