package tr.gov.tubitak.bte.mues.util;

import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;

/**
 * <AUTHOR>
 *
 */

public enum IncidentTypeEnum {
    // view isimleri ile ayni isimlendirmeler kullanilmistir.
    ESER_TUMBILGILER(0, "Eser Tüm Bilgiler ", ArtifactsSolrModel.class);

    private final Integer  code;

    private final String   label;

    private final Class<?> classRef;

    private String[]       childViews = {};

    private String[]       childSQLs  = {};

    private IncidentTypeEnum(final Integer code, final String label, final Class<?> classRef) {

        this(code, label, new String[0], classRef);
    }

    private IncidentTypeEnum(final Integer code, final String label, final String[] childViews, final Class<?> classRef) {
        this.code = code;
        this.label = label;
        this.childViews = childViews;
        this.classRef = classRef;
    }

    private IncidentTypeEnum(final Integer code, final String label, final String[] childViews, final String[] childSQLs, final Class<?> classRef) {
        this.code = code;
        this.label = label;
        this.childViews = childViews;
        this.classRef = classRef;
        this.childSQLs = childSQLs;
    }

    public Integer getCode() {
        return this.code;
    }

    public String[] getChildViews() {
        return this.childViews;
    }

    public String getLabel() {
        return this.label;
    }

    public Class<?> getClassRef() {
        return this.classRef;
    }

    public String[] getChildSQLs() {
        return this.childSQLs;
    }

    public static class Constants {
        private Constants() {
            // intentially blank
        }

    }

}
