package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Entity
@Table(name = "Eser_Atolye")
public class EserAtolye extends EserAtolyeSuper {

    private static final long serialVersionUID = 5262644200337308054L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public EserAtolye() {
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

}
