package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * 
*
 */
@Entity
@Table(name = "IliskilendirmeTur")
@NamedQuery(name = "IliskilendirmeTur.findEagerById", query = "SELECT t FROM IliskilendirmeTur t LEFT JOIN FETCH t.turGrubu WHERE t.id = :id")
@NamedQuery(name = "IliskilendirmeTur.findAll", query = "SELECT t FROM IliskilendirmeTur t LEFT JOIN FETCH t.turGrubu g ORDER BY t.silinmis, t.aktif DESC, g.ad")
@NamedQuery(name = "IliskilendirmeTur.findByNameAndAciklamaAndTur", query = "SELECT t FROM IliskilendirmeTur t LEFT JOIN FETCH t.turGrubu WHERE t.aktif = true AND t.silinmis = false AND t.turGrubu = :group AND (t.ad LIKE :str OR t.aciklama LIKE :str) ORDER BY t.ad")
@NamedQuery(name = "IliskilendirmeTur.findActive", query = "SELECT t FROM IliskilendirmeTur t LEFT JOIN FETCH t.turGrubu WHERE t.aktif = true AND t.silinmis = false ORDER BY t.ad")
public class IliskilendirmeTur extends AbstractEntity {

    private static final long      serialVersionUID = -8285011063348838723L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                 ad;

    @JoinColumn(name = "tur", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private IliskilendirmeTurGrubu turGrubu;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                 aciklama;

    public IliskilendirmeTur() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public IliskilendirmeTurGrubu getTurGrubu() {
        return this.turGrubu;
    }

    public void setTurGrubu(final IliskilendirmeTurGrubu turGrubu) {
        this.turGrubu = turGrubu;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.ad, MuesUtil.surroundWithParanthesis(this.turGrubu.getAd()));
    }

}
