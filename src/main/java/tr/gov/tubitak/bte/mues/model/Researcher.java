package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

/**
 *
 * <AUTHOR>
 */

@Audited
@Entity
@Table(name = "Researcher")
@NamedQuery(name = "Researcher.findEagerById", query = "SELECT r FROM Researcher r LEFT JOIN FETCH r.profession LEFT JOIN FETCH r.educationLevel WHERE r.id = :id")
@NamedQuery(name = "Researcher.findByNameAndSurname", query = "SELECT b FROM Researcher b WHERE b.aktif = true AND b.silinmis = false AND (b.ad LIKE :str OR b.surname LIKE :str) ORDER BY b.ad")
@NamedQuery(name = "Researcher.findByPassport", query = "SELECT b FROM Researcher b WHERE b.aktif = true AND b.silinmis = false AND  b.passportNo= :passport ORDER BY b.ad")
@NamedQuery(name = "Researcher.findByTCKN", query = "SELECT b FROM Researcher b WHERE b.aktif = true AND b.silinmis = false AND b.tcIdentityNo = :tc ORDER BY b.ad")
@NamedQuery(name = "Researcher.findAll", query = "SELECT r FROM Researcher r LEFT JOIN FETCH r.profession LEFT JOIN FETCH r.educationLevel ORDER BY r.silinmis, r.aktif DESC, r.ad")
@NamedQuery(name = "Researcher.findActive", query = "SELECT r FROM Researcher r WHERE r.aktif = true ORDER BY r.ad")
public class Researcher extends ExternalUserSuper implements EditPermissible {

    private static final long serialVersionUID = -5373408316258344689L;
    @Size(max = 20)
    @Column(name = "passportNo", length = 20)
    private String            passportNo;

    // native -> 1
    // foreigner -> 0
    @Column(name = "nativeForeigner")
    private Boolean           nativeForeigner;

    @Size(max = 50)
    @Column(name = "birthPlace", length = 50)
    private String            birthPlace;

    @Column(name = "birthDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              birthDate;

    @Size(max = 50)
    @Column(name = "workArea")
    private String            workArea;

    @Size(max = 50)
    @Column(name = "institution")
    private String            institution;

    @Size(max = 50)
    @Column(name = "university")
    private String            university;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "educationLevel", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Pick          educationLevel;

    @Size(max = 25)
    @Column(name = "nationality")
    private String            nationality;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "professionId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Meslek            profession;

    @Column(name = "recordDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              recordDate;

    @Size(max = 150)
    @Column(name = "description", length = 150)
    private String            aciklama;

    public Researcher() {
        // default constructor
    }

    // getters and setters ....................................................

    public String getPassportNo() {
        return this.passportNo;
    }

    public void setPassportNo(final String passportNo) {
        this.passportNo = passportNo;
    }

    public Boolean getNativeForeigner() {
        return this.nativeForeigner;
    }

    public void setNativeForeigner(final Boolean nativeForeigner) {
        this.nativeForeigner = nativeForeigner;
    }

    public String getBirthPlace() {
        return this.birthPlace;
    }

    public void setBirthPlace(final String birthPlace) {
        this.birthPlace = birthPlace;
    }

    public Date getBirthDate() {
        return this.birthDate;
    }

    public void setBirthDate(final Date birthDate) {
        this.birthDate = birthDate;
    }

    public String getWorkArea() {
        return this.workArea;
    }

    public void setWorkArea(final String workArea) {
        this.workArea = workArea;
    }

    public String getInstitution() {
        return this.institution;
    }

    public void setInstitution(final String institution) {
        this.institution = institution;
    }

    public String getUniversity() {
        return this.university;
    }

    public void setUniversity(final String university) {
        this.university = university;
    }

    public Pick getEducationLevel() {
        return this.educationLevel;
    }

    public void setEducationLevel(final Pick educationLevel) {
        this.educationLevel = educationLevel;
    }

    public String getNationality() {
        return this.nationality;
    }

    public void setNationality(final String nationality) {
        this.nationality = nationality;
    }

    public Meslek getProfession() {
        return this.profession;
    }

    public void setProfession(final Meslek profession) {
        this.profession = profession;
    }

    public Date getRecordDate() {
        return this.recordDate;
    }

    public void setRecordDate(final Date recordDate) {
        this.recordDate = recordDate;
    }

    @Override
    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.getAd(), this.getSurname());
    }

    @Override
    public Integer getUserIdentifier() {
        return this.getId();
    }

}
