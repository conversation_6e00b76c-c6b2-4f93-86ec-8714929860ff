package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "Virtual_Keyboard_Language")
@NamedQuery(name = "VirtualKeyboardLanguage.findEagerById", query = "SELECT kl FROM VirtualKeyboardLanguage kl LEFT JOIN FETCH kl.dil WHERE kl.id = :id")
@NamedQuery(name = "VirtualKeyboardLanguage.findAll", query = "SELECT kl FROM VirtualKeyboardLanguage kl LEFT JOIN FETCH kl.dil ORDER BY kl.silinmis, kl.aktif DESC")
@NamedQuery(name = "VirtualKeyboardLanguage.findByLangName", query = "SELECT kl FROM VirtualKeyboardLanguage kl LEFT JOIN FETCH kl.dil d WHERE d.aktif = true AND d.silinmis = false AND kl.aktif = true AND kl.silinmis = false AND (d.ad LIKE :str) ORDER BY d.ad, d.aciklama")
@NamedQuery(name = "VirtualKeyboardLanguage.findActive", query = "SELECT kl FROM VirtualKeyboardLanguage kl LEFT JOIN FETCH kl.dil WHERE kl.aktif = true AND kl.silinmis = false ")
@NamedNativeQuery(name = "VirtualKeyboardLanguage.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM Virtual_Keyboard_Character WHERE SILINMIS = 0 AND language_Id = :id) ")
public class VirtualKeyboardLanguage extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -5304566565823194477L;

    @JoinColumn(name = "dil_Id", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Dil               dil;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String            fotografPath;

    public VirtualKeyboardLanguage() {
    	//default constructor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Dil getDil() {
        return this.dil;
    }

    public void setDil(final Dil dil) {
        this.dil = dil;
    }

    @Override
    public String toString() {
        return this.getDil().getAd();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.getDil().getAd()).orElse("" + this.getId());
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

}
