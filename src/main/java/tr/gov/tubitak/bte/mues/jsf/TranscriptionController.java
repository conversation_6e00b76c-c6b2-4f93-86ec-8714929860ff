package tr.gov.tubitak.bte.mues.jsf;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;
import javax.json.Json;
import javax.json.JsonArrayBuilder;
import javax.json.JsonObject;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.Transcription;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardCharacter;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardLanguage;
import tr.gov.tubitak.bte.mues.session.TranscriptionFacade;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardCharacterFacade;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardLanguageFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class TranscriptionController extends AbstractController<Transcription> implements SingleFileUploadable {

	private static final long serialVersionUID = -7905785918797792754L;

	@Inject
	private VirtualKeyboardCharacterFacade virtualKeyboardCharacterFacade;

	@Inject
	private VirtualKeyboardLanguageFacade virtualKeyboardLanguageFacade;
	
	@Inject
	private TranscriptionFacade facade;

	@Inject
	private FileUploadHelper fileUploadHelper;

	@Inject
	private YaziTipiController yaziTipiController;

	private Boolean isKeyboardSet;

	private Dil yaziDiliVK;

	private String selectedDilAd;

	private List<String> photoPathList;

	private String keyboardData;

	public TranscriptionController() {
		super(Transcription.class);
	}

	@Override
	public void newRecord() {
		this.isKeyboardSet = false;
		super.newRecord();
	}

	public void uploadToTempFolder(final FileUploadEvent event) {
		this.getModel().setTranscriptionPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
	}
	
	@Override
	public void writeToPermanentFolder() {
		if (this.getModel().getTranscriptionPath() != null) {
			this.getModel().setTranscriptionPath(this.fileUploadHelper
					.writeMainCopyToFile(this.getModel().getTranscriptionPath(), FolderType.TRANSCRIPT));
		}
	}

	public void makeFileRelatedOperations(final Transcription transcription) {
		this.setModel(transcription);
		this.writeToPermanentFolder();
	}

	public List<String> buildFilePathFromModel(final Transcription model) {
        return this.fileUploadHelper.constructMainCopyImagePath(model.getTranscriptionPath(), FolderType.TRANSCRIPT);
	}

	public void handleLanguageChange(final SelectEvent<Dil> event)  {
		final Dil language = event.getObject();
		this.yaziTipiController.setDil(language);

		final List<VirtualKeyboardCharacter> letterList = this.virtualKeyboardCharacterFacade
				.findByDilId(language.getId());
		
		final List<Object[] > rawData = this.virtualKeyboardCharacterFacade.getRawCharacterData(language.getId());
		

		final JsonArrayBuilder outerJa = Json.createArrayBuilder();

		for (int i = 0; i < rawData.size(); i++) {
			String letter = letterList.get(i).getCharacter();
			String latin = letterList.get(i).getLatin();
			
			if ((i == 0) || ((i % 11) == 0)) {
				final JsonArrayBuilder ja = Json.createArrayBuilder();
				ja.add(letter).add(letter).add(1).add(0).add(true)
						.add(letterList.get(i).getLatin() != null ? latin : letter);
				
				outerJa.add(ja);
			} else {
				final JsonArrayBuilder ja = Json.createArrayBuilder();
				ja.add(letter).add(letter).add(1).add(0).add(false)
						.add(letterList.get(i).getLatin() != null ? latin : letter);
				
				outerJa.add(ja);
			}
		}
		

		if (!letterList.isEmpty()) {
			final JsonArrayBuilder ja = Json.createArrayBuilder();
			ja.add("⟵").add("8").add(8).add(9).add(true).add("");
			outerJa.add(ja);
			ja.add("__").add("32").add(32).add(9).add(false).add("");
			outerJa.add(ja);

			final JsonObject jo = Json.createObjectBuilder()
					.add("layout", Json.createArrayBuilder().add(Json.createArrayBuilder().add(outerJa))).build();
			 
			String jsonString = jo.toString();

			
			 byte[] utf8Bytes = jsonString.getBytes(StandardCharsets.UTF_8);
			 String base64Encoded = Base64.getEncoder().encodeToString(utf8Bytes);
			 PrimeFaces.current().executeScript(
					 "var decodedData =atob('"+base64Encoded + "');"
					 +" var charSet = JSON.parse("
			 		+ "        new TextDecoder('utf-8').decode("
			 		+ "            new Uint8Array([...decodedData].map(c => c.charCodeAt(0)))"
			 		+ "        )"
			 		+ "    );");
			

			this.setIsKeyboardSet(true);
		} else {
			this.setIsKeyboardSet(false);
			PrimeFaces.current().executeScript("delete charSet ;");
		}

	}

	public String fetchKeyboardPhoto( ) {
		

		if (this.getModel().getYaziDili()!= null && (this.selectedDilAd == null || !this.selectedDilAd.equals(this.getModel().getYaziDili().getAd()))) {

			this.selectedDilAd = this.getModel().getYaziDili().getAd();
			
			final List<VirtualKeyboardLanguage> keyboardList = this.virtualKeyboardLanguageFacade.filterByLangName(selectedDilAd);

			photoPathList = keyboardList.stream().map(VirtualKeyboardLanguage::getFotografPath).filter(Objects::nonNull)
					.collect(Collectors.toList());
		}

		return (photoPathList!=null && !this.photoPathList.isEmpty() ) ?  this.photoPathList.get(0):null;
	}

	// getters and setters ....................................................

	public List<Transcription> filterByEserAndTranscriptionInfo(final String query) {
        return this.facade.findByEserAndTranscriptionInfo(query);
    }
	
	@Override
	public TranscriptionFacade getFacade() {
		return this.facade;
	}

	public Boolean getIsKeyboardSet() {
		return this.isKeyboardSet;
	}

	public void setIsKeyboardSet(final Boolean isKeyboardSet) {
		this.isKeyboardSet = isKeyboardSet;
	}

	public Dil getYaziDiliVK() {
		return this.yaziDiliVK;
	}

	public void setYaziDiliVK(final Dil yaziDiliVK) {
		this.yaziDiliVK = yaziDiliVK;
	}

	public void fetchKeyboardData() {
		byte[] utf8Bytes = keyboardData.getBytes(StandardCharsets.UTF_8);
		String base64Encoded = Base64.getEncoder().encodeToString(utf8Bytes);
		
		PrimeFaces.current().ajax().addCallbackParam("keyboardData", base64Encoded);
		
	}

}