package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Izin;
import tr.gov.tubitak.bte.mues.session.IzinFacade;

@Named
@ViewScoped
public class IzinController extends AbstractController<Izin> {

    private static final long serialVersionUID = -7018757070409935193L;

    @Inject
    private IzinFacade        facade;

    public IzinController() {
        super(Izin.class);
    }

    @Override
    public IzinFacade getFacade() {
        return this.facade;
    }

}
