package tr.gov.tubitak.bte.mues.jsf;

import javax.enterprise.event.Event;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.jsf.event.SessionVariablesChangeEvent;
import tr.gov.tubitak.bte.mues.model.Pick;
import tr.gov.tubitak.bte.mues.session.PickFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

@Named
@ViewScoped
public class PickController extends AbstractController<Pick> {

    private static final long                  serialVersionUID = 5674238528961050396L;

    @Inject
    private PickFacade                         facade;

    @Inject
    private Event<SessionVariablesChangeEvent> event;

    public PickController() {
        super(Pick.class);
    }

    @Override
    public DBOperationResult create() {
        final DBOperationResult result = super.create();
        this.firePickChangeEvent();
        return result;
    }

    @Override
    public DBOperationResult update() {
        final DBOperationResult result = super.update();
        this.firePickChangeEvent();
        return result;
    }

    @Override
    public void delete() {
        super.delete();
        this.firePickChangeEvent();
    }

    @Override
    public void toggleActive(final Pick pick) {
        super.toggleActive(pick);
        this.firePickChangeEvent();
    }

    private void firePickChangeEvent() {
        final SessionVariablesChangeEvent payload = new SessionVariablesChangeEvent();
        this.event.fire(payload);
    }

    // getter .................................................................

    @Override
    public PickFacade getFacade() {
        return this.facade;
    }

}
