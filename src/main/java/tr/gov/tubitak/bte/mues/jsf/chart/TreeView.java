package tr.gov.tubitak.bte.mues.jsf.chart;

import java.io.Serializable;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.PrimeFaces;
import org.primefaces.event.NodeCollapseEvent;
import org.primefaces.event.NodeExpandEvent;
import org.primefaces.event.NodeSelectEvent;
import org.primefaces.event.timeline.TimelineSelectEvent;
import org.primefaces.model.DefaultTreeNode;
import org.primefaces.model.TreeNode;

import tr.gov.tubitak.bte.mues.jsf.UygarlikDonemController;
import tr.gov.tubitak.bte.mues.model.Cag;
import tr.gov.tubitak.bte.mues.model.Donem;
import tr.gov.tubitak.bte.mues.model.Hukumdar;
import tr.gov.tubitak.bte.mues.model.Kronoloji;
import tr.gov.tubitak.bte.mues.model.Uygarlik;
import tr.gov.tubitak.bte.mues.model.UygarlikDonem;
import tr.gov.tubitak.bte.mues.session.CagFacade;
import tr.gov.tubitak.bte.mues.session.DonemFacade;
import tr.gov.tubitak.bte.mues.session.HukumdarFacade;
import tr.gov.tubitak.bte.mues.session.KronolojiFacade;
import tr.gov.tubitak.bte.mues.session.UygarlikDonemFacade;
import tr.gov.tubitak.bte.mues.session.UygarlikFacade;

/**
 * ali.kelle
 */

@Named("treeView")
@ViewScoped
public class TreeView implements Serializable {

    private static final String              UYGARLIK         = "Uygarlik";

    private static final String              DONEM            = "Donem";

    private static final String              KRONOLOJI        = "Kronoloji";

    private static final String              NEXT_LEVEL       = "nextLevel";

    private static final long                serialVersionUID = 2804940370506179911L;

    @Inject
    private KronolojiFacade                  kronolojiFacade;

    @Inject
    private CagFacade                        cagFacade;

    @Inject
    private DonemFacade                      donemFacade;

    @Inject
    private UygarlikDonemFacade              uygarlikDonemFacade;

    @Inject
    private UygarlikFacade                   uygarlikFacade;

    @Inject
    private HukumdarFacade                   hukumdarFacade;

    private transient TreeNode<TreeNodeItem> root;

    private transient TreeNode<TreeNodeItem> selectedNode;

    @Inject
    private TimeLineView                     timelineView;

    @Inject
    private UygarlikDonemController          uygarlikDonemController;

    @PostConstruct
    public void init() {
        this.root = new DefaultTreeNode<>(new TreeNodeItem("zaman", null, null, null, null), null);
    }

    public void createTreeAndTimeLineEvent() {
        this.root = new DefaultTreeNode<>(new TreeNodeItem("zaman", null, null, null, null), null);
        this.assignKronolojiTime();

        this.timelineView.setIsTreeRender(true);
        this.timelineView.createTimelineGroup(this.root);
    }

    public void assignKronolojiTime() {
        // Kronoloji ile kronolojiye baglı cagdan tarihler çekilerek id ler üzerinden eşleştirme yapıyoruz.
        final List<Object[]> kronolojiIDListWithTermDates = this.cagFacade.findAllKronolojiAndTermDates();
        for (final Kronoloji each : this.kronolojiFacade.findAll()) {

            boolean includeFlag = false;
            for (int i = 0; i < kronolojiIDListWithTermDates.size(); i++) {
                // 0:termStart, 1:termEnd, 2:kronolojiID
                final Integer id = (Integer) kronolojiIDListWithTermDates.get(i)[2];
                final Integer startTerm = (Integer) kronolojiIDListWithTermDates.get(i)[0];
                final Integer endTerm = (Integer) kronolojiIDListWithTermDates.get(i)[1];

                if (each.getId().equals(id)) {
                    includeFlag = true;
                    final TreeNode<TreeNodeItem> kronolojiLevel = new DefaultTreeNode<>(new TreeNodeItem(KRONOLOJI, startTerm, endTerm, each, each.getId()), this.root);
                    new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), kronolojiLevel);
                }
            }

            // tarihi olmayan değerler veri tabanından gelmiyor. Bu yüzden id'ler eşleşmediğinde tarihler için null değeri atıyoruz.
            if (!includeFlag) {
                final TreeNode<TreeNodeItem> kronolojiLevel = new DefaultTreeNode<>(new TreeNodeItem(KRONOLOJI, null, null, each, each.getId()), this.root);
                new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), kronolojiLevel);
            }
        }
    }

    public void onNodeExpand(final NodeExpandEvent event) {
        if ((event.getTreeNode() != null) && event.getTreeNode().equals(this.selectedNode)) {
            return;
        }
        this.expandCurrentNode(event.getTreeNode());

    }

    public void expandCurrentNode(final TreeNode<TreeNodeItem> currentNode) {
        if (currentNode != null) {

            // alt node'ları sil
            currentNode.getChildren().clear();

            final TreeNodeItem treeNodeItem = currentNode.getData();

            // seçilen level e göre bir alttaki node ları doldur
            if (treeNodeItem.getClassType().equals(KRONOLOJI)) {

                final List<Cag> cagList = this.cagFacade.filterByNameAndKronoloji("", (Kronoloji) treeNodeItem.getObj());

                for (final Cag each : cagList) {
                    final TreeNode<TreeNodeItem> cagLevel = new DefaultTreeNode<>(new TreeNodeItem("Cag", each.getTermStart(), each.getTermEnd(), each, each.getId()), currentNode);
                    // > işareti gelmesi için boş bir node eklenmektedir.
                    new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), cagLevel);

                }
            }

            if (treeNodeItem.getClassType().equals("Cag")) {

                final Cag cag = (Cag) treeNodeItem.getObj();

                final List<Donem> donemList = this.donemFacade.filterByNameAndCag("", cag);

                // çağ ile uygarlik dönem tarihleri çekilerek id ler üzerinden eşleştirme yapıyoruz.
                final List<Object[]> donemIDListWithTermDates = this.uygarlikDonemFacade.findDonemTermDatesByCagId(cag.getId());

                for (final Donem each : donemList) {

                    boolean includeFlag = false;
                    for (int i = 0; i < donemIDListWithTermDates.size(); i++) {
                        // 0:termStart, 1:termEnd, 2:kronolojiID
                        final Integer id = (Integer) donemIDListWithTermDates.get(i)[2];
                        final Integer startTerm = (Integer) donemIDListWithTermDates.get(i)[0];
                        final Integer endTerm = (Integer) donemIDListWithTermDates.get(i)[1];

                        if (each.getId().equals(id)) {
                            includeFlag = true;
                            final TreeNode<TreeNodeItem> donemLevel = new DefaultTreeNode<>(new TreeNodeItem(DONEM, startTerm, endTerm, each, each.getId()), currentNode);
                            new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), donemLevel);
                        }
                    }

                    // tarihi olmayan değerler veri tabanından gelmiyor. Bu yüzden id'ler eşleşmediğinde tarihler için null değeri atıyoruz.
                    if (!includeFlag) {
                        final TreeNode<TreeNodeItem> donemLevel = new DefaultTreeNode<>(new TreeNodeItem(DONEM, null, null, each, each.getId()), currentNode);
                        // > işareti gelmesi için boş bir node eklenmektedir.
                        new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), donemLevel);
                    }

                }

            }

            if (treeNodeItem.getClassType().equals(DONEM)) {

                final Donem donem = (Donem) treeNodeItem.getObj();

                final List<Uygarlik> uygarlikList = this.uygarlikDonemFacade.filterByNameAndDonem("", donem);

                // donem ile uygarlik tarihleri çekilerek id ler üzerinden eşleştirme yapıyoruz.
                final List<Object[]> uygarlikIDListWithTermDates = this.hukumdarFacade.findUygarlikTermDatesByDonemId(donem.getId());

                for (final Uygarlik each : uygarlikList) {

                    boolean includeFlag = false;
                    for (int i = 0; i < uygarlikIDListWithTermDates.size(); i++) {
                        // 0:termStart, 1:termEnd, 2:kronolojiID
                        final Integer id = (Integer) uygarlikIDListWithTermDates.get(i)[2];
                        final Integer startTerm = (Integer) uygarlikIDListWithTermDates.get(i)[0];
                        final Integer endTerm = (Integer) uygarlikIDListWithTermDates.get(i)[1];

                        if (each.getId().equals(id)) {
                            includeFlag = true;
                            final TreeNode<TreeNodeItem> uygarlikLevel = new DefaultTreeNode<>(new TreeNodeItem(UYGARLIK, startTerm, endTerm, each, each.getId()), currentNode);
                            new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), uygarlikLevel);
                        }
                    }

                    // tarihi olmayan değerler veri tabanından gelmiyor. Bu yüzden id'ler eşleşmediğinde tarihler için null değeri atıyoruz.
                    if (!includeFlag) {
                        final TreeNode<TreeNodeItem> uygarlikLevel = new DefaultTreeNode<>(new TreeNodeItem(UYGARLIK, null, null, each, each.getId()), currentNode);
                        // > işareti gelmesi için boş bir node eklenmektedir.
                        new DefaultTreeNode<>(new TreeNodeItem(NEXT_LEVEL, 0, 0, null, null), uygarlikLevel);
                    }

                }

            }

            if (treeNodeItem.getClassType().equals(UYGARLIK)) {

                final List<Hukumdar> hukumdarList = this.hukumdarFacade.filterByNameAndUygarlik("", (Uygarlik) treeNodeItem.getObj());

                for (final Hukumdar each : hukumdarList) {
                    currentNode.getChildren().add(new DefaultTreeNode<>(new TreeNodeItem("Hukumdar", each.getTermStart(), each.getTermEnd(), each, each.getId()), currentNode));
                }
            }

            this.timelineView.createTimelineGroup(this.root);
            PrimeFaces.current().ajax().update(":treeTableSecimForm:timelinePanel");

        }

    }

    public void onNodeCollapse(final NodeCollapseEvent e) {
        if ((e != null) && (e.getTreeNode() != null)) {
            e.getTreeNode().getChildren().clear();
            this.timelineView.createTimelineGroup(this.root);
        }

    }

    public void onNodeSelect(final NodeSelectEvent event) {
        if ((event.getTreeNode() != null) && event.getTreeNode().equals(this.selectedNode)) {
            return;
        }
        this.selectedNode = event.getTreeNode();
        if (event.getTreeNode().isExpanded()) {
            // closes if it is open.
            event.getTreeNode().setExpanded(false);
        } else {
            // open if it is closed.
            this.expandCurrentNode(this.selectedNode);// all children are closed under this
            event.getTreeNode().setExpanded(true);
        }
    }

    public void onSelect(final TimelineSelectEvent<TreeNodeItem> e) {
        this.assignFromSelectedNode(e.getTimelineEvent().getData());
    }

    public void selectTreeNodeItem() {
        final TreeNodeItem treeNodeItem = this.selectedNode.getData();
        this.assignFromSelectedNode(treeNodeItem);
    }

    public void assignFromSelectedNode(final TreeNodeItem treeNodeItem) {

        if (treeNodeItem.getClassType().equals("Hukumdar")) {

            final Hukumdar hukumdar = this.hukumdarFacade.findById(treeNodeItem.getId());
            this.uygarlikDonemController.setHukumdar(hukumdar);
            this.uygarlikDonemController.handleHukumdarChange();

            this.uygarlikDonemController.getModel().setUygarlik(this.uygarlikFacade.findById(hukumdar.getUygarlik().getId()));

            final List<UygarlikDonem> uygarlikDonem = this.uygarlikDonemFacade.findByUygarlik(this.uygarlikDonemController.getModel().getUygarlik());
            this.uygarlikDonemController.getModel().setDonem(this.donemFacade.findById(uygarlikDonem.get(0).getDonem().getId()));

            this.uygarlikDonemController.setCag(this.cagFacade.findById(this.uygarlikDonemController.getModel().getDonem().getCag().getId()));

            this.uygarlikDonemController.setKronoloji(this.kronolojiFacade.findById(this.uygarlikDonemController.getCag().getKronoloji().getId()));

        } else if (treeNodeItem.getClassType().equals(UYGARLIK)) {

            this.uygarlikDonemController.getModel().setUygarlik(this.uygarlikFacade.findById(treeNodeItem.getId()));
            this.uygarlikDonemController.handleUygarlikChange();

            final List<UygarlikDonem> uygarlikDonem = this.uygarlikDonemFacade.findByUygarlik(this.uygarlikDonemController.getModel().getUygarlik());
            this.uygarlikDonemController.getModel().setDonem(this.donemFacade.findById(uygarlikDonem.get(0).getDonem().getId()));

            this.uygarlikDonemController.setCag(this.cagFacade.findById(this.uygarlikDonemController.getModel().getDonem().getCag().getId()));

            this.uygarlikDonemController.setKronoloji(this.kronolojiFacade.findById(this.uygarlikDonemController.getCag().getKronoloji().getId()));

        } else if (treeNodeItem.getClassType().equals(DONEM)) {

            this.uygarlikDonemController.getModel().setDonem(this.donemFacade.findById(treeNodeItem.getId()));
            this.uygarlikDonemController.handleDonemChange();

            this.uygarlikDonemController.setCag(this.cagFacade.findById(this.uygarlikDonemController.getModel().getDonem().getCag().getId()));

            this.uygarlikDonemController.setKronoloji(this.kronolojiFacade.findById(this.uygarlikDonemController.getCag().getKronoloji().getId()));

        } else if (treeNodeItem.getClassType().equals("Cag")) {

            this.uygarlikDonemController.setCag(this.cagFacade.findById(treeNodeItem.getId()));
            this.uygarlikDonemController.handleCagChange();

            this.uygarlikDonemController.setKronoloji(this.kronolojiFacade.findById(this.uygarlikDonemController.getCag().getKronoloji().getId()));

        } else if (treeNodeItem.getClassType().equals(KRONOLOJI)) {

            this.uygarlikDonemController.setKronoloji(this.kronolojiFacade.findById(treeNodeItem.getId()));
            this.uygarlikDonemController.handleKronolojiChange();

        }
        this.selectedNode = null;
        PrimeFaces.current().executeScript("PF('growlMessageWidget').renderMessage({'summary':" + "'Seçim Başarılı !'" + ", 'detail':" + "'Seçilen Değerler Aktarıldı'" + ", 'severity':'info'})");

    }

    public TreeNode<TreeNodeItem> getRoot() {
        return this.root;
    }

    public TreeNode<TreeNodeItem> getSelectedNode() {
        return this.selectedNode;
    }

    public void setSelectedNode(final TreeNode<TreeNodeItem> selectedNode) {
        // intentially left blank
    }

}