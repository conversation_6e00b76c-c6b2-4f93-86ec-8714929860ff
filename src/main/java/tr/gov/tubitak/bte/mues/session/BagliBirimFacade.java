package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.util.ApplicationSpecificQueries;

/**
 *
 * 
 */
@RequestScoped
public class BagliBirimFacade extends AbstractFacade<BagliBirim> {

    @Inject
    ApplicationSpecificQueries applicationSpecificQueries;

    public BagliBirimFacade() {
        super(BagliBirim.class);
    }

    public Integer findNumberOfBagliBirims() {
        return ((Integer) this.em.createNativeQuery("SELECT count(*) FROM BAGLI_BIRIM where AKTIF='1' and SILINMIS='0'")
                                 .getSingleResult()).intValue();
    }

    public List<BagliBirim> findByMudurluk(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("BagliBirim.findByMudurluk", BagliBirim.class).setParameter("muzeler", muzelist).getResultList();
    }
    
    public List<BagliBirim> findVisibleMuseums() {
        return this.em.createNamedQuery("BagliBirim.findVisibleMuseums", BagliBirim.class).getResultList();
    }

    public List<BagliBirim> findByMudurluk(final Integer mudurlukId) {
        return this.em.createNamedQuery("BagliBirim.findByMudurlukId", BagliBirim.class).setParameter("mudurlukId", mudurlukId).getResultList();
    }

    public void toggleActiveSelfAndDescendants(final BagliBirim bagliBirim) {
        this.getEM().createNativeQuery(this.applicationSpecificQueries.activeSelfDescendantsBagliBirim()).setParameter(1, !bagliBirim.getAktif()).setParameter(2, bagliBirim.getId()).executeUpdate();
    }

}
