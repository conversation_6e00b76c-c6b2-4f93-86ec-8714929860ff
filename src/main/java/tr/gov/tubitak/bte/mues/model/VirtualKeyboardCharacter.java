package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "Virtual_Keyboard_Character")
@NamedQuery(name = "VirtualKeyboardCharacter.findEagerById", query = "SELECT kc FROM VirtualKeyboardCharacter kc LEFT JOIN FETCH kc.virtualKeyboardLanguage WHERE kc.id = :id")
@NamedQuery(name = "VirtualKeyboardCharacter.findAll", query = "SELECT kc FROM VirtualKeyboardCharacter kc LEFT JOIN FETCH kc.virtualKeyboardLanguage ORDER BY kc.silinmis, kc.aktif")
@NamedQuery(name = "VirtualKeyboardCharacter.findActive", query = "SELECT kc FROM VirtualKeyboardCharacter kc LEFT JOIN FETCH kc.virtualKeyboardLanguage WHERE kc.aktif = true AND kc.silinmis = false ")
@NamedQuery(name = "VirtualKeyboardCharacter.findByDilId", query = "SELECT kc FROM VirtualKeyboardCharacter kc LEFT JOIN FETCH kc.virtualKeyboardLanguage kl LEFT JOIN FETCH kl.dil d WHERE d.id = :dilId AND kc.aktif = true AND kc.silinmis = false ORDER BY kc.boardOrder ")
@NamedQuery(name = "VirtualKeyboardCharacter.findByLanguageId", query = "SELECT kc FROM VirtualKeyboardCharacter kc LEFT JOIN FETCH kc.virtualKeyboardLanguage WHERE kc.virtualKeyboardLanguage.id = :languageId AND kc.aktif = true AND kc.silinmis = false ORDER BY kc.boardOrder ")
public class VirtualKeyboardCharacter extends AbstractEntity implements DeleteValidatable {

    private static final long       serialVersionUID = -5304566565823194477L;

    @JoinColumn(name = "language_Id", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private VirtualKeyboardLanguage virtualKeyboardLanguage;

    @Size(max = 50)
    @Column(name = "character", length = 50)
    private String                  character;

    @Column(name = "boardOrder")
    private Integer                 boardOrder;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                  aciklama;
    
    @Size(max = 50)
    @Column(name = "LATIN", length = 50)
    private String 					latin;

    public VirtualKeyboardCharacter() {
    	// blank constructor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Integer getBoardOrder() {
        return this.boardOrder;
    }

    public void setBoardOrder(final Integer boardOrder) {
        this.boardOrder = boardOrder;
    }

    public VirtualKeyboardLanguage getVirtualKeyboardLanguage() {
        return this.virtualKeyboardLanguage;
    }

    public void setVirtualKeyboardLanguage(final VirtualKeyboardLanguage virtualKeyboardLanguage) {
        this.virtualKeyboardLanguage = virtualKeyboardLanguage;
    }

    public String getCharacter() {
        return this.character;
    }

    public void setCharacter(final String character) {
        this.character = character;
    }

    public String getLatin() {
		return latin;
	}

	public void setLatin(final String latin) {
		this.latin = latin;
	}

	@Override
    public String toString() {
        return this.getCharacter();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.character).orElse("" + this.getId());
    }

}