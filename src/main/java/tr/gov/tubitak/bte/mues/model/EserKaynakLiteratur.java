package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Entity
@Table(name = "Eser_KaynakLiteratur")
@NamedNativeQuery(name = "EserKaynakLiteratur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM Eser_KaynakLiteratur WHERE SILINMIS = 0 AND literaturId = :id)")
public class EserKaynakLiteratur extends EserKaynakLiteraturSuper implements DeleteValidatable {

    private static final long serialVersionUID = -4924530238332485943L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public EserKaynakLiteratur() {
        // default constructor
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

}
