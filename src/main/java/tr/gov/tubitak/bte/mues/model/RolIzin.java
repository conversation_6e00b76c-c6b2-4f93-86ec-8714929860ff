package tr.gov.tubitak.bte.mues.model;

import java.io.Serializable;

import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ROL_IZIN")
public class RolIzin extends RolIzinSuper implements Serializable {

    private static final long serialVersionUID = -4370667765545873137L;

    public RolIzin() {
    }

    public RolIzin(final Integer rolId, final Integer izinId) {
        super(rolId, izinId);
    }

}
