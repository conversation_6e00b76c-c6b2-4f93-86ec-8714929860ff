package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.session.AlanKonumuFacade;
import tr.gov.tubitak.bte.mues.session.BinaFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class BinaController extends AbstractController<Bina> implements SingleFileUploadable {

    private static final long serialVersionUID = 8715924481860616705L;

    @Inject
    private BinaFacade        facade;

    @Inject
    private FileUploadHelper  fileUploadHelper;

    @Inject
    private AlanKonumuFacade  alanKonumuFacade;

    @Inject
    private SessionBean       sessionBean;

    private Mudurluk          mudurluk;

    private String            dwgFileToDelete;

    public BinaController() {
        super(Bina.class);
    }

    @PostConstruct
    private void init() {
        this.mudurluk = this.sessionBean.filterByPermission("bina:guncelle");
        this.dwgFileToDelete = "";
    }

    @Override
    public DBOperationResult update() {
        final DBOperationResult result = super.update();
        if ((this.getDwgFileToDelete() != null) && !this.getDwgFileToDelete().isBlank() && result.isSuccess()) {
            this.fileUploadHelper.deleteDwgFilePermanently(this.dwgFileToDelete);
        }
        return result;
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.mudurluk = this.sessionBean.filterByPermission("bina:guncelle");
        this.changeUnitIfOnlyOne();
    }

    @Override
    public void showDetail(final Bina item) {
        super.showDetail(item);
        this.mudurluk = this.getModel().getBagliBirim().getMudurluk();
    }

    @Override
    public void toggleActive(final Bina bina) {
        this.facade.toggleActiveSelfAndDescendants(bina);
        this.setModel(bina);
        this.getModel().setAktif(!this.getModel().getAktif());
    }

    public void handleMudurlukChange(final SelectEvent<Mudurluk> event) {
        super.logger.debug("müze mdüdürlüğü changed " + event.getSource().toString());
        this.getModel().setBagliBirim(null);
        this.changeUnitIfOnlyOne();
        this.nullVariables();
    }

    public void handleBagliBirimChange(final SelectEvent<BagliBirim> event) {
        super.logger.debug("Bağli Birim Changed" + event.getSource().toString());
        this.nullVariables();
    }

    // Sets unit by mudurluk if only one
    public void changeUnitIfOnlyOne() {
        if (this.mudurluk != null) {
            final List<BagliBirim> bagliBirimList = this.filterByNameAndMudurluk("");
            this.getModel().setBagliBirim(null);
            if (bagliBirimList.size() == 1) {
                this.getModel().setBagliBirim(bagliBirimList.iterator().next());
            }
        }
    }

    public List<BagliBirim> filterByNameAndMudurluk(final String query) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndMudurluk(query, this.mudurluk);
    }

    public List<Bina> filterByNameAndBagliBirim(final String query) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndBagliBirim(query, this.getModel().getBagliBirim());
    }

    private void nullVariables() {
        this.getModel().setKod(null);
        this.getModel().setAd(null);
    }

    public void uploadDocumentToTempFolder(final FileUploadEvent event) {
        this.getModel().setPdfPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDwgDocumentToTempFolder(final FileUploadEvent event) {
        if ((this.getModel().getDwgPath() != null) && !this.getModel().getDwgPath().isBlank()) {
            this.setDwgFileToDelete(this.getModel().getDwgPath());
        }
        this.getModel().setDwgPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void deleteDwgFileFromModel() {
        this.setDwgFileToDelete(this.getModel().getDwgPath());
        this.getModel().setDwgPath(null);
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getPdfPath() != null) {
            this.getModel().setPdfPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getPdfPath(), FolderType.OTHER));
        }

        if (this.getModel().getDwgPath() != null) {
            this.getModel().setDwgPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDwgPath(), FolderType.DWG));
        }
    }

    // getters and setters ....................................................

    @Override
    public BinaFacade getFacade() {
        return this.facade;
    }

    @Override
    public List<Bina> getItems() {
        if (this.items == null) {
            final List<Mudurluk> mudurluguListByPermissionName = this.sessionBean.fetchMudurlukListByPermission("bina:listele");

            if (mudurluguListByPermissionName != null) {
                this.items = this.getFacade().findByMudurluk(mudurluguListByPermissionName);
            }
        }
        return this.items;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public String getDwgFileToDelete() {
        return this.dwgFileToDelete;
    }

    public void setDwgFileToDelete(final String dwgFile) {
        this.dwgFileToDelete = dwgFile;
    }

}
