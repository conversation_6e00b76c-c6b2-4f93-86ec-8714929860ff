package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.constraint.Email;
import tr.gov.tubitak.bte.mues.search.SearchConstants;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Entity
@Table(name = "TUZEL_KISI")
@NamedQuery(name = "TuzelKisi.findEagerById", query = "SELECT t FROM TuzelKisi t LEFT JOIN FETCH t.il LEFT JOIN FETCH t.ilce WHERE t.id = :id")
@NamedQuery(name = "TuzelKisi.findAll", query = "SELECT t FROM TuzelKisi t LEFT JOIN FETCH t.il LEFT JOIN FETCH t.ilce ORDER BY t.silinmis, t.aktif DESC, t.ticariUnvan")
@NamedQuery(name = "TuzelKisi.findActive", query = "SELECT t FROM TuzelKisi t LEFT JOIN FETCH t.il LEFT JOIN FETCH t.ilce WHERE t.aktif = true AND t.silinmis = false ORDER BY t.ticariUnvan")
@NamedQuery(name = "TuzelKisi.filterByFullNameAndAciklamaPreventDuplicate", query = "SELECT t FROM TuzelKisi t LEFT JOIN FETCH t.il LEFT JOIN FETCH t.ilce WHERE t.id NOT IN :ids AND t.aktif = true AND t.silinmis = false AND t.ticariUnvan LIKE :str OR t.aciklama LIKE :str ORDER BY t.ticariUnvan")
@NamedNativeQuery(name = "TuzelKisi.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_HAREKET WHERE SILINMIS = 0 AND TESLIM_EDEN_TUZEL_KISI_ID = :id) + "
                                                                   + "(SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL_GOREV WHERE SILINMIS = 0 AND TUZEL_KISI_ID = :id) + "
                                                                   + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_Movement WHERE SILINMIS = 0 AND legalEntityId = :id)")
public class TuzelKisi extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -231469881704341664L;

    @Size(max = 11)
    @Column(name = "VERGI_KIMLIK_NO", length = 11)
    private String            vergiKimlikNo;

    @Size(max = 150)
    @Column(name = "VERGI_DAIRESI", length = 150)
    private String            vergiDairesi;

    @Size(max = 150)
    @Column(name = "TICARI_UNVAN", length = 150)
    private String            ticariUnvan;

    @Size(max = 24)
    @Column(name = "IBAN_NO", length = 24)
    private String            ibanNo;

    @Size(max = 150)
    @Column(name = "ADRES", length = 150)
    private String            adres;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_IS", length = 25)
    private String            telefonIs;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_FAKS", length = 25)
    private String            faks;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_CEP", length = 25)
    private String            telefonCep;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_CEP2", length = 25)
    private String            telefonCep2;

    @Email
    @Size(max = 50)
    @Column(name = "EPOSTA_KURUMSAL", length = 50)
    private String            epostaKurumsal;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_YURT_DISI", length = 25)
    private String            telefonYurtDisi;

    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                il;

    @JoinColumn(name = "ILCE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Ilce              ilce;

    public TuzelKisi() {
    }

    // getters and setters ....................................................

    public String getVergiKimlikNo() {
        return this.vergiKimlikNo;
    }

    public void setVergiKimlikNo(final String vergiKimlikNo) {
        this.vergiKimlikNo = vergiKimlikNo;
    }

    public String getVergiDairesi() {
        return this.vergiDairesi;
    }

    public void setVergiDairesi(final String vergiDairesi) {
        this.vergiDairesi = vergiDairesi;
    }

    public String getTicariUnvan() {
        return this.ticariUnvan;
    }

    public void setTicariUnvan(final String ticariUnvan) {
        this.ticariUnvan = ticariUnvan;
    }

    public String getIbanNo() {
        return this.ibanNo;
    }

    public void setIbanNo(final String ibanNo) {
        this.ibanNo = ibanNo;
    }

    public String getAdres() {
        return this.adres;
    }

    public void setAdres(final String adres) {
        this.adres = adres;
    }

    public String getTelefonIs() {
        return this.telefonIs;
    }

    public void setTelefonIs(final String telefonIs) {
        this.telefonIs = telefonIs;
    }

    public String getFaks() {
        return this.faks;
    }

    public void setFaks(final String faks) {
        this.faks = faks;
    }

    public String getTelefonCep() {
        return this.telefonCep;
    }

    public void setTelefonCep(final String telefonCep) {
        this.telefonCep = telefonCep;
    }

    public String getTelefonCep2() {
        return this.telefonCep2;
    }

    public void setTelefonCep2(final String telefonCep2) {
        this.telefonCep2 = telefonCep2;
    }

    public String getEpostaKurumsal() {
        return this.epostaKurumsal;
    }

    public void setEpostaKurumsal(final String epostaKurumsal) {
        if (epostaKurumsal != null) {
            this.epostaKurumsal = epostaKurumsal.toLowerCase();
        }
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getTelefonYurtDisi() {
        return this.telefonYurtDisi;
    }

    public void setTelefonYurtDisi(final String telefonYurtDisi) {
        this.telefonYurtDisi = telefonYurtDisi;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public Ilce getIlce() {
        return this.ilce;
    }

    public void setIlce(final Ilce ilce) {
        this.ilce = ilce;
    }

    @Override
    public String toString() {
        return Joiner.on(" ").skipNulls().join(this.ticariUnvan, MuesUtil.surroundWithParanthesis(this.vergiKimlikNo));
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ticariUnvan).orElse(SearchConstants.BLANKSTRING);
    }

}
