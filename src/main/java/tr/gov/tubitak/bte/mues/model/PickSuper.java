package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Audited
@MappedSuperclass
@NamedQuery(name = "Pick.findEagerById", query = "SELECT p FROM Pick p LEFT JOIN FETCH p.grup WHERE p.id = :id")
@NamedQuery(name = "Pick.findAll", query = "SELECT p FROM Pick p LEFT JOIN FETCH p.grup g ORDER BY p.silinmis, p.aktif, g.ad, p.grup.kod, p.rank")
@NamedQuery(name = "Pick.findActive", query = "SELECT p FROM Pick p LEFT JOIN FETCH p.grup g WHERE p.aktif = true AND p.silinmis = false ORDER BY g.kod, p.rank")

public class PickSuper extends AbstractEntity {

    private static final long serialVersionUID = 2920113423714474511L;

    @Size(max = 80)
    @Column(name = "AD", length = 80)
    private String            ad;

    @Column(name = "ACIKLAMA")
    private String            aciklama;

    @Column(name = "rank")
    private Integer           rank;

    public PickSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public Integer getRank() {
        return this.rank;
    }

    public void setRank(final Integer rank) {
        this.rank = rank;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad + MuesUtil.surroundWithParanthesis(this.aciklama);
    }

}
