package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.ValidAlan;
import tr.gov.tubitak.bte.mues.constraint.validator.AlanGroup;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ALAN")
@NamedNativeQuery(name = "Alan.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ALAN_KONUMU WHERE SILINMIS = 0 AND ALAN_ID = :id)")

@ValidAlan(groups = AlanGroup.class)
public class Alan extends AlanSuper {

    private static final long serialVersionUID = 311096275077294371L;

    public Alan() {
        // blank constructor
    }

    // getters and setters ....................................................

}
