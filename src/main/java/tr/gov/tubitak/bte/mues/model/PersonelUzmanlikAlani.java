package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "PersonelUzmanlik<PERSON><PERSON>")
@NamedQuery(name = "PersonelUzmanlikAlani.findEagerById", query = "SELECT p FROM PersonelUzmanlikAlani p LEFT JOIN FETCH p.personel LEFT JOIN FETCH p.uzmanlikAlani WHERE p.id = :id")
@NamedQuery(name = "PersonelUzmanlikAlani.findByPersonelId", query = "SELECT p FROM PersonelUzmanlikAlani p LEFT JOIN FETCH p.personel LEFT JOIN FETCH p.uzmanlikAlani WHERE p.personel.id = :id")
@NamedQuery(name = "PersonelUzmanlikAlani.findAll", query = "SELECT p FROM PersonelUzmanlikAlani p LEFT JOIN FETCH p.personel LEFT JOIN FETCH p.uzmanlikAlani")
@NamedQuery(name = "PersonelUzmanlikAlani.findActive", query = "SELECT p FROM PersonelUzmanlikAlani p WHERE p.aktif = true AND p.silinmis = false")
public class PersonelUzmanlikAlani extends AbstractEntity {

    private static final long serialVersionUID = 1035253373119984286L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Personel          personel;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "UZMANLIK_ALANI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private UzmanlikAlani     uzmanlikAlani;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public PersonelUzmanlikAlani() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    public Personel getPersonel() {
        return this.personel;
    }

    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public UzmanlikAlani getUzmanlikAlani() {
        return this.uzmanlikAlani;
    }

    public void setUzmanlikAlani(final UzmanlikAlani uzmanlikAlani) {
        this.uzmanlikAlani = uzmanlikAlani;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return "{Personel: " + this.getPersonel().getAd() + ", Uzmanlık Alanı: " + this.getUzmanlikAlani().getAd() + "}";
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.getPersonel().getAd(), MuesUtil.surroundWithParanthesis(this.getUzmanlikAlani().getAd()));
    }

}
