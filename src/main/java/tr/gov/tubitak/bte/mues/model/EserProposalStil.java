package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "EP_Eser_Style")

public class EserProposalStil extends EserStilSuper {

    private static final long serialVersionUID = 5262644233337308054L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalStil() {
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
