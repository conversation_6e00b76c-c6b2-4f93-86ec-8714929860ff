package tr.gov.tubitak.bte.mues.search;

import java.util.HashMap;
import java.util.Map;
import java.util.ResourceBundle;

import javax.faces.context.FacesContext;
import javax.inject.Inject;

public enum LogicalOperatorEnum {

    /** VE operatörü. */
    AND(1, 2),

    /** VEYA operatörü. */
    OR(2, 2),

    /** DEĞİL operatörü. */
    NOT(3, 1),

    /** Operatör yok. */
    NULL(0, 0);

    private static Map<Integer, LogicalOperatorEnum> map = new HashMap<>();

    static {
        for (final LogicalOperatorEnum each : LogicalOperatorEnum.values()) {
            map.put(each.getCode(), each);
        }
    }

    private String         label;

    private int            minElementCount;

    private final int      code;

    @Inject
    private ResourceBundle bundle;

    private LogicalOperatorEnum(final int code, final int minElementCount) {
        this.code = code;
        this.minElementCount = minElementCount;
        this.label = "search.logicalOperators." + this.name();
    }

    /**
     * Kodu verilen mantıksal operatörün <code>enum</code> de<PERSON><PERSON>ni döner.
     *
     * @param code mantıksal operatör kodu
     * @return mantıksal operatör
     */
    public static LogicalOperatorEnum parse(final int code) {
        return map.get(code);
    }

    // getters ................................................................

    public String getLabel() {
        return this.label;
    }

    public String getText() {
        return SearchConstants.STYLE_TAG + this.getBundle().getString(this.label) + SearchConstants.CLOSE_STYLE_TAG;
    }

    public String getSql() {
        return this.name();
    }

    public int getMinCriteriaCount() {
        return this.minElementCount;
    }

    public int getCode() {
        return this.code;
    }

    private ResourceBundle getBundle() {
        if (this.bundle == null) {
            final FacesContext ctx = FacesContext.getCurrentInstance();
            if (ctx != null) {
                this.bundle = ResourceBundle.getBundle("/labels", ctx.getViewRoot().getLocale());
            }
        }
        return this.bundle;
    }

}
