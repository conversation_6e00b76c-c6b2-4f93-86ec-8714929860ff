package tr.gov.tubitak.bte.mues.model;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public enum MailEnum {

    DEFAULT(1, "Test Eposta", "<PERSON><PERSON><PERSON><PERSON><PERSON>, \nOtomatik olarak üretilen bu eposta test amaçlıdır. Lütfen dikkate almayınız. \nTeşekkürler..."),

    ARTIFACT_CREATE_APPROVED(2, "Yeni Eser Girişi Onaylandı", "<PERSON><PERSON><PERSON> ilgililer, \n{0} tarafından girilen yeni eser \"{1}\", {2} tarafından onaylanarak sisteme dahil edilmiştir. \nBilgilerinize..."),

    ARTIFACT_CREATE_REJECTED(3, "Yeni Eser Girişi Reddedildi", "<PERSON><PERSON><PERSON> ilgililer, \n{0} tarafından girilen yeni eser \"{1}\", {2} tarafından reddedilmiştir. \nBilgilerinize..."),

    ARTIFACT_UPDATE_APPROVED(4, "Eser Güncelleme Onaylandı",
            "<PERSON><PERSON>ymetli alıcı<PERSON>, \n{0} tarafından yeniden düzenlenen eser \"{1}\", {2} tarafından onaylanarak sistemde güncellenmiştir. \nBilgilerinize..."),

    ARTIFACT_UPDATE_REJECTED(5, "Eser Güncelleme Reddedildi", "Sayın ilgililer, \n{0} tarafından yeniden düzenlenen eser \"{1}\", {2} tarafından reddedilmiştir. \nBilgilerinize..."),

    ARTIFACT_JOIN_APPROVED(6, "Eser Birleştirme Onaylandı", "Sayın ilgililer, \n{0} tarafından birleştirilen eser \"{1}\", {2} tarafından onaylanarak sistemde güncellenmiştir. \nBilgilerinize..."),

    ARTIFACT_JOIN_REJECTED(7, "Eser Birleştirme Reddedildi", "Sayın ilgililer, \n{0} tarafından birleştirilen eser \"{1}\", {2} tarafından reddedilmiştir. \nBilgilerinize..."),

    ARTIFACT_GROUP_APPROVED(8, "Eser İlişkilendirme Onaylandı", "Sayın ilgililer, \n{0} tarafından ilişkilendirilen eser \"{1}\", {2} onaylanarak sistemde güncellenmiştir. \nBilgilerinize..."),

    ARTIFACT_GROUP_REJECTED(9, "Eser İlişkilendirme Reddedildi", "Sayın ilgililer, \n{0} tarafından yapılan eser ilişkilendirmesi \"{1}\", {2} tarafından reddedilmiştir. \nBilgilerinize..."),

    PASSWORD_RESET(10, "Şifre Değişikliği", "Sayın {0}, \n{1} şifreniz sıfırlanmıştır. \nYeni şifreniz: {2}\nBilginize..."),

    SECURITY_IMPLEMENTATION_LAB(11, "Güvenlik Uygulamasındaki Eser Laboratuvara Alındı",
            "Kıymetli alıcılar, Güvenlik uygulaması kapsamında bulunan \n{0} Kültür Varlığı ID'sine sahip eser(ler), {1} laboratuvarında işleme alınmıştır. \nBilgilerinize..."),

    SECURITY_IMPLEMENTATION_LIABILITY(12, "Güvenlik Uygulamasındaki Eser Zimmet Değişikliği",
            "Kıymetli alıcılar, Güvenlik uygulaması kapsamında bulunan \n{0} Kültür Varlığı ID'sine sahip eser(ler) için zimmet değişikliği olmuştur. \nBilgilerinize..."),

    SECURITY_IMPLEMENTATION_TRANSFER(13, "Güvenlik Uygulamasındaki Eserlerin Müzeler Arası Devri",
            "Kıymetli alıcılar, Güvenlik uygulaması kapsamında bulunan\n {0} Kültür Varlığı ID'sine sahip eser(ler) için müzeler arası devir yapılmıştır. \nBilgilerinize...");

    private static final Logger logger = LoggerFactory.getLogger(MailEnum.class);

    private final Integer       code;

    private final String        title;

    private final String        body;

    private MailEnum(final Integer code, final String label, final String icon) {
        this.code = code;
        this.title = label;
        this.body = icon;
    }

    public static MailEnum parse(final Integer number) {
        for (final MailEnum each : MailEnum.values()) {
            if (number.equals(each.getCode())) {
                return each;
            }
        }
        logger.error("[Mail.parse] : Hata : {} ile eşleşen posta (Mail) tipi bulunamadi.", number);

        return MailEnum.DEFAULT;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getTitle() {
        return this.title;
    }

    public String getBody() {
        return this.body;
    }

}
