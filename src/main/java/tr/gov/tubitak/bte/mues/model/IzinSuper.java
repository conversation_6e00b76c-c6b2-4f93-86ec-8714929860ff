package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "Izin.findEagerById", query = "SELECT i FROM Izin i WHERE i.id = :id")
@NamedQuery(name = "Izin.findAll", query = "SELECT i FROM Izin i ORDER BY i.silinmis, i.aktif DESC, i.ad")
@NamedQuery(name = "Izin.findAllWithRols", query = "SELECT i FROM Izin i LEFT JOIN FETCH i.rolIzinCollection ri LEFT JOIN FETCH ri.rol ORDER BY i.silinmis, i.aktif DESC, i.ad")
@NamedQuery(name = "Izin.findActive", query = "SELECT i FROM Izin i LEFT JOIN FETCH i.menu WHERE i.aktif = true AND i.silinmis = false ORDER BY i.ad")
@NamedNativeQuery(name = "Izin.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ROL_IZIN WHERE SILINMIS = 0 AND IZIN_ID = :id)")
public class IzinSuper extends AbstractEntity implements DeleteValidatable {

    private static final long   serialVersionUID = 6491194072935286923L;

    @Size(max = 100)
    @Column(name = "AD", length = 100)
    private String              ad;

    @Size(max = 100)
    @Column(name = "KOD", length = 100)
    private String              kod;

    @Size(max = 300)
    @Column(name = "ACIKLAMA", length = 300)
    private String              aciklama;

    @OneToMany(mappedBy = "izin")
    private Collection<RolIzin> rolIzinCollection;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "menuId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Menu                menu;

    public IzinSuper() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<RolIzin> getRolIzinCollection() {
        return this.rolIzinCollection;
    }

    public void setRolIzinCollection(final Collection<RolIzin> rolIzinCollection) {
        this.rolIzinCollection = rolIzinCollection;
    }

    public Menu getMenu() {
        return this.menu;
    }

    public void setMenu(final Menu menu) {
        this.menu = menu;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }
}
