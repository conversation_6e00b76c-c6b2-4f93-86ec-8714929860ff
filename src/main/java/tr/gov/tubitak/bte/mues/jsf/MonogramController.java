package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import tr.gov.tubitak.bte.mues.model.Monogram;
import tr.gov.tubitak.bte.mues.session.MonogramFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class MonogramController extends AbstractController<Monogram> implements SingleFileUploadable {

    private static final long       serialVersionUID = -8962274757448225195L;

    @Inject
    private FileUploadHelper        fileUploadHelper;

    @Inject
    private MuesParameters          muesParameters;

    @Inject
    private MonogramFacade           facade;

    public MonogramController() {
        super(Monogram.class);
    }

    public List<Monogram> filterByNameAndAciklama(final String query) {
        return this.facade.filterByNameAndAciklama(query);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.muesParameters.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
    }

    // getters and setters ....................................................

    @Override
    public MonogramFacade getFacade() {
        return this.facade;
    }

}
