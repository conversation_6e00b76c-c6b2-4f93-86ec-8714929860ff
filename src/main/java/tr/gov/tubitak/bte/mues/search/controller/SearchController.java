package tr.gov.tubitak.bte.mues.search.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.event.ValueChangeEvent;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.omnifaces.cdi.ViewScoped;
import org.omnifaces.util.Faces;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.MetaDataController;
import tr.gov.tubitak.bte.mues.jsf.VirtualKeyboardController;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyGroupLoadSolrDataModel;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazySolrDataModel;
import tr.gov.tubitak.bte.mues.search.AbstractSearchController;
import tr.gov.tubitak.bte.mues.search.ComparisonOperatorEnum;
import tr.gov.tubitak.bte.mues.search.CompoundCriterion;
import tr.gov.tubitak.bte.mues.search.CriterionFactory;
import tr.gov.tubitak.bte.mues.search.CriterionModel;
import tr.gov.tubitak.bte.mues.search.ICriterion;
import tr.gov.tubitak.bte.mues.search.LogicalOperatorEnum;
import tr.gov.tubitak.bte.mues.search.SearchConstants;
import tr.gov.tubitak.bte.mues.search.SearchQualifier;
import tr.gov.tubitak.bte.mues.service.IndexingService;
import tr.gov.tubitak.bte.mues.service.SolrLockService;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;
import tr.gov.tubitak.bte.mues.util.Encryptor;
import tr.gov.tubitak.bte.mues.util.IncidentTypeEnum;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.PropertyUtil;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

@Named
@ViewScoped
@SearchQualifier
public class SearchController extends AbstractSearchController<ArtifactsSolrModel> {

    private static final long serialVersionUID = -964651417540274239L;

    private static final Logger logger = LoggerFactory.getLogger(SearchController.class);

    @Inject
    private VirtualKeyboardController virtualKeyboardController;

    @Inject
    private MetaDataController metaDataController;

    @Inject
    private SolrLockService           solrLockService;

    @Inject
    private IndexingService           indexingService;

    @Inject
    private PropertyUtil              propertyUtil;

    private boolean                   benimEserler;

    private String                    qrcode;

    private Integer                   artifactId;

    private Boolean                   renderSayi       = false;

    private Boolean                   renderId         = true;

    public SearchController() {
        super(ArtifactsSolrModel.class, "eser", SolrEnum.ENVANTER);
    }

    @Override
    @PostConstruct
    public void init() {
        super.init();
    }

    public void valueChanged(final ValueChangeEvent valueChangeEvent) {
        this.artifactId = (Integer) valueChangeEvent.getNewValue();
    }

    public ICriterion addQueryMyArtifacts(final ICriterion criterion) {

        if (!this.benimEserler) {
            return criterion;
        }
        final Metadata metadata = this.getSolrSearcher().getMetaDataMap().get("eserYaratmaKullaniciId");

        final CriterionModel id = new CriterionModel(metadata, this.getSessionBean().getCurrentUser().getId().toString());
        id.setCondition(ComparisonOperatorEnum.EQUALS);

        final ICriterion criterion1 = CriterionFactory.createCriterion(id);

        final CompoundCriterion compoundCriterion0 = new CompoundCriterion();

        compoundCriterion0.setChildren(new ArrayList<>());
        compoundCriterion0.setLogicalOperator(LogicalOperatorEnum.AND);

        compoundCriterion0.getChildren().add(criterion);
        compoundCriterion0.getChildren().add(criterion1);

        return compoundCriterion0;

    }

    @Override
    public void handleMetadataSelectDetailedSearch(final SelectEvent<Metadata> event) {

        final Metadata metadata = event.getObject();
        // Transkripsiyon seçildiği zaman sanal klavye render olur.
        if (metadata.getIndexFaceName().equals("Eser Transkripsiyon Metin Çevirisi")) {
            this.virtualKeyboardController.setIsTranscriptionSelected(true);
        } else {
            this.changeKeyboardVisibility();
        }

        super.handleMetadataSelectDetailedSearch(event);

    }

    @Override
    public void addCriterion() {
        // arama kriteri eklendikten sonra klavye kaybolur
        this.changeKeyboardVisibility();

        super.addCriterion();
    }

    public void changeKeyboardVisibility() {
        this.virtualKeyboardController.setIsTranscriptionSelected(false);
        this.virtualKeyboardController.setIsKeyboardActive(false);
        this.virtualKeyboardController.setIsKeyboardSet(false);
    }

    public void checkPermissibleArtifact(final ComponentSystemEvent event) {

        if (logger.isInfoEnabled()) {
            logger.info("checkPermissibleArtifact{}", event);
        }
        if (this.artifactId == null) {
            return;
        }

        try {
            final CriterionModel id = new CriterionModel(this.getSolrSearcher().getMetaDataMap().get("id"), this.artifactId + "");
            id.setCondition(ComparisonOperatorEnum.EQUALS);

            this.getSolrSearcher().setSolrQuery(new SolrQuery(this.addQuerySearchPermision(CriterionFactory.createCriterion(id)).getSql()));

            if (this.getSolrSearcher().makeSearch().getResults().getNumFound() < 1) {
                Faces.redirect("hata/403.xhtml?e=" + this.artifactId);
            }

        } catch (SolrServerException | IOException e) {
            logger.error("Sorguda bir hata meydana geldi. {}", e.getMessage());
            MuesUtil.showMessage("Sorguda bir hata meydana geldi. ", FacesMessage.SEVERITY_ERROR);
        }
    }

    @Override
    public ICriterion addQuerySearchPermision(final ICriterion criterion) {

        return this.addQuerySearchPermision(criterion, "sorgu:eserEnvanter", ApplicationType.ENVANTER, "muzeMudurluguAd");

    }

    public void makeGroupQueryInquery() {

        this.setRenderSayi(true);
        this.setRenderId(false);
        final SolrQuery solrQuery;

        if (this.getCriteriaList().isEmpty()) {

            solrQuery = new SolrQuery(SearchConstants.ALL_QUERY);

        } else {

            solrQuery = this.composeDetailedSearchQuery();

        }

        solrQuery.set("fl", "id"); // tüm alanları getirmemesi için id alanı set edilmiştir.
        solrQuery.setFacet(true);

        final List<Metadata> pivotFieldList = this.getSelectedList();

        final List<String> pivotFieldStringList = new ArrayList<>();
        for (final Metadata metadata : pivotFieldList) {
            pivotFieldStringList.add(metadata.getName());
        }

        final String facetPivotFields = pivotFieldStringList.stream().collect(Collectors.joining(","));

        solrQuery.addFacetPivotField(facetPivotFields);

        this.getSolrSearcher().setSolrQuery(solrQuery);

        this.setSearchResultsVisible(true);
        this.resetTable();

        this.setLazyDataModel(new LazyGroupLoadSolrDataModel<>(this.getSolrSearcher(), pivotFieldList, ArtifactsSolrModel.class));

    }

    /**
     * Solr more Lik this request handler.
     */
    public void makeMorelLikeThisInquery() {

        final List<Metadata> metadataList = this.metaDataController.getMetadataDualList().getTarget();

        if (metadataList.isEmpty()) {
            MuesUtil.showMessage(this.getSearchUtil().getSelectCriterionWarningText(), FacesMessage.SEVERITY_WARN);
            return;
        }
        final SolrQuery query = new SolrQuery("id:" + this.getSelectionList().iterator().next().getId());
        query.set("mlt", "true");
        query.setRequestHandler("/mlt");

        final String facetPivotFields = metadataList.stream().map(Metadata::getName).collect(Collectors.joining(","));

        query.set("mlt.qf", facetPivotFields);
        query.set("mlt.fl", facetPivotFields);

        // Converting List to Array of strings

        this.getSolrSearcher().setSolrQuery(query);

        this.setChildSearchResultsVisible(true);

        this.setChildSearchLazyDataModel((new LazySolrDataModel<>(this.getSolrSearcher(), ArtifactsSolrModel.class, this)));

    }

    /***
     * Search image based similarity by selected row on dataTable
     */
    public void searchImageBasedSimilartyBySelection() {
        if (!this.getSelectionList().isEmpty()) {
            this.getImageSearchUtil().setFile(this.getSelectionList().iterator().next().getEserTanimlayiciFotografPath());
            this.resetTable();
            this.searchImageBasedSimilarity();
        }
    }

    @Override
    public void resetSearchFields() {
        this.removeUploadedPhoto();
        super.resetSearchFields();
    }

    public void indexToSolr() {
        if (Boolean.TRUE.equals(this.solrLockService.getGlobalSolrSchedulerLock().get(SolrEnum.ENVANTER))) {
            final String anotherOnProgress = this.propertyUtil.getMessage("indexing.anotherOnProgress", SolrEnum.ENVANTER.name());
            PrimeFaces.current().executeScript("PF('growlMessageWidget').renderMessage({'summary':'" + anotherOnProgress + "', 'severity':'warning'})");
        } else {
            final String startedOnProgressMessage = this.propertyUtil.getMessage("indexing.startedOnProgress", SolrEnum.ENVANTER.name());
            this.indexingService.indexToSolrAsync(this.searcherIndexingUtil, this.solrLastIndexTimesFacade, SolrEnum.ENVANTER, IncidentTypeEnum.ESER_TUMBILGILER.name());
            PrimeFaces.current().executeScript("PF('growlMessageWidget').renderMessage({'summary':'" + startedOnProgressMessage + "', 'severity':'info'})");
        }
    }

    /***
     * Removes the uploded photograph
     */
    public void removeUploadedPhoto() {
        this.getImageSearchUtil().removePhoto();
        this.getImageSearchUtil().getModel().setFotografPath(null);
    }

    public String getQrcode() {
        return this.qrcode;
    }

    public void setQrcode(final String id) {
        if (id != null) {
            final Optional<String> tmp = Encryptor.encrypt(String.valueOf(id));
            if (tmp.isPresent()) {
                this.qrcode = tmp.get();
            }
        }

    }

    public boolean isBenimEserler() {
        return this.benimEserler;
    }

    public void setBenimEserler(final boolean benimEserler) {
        this.benimEserler = benimEserler;
    }

    @Override
    public Boolean getRenderSayi() {
        return this.renderSayi;
    }

    @Override
    public void setRenderSayi(final Boolean renderSayi) {
        this.renderSayi = renderSayi;
    }

    public Boolean getRenderId() {
        return this.renderId;
    }

    public void setRenderId(final Boolean renderId) {
        this.renderId = renderId;
    }

}
