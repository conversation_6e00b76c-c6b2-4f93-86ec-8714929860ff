package tr.gov.tubitak.bte.mues.model;

import java.util.Date;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

@MappedSuperclass
public class CommentSuper extends AbstractEntity {

    private static final long serialVersionUID = -376151075109953794L;

    @Size(max = 250)
    @Column(name = "text", length = 250)
    private String            text;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "commenter")
    private Kullanici         by;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dateCommented")
    private Date              when;

    public CommentSuper() {
        // blank constructor
    }

    // getters and setters ............................................

    public String getText() {
        return this.text;
    }

    public void setText(final String text) {
        this.text = text;
    }

    public Kullanici getBy() {
        return this.by;
    }

    public void setBy(final Kullanici by) {
        this.by = by;
    }

    public Date getWhen() {
        return this.when;
    }

    public void setWhen(final Date when) {
        this.when = when;
    }

    @Override
    public String toString() {
        return this.text + " - by " + this.by;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.text).orElse("" + this.getId());
    }

}
