package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.List;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.primefaces.PrimeFaces;

import tr.gov.tubitak.bte.mues.constraint.ValidAlanKonumu;
import tr.gov.tubitak.bte.mues.jsf.AlanKonumuController;
import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.AlanKonumu;

public class AlanKonumuValidator implements ConstraintValidator<ValidAlanKonumu, AlanKonumu> {
	
	@Inject
    protected EntityManager em;
	
	@Inject
	private AlanKonumuController alanKonumuController;

	@Override
	public boolean isValid(final AlanKonumu alanKonumu, final ConstraintValidatorContext context) {
		boolean result = true;
		final Alan alan = this.alanKonumuController.getAlan();
		
		if(alanKonumu.getKod() != null) {
			final List resultList = this.em.createNativeQuery("SELECT KOD FROM ALAN_KONUMU WHERE KOD = :kod AND ALAN_ID = :alanId")
                    .setParameter("kod", alanKonumu.getKod())
                    .setParameter("alanId", alan.getId())
                    .getResultList();
			
			if((resultList != null && !resultList.isEmpty()) && 
					(alanKonumuController.getModel().getId() == null ||
					(alanKonumuController.getModel().getId() != null && 
					!alanKonumuController.getModel().getKod().equals(alanKonumu.getKod())))) {
				this.raiseFlag("Aynı alan konumu kodu sistemde kayıtlıdır, lütfen kontrol ediniz.", context);
                this.addCssErrorClassToComponent("editorDialog:formEditor:alanKonumuKodu");
                result = false;
			}
		}
		
		return result;
	}
	
	private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

    private void addCssErrorClassToComponent(final String componentId) {
        PrimeFaces.current().executeScript("$(PrimeFaces.escapeClientId(\"" + componentId + "\")).addClass('required-input-field error')");
    }
    
}
