
package tr.gov.tubitak.bte.mues.search;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Kriter tipleri.
 */
public enum CriterionEnum {

    /** Null kriter. */
    NULL(-1, ComparisonOperatorEnum.NOP),

    /** Birleşik kriter. */
    COMPOUND(0),

    /** Metin kriteri. */
    TEXT(1, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS, ComparisonOperatorEnum.CONTAINS, ComparisonOperatorEnum.NOT_CONTAINS, ComparisonOperatorEnum.ISNULL,
            ComparisonOperatorEnum.NOTNULL),

    /** Sayı kriteri. */
    // NUMBER(2, ComparisonOperatorEnum.GREATER_THAN, ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.EQUALS,
    // ComparisonOperatorEnum.BETWEEN),
    NUMBER(2, ComparisonOperatorEnum.GREATER_THAN, ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS, ComparisonOperatorEnum.ISNULL),

    /** Tarih kriteri. */
    // DATE(3, ComparisonOperatorEnum.GREATER_THAN, ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.BETWEEN,
    // ComparisonOperatorEnum.ISNULL),
    DATE(3, ComparisonOperatorEnum.GREATER_THAN, ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.BETWEEN, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS,
            ComparisonOperatorEnum.ISNULL),

    /** Metin Arama kriteri. */
    FREETEXT(4, ComparisonOperatorEnum.CONTAINS, ComparisonOperatorEnum.NOT_CONTAINS, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS, ComparisonOperatorEnum.ISNULL),

    /** Çoklu liste kriteri. */
    MULTI_LIST(5, ComparisonOperatorEnum.CONTAINS, ComparisonOperatorEnum.NOT_CONTAINS, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS, ComparisonOperatorEnum.NOTNULL, ComparisonOperatorEnum.ISNULL),

    /** Mantıksal kriter. */
    BOOLEAN(6, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS, ComparisonOperatorEnum.ISNULL),

    /** coklu numeric kriter. */
    MULTI_NUMBERIC(7, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS, ComparisonOperatorEnum.CONTAINS_IN, ComparisonOperatorEnum.NOT_CONTAINS_IN, ComparisonOperatorEnum.GREATER_THAN,
            ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.ISNULL),

    /** Çoklu Tarih kriteri. */
    // MULTI_DATE(8, ComparisonOperatorEnum.GREATER_THAN, ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.EQUALS,
    // ComparisonOperatorEnum.BETWEEN,
    // ComparisonOperatorEnum.ISNULL),
    MULTI_DATE(8, ComparisonOperatorEnum.GREATER_THAN, ComparisonOperatorEnum.LESS_THAN, ComparisonOperatorEnum.BETWEEN, ComparisonOperatorEnum.EQUALS, ComparisonOperatorEnum.NOT_EQUALS,
            ComparisonOperatorEnum.ISNULL);

    private static final Map<Integer, CriterionEnum> map = new HashMap<>();
    static {
        for (final CriterionEnum each : CriterionEnum.values()) {
            map.put(each.getCode(), each);
        }
    }

    private final Integer                      code;

    private final List<ComparisonOperatorEnum> conditions = new ArrayList<>();

    /**
     * Yapıcı metot.
     *
     * @param code kod numarası
     * @param conditions aldığı arama koşulları
     */
    private CriterionEnum(final Integer code, final ComparisonOperatorEnum... conditions) {
        this.code = code;
        for (final ComparisonOperatorEnum each : conditions) {
            this.conditions.add(each);
        }
    }

    /**
     * Kod numarasına göre ilgili kriteri döner.
     *
     * @param code kod numarası
     * @return kriter
     */
    public static CriterionEnum parse(final Integer code) {
        return map.get(code);
    }

    // getters ................................................................

    public List<ComparisonOperatorEnum> getConditions() {
        return this.conditions;
    }

    public Integer getCode() {
        return this.code;
    }

}
