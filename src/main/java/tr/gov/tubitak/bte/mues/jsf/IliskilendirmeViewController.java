package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Iliskilendirme;
import tr.gov.tubitak.bte.mues.session.IliskilendirmeFacade;

@Named
@ViewScoped
public class IliskilendirmeViewController extends AbstractController<Iliskilendirme> {

    private static final long    serialVersionUID = 7446325332897316183L;

    @Inject
    private IliskilendirmeFacade facade;

    public IliskilendirmeViewController() {
        super(Iliskilendirme.class);
    }

    // getters and setters ....................................................

    @Override
    public IliskilendirmeFacade getFacade() {
        return this.facade;
    }

    public void findIliskilendirme(final Iliskilendirme iliskilendirme) {
        if (iliskilendirme != null) {
            this.setModel(this.facade.findEagerById(iliskilendirme.getId()));
        }
    }

}
