package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.MalzemeGrubu;
import tr.gov.tubitak.bte.mues.session.MalzemeGrubuFacade;

@Named
@ViewScoped
public class MalzemeGrubuController extends AbstractController<MalzemeGrubu> {

    private static final long      serialVersionUID = -3858385371020009803L;

    @Inject
    private MalzemeGrubuFacade facade;

    public MalzemeGrubuController() {
        super(MalzemeGrubu.class);
    }

    @Override
    public MalzemeGrubuFacade getFacade() {
        return this.facade;
    }

}
