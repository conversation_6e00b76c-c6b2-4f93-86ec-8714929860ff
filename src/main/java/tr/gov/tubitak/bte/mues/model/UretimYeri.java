package tr.gov.tubitak.bte.mues.model;

// import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
//import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "UretimYeri")
@NamedQuery(name = "UretimYeri.findEagerById", query = "SELECT u FROM UretimYeri u WHERE u.id = :id")
@NamedQuery(name = "UretimYeri.findAll", query = "SELECT u FROM UretimYeri u ORDER BY u.silinmis, u.aktif DESC, u.ad")
@NamedQuery(name = "UretimYeri.findActive", query = "SELECT u FROM UretimYeri u WHERE u.aktif = true AND u.silinmis = false ORDER BY u.ad")
@NamedQuery(name = "UretimYeri.findByNameAndAciklama", query = "SELECT u FROM UretimYeri u WHERE u.aktif = true AND u.silinmis = false AND (u.ad LIKE :str OR u.aciklama LIKE :str) ORDER BY u.ad, u.aciklama")
@NamedNativeQuery(name = "UretimYeri.validateBeforeDelete", query = "SELECT (SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END FROM ESER WHERE SILINMIS = 0 AND uretimYeriId = :id)")
public class UretimYeri extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 6522698142338630329L;

    @JoinColumn(name = "uygarlik", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Uygarlik          uygarlik;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public UretimYeri() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Uygarlik getUygarlik() {
        return this.uygarlik;
    }

    public void setUygarlik(final Uygarlik uygarlik) {
        this.uygarlik = uygarlik;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
