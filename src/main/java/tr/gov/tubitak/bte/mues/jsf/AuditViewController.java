package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.inject.Inject;
import javax.inject.Named;

import org.hibernate.envers.RevisionType;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.model.LazyDataModel;

import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.mapping.AuditView;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyAuditViewDataModel;
import tr.gov.tubitak.bte.mues.session.AuditViewFacade;

@Named
@ViewScoped
public class AuditViewController implements Serializable {

    private static final long         serialVersionUID = -4790994858737662781L;

    @Inject
    private transient AuditViewFacade facade;

    private LazyDataModel<AuditView>  lazyAuditViewDataModel;

    /**
     * @constructor
     */
    public AuditViewController() {
        // default constructor
    }

    public void resetTable() {
        ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("auditViewForm:auditViewTableId")).reset();
    }

    public void loadDataTable() {
        this.resetTable();
        this.lazyAuditViewDataModel = new LazyAuditViewDataModel(this.facade);
    }

    public List<SelectItem> filterEventNames(final String str) {

        final List<SelectItem> items = new ArrayList<>();

        for (final AuditEvent each : AuditEvent.values()) {

            if ((each.getCode() > 2) && (str.isEmpty() || each.getLabel().toLowerCase().contains(str.toLowerCase()))) {
                items.add(new SelectItem(each.getCode(), each.getLabel()));
            }
        }
        return items;
    }

    public String fetchScreenValue(final Integer re) {
        return Optional.ofNullable(AuditEvent.parseByCode(re)).map(AuditEvent::getLabel).orElse("");
    }

    public String fetchScreenValue(final RevisionType re) {
        return AuditEvent.parseByCode(re.getRepresentation()).getLabel();
    }

    // getters and setters ....................................................

    public LazyDataModel<AuditView> getLazyAuditViewDataModel() {
        if (this.lazyAuditViewDataModel == null) {
            this.loadDataTable();
        }
        return this.lazyAuditViewDataModel;
    }

    public void setLazyAuditViewDataModel(final LazyDataModel<AuditView> auditViewDataModel) {
        this.lazyAuditViewDataModel = auditViewDataModel;
    }

}
