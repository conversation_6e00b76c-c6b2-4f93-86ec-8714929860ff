/* TUBITAK-<PERSON><PERSON><PERSON><PERSON> BTE, Gebze - Kocaeli, 2017© */
package tr.gov.tubitak.bte.mues.util;

/**
*
 *
 */
public enum FolderType {

    /** <PERSON> */
    IMAGE_AK("image/ak"),

    ACTIVITY("activity"),

    /** <PERSON><PERSON><PERSON><PERSON><PERSON> */
    IMAGE_DK("image/dk"),

    INVENTORY("inventory"),

    TRANSCRIPT("transcript"),

    PROBLEM("problem"),

    DETERMINATION("determination"),

    EXTERNAL_PROCESS("external_process"),

    ANALYSIS("analysis"),

    SAMPLE_ANALYSIS("sample_analysis"),

    COMMISSION("commission"),

    REQUEST("request"),

    TREASUREREQUEST("treasure_request"),

    ARTIFACT("artifact"),

    APPLIED_WORK("applied_work"),

    /** Teslim alma (kabul) */
    ADMITTANCE("admittance"),

    /** İç teslim alma (iç kabul) */
    INTERNAL_ADMITTANCE("internal_admittance"),

    /** <PERSON><PERSON><PERSON> teslim etme */
    DELIVERY("delivery"),

    /** Onay */
    ACK("ack"),

    HELP("help"),

    /** Video/ses */
    MEDIA("media"),

    OTHER("other"),

    DWG("dwg"),

    ABROADARTIFACT("abroad_artifact"),

    ACTION("action"),

    TEMPORARY_ADMISSION_RECEIPT("temporary_admission_receipt"),

    /*KMS teslim alma doküman*/
    KMSDOCUMENT("kmsdocument"),

    SECURITYIMPLEMENTATION("security_implementation"),

    // OMK DENETIM RAPORLAMA
    MUSEUM_INSPECTION_REPORT("museum_inspection_report");

    private String folderPath;

    private FolderType(final String folderPath) {
        this.folderPath = folderPath;
    }

    public String getFolderPath() {
        return this.folderPath;
    }

}