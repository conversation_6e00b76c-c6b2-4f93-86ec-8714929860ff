package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.MalzemeSuslemeTeknigi;

/**
 *
*
 */
@RequestScoped
public class MalzemeSuslemeTeknigiFacade extends AbstractFacade<MalzemeSuslemeTeknigi> {

    public MalzemeSuslemeTeknigiFacade() {
        super(MalzemeSuslemeTeknigi.class);
    }

    public List<MalzemeSuslemeTeknigi> filterByNameAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("MalzemeSuslemeTeknigi.findByNameAndMalzeme", MalzemeSuslemeTeknigi.class)
                      .setParameter("ad", "%" + query + "%")
                      .setParameter("malzeme", malzeme)
                      .getResultList();
    }

    public List<MalzemeSuslemeTeknigi> findActiveGroupByMalzeme() {
        return this.em.createNamedQuery("MalzemeSuslemeTeknigi.findActiveGroupByMalzeme", MalzemeSuslemeTeknigi.class).getResultList();
    }

    public List<MalzemeSuslemeTeknigi> findActiveGroupBySuslemeTeknigi() {
        return this.em.createNamedQuery("MalzemeSuslemeTeknigi.findActiveGroupBySuslemeTeknigi", MalzemeSuslemeTeknigi.class).getResultList();
    }

}
