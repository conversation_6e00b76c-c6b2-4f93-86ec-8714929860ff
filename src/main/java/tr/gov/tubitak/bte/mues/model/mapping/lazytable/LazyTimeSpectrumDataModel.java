package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.mapping.TimeSpectrum;
import tr.gov.tubitak.bte.mues.session.TimeSpectrumLazyLoadFacade;

public class LazyTimeSpectrumDataModel extends LazyDataModel<TimeSpectrum> {

    private static final long                serialVersionUID = -4739489425974372483L;

    private static final Logger              logger           = LoggerFactory.getLogger(LazyTimeSpectrumDataModel.class);

    private final TimeSpectrumLazyLoadFacade timeSpectrumFacade;

    private List<TimeSpectrum>               data;

    public LazyTimeSpectrumDataModel(final TimeSpectrumLazyLoadFacade timeSpectrumFacade) {
        this.timeSpectrumFacade = timeSpectrumFacade;
    }

    @Override
    public List<TimeSpectrum> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        // this.setRowCount(this.timeSpectrumFacade.count(filterBy));

        this.data = this.timeSpectrumFacade.fetchData(first, pageSize, sortBy, filterBy);
        logger.debug("filters: {} _ multiSortMeta Length,  {}", sortBy, filterBy);

        return this.data;
    }
    
    @Override
	public int count(Map<String, FilterMeta> filterBy) {
		return this.timeSpectrumFacade.count(filterBy);
	}

    @Override
    public TimeSpectrum getRowData(final String rowKey) {
        for (final TimeSpectrum each : this.data) {
            if (each.getId().equals(Long.valueOf(rowKey))) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final TimeSpectrum timeSpectrum) {
        return timeSpectrum.getId().toString();
    }

}
