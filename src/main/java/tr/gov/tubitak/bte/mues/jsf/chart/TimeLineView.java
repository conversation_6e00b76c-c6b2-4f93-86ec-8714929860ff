package tr.gov.tubitak.bte.mues.jsf.chart;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import javax.annotation.PostConstruct;
import javax.faces.view.ViewScoped;
import javax.inject.Named;

import org.primefaces.model.TreeNode;
import org.primefaces.model.timeline.TimelineEvent;
import org.primefaces.model.timeline.TimelineGroup;
import org.primefaces.model.timeline.TimelineModel;

import tr.gov.tubitak.bte.mues.util.DateUtil;

/**
 * ali.kelle
 */

@Named("timeLineView")
@ViewScoped
public class TimeLineView implements Serializable {

    private static final long                   serialVersionUID = 1L;

    private TimelineModel<TreeNodeItem, String> timelineModel;

    private LocalDateTime                       start;

    private LocalDateTime                       end;

    private LocalDateTime                       min;

    private long                                zoomMin;

    private ZoneId                              timeZone;

    private Locale                              locale;

    private Boolean                             isTreeRender;

    @PostConstruct
    public void init() {

        this.timelineModel = new TimelineModel<>();
        this.timeZone = ZoneId.of("UTC");
        this.locale = new Locale("tr", "TR");
        this.zoomMin = 1000L * 60 * 60 * 24 * 31 * 12 * 5;
        this.min = LocalDateTime.of(-20000, 1, 1, 0, 0);
        this.end = LocalDateTime.of(3500, 1, 1, 0, 0);
        this.start = LocalDateTime.of(-4000, 1, 1, 0, 0);

    }

    public void createTimelineEvent(final TreeNode<TreeNodeItem> treeNode, final int level) {
        if ((treeNode.getData().getMaxDate() != null) && (treeNode.getData().getMinDate() != null)) {
            if (treeNode.getData().getMinDate() < -20000) {
                treeNode.getData().setMinDate(-20000);
            }

            final TimelineEvent<TreeNodeItem> event = TimelineEvent.<TreeNodeItem> builder()
                                                                   .data(treeNode.getData())
                                                                   .styleClass("level" + level)
                                                                   .startDate(LocalDateTime.ofInstant(DateUtil.getEndOfYear(treeNode.getData().getMinDate()).toInstant(), ZoneId.of("UTC")))
                                                                   .endDate(LocalDateTime.ofInstant(DateUtil.getEndOfYear(treeNode.getData().getMaxDate()).toInstant(), ZoneId.of("UTC")))
                                                                   .group(treeNode.getRowKey())
                                                                   .build();

            this.timelineModel.add(event);
        }
    }

    public void createTimelineGroup(final TreeNode<TreeNodeItem> treeNode) {
        this.timelineModel.clear();
        if (this.timelineModel.getGroups() != null) {
            this.timelineModel.getGroups().clear();
        }
        // Kronoloji bilgilerini timelineModele set etme
        for (final TreeNode<TreeNodeItem> level1 : treeNode.getChildren()) {
            final List<String> level2Children = new ArrayList<>();
            // Cag bilgilerini timelineModele set etme
            for (final TreeNode<TreeNodeItem> level2 : level1.getChildren()) {
                if (level2.getData().getId() != null) {
                    final List<String> level3Children = new ArrayList<>();
                    // Dönem bilgilerini timelineModele set etme
                    for (final TreeNode<TreeNodeItem> level3 : level2.getChildren()) {
                        if (level3.getData().getId() != null) {
                            final List<String> level4Children = new ArrayList<>();
                            // Uygarlık bilgilerini timelineModele set etme
                            for (final TreeNode<TreeNodeItem> level4 : level3.getChildren()) {
                                if (level4.getData().getId() != null) {
                                    final List<String> level5Children = new ArrayList<>();
                                    // Hükümdarlık bilgilerini timelineModele set etme
                                    for (final TreeNode<TreeNodeItem> level5 : level4.getChildren()) {
                                        if (level5.getData().getId() != null) {
                                            level5Children.add(level5.getRowKey());
                                            final TimelineGroup<String> level5Group = new TimelineGroup<>(level5.getRowKey(), level5.getData().toString(), 5);
                                            this.timelineModel.addGroup(level5Group);
                                            this.createTimelineEvent(level5, 5);
                                        }
                                    }
                                    level4Children.add(level4.getRowKey());
                                    final TimelineGroup<String> level4Group = new TimelineGroup<>(level4.getRowKey(), level4.getData().toString(), 4, level5Children);
                                    this.timelineModel.addGroup(level4Group);
                                    this.createTimelineEvent(level4, 4);
                                }
                            }
                            level3Children.add(level3.getRowKey());
                            final TimelineGroup<String> level3Group = new TimelineGroup<>(level3.getRowKey(), level3.getData().toString(), 3, level4Children);
                            this.timelineModel.addGroup(level3Group);
                            this.createTimelineEvent(level3, 3);
                        }
                    }

                    level2Children.add(level2.getRowKey());
                    final TimelineGroup<String> level2Group = new TimelineGroup<>(level2.getRowKey(), level2.getData().toString(), 2, level3Children);
                    this.createTimelineEvent(level2, 2);
                    this.timelineModel.addGroup(level2Group);
                }
            }

            final TimelineGroup<String> level1Group = new TimelineGroup<>(level1.getRowKey(), level1.getData().toString(), 1, level2Children);
            this.createTimelineEvent(level1, 1);
            this.timelineModel.addGroup(level1Group);
        }

    }

    public LocalDateTime getStart() {
        return this.start;
    }

    public void setStart(final LocalDateTime start) {
        this.start = start;
    }

    public LocalDateTime getEnd() {
        return this.end;
    }

    public void setEnd(final LocalDateTime end) {
        this.end = end;
    }

    public LocalDateTime getMin() {
        return this.min;
    }

    public void setMin(final LocalDateTime min) {
        this.min = min;
    }

    public long getZoomMin() {
        return this.zoomMin;
    }

    public void setZoomMin(final long zoomMin) {
        this.zoomMin = zoomMin;
    }

    public ZoneId getTimeZone() {
        return this.timeZone;
    }

    public void setTimeZone(final ZoneId timeZone) {
        this.timeZone = timeZone;
    }

    public Locale getLocale() {
        return this.locale;
    }

    public void setLocale(final Locale locale) {
        this.locale = locale;
    }

    public TimelineModel<TreeNodeItem, String> getTimelineModel() {
        return this.timelineModel;
    }

    public void setTimelineModel(final TimelineModel<TreeNodeItem, String> timelineModel) {
        this.timelineModel = timelineModel;
    }

    public Boolean getIsTreeRender() {
        return this.isTreeRender;
    }

    public void setIsTreeRender(final Boolean isTreeRender) {
        this.isTreeRender = isTreeRender;
    }

}