package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "MALZEME_SUSLEME_TEKNIGI")
@NamedQuery(name = "MalzemeSuslemeTeknigi.findEagerById", query = "SELECT x FROM MalzemeSuslemeTeknigi x JOIN FETCH x.malzeme JOIN FETCH x.suslemeTeknigi WHERE x.id = :id")
@NamedQuery(name = "MalzemeSuslemeTeknigi.findAll", query = "SELECT x FROM MalzemeSuslemeTeknigi x JOIN FETCH x.suslemeTeknigi JOIN FETCH x.malzeme xm ORDER BY x.silinmis, x.aktif DESC, xm.ad")
@NamedQuery(name = "MalzemeSuslemeTeknigi.findActive", query = "SELECT x FROM MalzemeSuslemeTeknigi x JOIN FETCH x.malzeme LEFT JOIN FETCH x.suslemeTeknigi WHERE x.aktif = true AND x.silinmis = false ORDER BY x.suslemeTeknigi.ad")
@NamedQuery(name = "MalzemeSuslemeTeknigi.findActiveGroupByMalzeme", query = "SELECT x FROM MalzemeSuslemeTeknigi x WHERE x.aktif = true AND x.silinmis = false GROUP BY x.malzeme")
@NamedQuery(name = "MalzemeSuslemeTeknigi.findByNameAndMalzeme", query = "SELECT x FROM MalzemeSuslemeTeknigi x WHERE x.malzeme = :malzeme AND x.aktif = true AND x.silinmis = false")
@NamedNativeQuery(name = "MalzemeSuslemeTeknigi.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_SUSLEME_TEKNIGI WHERE SILINMIS = 0 AND (SUSLEME_TEKNIGI_ID = :id OR MALZEME_ID = :id))")
public class MalzemeSuslemeTeknigi extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -5888562597226421209L;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "MALZEME_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Malzeme           malzeme;

    @JoinColumn(name = "SUSLEME_TEKNIGI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private SuslemeTeknigi    suslemeTeknigi;

    public MalzemeSuslemeTeknigi() {
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Malzeme getMalzeme() {
        return this.malzeme;
    }

    public void setMalzeme(final Malzeme malzeme) {
        this.malzeme = malzeme;
    }

    public SuslemeTeknigi getSuslemeTeknigi() {
        return this.suslemeTeknigi;
    }

    public void setSuslemeTeknigi(final SuslemeTeknigi suslemeTeknigi) {
        this.suslemeTeknigi = suslemeTeknigi;
    }

    @Override
    public String toString() {

        return Optional.ofNullable(this.malzeme).flatMap(malzeme -> Optional.of("{Malzeme: " + malzeme.getAd())).toString()
               + Optional.ofNullable(this.suslemeTeknigi).flatMap(suslemeTeknigi -> Optional.of(", SuslemeTeknigi: " + suslemeTeknigi.getAd()));

    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.malzeme.getAd()).orElse("" + this.suslemeTeknigi.getAd());
    }
}
