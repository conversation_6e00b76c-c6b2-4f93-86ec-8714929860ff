package tr.gov.tubitak.bte.mues.model;

import java.io.Serializable;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.validation.constraints.NotNull;

/**
 *
*
 */
@Embeddable
public class RolIzinPK implements Serializable {

    private static final long serialVersionUID = -9102811644322208072L;

    @Basic(optional = false)
    @NotNull
    @Column(name = "ROL_ID")
    private int               rolId;

    @Basic(optional = false)
    @NotNull
    @Column(name = "IZIN_ID")
    private int               izinId;

    public RolIzinPK() {
    }

    // getters and setters ....................................................

    public RolIzinPK(final int rolId, final int izinId) {
        this.rolId = rolId;
        this.izinId = izinId;
    }

    public int getRolId() {
        return this.rolId;
    }

    public void setRolId(final int rolId) {
        this.rolId = rolId;
    }

    public int getIzinId() {
        return this.izinId;
    }

    public void setIzinId(final int izinId) {
        this.izinId = izinId;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += this.rolId;
        hash += this.izinId;
        return hash;
    }

    @Override
    public boolean equals(final Object object) {
        if (!(object instanceof RolIzinPK)) {
            return false;
        }
        final RolIzinPK other = (RolIzinPK) object;
        if (this.rolId != other.rolId) {
            return false;
        }
        if (this.izinId != other.izinId) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "{rolId: " + this.rolId + ", izinId: " + this.izinId + "}";
    }

}
