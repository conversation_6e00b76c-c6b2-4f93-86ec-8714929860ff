package tr.gov.tubitak.bte.mues.model;


import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@Entity
@Table(name = "Transcription")
@NamedQuery(name = "Transcription.findByEserIdAndTranscription", query = "SELECT a FROM Transcription a LEFT JOIN FETCH a.eser e LEFT JOIN FETCH a.monogram WHERE a.aktif = true AND a.silinmis = false AND (a.metaphrase LIKE :str OR e.genelAciklama LIKE :str OR e.permanentId LIKE :str) ")
public class Transcription extends TranscriptionSuper {

    private static final long serialVersionUID = -1257253912454192664L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "eser", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public Transcription() {
        // default constructor
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }
    
    public String getTrimmedMetaphrase() {
    	 return (this.getMetaphrase()!=null && this.getMetaphrase().length() > 100) ? (this.getMetaphrase().substring(0, 100)+ "..." ): this.getMetaphrase()  ;
                 
    }

}
