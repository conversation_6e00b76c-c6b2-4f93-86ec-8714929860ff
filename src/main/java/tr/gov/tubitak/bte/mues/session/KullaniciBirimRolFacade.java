package tr.gov.tubitak.bte.mues.session;


import java.util.Date;
import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.PersistenceException;
import javax.transaction.Transactional;
import javax.validation.ConstraintViolationException;

import tr.gov.tubitak.bte.mues.model.Announcement;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.DateUtil;

/**
 *
*
 */
@RequestScoped
public class KullaniciBirimRolFacade extends AbstractFacade<KullaniciBirimRol> {

    @Inject
    private KullaniciFacade    kullaniciFacade;

    @Inject
    private PersonelFacade     personelFacade;

    @Inject
    private AnnouncementFacade announcementFacade;

    public KullaniciBirimRolFacade() {
        super(KullaniciBirimRol.class);
    }

    public Kullanici create(final Personel p) {
    	
    	 Kullanici kullanici = new Kullanici();

        Kullanici temp = this.kullaniciFacade.findKullaniciByTCKN(p.getTcKimlikNo());
        if (temp != null) {
            kullanici = temp;

        } else {
            kullanici.setPersonel(p);
            kullanici.setKullaniciAdi(p.getTcKimlikNo());
            kullanici.setAd(p.getAd());
            kullanici.setSoyad(p.getSoyad());

            // set the last announcement to the user
            final Announcement announcement = this.announcementFacade.findByMaxId();
            kullanici.setDuyuruNumarasi(announcement);
        }
        kullanici.setAktif(true);
        kullanici.setSilinmis(false);

        // try {
        // // TODO back-door added for testing purposes. After test completed, this will be removed
        // kullanici.setSifreHash(Base64.getEncoder().encodeToString(MessageDigest.getInstance("SHA-256").digest("1234".getBytes())));
        // } catch (final NoSuchAlgorithmException e) {
        // this.logger.error("[create] : Hata : {}", e.getMessage(), e);
        // }

        return kullanici;
    }

    

    public Personel updatePersonelMuze(final KullaniciBirimRol kullaniciBirimRol, final Personel personel) {

        Personel person = null;

        final Mudurluk mudurluk = kullaniciBirimRol.getMudurluk();

        if (mudurluk != null) {

            if ((kullaniciBirimRol.getKullanici() != null) && (kullaniciBirimRol.getKullanici().getId() != null)) {
                person = this.kullaniciFacade.findEagerById(kullaniciBirimRol.getKullanici().getId()).getPersonel();

            } else {
                person = this.personelFacade.findEagerById(personel.getId());
            }

            // Merkez kullanıcılarının müze müdürlükleri yetki ile değişmiyor personel ekranlarında özellikle değiştirmek gerekemektedir.
            if ((person != null) && ((person.getMudurluk() == null) || !person.getMudurluk().getKod().startsWith("00."))) {
                person.setMudurluk(kullaniciBirimRol.getMudurluk());
                person.setMudurlukAd(kullaniciBirimRol.getMudurluk().getAd());
            }
        }
        return person;
    }

    // O anki tarihe göre KULLANICI_BIRIM_ROL tablosundaki kayıtlar için bu tarih aralığında olmayanları PASIF ve SILINMIS=1, olanları AKTIF ve
    // SILINMIS=0 yapmak için kullanılır
    @Transactional
    public DBOperationResult updateForTimer() {
        try {

            final Date currentTime = DateUtil.getCurrentDate();

            this.getEM()
                .createNativeQuery("UPDATE KULLANICI_BIRIM_ROL SET AKTIF=0, SILINMIS=1 WHERE (assignmentEndTime < :date or assignmentStartTime >= :date) and (isDeletedByUser=0) and AKTIF=1 and SILINMIS=0")
                .setParameter("date", currentTime)
                .executeUpdate();

            this.getEM()
                .createNativeQuery("UPDATE KULLANICI_BIRIM_ROL SET AKTIF=1, SILINMIS=0 WHERE (assignmentEndTime > :date and assignmentStartTime <= :date) and (isDeletedByUser=0) and AKTIF=0 and SILINMIS=1")
                .setParameter("date", currentTime)
                .executeUpdate();

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    /** rol -> kod */
    public List<KullaniciBirimRol> findByMudurlukAndRol(final Integer mudurlukId, final String rolCode) {
        return this.em.createNamedQuery("KullaniciBirimRol.findByMudurlukAndRol", KullaniciBirimRol.class).setParameter("muzeId", mudurlukId).setParameter("kod", rolCode).getResultList();
    }

    public List<KullaniciBirimRol> findByMudurlukAndRol(final Integer mudurlukId) {
        return this.em.createNamedQuery("KullaniciBirimRol.findByMudurlukAndRol", KullaniciBirimRol.class).setParameter("kod", "muze_muduru").setParameter("muzeId", mudurlukId).getResultList();
    }


    public List<KullaniciBirimRol> fetchKbrByUser(final Kullanici kullanici) {
        return this.em.createNamedQuery("KullaniciBirimRol.fetchKbrByUser", KullaniciBirimRol.class).setParameter("kullanici", kullanici).getResultList();
    }

    public List<KullaniciBirimRol> fetchKbrByUserAndMudurluk(final Kullanici kullanici, final Mudurluk mudurluk) {
        return this.em.createNamedQuery("KullaniciBirimRol.fetchKbrByUserAndMudurluk", KullaniciBirimRol.class).setParameter("kullanici", kullanici).setParameter("mudurluk", mudurluk).getResultList();
    }

    public List<KullaniciBirimRol> findByMudurluk(final List<Mudurluk> mudurlukList, final boolean showRolesWithoutMuseums) {
        return this.em.createNamedQuery("KullaniciBirimRol.findByMudurluk", KullaniciBirimRol.class).setParameter("muzeler", mudurlukList).setParameter("showRolesWithoutMuseums", showRolesWithoutMuseums).getResultList();
    }

}