package tr.gov.tubitak.bte.mues.search.controller;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.faces.event.ValueChangeEvent;
import javax.inject.Inject;

import org.apache.solr.client.solrj.SolrClient;
import org.apache.solr.client.solrj.SolrServerException;
import org.primefaces.component.autocomplete.AutoComplete;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.SessionBean;
import tr.gov.tubitak.bte.mues.model.Identifiable;
import tr.gov.tubitak.bte.mues.request.FileDownloadController;
import tr.gov.tubitak.bte.mues.search.AbstractSearchController;
import tr.gov.tubitak.bte.mues.search.ComparisonOperatorEnum;
import tr.gov.tubitak.bte.mues.search.CompoundCriterion;
import tr.gov.tubitak.bte.mues.search.CriterionModel;
import tr.gov.tubitak.bte.mues.search.ICriterion;
import tr.gov.tubitak.bte.mues.search.LogicalOperatorEnum;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.SearchUtil;

public abstract class AbstractSpecialSearchController<T extends Identifiable<?>> implements Serializable {

    private static final long                       serialVersionUID             = 6763905455861013371L;

    private static final Logger                     logger                       = LoggerFactory.getLogger(AbstractSpecialSearchController.class);

    protected AbstractSearchController<T>           searchController;

    @Inject
    protected SessionBean                           sessionBean;

    @Inject
    protected SolrSearcher                          solrSearcher;

    @Inject
    protected SearchUtil                            searchUtil;

    @Inject
    protected FileDownloadController                fileDownloadController;

    protected transient Map<String, CriterionModel> selectedMetaDataMap          = new HashMap<>();

    private SolrClient                              server;

    public static final String                      HIERARCHICAL_FACET_SEPARATOR = "/";

    public abstract void makeInquery();

    public void init() {
        this.selectedMetaDataMap.clear();
    }

    public void valueChangeListener(final ValueChangeEvent changeEvent) {

        if (changeEvent.getSource() != null) {
            final AutoComplete au = (AutoComplete) changeEvent.getSource();

            final CriterionModel selected = (CriterionModel) au.getValue();
            if ((selected != null) && (selected.getMetadata() != null)) {
                this.selectedMetaDataMap.remove(selected.getMetadata().getName());
            }
        }

    }

    public void solrHelpValueChangeListener(final ValueChangeEvent changeEvent) {
        this.fileDownloadController.showPdf(changeEvent.getNewValue().toString(), FolderType.HELP);
    }

    public ICriterion composeCriterions() {

        final Set<String> keySet = this.selectedMetaDataMap.keySet();
        final CompoundCriterion compoundCriterion = new CompoundCriterion();
        compoundCriterion.setChildren(new ArrayList<>());

        compoundCriterion.setLogicalOperator(LogicalOperatorEnum.AND);

        if (!keySet.isEmpty()) {

            for (final String key : keySet) {
                final CriterionModel criterionModel = this.selectedMetaDataMap.get(key);
                final List<ICriterion> criteriaList = criterionModel.getCriteriaList();
                compoundCriterion.getChildren().addAll(criteriaList);
            }
        }

        return compoundCriterion;
    }

    public List<CriterionModel> autocompleteForMetadata(final String query, final String metadataName, final ComparisonOperatorEnum contains, final Integer code) throws IOException,
            SolrServerException {
        final CriterionModel model = this.selectedMetaDataMap.remove(metadataName);

        final String filter = this.composeCriterions().getSql();
        final List<CriterionModel> list = this.searchController.autocompleteForMetadata(query, metadataName, contains, code, filter);

        if (model != null) {
            this.selectedMetaDataMap.put(metadataName, model);
        }

        return list;

    }

    /// buna gerek var mi bakayim.
    public void resetSearchFields() {
        logger.debug("[resetSearchFields] : ");
        this.searchController.getSelectionList().clear();
        this.searchController.resetSearchFields();
        this.init();
    }

    public Map<String, CriterionModel> getSelectedMetaDataMap() {
        return this.selectedMetaDataMap;
    }

    public void setSelectedMetaDataMap(final Map<String, CriterionModel> selectedMetaDataMap) {
        this.selectedMetaDataMap = selectedMetaDataMap;
    }

    public SolrClient getServer() {
        return this.server;
    }

    public void setServer(final SolrClient server) {
        this.server = server;
    }

    public SolrSearcher getSolrSearcher() {
        return this.solrSearcher;
    }

}
