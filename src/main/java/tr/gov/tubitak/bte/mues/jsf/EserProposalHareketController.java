package tr.gov.tubitak.bte.mues.jsf;

import java.util.LinkedHashSet;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalHareket;
import tr.gov.tubitak.bte.mues.model.EserProposalHareketSahis;
import tr.gov.tubitak.bte.mues.model.Sahis;
import tr.gov.tubitak.bte.mues.session.EserProposalHareketFacade;

@Named
@ViewScoped
public class EserProposalHareketController extends AbstractController<EserProposalHareket> {

    private static final long                  serialVersionUID = 6621033666668822740L;

    @Inject
    private EserProposalHareketFacade          facade;

    @Inject
    private EserProposalHareketSahisController eserHareketSahisController;

    @Inject
    private SahisController                    sahisController;

    public EserProposalHareketController() {
        super(EserProposalHareket.class);
    }

    public void addEserHareketSahis() {
        if (this.getModel().getTeslimEdenSahis() == null) {
            this.getModel().setTeslimEdenSahis(new LinkedHashSet<>());
        }
        for (final Sahis sahis : this.sahisController.getSelectionList()) {
            final EserProposalHareketSahis eserProposalHareketSahis = new EserProposalHareketSahis();
            eserProposalHareketSahis.setSahis(sahis);
            eserProposalHareketSahis.setEserHareket(this.getModel());
            if (!(this.getModel().getTeslimEdenSahis().stream().anyMatch(x -> x.getSahis().getId().equals(eserProposalHareketSahis.getSahis().getId())))) {
                this.getModel().getTeslimEdenSahis().add(eserProposalHareketSahis);
            }
        }

        this.getModel().getTeslimEdenSahis().removeIf(x -> this.sahisController.getSelectionList().stream().noneMatch(s -> s.getId().equals(x.getSahis().getId())));

    }

    public void restoreSahis() {
        this.sahisController.newRecord();

        this.sahisController.getSelectionList().clear();
        for (final EserProposalHareketSahis eserHareketSahis : this.getModel().getTeslimEdenSahis()) {
            this.sahisController.getSelectionList().add(eserHareketSahis.getSahis());
        }
        this.sahisController.loadDataTable();
    }

    public void removeEserHareketSahis(final EserProposalHareketSahis eserHareketSahis) {
        this.getModel().getTeslimEdenSahis().remove(eserHareketSahis);
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.eserHareketSahisController.setModel(null);
        this.sahisController.getSelectionList().clear();
    }

    @Override
    public void setModel(final EserProposalHareket eserHareket) {
        super.setModel(eserHareket);

        // set teslimEdenSahis for edit
        if (!eserHareket.getTeslimEdenSahis().isEmpty()) {
            this.eserHareketSahisController.setModel(eserHareket.getTeslimEdenSahis().iterator().next());
        }
    }

    // getters and setters ....................................................

    @Override
    public EserProposalHareketFacade getFacade() {
        return this.facade;
    }

}
