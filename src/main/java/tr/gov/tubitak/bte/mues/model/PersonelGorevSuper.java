package tr.gov.tubitak.bte.mues.model;

import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "PersonelGorev.findEagerById", query = "SELECT x FROM PersonelGorev x LEFT JOIN FETCH x.mudurluk LEFT JOIN FETCH x.personel xp LEFT JOIN FETCH xp.mudurluk  LEFT JOIN FETCH x.unvan LEFT JOIN FETCH x.kadroDurumu LEFT JOIN FETCH x.tuzelKisi LEFT JOIN FETCH x.gorevlendirmeTipi WHERE x.id = :id")
@NamedQuery(name = "PersonelGorev.findAll", query = "SELECT x FROM PersonelGorev x JOIN FETCH x.kadroDurumu JOIN FETCH x.mudurluk JOIN FETCH x.personel JOIN FETCH x.tuzelKisi JOIN FETCH x.unvan LEFT JOIN FETCH x.gorevlendirmeTipi ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "PersonelGorev.findActive", query = "SELECT x FROM PersonelGorev x WHERE x.aktif = true AND x.silinmis = false")
@NamedQuery(name = "PersonelGorev.findByNameAndMudurluk", query = "SELECT x.personel FROM PersonelGorev x WHERE x.aktif = true AND x.silinmis = false AND x.mudurluk = :muze AND x.personel.ad LIKE :ad ORDER BY x.personel.ad")
@NamedQuery(name = "PersonelGorev.findByPersonelAndMudurluks", query = "SELECT x FROM PersonelGorev x LEFT JOIN FETCH x.personel xp LEFT JOIN FETCH xp.mudurluk LEFT JOIN FETCH x.mudurluk LEFT JOIN FETCH x.unvan LEFT JOIN FETCH x.kadroDurumu LEFT JOIN FETCH x.gorevlendirmeTipi WHERE x.aktif = true AND x.silinmis = false AND x.mudurluk IN :museums AND (:personel is null OR x.personel = :personel) ORDER BY x.personel.ad")
@NamedQuery(name = "PersonelGorev.findByMudurluk", query = "SELECT x FROM PersonelGorev x LEFT JOIN FETCH x.gorevlendirmeTipi LEFT JOIN FETCH x.personel LEFT JOIN FETCH x.mudurluk LEFT JOIN FETCH x.unvan LEFT JOIN FETCH x.kadroDurumu WHERE x.aktif = true AND x.silinmis = false AND (x.mudurluk in (:muze) ) ORDER BY x.personel.ad")
public class PersonelGorevSuper extends AbstractEntity {

    private static final long serialVersionUID = 8689864410641256993L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "GOREVLENDIRME_TIPI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MuesPick          gorevlendirmeTipi;

    @Column(name = "GOREV_BASLAMA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              gorevBaslamaTarihi;

    @Column(name = "GOREV_BITIS_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              gorevBitisTarihi;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "KADRO_DURUMU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private KadroDurum        kadroDurumu;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "MUZE_MUDURLUGU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk          mudurluk;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Personel          personel;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "TUZEL_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private TuzelKisi         tuzelKisi;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "UNVAN_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Unvan             unvan;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "gorevDocPath", length = 150)
    private String            gorevDocPath;

    public PersonelGorevSuper() {
        // Blank Constructor
    }

    // getters and setters ....................................................

    public MuesPick getGorevlendirmeTipi() {
        return this.gorevlendirmeTipi;
    }

    public void setGorevlendirmeTipi(final MuesPick gorevlendirmeTipi) {
        this.gorevlendirmeTipi = gorevlendirmeTipi;
    }

    public Date getGorevBaslamaTarihi() {
        return this.gorevBaslamaTarihi;
    }

    public void setGorevBaslamaTarihi(final Date gorevBaslamaTarihi) {
        this.gorevBaslamaTarihi = gorevBaslamaTarihi;
    }

    public Date getGorevBitisTarihi() {
        return this.gorevBitisTarihi;
    }

    public void setGorevBitisTarihi(final Date gorevBitisTarihi) {
        this.gorevBitisTarihi = gorevBitisTarihi;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public KadroDurum getKadroDurumu() {
        return this.kadroDurumu;
    }

    public void setKadroDurumu(final KadroDurum kadroDurumu) {
        this.kadroDurumu = kadroDurumu;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Personel getPersonel() {
        return this.personel;
    }

    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public TuzelKisi getTuzelKisi() {
        return this.tuzelKisi;
    }

    public void setTuzelKisi(final TuzelKisi tuzelKisi) {
        this.tuzelKisi = tuzelKisi;
    }

    public Unvan getUnvan() {
        return this.unvan;
    }

    public void setUnvan(final Unvan unvan) {
        this.unvan = unvan;
    }

    public String getGorevDocPath() {
        return this.gorevDocPath;
    }

    public void setGorevDocPath(final String gorevDocPath) {
        this.gorevDocPath = gorevDocPath;
    }

    @Override
    public String toString() {
        return this.personel.getTitle() + "; gorevlendirme tipi: " + this.gorevlendirmeTipi;
    }

    @Override
    public String getTitle() {
        return Stream.of(this.personel.getTitle(), this.mudurluk == null ? "" : this.mudurluk.getTitle()).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(", "));
    }

}
