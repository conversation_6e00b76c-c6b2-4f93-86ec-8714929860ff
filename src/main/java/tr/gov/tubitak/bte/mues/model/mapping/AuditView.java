package tr.gov.tubitak.bte.mues.model.mapping;

import java.math.BigInteger;
import java.text.DecimalFormat;
import java.util.Date;

import javax.persistence.Table;

@Table(name = "AuditView")
public class AuditView extends AuditViewSuper {

    private static final long serialVersionUID = -5371994113500240657L;
    
    private static final String        TR_M             = "TR.M.%s";

    private static final DecimalFormat df               = new DecimalFormat("000,000,000");
    
    private Integer		  	  permanentId;
    
    private String 			  eserEnvanterNo;
    
    private String			  eserID;
    
    private String 			  temporaryID;

    public AuditView(final BigInteger id, final String summary, final Integer eventId, final Date eventTime, final String userName, final String userIp, final String adSoyad, final String mudurluk, final Integer permanentId, final String eserEnvanterNo, final String eserID,final String temporaryID) {
        super(id, summary, eventId, eventTime, userName, userIp, adSoyad, mudurluk);
        this.permanentId = permanentId;
        this.eserEnvanterNo = eserEnvanterNo;
        this.eserID = eserID;
        this.temporaryID = temporaryID;
    }

    public Integer getPermanentId() {
		return permanentId;
	}

	public void setPermanentId(Integer permanentId) {
		this.permanentId = permanentId;
	}

	public String getEserEnvanterNo() {
		return eserEnvanterNo;
	}

	public void setEserEnvanterNo(String eserEnvanterNo) {
		this.eserEnvanterNo = eserEnvanterNo;
	}

	public String getEserID() {
		return eserID;
	}

	public void setEserID(String eserId) {
		this.eserID = eserId;
	}

	public String getTemporaryID() {
		return temporaryID;
	}

	public void setTemporaryID(String temporaryId) {
		this.temporaryID = temporaryId;
	}
	
	public String getFormattedEserID() {
        if (this.permanentId == null) {
            return null;
        }
        return String.format(TR_M, df.format(this.permanentId).replace(",", "."));
    }
	
}
