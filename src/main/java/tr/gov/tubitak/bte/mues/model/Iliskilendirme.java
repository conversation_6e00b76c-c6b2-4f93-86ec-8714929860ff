package tr.gov.tubitak.bte.mues.model;

import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.ValidArtifactJoinable;
import tr.gov.tubitak.bte.mues.constraint.validator.ArtifactJoinGroup;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "Iliskilendirme")

@NamedQuery(name = "Iliskilendirme.findEagerById", query = "SELECT i FROM Iliskilendirme i "
                                                           + "LEFT JOIN FETCH i.groupedArtifacts a LEFT JOIN FETCH a.anaFotograf  "
                                                           + "LEFT JOIN FETCH a.eserAltTur ar LEFT JOIN FETCH ar.eserTur "
                                                           + "LEFT JOIN FETCH i.tur t LEFT JOIN FETCH t.turGrubu "
                                                           + "WHERE i.id = :id")

@NamedQuery(name = "Iliskilendirme.findAll", query = "SELECT DISTINCT i FROM Iliskilendirme i "
                                                     + "LEFT JOIN FETCH i.groupedArtifacts a "
                                                     + "LEFT JOIN FETCH i.tur t LEFT JOIN FETCH t.turGrubu g "
                                                     + "ORDER BY i.silinmis, i.aktif DESC, g.ad")

@NamedQuery(name = "Iliskilendirme.findActive", query = "SELECT DISTINCT i FROM Iliskilendirme i WHERE i.aktif = true AND i.silinmis = false " + "ORDER BY i.ad")

@NamedQuery(name = "Iliskilendirme.findByNameOrInventoryNo", query = "SELECT DISTINCT i  FROM Iliskilendirme i "
                                                                     + "LEFT JOIN FETCH i.groupedArtifacts a "
                                                                     + "WHERE i.aktif = true AND i.silinmis = false AND (i.ad LIKE :str OR a.envanterNo LIKE :str) "
                                                                     + "ORDER BY i.ad")

@NamedQuery(name = "Iliskilendirme.findByMudurluk", query = "SELECT DISTINCT i FROM Iliskilendirme i "
                                                            + "LEFT JOIN FETCH i.groupedArtifacts e LEFT JOIN FETCH e.eserDepos d LEFT JOIN FETCH d.alanKonumu k LEFT JOIN FETCH k.alan a LEFT JOIN FETCH a.bina b LEFT JOIN FETCH b.bagliBirim bb LEFT JOIN FETCH bb.mudurluk m "
                                                            + "LEFT JOIN FETCH i.tur t LEFT JOIN FETCH t.turGrubu "
                                                            + "WHERE (m IN :muzeler) or m is null ")

@NamedQuery(name = "Iliskilendirme.findEagerByEser", query = "SELECT DISTINCT i FROM Iliskilendirme i "
                                                             + "LEFT JOIN FETCH i.groupedArtifacts a LEFT JOIN FETCH a.anaFotograf  "
                                                             + "LEFT JOIN FETCH i.tur t LEFT JOIN FETCH t.turGrubu "
                                                             + "WHERE :eser in a")

@NamedNativeQuery(name = "Iliskilendirme.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER WHERE SILINMIS = 0 AND gruplamaId = :id)")

@ValidArtifactJoinable(groups = ArtifactJoinGroup.class)
public class Iliskilendirme extends AbstractEntity implements ArtifactJoinable {

    private static final long serialVersionUID = 198854914834604059L;

    @Size(max = 50)
    @Column(name = "AD", unique = true, length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String            fotografPath;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "turId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private IliskilendirmeTur tur;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(mappedBy = "iliskilendirme", fetch = FetchType.LAZY, cascade = { CascadeType.MERGE, CascadeType.REMOVE, CascadeType.REFRESH })
    private Set<Eser>         groupedArtifacts;

    public Iliskilendirme() {
        // default constructor
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public Set<Eser> getCombinedArtifacts() {
        if (this.groupedArtifacts == null) {
            this.groupedArtifacts = new LinkedHashSet<>();
        }
        return this.groupedArtifacts;
    }

    @Override
    public void setCombinedArtifacts(final Set<Eser> groupedArtifacts) {
        this.setGroupedArtifacts(groupedArtifacts);
    }

    public Set<Eser> getGroupedArtifacts() {
        return this.groupedArtifacts;
    }

    public void setGroupedArtifacts(final Set<Eser> groupedArtifacts) {
        this.groupedArtifacts = groupedArtifacts;
    }

    public IliskilendirmeTur getTur() {
        return this.tur;
    }

    public void setTur(final IliskilendirmeTur tur) {
        this.tur = tur;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

    public String getArtifactTitles() {
        final StringJoiner sj = new StringJoiner(", ");
        this.groupedArtifacts.stream().forEach(x -> sj.add(x.getEserId()));
        return sj.toString();
    }

}
