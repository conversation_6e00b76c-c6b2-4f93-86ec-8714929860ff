package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "EserZimmet.findEagerById", query = "SELECT e FROM EserZimmet e LEFT JOIN FETCH e.zimmetPersonel  LEFT JOIN FETCH e.eser WHERE e.id = :id")
@NamedQuery(name = "EserZimmet.findAll", query = "SELECT e FROM EserZimmet e ORDER BY e.silinmis, e.aktif DESC")
@NamedQuery(name = "EserZimmet.findbyEser", query = "SELECT DISTINCT e FROM EserZimmet e LEFT JOIN FETCH e.zimmetPersonel WHERE e.aktif = true AND e.silinmis = false AND e.eser = :id")
@NamedQuery(name = "EserZimmet.findbyEserId", query = "SELECT DISTINCT e FROM EserZimmet e LEFT JOIN FETCH e.zimmetPersonel WHERE e.aktif = true AND e.silinmis = false AND e.eser.id = :id")
@NamedQuery(name = "EserZimmet.findApprovedEserByUserId", query = "SELECT DISTINCT ez FROM EserZimmet ez LEFT JOIN FETCH ez.eser e "
                                                                  + " LEFT JOIN FETCH ez.zimmetPersonel WHERE ez.aktif = true AND ez.silinmis = false AND ez.zimmetPersonel  = :zimmetPersonel AND e.versiyon=:version ORDER BY e.id")
@NamedQuery(name = "EserZimmet.findActive", query = "SELECT e FROM EserZimmet e WHERE e.aktif = true AND e.silinmis = false")
@NamedQuery(name = "EserZimmet.checkBeforeUnauthorizing", query = "SELECT ez FROM EserZimmet ez JOIN FETCH ez.eser e JOIN FETCH ez.zimmetPersonel zp WHERE e.aktif = true AND e.silinmis = false AND zp.id = :id")

public class EserZimmetSuper extends AbstractEntity {

    private static final long serialVersionUID = -5646334757253460716L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    @Column(name = "ZIMMET_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              zimmetTarihi;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Date getZimmetTarihi() {
        return this.zimmetTarihi;
    }

    public void setZimmetTarihi(final Date zimmetTarihi) {
        this.zimmetTarihi = zimmetTarihi;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    @Override
    public String getTitle() {
        return this.toString();
    }

}
