package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalAtolye;
import tr.gov.tubitak.bte.mues.session.EserProposalAtolyeFacade;

@Named
@ViewScoped
public class EserProposalAtolyeController extends AbstractController<EserProposalAtolye> {

    private static final long serialVersionUID = -4465419352459118582L;

    @Inject
    private EserProposalAtolyeFacade  facade;

    public EserProposalAtolyeController() {
        super(EserProposalAtolye.class);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalAtolyeFacade getFacade() {
        return this.facade;
    }

}
