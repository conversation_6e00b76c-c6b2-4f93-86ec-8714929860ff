package tr.gov.tubitak.bte.mues.model;

import java.util.StringJoiner;

import javax.persistence.AssociationOverride;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */

@Entity
@Table(name = "ESER_MALZEME_YAPIM_TEKNIGI")
@AssociationOverride(name = "renks")
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
public class EserMalzemeYapimTeknigi extends EserMalzemeYapimTeknigiSuper {

    private static final long serialVersionUID = 3679491821968536597L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public EserMalzemeYapimTeknigi() {
        // default constructor
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    public String getRenkAds() {
        final StringJoiner sj = new StringJoiner(", ");
        if ((this.getRenks() != null)) {
            this.getRenks().stream().forEach(x -> sj.add(x.getAd()));
        } else {
            return null;
        }
        return sj.toString();
    }

}
