package tr.gov.tubitak.bte.mues.constraint;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Payload;
import javax.validation.ReportAsSingleViolation;

@ReportAsSingleViolation
@Documented
@Target({ ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface FilePathCheck {

    String message() default "{tr.gov.tubitak.bte.mues.constraint.FilePathCheck}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}