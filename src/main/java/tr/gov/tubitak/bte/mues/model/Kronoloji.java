package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
 */
@Entity
@Table(name = "KRONOLOJI")
@NamedQuery(name = "Kronoloji.findEagerById", query = "SELECT k FROM Kronoloji k WHERE k.id = :id")
@NamedQuery(name = "Kronoloji.findAll", query = "SELECT k FROM Kronoloji k ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "Kronoloji.findActive", query = "SELECT k FROM Kronoloji k WHERE k.aktif = true AND k.silinmis = false ORDER BY k.ad")
@NamedNativeQuery(name = "Kronoloji.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM CAG WHERE SILINMIS = 0 AND KRONOLOJI_ID = :id)")
public class Kronoloji extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -5689928006275922864L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @OneToMany(mappedBy = "kronoloji")
    private Collection<Cag>   cagList;

    public Kronoloji() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<Cag> getCagList() {
        return this.cagList;
    }

    public void setCagList(final Collection<Cag> cagList) {
        this.cagList = cagList;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
