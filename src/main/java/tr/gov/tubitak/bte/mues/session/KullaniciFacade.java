package tr.gov.tubitak.bte.mues.session;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.NoResultException;

import org.apache.shiro.SecurityUtils;

import tr.gov.tubitak.bte.mues.model.Announcement;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.util.ApplicationSpecificQueries;

@RequestScoped
public class KullaniciFacade extends AbstractFacade<Kullanici> {

	@Inject
	ApplicationSpecificQueries applicationSpecificQueries;

	public KullaniciFacade() {
		super(Kullanici.class);
	}

	public Kullanici findKullanici(final String kullaniciAdi) {
        return this.getEM().createNamedQuery("Kullanici.findByUsername", Kullanici.class).setParameter("tckn", kullaniciAdi).getSingleResult();
	}

	

	public Kullanici findCurrentUser() {
        return this.getEM().createNamedQuery("Kullanici.findActiveByUsername", Kullanici.class).setParameter("tckn", SecurityUtils.getSubject().getPrincipal()).getSingleResult();
	}

	public Kullanici findKullaniciByTCKN(final String kullaniciAdi) {
		try {
            return this.getEM().createNamedQuery("Kullanici.findByUsername", Kullanici.class).setParameter("tckn", kullaniciAdi).getSingleResult();
		} catch (final NoResultException e) {
			return null;
		}
	}

	public List<Kullanici> findUsersRequestingPasswordReset() {
        return this.getEM().createNamedQuery("Kullanici.findUsersRequestingPasswordReset", Kullanici.class).getResultList();
	}

	@SuppressWarnings("unchecked")
	public Set<Integer> preparePermittedUserNames(final int personelId) {

        return new HashSet<>(this.getEM().createNativeQuery(this.applicationSpecificQueries.preparePermittedUserName()).setParameter("personelId", personelId).getResultList());
	}

	public List<Kullanici> findByMudurluk(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("Kullanici.findByMudurluk", Kullanici.class).setParameter("muzeler", muzelist).getResultList();
	}

	public Integer findByMudurlukActive(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("Kullanici.findByMudurlukActive", Kullanici.class).setParameter("muzeler", muzelist).getResultList().size();
	}

	public Integer findNumberOfUsers() {
		return ((Integer) this.em.createNativeQuery("SELECT count(*) FROM KULLANICI").getSingleResult()).intValue();
	}

	public List<Kullanici> filterByNameAndMudurluk(final String query, final List<Mudurluk> muzeler) {
		return this.em.createNamedQuery("Kullanici.findByNameAndMudurluks", Kullanici.class)
                      .setParameter("muzeler", muzeler)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
				.getResultList();
	}

    public List<Kullanici> filterByNameAndMudurlukExcludeIds(final String query, final List<Integer> directorates, final List<Integer> ids) {
		return this.em.createNamedQuery("Kullanici.filterByNameAndMudurlukExcludeIds", Kullanici.class)
                      .setParameter("directorates", directorates)
                      .setParameter("ids", ids)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .getResultList();
	}

	public List<Kullanici> filterByMudurlukAndRol(final List<Mudurluk> muzeler, final List<Rol> roller) {
        return this.em.createNamedQuery("Kullanici.findByMudurlukAndRol", Kullanici.class).setParameter("muzeler", muzeler).setParameter("roller", roller).getResultList();
	}

	public List<Kullanici> filterByMuze(final List<Mudurluk> muzeler) {
        return this.em.createNamedQuery("Kullanici.findByMuze", Kullanici.class).setParameter("muzeler", muzeler).getResultList();
	}

	@SuppressWarnings("unchecked")
	public List<Announcement> findAnnouncements(final Kullanici kullanici) {

        return this.em.createNativeQuery(this.applicationSpecificQueries.findAnnouncements(), Announcement.class).setParameter("id", kullanici.getId()).getResultList();
	}

	public String ifNotKVKKApprovedReturnPath(final Kullanici kullanici) {
		try {
			if ((kullanici.getGdpApproved() != null) && kullanici.getGdpApproved().equals(Boolean.FALSE)) {
                return (String) this.em.createNativeQuery("SELECT top 1 a.documentPath FROM GeneralDataProtectionRegulation a where a.AKTIF=1 AND a.SILINMIS=0 ORDER BY a.ID DESC").getSingleResult();
			}
		} catch (final NoResultException e) {
			return null;
		}
		return null;
	}

	public List<Kullanici> findByExternalUser(final Integer externalUserId) {
        return this.getEM().createNamedQuery("Kullanici.findByExternalUser", Kullanici.class).setParameter("id", externalUserId).getResultList();
	}

	public List<Kullanici> filterByExternalUserName(final String query) {
        return this.em.createNamedQuery("Kullanici.filterByExternalUserName", Kullanici.class).setParameter("str", "%" + query.replaceAll("\\s+", "") + "%").getResultList();
	}

	public List<Kullanici> findByUsernames(final Set<String> usernames) {
        return this.getEM().createNamedQuery("Kullanici.findByUsernames", Kullanici.class).setParameter("usernames", usernames).getResultList();
	}

}
