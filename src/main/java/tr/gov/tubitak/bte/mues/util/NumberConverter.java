package tr.gov.tubitak.bte.mues.util;

import java.math.BigDecimal;

/**
*
* <AUTHOR>
*/
public class NumberConverter {	
    
    private static final String[] ONES = {"", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"};
    
    private static final String[] TENS = {"", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"};
    
    private static final String[]  THOUSANDS = {"", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Quadrillion", "Quintillion", "Sextillion", "Septillion", "Octillion", "Nonillion", "Decillion", "Undecillion", "Duodecillion", "Tredecillion", "Quattuordecillion", "Quindecillion", "Sexdecillion", "Septendecillion", "Octodecillion", "Novemdecillion", "Vigintillion"};
   
    public static String numberToWord(Integer number) {
        return numberToWord(number.toString());
    }
    
    public static String numberToWordForTL(BigDecimal number) {
    	if(number != null) {
    		final String TL = number.toBigInteger().toString();
            final String Krs = number.remainder(BigDecimal.ONE).movePointRight(number.scale()).toBigInteger().toString();
            return numberToWord(TL) + "TL" + ((Krs == null || Krs == "00" || Krs == "0") ? "" : ", "+numberToWord(Krs) + "Krş.");
    	}
        return "";
    }

    public static String numberToWord(String numberStr) {
    	if(numberStr == null) {
    		return "";
    	}
    	BigDecimal numberBigDec = new BigDecimal(numberStr);
        if (numberBigDec.signum() == -1) {
            return "Eksi " + numberToWord(numberBigDec.negate().toString());
        }
        if (numberBigDec.compareTo(BigDecimal.ZERO) == 0) {
            return "Sıfır";
        }

        String numberWord = "";
        int thousandsDigit = 0;
        String part="";
        int begin = numberStr.length();
        while (begin >= 3) {
            part = numberStr.substring(begin - 3, begin);
            if (thousandsDigit == 1 && (part.equals("001") || part.equals("01") || part.equals("1"))) {
            	numberWord = THOUSANDS[thousandsDigit++]  + numberWord;
            } else if(part.equals("000")){
            	thousandsDigit++;
            }
            else {
            	numberWord = getNumberWord(part)  + THOUSANDS[thousandsDigit++] + numberWord;
            }
            begin -= 3;
        }
        if (begin > 0) {
             part = numberStr.substring(0, begin);
            numberWord = getNumberWord(part) + THOUSANDS[thousandsDigit++] + numberWord;
        }
        return numberWord;
    }

    public static String getNumberWord(String s) {
        String word = "";
        int digits = 0;
        for (int i = s.length() - 1; i >= 0; i--) {
            int digit = s.charAt(i) - '0';
            if (digits == 0) {
            	word = ONES[digit] + word;
            } else if (digits % 3 == 1) {
            	word = TENS[digit] + word;
            } else if (digits % 3 == 2 && digit != 0) {
            	word = (digit != 1 ? ONES[digit] + "" : "") + "Yüz" + word;
            }
            digits++;
        }
        return word.trim();
    }

}
