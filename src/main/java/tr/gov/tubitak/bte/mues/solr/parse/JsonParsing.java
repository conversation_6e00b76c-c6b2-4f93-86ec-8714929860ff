package tr.gov.tubitak.bte.mues.solr.parse;

import java.io.InputStream;
import java.util.Map.Entry;
import java.util.Set;

import javax.json.Json;
import javax.json.JsonArray;
import javax.json.JsonObject;
import javax.json.JsonReader;
import javax.json.JsonValue;
import javax.json.stream.JsonParser;
import javax.json.stream.JsonParser.Event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class JsonParsing {

    private static final Logger logger   = LoggerFactory.getLogger(JsonParsing.class);

    private int                 numFound = 0;

    public Set<Entry<String, JsonValue>> parseDocs(final InputStream inputStream) {

        try (final JsonReader rdr = Json.createReader(inputStream)) {

            final JsonObject jobject = rdr.readObject();
            JsonObject jobject1 = jobject.getJsonObject("responseHeader");
            jobject1 = jobject.getJsonObject("response");
            final JsonArray jobject2 = jobject1.getJsonArray("docs");
            this.setNumFound(jobject1.getInt("numFound"));

            final JsonValue jobject4 = jobject2.get(0);
            jobject4.getValueType();
            final JsonObject jsonObject = (JsonObject) jobject4;
            for (final Entry<String, JsonValue> entry : jsonObject.entrySet()) {
                System.out.println(entry.getKey());
                System.out.println(entry.getValue());
            }
            return jsonObject.entrySet();
        }

    }

    public boolean parseResponseStatus(final InputStream inputStream) {

        try (final JsonReader rdr = Json.createReader(inputStream)) {
            // ulaştığını kontrol etmek yeterli Asyncçalışıyor zaten devamlı status 0 dönüyor.
            final JsonObject readObject = rdr.readObject();
            return "idle".equals(readObject.getString("status"));
        } catch (final Exception e) {
            logger.debug("parseResponseStatus", e.getMessage());
            return false;
        }

    }

    public JsonArray parseFacets(final InputStream inputStream, final String metadataName) {

        try (final JsonReader rdr = Json.createReader(inputStream)) {

            final JsonObject jobject = rdr.readObject();
            JsonObject jobject1 = jobject.getJsonObject("facets");
            if (jobject1.getInt("count") > 0) {

                jobject1 = jobject1.getJsonObject(metadataName);

                final JsonArray jsonArray = (jobject1.getJsonArray("buckets"));

                return jsonArray;
            } else {
                return null;
            }
        }
    }

    public String parseTraverse(final InputStream inputStream) {

        try (final JsonParser parser = Json.createParser(inputStream)) {

            while (parser.hasNext()) {
                final Event e = parser.next();
                if (e == Event.KEY_NAME) {
                    switch (parser.getString()) {
                        case "name":
                            parser.next();
                            System.out.print(parser.getString());
                            System.out.print(": ");
                        break;
                        case "message":
                            parser.next();
                            System.out.println(parser.getString());
                            System.out.println("---------");
                        break;
                    }
                }
            }
        }
        return "".toString();
    }

    public int getNumFound() {
        return this.numFound;
    }

    public void setNumFound(final int numFound) {
        this.numFound = numFound;
    }

}
