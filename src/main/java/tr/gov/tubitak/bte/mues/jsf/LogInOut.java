package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.web.util.SavedRequest;
import org.apache.shiro.web.util.WebUtils;
import org.omnifaces.util.Faces;
import org.omnifaces.util.Messages;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.session.AuditFacade;
import tr.gov.tubitak.bte.mues.shiro.OAuth2Token;

@Named
@RequestScoped
public class LogInOut
{

    private static final String WRONG_USERNAME_PASSWORD = "Hatalı kullanıcı adı/şifre!";

    private static final String USER_NOT_VERIFIED       = "Kullanıcı doğrulanamadı!";

    private static final Logger logger                  = LoggerFactory.getLogger(LogInOut.class);

    public static final String  HOME_URL                = "anasayfa?";

    public static final String  LOGIN_URL               = "giris?";

    public static final String  FACES_REDIRECT_TRUE     = "faces-redirect=true";

    @Inject
    private AuditFacade         auditFacade;

    @Inject
    private AbstractParameters  parameters;

    private String              username;

    private String              password;

    private boolean             remember;

    private String              code;

    private String              state;

    public void login() throws IOException {

        try {
            SecurityUtils.getSubject().login(new UsernamePasswordToken(this.username, this.password, this.remember));

            this.auditFacade.log(AuditEvent.SistemeGiris, SecurityUtils.getSubject().getPrincipal().toString() + AuditEvent.SistemeGiris.getLabel());
            this.redirectToOriginatorPage();
            // bir önceki oturumu siler
            if (this.isLoggedBefore(SecurityUtils.getSubject().getPrincipal().toString())) {
                this.parameters.getLoginMap().get(SecurityUtils.getSubject().getPrincipal().toString()).logout();
            }
            this.parameters.getLoginMap().put(SecurityUtils.getSubject().getPrincipal().toString(), SecurityUtils.getSubject());
        } catch (final AuthenticationException e) {

            logger.error("[login] : Hata : {}", e.getMessage(), e);
            this.auditFacade.log(AuditEvent.SistemeGirisHata, this.username + AuditEvent.SistemeGirisHata, WRONG_USERNAME_PASSWORD);
            Messages.addGlobalError(WRONG_USERNAME_PASSWORD);
        }
    }

    public void loginSSO() throws IOException {
        // if (this.parameters.getLogins().contains(this.username)) {
        // return;
        // }
        if (SecurityUtils.getSubject().isAuthenticated()) {
            this.redirectToOriginatorPage();
            return;
        }
        try {
            if (this.code == null) {
                return;
            }
            if (this.state == null) {
                logger.warn("[loginSSO] : Yetkisiz erişim denemesi!!!");
                return;
            }
            SecurityUtils.getSubject().login(new OAuth2Token(this.code));
            this.auditFacade.log(AuditEvent.SistemeGirisSSO, SecurityUtils.getSubject().getPrincipal().toString(), AuditEvent.SistemeGirisSSO.getLabel(), " E-devlet Şifresi ile sisteme giriş yaptı");
            this.redirectToOriginatorPage();
            // bir önceki oturumu siler
            if (this.isLoggedBefore(SecurityUtils.getSubject().getPrincipal().toString())) {
                this.parameters.getLoginMap().get(SecurityUtils.getSubject().getPrincipal().toString()).logout();
            }
            this.parameters.getLoginMap().put(SecurityUtils.getSubject().getPrincipal().toString(), SecurityUtils.getSubject());

        } catch (final AuthenticationException e) {
            logger.error("[loginSSO] : Hata : {} {} {}", this.username, e.getMessage(), e);
            this.auditFacade.log(AuditEvent.SistemeGirisSSOHata, this.username, USER_NOT_VERIFIED);
            Messages.addFlashGlobalError(USER_NOT_VERIFIED);
            Faces.redirect(HOME_URL + FACES_REDIRECT_TRUE);
        } catch (final Exception e) {
            logger.error("[loginSSO] : Hata : {} {} {}", this.username, e.getMessage(), e);
            this.auditFacade.log(AuditEvent.SistemeGirisSSOHata, this.username, USER_NOT_VERIFIED);
            Messages.addFlashGlobalError(USER_NOT_VERIFIED);

        }
    }

    private void redirectToOriginatorPage() {
        final SavedRequest savedRequest = WebUtils.getAndClearSavedRequest(Faces.getRequest());
        Faces.redirect(savedRequest != null ? savedRequest.getRequestUrl() : HOME_URL + FACES_REDIRECT_TRUE);
    }

    public void logout() {
        logger.debug("[logout] : Logout method called for principal {}", SecurityUtils.getSubject().getPrincipal());
        this.parameters.getLoginMap().remove(SecurityUtils.getSubject().getPrincipal().toString());

        SecurityUtils.getSubject().logout();
        Faces.invalidateSession();
        Faces.redirect(LOGIN_URL + FACES_REDIRECT_TRUE);
    }

    public void keepAlive() {
        if (logger.isInfoEnabled()) {
            logger.info("[keepAlive] : Oturum uzatıldı");
        }
        // This method is just used to make an ajax request for keep-alive purposes
        // It doesn't have a body
    }

    private boolean isLoggedBefore(final String key) {
        return this.parameters.getLoginMap().containsKey(key);
    }

    // getters and setters ....................................................

    public String getUsername() {
        return this.username;
    }

    public void setUsername(final String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public void setPassword(final String password) {
        this.password = password;
    }

    public boolean isRemember() {
        return this.remember;
    }

    public void setRemember(final boolean remember) {
        this.remember = remember;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(final String code) {
        this.code = code;
    }

    public String getState() {
        return this.state;
    }

    public void setState(final String state) {
        this.state = state;
    }

}
