package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.EserAltTur;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class EserAltTurFacade extends AbstractFacade<EserAltTur> {

    public EserAltTurFacade() {
        super(EserAltTur.class);
    }

    @Override
    public List<EserAltTur> filterByName(final String paramName, final String paramValue) {
        return this.em.createNamedQuery("EserAltTur.findByName", EserAltTur.class).setParameter(paramName, paramValue).getResultList();
    }

    public List<EserAltTur> findByNameAndAciklamaAndUstTurName(final String query) {
        return this.em.createNamedQuery("EserAltTur.findByNameAndAciklamaAndUstTur", EserAltTur.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
