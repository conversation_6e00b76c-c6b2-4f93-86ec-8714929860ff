package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Atolye;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class AtolyeFacade extends AbstractFacade<Atolye> {

    public AtolyeFacade() {
        super(Atolye.class);
    }

    public List<Atolye> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Atolye.findByNameAndAciklama", Atolye.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
