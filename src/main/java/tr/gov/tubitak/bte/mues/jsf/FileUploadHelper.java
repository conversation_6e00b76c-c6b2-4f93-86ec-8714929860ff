package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.nio.file.FileAlreadyExistsException;
import java.nio.file.Files;
import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.primefaces.model.file.UploadedFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.coobird.thumbnailator.Thumbnails;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesException;

@Named
@RequestScoped
public class FileUploadHelper implements Serializable {

    private static final long   serialVersionUID     = 3864015432061229280L;

    private static final Logger logger               = LoggerFactory.getLogger(FileUploadHelper.class);

    public static final int     USAGE_COPY_THRESHOLD = 500000;

    public static final String  BASE_FOLDER_MUES     = "images.baseFolder";

    public static final String  BASE_FOLDER_OMK      = "omkImages.baseFolder";

    public static final String  BASE_MUES            = "mues";

    public static final String  BASE_OMK             = "muesomk";

    public static final String  BASE_KAM             = "mueskam";

    public static final String  BASE_LAB             = "mueslab";

    @Inject
    private AbstractParameters  params;

    private byte[]              fotograf;

    public FileUploadHelper() {
        // blank constructor
    }

    public Path writeFileToTempFolder(final UploadedFile source) {
        this.fotograf = source.getContent();
        final String newFileName = UUID.randomUUID().toString();
        final Path tempPath = Paths.get(this.params.getTempDir(), newFileName);

        try (InputStream input = source.getInputStream()) {
            Files.copy(input, tempPath);

            if (logger.isDebugEnabled()) {
                logger.debug("[writeFileToTempFolder] : {} - {}", newFileName, tempPath);
            }

        } catch (final IOException e) {
            logger.error("[writeFileToTempFolder] : Hata : {}", e.getMessage(), e);
        }

        return tempPath;
    }

    public Path writeFileToTempFolderWithExtension(final UploadedFile source) {
        this.fotograf = source.getContent();
        final String extension = this.fetchFileExtension(source.getFileName());
        final String newFileName = UUID.randomUUID().toString() + "." + extension;
        final Path tempPath = Paths.get(this.params.getTempDir(), newFileName);

        try (InputStream input = source.getInputStream()) {
            Files.copy(input, tempPath);

            if (logger.isDebugEnabled()) {
                logger.debug("[writeFileToTempFolder] : {} - {}", newFileName, tempPath);
            }

        } catch (final IOException e) {
            logger.error("[writeFileToTempFolder] : Hata : {}", e.getMessage(), e);
        }

        return tempPath;
    }

    public String writeMainCopyToFile(final String tempFile, final FolderType type) {

        if (!tempFile.startsWith(this.params.getTempDir())) {
            return tempFile;
        }

        final Path tempFile1 = Paths.get(tempFile);
        if (tempFile1.getFileName() == null) {
            throw new MuesException("writeMainCopyToFile method called with null filename");
        }
        final Path finalAbsolutePath = this.params.generateMainCopyPath(tempFile1.getFileName().toString(), type);
        final String filePath = finalAbsolutePath.subpath(finalAbsolutePath.getNameCount() - 4, finalAbsolutePath.getNameCount()).toString().replace("\\", "/");
        try {
            this.createMainCopyWithFolders(tempFile1, type, finalAbsolutePath);
        } catch (final IOException e) {
            logger.error("[writeMainCopyToFile] : Hata : {}", e.getMessage(), e);
        } finally {
            try {
                Files.deleteIfExists(tempFile1);
            } catch (final IOException e) {
                logger.error("[writeMainCopyToFile] : Hata : {}", e.getMessage(), e);
            }
        }

        return filePath;
    }

    public List<String> constructMainCopyImagePath(final String tempFile, final FolderType type) {

        final List<String> fileList = new ArrayList<>();

        if (tempFile.startsWith(this.params.getTempDir())) {
            fileList.add(tempFile);
        } else {

            fileList.add(this.params.getAbsolutePath(tempFile, type).toString());
            if (type == FolderType.IMAGE_AK) {
                fileList.add(this.params.getImageUsageCopyPath(tempFile));
            }
        }

        return fileList;
    }

    public void deleteFilesPermanently(final List<String> filePaths) {
        for (final String filePath : filePaths) {
            try {
                Files.delete(Path.of(filePath));

            } catch (final IOException e) {
                logger.error("[deleteFilesPermanently] : Dosya silmede hata  : {}", e.getMessage(), e);
            }

        }
    }

    public void deleteDwgFilePermanently(final String filePath) {
        try {
            if (Files.exists(this.params.getAbsolutePath(filePath, FolderType.DWG))) {
                Files.delete(this.params.getAbsolutePath(filePath, FolderType.DWG));
            } else if (Files.exists(this.params.getAbsolutePath(filePath + ".dwg", FolderType.DWG))) {
                Files.delete(this.params.getAbsolutePath(filePath + ".dwg", FolderType.DWG));
            }

        } catch (final IOException e) {
            logger.error("[deleteFilesPermanently] : Dosya silmede hata  : {}", e.getMessage(), e);
        }
    }

    public void createMainCopyWithFolders(final Path tempFile, final FolderType type, final Path finalAbsolutePath) throws IOException {
        Files.createDirectories(finalAbsolutePath.getParent());
        Files.copy(tempFile, finalAbsolutePath);
        if (type == FolderType.IMAGE_AK) {
            this.generateImageUsageCopy(tempFile);
        }
        logger.info("[createMainCopyWithFolders] : {}", finalAbsolutePath);
    }

    private void generateImageUsageCopy(final Path tempFile) {
        if (tempFile.getFileName() == null) {
            throw new MuesException("generateUsageCopy method called with null filename");
        }
        final Path finalAbsoluteUCPath = this.params.generateFilePathViaFilename(tempFile.getFileName().toString(), FolderType.IMAGE_DK);

        try {
            this.createUsageCopyWithFolders(tempFile, finalAbsoluteUCPath);
        } catch (final IOException e) {
            logger.error("[generateUsageCopy] : Hata : {}", e.getMessage(), e);
        }
    }

    private void createUsageCopyWithFolders(final Path tempFile, final Path finalAbsoluteUCPath) throws IOException {
        Files.createDirectories(finalAbsoluteUCPath.getParent());
        Thumbnails.of(tempFile.toFile()).size(128, 128).outputFormat("png").toFile(finalAbsoluteUCPath.toString());
        logger.info("[generateUsageCopy] : {}", finalAbsoluteUCPath);
    }

    /***
     * Copies the sourceFile and its usageCopy to the targetApp
     * 
     * @param baseFolder
     * @param sourcePath
     * @param sourceApp
     * @param targetApp
     * @param type
     * @throws IOException
     */
    public void copyImage(final String path, final String sourceBaseFolder, final String sourceApp, final String targetApp) {
        try {
            final Path sourcePath = this.params.retrieveMainCopyURL(sourceBaseFolder, path);
            final Path targetPath = Paths.get(this.replaceLast(sourcePath.toString(), sourceApp, targetApp));

            Files.createDirectories(targetPath.getParent());
            Files.copy(sourcePath, targetPath);
            // replace ak with dk for the usageCopy path
            final Path usageCopyPath = Paths.get(targetPath.toString().replace(FolderType.IMAGE_AK.getFolderPath().split("/")[1], FolderType.IMAGE_DK.getFolderPath().split("/")[1]));
            this.createUsageCopyWithFolders(sourcePath, usageCopyPath);
            logger.info("[copyFile] : {}", targetPath);
        } catch (final FileAlreadyExistsException e) {
            logger.info("[copyFile] : FileAlreadyExists : {}", e.toString());
        } catch (final IOException | InvalidPathException e) {
            logger.error("[copyFile] : Hata : {} {}", e.getMessage(), e.toString());
        }

    }

    /***
     * Copies the sourceFile and its usageCopy to the targetApp
     * 
     * @param baseFolder
     * @param sourcePath
     * @param sourceApp
     * @param targetApp
     * @param type
     * @throws IOException
     */
    public void copyDocument(final String path, final String sourceBaseFolder, final String sourceApp, final String targetApp) {
        try {
            final Path sourcePath = this.params.retriveCopyURLWithFolderType(sourceBaseFolder, FolderType.OTHER, path);
            final Path targetPath = Paths.get(this.replaceLast(sourcePath.toString(), sourceApp, targetApp));

            Files.createDirectories(targetPath.getParent());
            Files.copy(sourcePath, targetPath);
            // replace ak with dk for the usageCopy path
            logger.info("[copyFile] : {}", targetPath);
        } catch (final FileAlreadyExistsException e) {
            logger.info("[copyFile] : FileAlreadyExists : {}", e.toString());
        } catch (final IOException | InvalidPathException e) {
            logger.error("[copyFile] : Hata : {} {}", e.getMessage(), e.toString());
        }
    }

    public void copyFile(final String path, final String sourceBaseFolder, final String sourceApp, final String targetApp, final FolderType folderType) {
        try {
            final Path sourcePath = this.params.retriveCopyURLWithFolderType(sourceBaseFolder, folderType, path);
            final Path targetPath = Paths.get(this.replaceLast(sourcePath.toString(), sourceApp, targetApp));

            Files.createDirectories(targetPath.getParent());
            Files.copy(sourcePath, targetPath);
            logger.info("[copyFile] : {}", targetPath);
        } catch (final FileAlreadyExistsException e) {
            logger.info("[copyFile] : FileAlreadyExists : {}", e.toString());
        } catch (final IOException | InvalidPathException e) {
            logger.error("[copyFile] : Hata : {}", e.getMessage(), e.toString());
        }
    }

    /***
     * Replaces the last regex in text with the given replacement
     * 
     * @param text
     * @param regex
     * @param replacement
     * @return replaced text
     */
    public String replaceLast(final String text, final String regex, final String replacement) {
        return text.replaceFirst("(?s)(.*)" + regex, "$1" + replacement);
    }

    private String fetchFileExtension(final String fileName) {
        final int dotIndex = fileName.lastIndexOf('.');
        if ((dotIndex > 0) && (dotIndex < (fileName.length() - 1))) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "";
    }

    // getter and setter ......................................................

    public byte[] getFotograf() {
        return this.fotograf;
    }

    public void setFotograf(final byte[] fotograf) {
        this.fotograf = fotograf;
    }

}
