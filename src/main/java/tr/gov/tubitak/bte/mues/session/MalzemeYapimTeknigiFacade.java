package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.MalzemeYapimTeknigi;

/**
 *
*
 */
@RequestScoped
public class MalzemeYapimTeknigiFacade extends AbstractFacade<MalzemeYapimTeknigi> {

    public MalzemeYapimTeknigiFacade() {
        super(MalzemeYapimTeknigi.class);
    }

    public List<MalzemeYapimTeknigi> findActiveGroupByMalzeme() {
        return this.em.createNamedQuery("MalzemeYapimTeknigi.findActiveGroupByMalzeme", MalzemeYapimTeknigi.class).getResultList();
    }

    public List<MalzemeYapimTeknigi> findActiveGroupByYapimTeknigi() {
        return this.em.createNamedQuery("MalzemeYapimTeknigi.findActiveGroupByYapimTeknigi", MalzemeYapimTeknigi.class).getResultList();
    }

}
