package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Malzeme;

/**
 *
*
 */
@RequestScoped
public class MalzemeFacade extends AbstractFacade<Malzeme> {

    public MalzemeFacade() {
        super(Malzeme.class);
    }

    public List<Malzeme> filterByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Malzeme.findByNameAndAciklama", Malzeme.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
