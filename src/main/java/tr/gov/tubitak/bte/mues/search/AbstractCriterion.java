package tr.gov.tubitak.bte.mues.search;

import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * 
 *         <PERSON><PERSON> t<PERSON>, SEARCH_CRITERIA tablosuyla eşleştirilmiş model sınıfı .
 *
 */
@Entity
@Table(name = "SearchCriteria")
@DiscriminatorColumn(discriminatorType = DiscriminatorType.INTEGER, name = "criterionType")
public abstract class AbstractCriterion extends AbstractCriterionSuper {

    private static final long serialVersionUID = 5050448009701860228L;

    public AbstractCriterion() {
    }

}
