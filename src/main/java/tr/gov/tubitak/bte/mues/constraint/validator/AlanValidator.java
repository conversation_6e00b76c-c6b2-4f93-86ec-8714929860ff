package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.List;

import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.primefaces.PrimeFaces;

import tr.gov.tubitak.bte.mues.constraint.ValidAlan;
import tr.gov.tubitak.bte.mues.jsf.AlanController;
import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.Bina;

public class AlanValidator implements ConstraintValidator<ValidAlan, Alan> {
	
	@Inject
    protected EntityManager em;
	
	@Inject
	private AlanController alanController;

	@Override
	public boolean isValid(final Alan alan, final ConstraintValidatorContext context) {
		boolean result = true;
		final Bina bina = this.alanController.getModel().getBina();
		
		if(alan.getKod() != null) {
			final List resultList = this.em.createNativeQuery("SELECT KOD FROM ALAN WHERE KOD = :kod AND BINA_ID = :binaId")
                    .setParameter("kod", alan.getKod())
                    .setParameter("binaId", bina.getId())
                    .getResultList();
			
			if((resultList != null && !resultList.isEmpty()) && 
					(alanController.getModel().getId() == null ||
					(alanController.getModel().getId() != null && 
					!alanController.getModel().getKod().equals(alan.getKod())))) {
				this.raiseFlag("Aynı alan kodu sistemde kayıtlıdır, lütfen kontrol ediniz.", context);
                this.addCssErrorClassToComponent("editorDialog:formEditor:alanKodu");
                result = false;
			}
		}
		
		return result;
	}
	
	private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

    private void addCssErrorClassToComponent(final String componentId) {
        PrimeFaces.current().executeScript("$(PrimeFaces.escapeClientId(\"" + componentId + "\")).addClass('required-input-field error')");
    }

}
