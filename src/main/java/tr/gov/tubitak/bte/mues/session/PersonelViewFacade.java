package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

/**
 * <AUTHOR>
 *
 */
@RequestScoped
public class PersonelViewFacade extends AbstractFacade<PersonelView> {

    public PersonelViewFacade() {
        super(PersonelView.class);
    }

    public List<PersonelView> findByIds(final List<Integer> ids) {
        return this.em.createNamedQuery("PersonelView.findByIds", PersonelView.class).setParameter("ids", MuesUtil.toIds(ids)).getResultList();
    }

    public List<PersonelView> findAllPersonnel() {
        return this.em.createNamedQuery("PersonelView.findAllPersonnel", PersonelView.class).getResultList();
    }

    public List<PersonelView> filterByFullNameAndAciklamaAndMuseumDirectorates(final String query, final List<Mudurluk> mudurlukList) {
        return this.em.createNamedQuery("PersonelView.findByFullNameAndAciklamaAndMuseumDirectorates", PersonelView.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("muzes", MuesUtil.toIds(mudurlukList))
                      .getResultList();
    }

    public List<PersonelView> filterByNameAndMudurlukAndAppType(final String query, final List<Mudurluk> mudurlukList, final ApplicationType appType) {
        return this.em.createNamedQuery("PersonelView.findByNameAndMudurlukAndAppType", PersonelView.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("muzes", MuesUtil.toIds(mudurlukList))
                      .setParameter("appType", Integer.valueOf(appType.getCode()))
                      .getResultList();
    }

    public List<PersonelView> findPersonelViewByAppType(final ApplicationType appType) {
        return this.em.createNamedQuery("PersonelView.findPersonelViewByAppType", PersonelView.class).setParameter("appType", Integer.valueOf(appType.getCode())).getResultList();
    }

}
