package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Birlestirme;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.session.BirlestirmeFacade;

@Named
@ViewScoped
public class BirlestirmeViewController extends AbstractController<Birlestirme> {

    private static final long serialVersionUID = 7446325332897316183L;

    @Inject
    private BirlestirmeFacade facade;

    public BirlestirmeViewController() {
        super(Birlestirme.class);
    }

    // getters and setters ....................................................

    @Override
    public BirlestirmeFacade getFacade() {
        return this.facade;
    }

    public void fixBirlestirmeById(final Eser eser) {
        final List<Birlestirme> findEagerByEser = this.facade.findEagerByEser(eser);
        if (!findEagerByEser.isEmpty()) {
            this.setModel(findEagerByEser.get(0));
        }
    }
}
