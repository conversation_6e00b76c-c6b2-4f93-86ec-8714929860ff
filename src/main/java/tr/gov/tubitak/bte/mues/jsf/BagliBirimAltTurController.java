package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.BagliBirimAltTur;
import tr.gov.tubitak.bte.mues.session.BagliBirimAltTurFacade;

@Named
@ViewScoped
public class BagliBirimAltTurController extends AbstractController<BagliBirimAltTur> {

    private static final long      serialVersionUID = -2272968981172922632L;

    @Inject
    private BagliBirimAltTurFacade facade;

    public BagliBirimAltTurController() {
        super(BagliBirimAltTur.class);
    }

    // getters and setters ....................................................

    @Override
    public BagliBirimAltTurFacade getFacade() {
        return this.facade;
    }

}
