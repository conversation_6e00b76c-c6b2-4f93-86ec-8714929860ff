package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.MeasureType;

/**
 *
*
 */
@RequestScoped
public class MeasureTypeFacade extends AbstractFacade<MeasureType> {

    public MeasureTypeFacade() {
        super(MeasureType.class);
    }

    public List<MeasureType> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("MeasureType.findByNameAndAciklama", MeasureType.class)
                      .setParameter("str", "%" + query + "%")
                      .getResultList();
    }

}
