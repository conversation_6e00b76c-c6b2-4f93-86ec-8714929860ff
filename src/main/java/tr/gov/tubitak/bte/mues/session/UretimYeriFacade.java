package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.UretimYeri;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class UretimYeriFacade extends AbstractFacade<UretimYeri> {

    public UretimYeriFacade() {
        super(UretimYeri.class);
    }

    public List<UretimYeri> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("UretimYeri.findByNameAndAciklama", UretimYeri.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
