/***********************************************************
 * SearchUtil.java - mues Projesi
 *
 * Kullanılan JRE: 1.8.0_91
 *
 * halis.yilboga - 20.Tem.2016
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2012©
 ***********************************************************/

package tr.gov.tubitak.bte.mues.util;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map.Entry;
import java.util.ResourceBundle;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import org.apache.poi.ss.formula.functions.T;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.beans.DocumentObjectBinder;
import org.apache.solr.client.solrj.impl.Http2SolrClient;
import org.apache.solr.client.solrj.response.FacetField;
import org.apache.solr.client.solrj.response.PivotField;
import org.apache.solr.client.solrj.response.RangeFacet;
import org.apache.solr.client.solrj.response.json.BucketJsonFacet;
import org.apache.solr.client.solrj.response.json.NestableJsonFacet;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrInputDocument;
import org.apache.solr.common.params.FacetParams;
import org.apache.solr.common.params.ModifiableSolrParams;
import org.apache.solr.common.util.NamedList;
import org.noggit.JSONUtil;

import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.search.SearchConstants;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

/**
 * The Class SearchUtil.
 */
@RequestScoped
public class SearchUtil extends ModifiableSolrParams implements Serializable {

    private static final String          RANGFORMAT       = "f.%s.%s";

    private static final long            serialVersionUID = 821753267941800892L;

    @Inject
    private transient ResourceBundle     bundle;

    @Inject
    private transient AbstractParameters parameters;

    /**
     * Yapıcı metot.
     */
    public SearchUtil() {
        // default constructor
    }

    public List<T> parsePivotResultToORMModel(final SolrDocumentList solrDocList, final List<String> parsePivotResult, final String[] fields, final Class<T> clazz) {

        for (final String string : parsePivotResult) {
            final SolrDocument sd = new SolrDocument();
            final String[] split = string.split(SearchConstants.SEPERATION_CHARS);

            for (int j = 0; j < split.length; j++) {

                sd.addField(fields[j], split[j]);
            }

            solrDocList.add(sd);
        }
        final DocumentObjectBinder documentObjectBinder = new DocumentObjectBinder();
        return documentObjectBinder.getBeans(clazz, solrDocList);
    }

    public static void parseJsonFacetResult(final NestableJsonFacet nestableJsonFacet, final StringBuilder outputBuilder, final List<String> outputItems) {
        if (nestableJsonFacet.getBucketBasedFacetNames().isEmpty() && (outputBuilder != null)) {
            outputItems.add(outputBuilder.toString());
        }
        for (final String bucketName : nestableJsonFacet.getBucketBasedFacetNames()) {
            for (final BucketJsonFacet bucket : nestableJsonFacet.getBucketBasedFacets(bucketName).getBuckets()) {
                final StringBuilder outputBuilder2 = new StringBuilder();
                if (outputBuilder != null) {
                    outputBuilder2.append(outputBuilder).append(SearchConstants.SEPERATION_CHARS);
                }
                outputBuilder2.append(String.valueOf(bucket.getVal()));
                for (final String statName : bucket.getStatNames()) {
                    // final Integer sumField = Integer.valueOf(bucket.getStatValue(statName).toString());
                    final Integer sumField = Double.valueOf(bucket.getStatValue(statName).toString()).intValue();
                    outputBuilder2.append(SearchConstants.SEPERATION_CHARS).append(sumField);
                }
                parseJsonFacetResult(bucket, outputBuilder2, outputItems);
            }
        }
    }

    public static List<String> parsePivotResult(final NamedList<List<PivotField>> pivotEntryList) {
        final List<String> outputItems = new ArrayList<>();

        for (final Entry<String, List<PivotField>> pivotEntry : pivotEntryList) {
            // TODO bu adımda eser durumları ile ilgili veriler gelecek süreç tamamlanmış veya devam eden veriler, süreç durumu esere de kaydedilmeli
            pivotEntry.getValue().forEach(pivotField -> renderOutput(new StringBuilder(), pivotField, outputItems));
        }
        final List<String> output = new ArrayList<>(outputItems);
        Collections.sort(output);

        return output;
    }

    public static List<String> parseFacetFieldResult(final List<FacetField> facetEntryList, final StringBuilder str) {
        final List<String> outputItems = new ArrayList<>();
        for (final FacetField facetField : facetEntryList) {
            @SuppressWarnings("unchecked")
            final List<FacetField.Count> counts = facetField.getValues();
            counts.forEach(xFacet -> facetFieldRenderOutput(new StringBuilder(str), xFacet, outputItems));
        }

        return outputItems;
    }

    public static List<String> parseFacetResult(final List<RangeFacet> facetPivot2, final StringBuilder str) {
        final List<String> outputItems = new ArrayList<>();

        for (final RangeFacet facetEntry : facetPivot2) {

            @SuppressWarnings("unchecked")
            final List<RangeFacet.Count> counts = facetEntry.getCounts();

            counts.forEach(xFacet -> renderOutput(new StringBuilder(str), xFacet, outputItems));
        }

        return outputItems;
    }

    private static void renderOutput(final StringBuilder sb, final PivotField field, final List<String> outputItems) {

        final String fieldValue = field.getValue() != null ? (field.getValue().toString()).trim() : null;
        final StringBuilder outputBuilder = new StringBuilder(sb);
        if (field.getPivot() != null) {
            if (outputBuilder.length() > 0) {
                outputBuilder.append(SearchConstants.SEPERATION_CHARS);
            }
            outputBuilder.append(fieldValue);
            field.getPivot().forEach(subField -> renderOutput(outputBuilder, subField, outputItems));
        } else {
            if (outputBuilder.length() > 0) {
                outputBuilder.append(SearchConstants.SEPERATION_CHARS);
            }
            outputBuilder.append(fieldValue);

            if (field.getFacetRanges() != null) {
                SearchUtil.parseFacetResult(field.getFacetRanges(), outputBuilder);
                field.getFacetRanges().get(0).getCounts().forEach(subField -> renderOutput(outputBuilder, subField, outputItems));
            } else {

                outputItems.add(outputBuilder.append(SearchConstants.SEPERATION_CHARS).append(field.getCount()).toString());
            }
        }

    }

    private static void renderOutput(final StringBuilder sb, final Object field, final List<String> outputItems) {

        final StringBuilder outputBuilder = new StringBuilder(sb);
        if (field != null) {
            final RangeFacet.Count cField = (RangeFacet.Count) (field);
            if (outputBuilder.length() > 0) {
                outputBuilder.append(SearchConstants.SEPERATION_CHARS);
            }
            outputBuilder.append(cField.getValue());
            final StringBuilder append = outputBuilder.append(SearchConstants.SEPERATION_CHARS).append(cField.getCount());
            outputItems.add(append.toString());

        }
    }

    private static void facetFieldRenderOutput(final StringBuilder sb, final Object field, final List<String> outputItems) {

        final StringBuilder outputBuilder = new StringBuilder(sb);
        if (field != null) {
            final FacetField.Count cField = (FacetField.Count) (field);
            if (outputBuilder.length() > 0) {
                outputBuilder.append(SearchConstants.SEPERATION_CHARS);
            }
            outputBuilder.append(cField.getName());
            final StringBuilder append = outputBuilder.append(SearchConstants.SEPERATION_CHARS).append(cField.getCount());
            outputItems.add(append.toString());

        }
    }

    public static String convertResponseToJson(final SolrDocumentList docs) {

        return JSONUtil.toJSON(docs); // this has the json documents
    }

    public void addDateRangeFacet(final String field, final String start, final String end, final String gap, final SolrQuery solrQuery, final String tag) {
        solrQuery.add(FacetParams.FACET_RANGE, tag + field);
        solrQuery.add(String.format(Locale.ROOT, RANGFORMAT, field, FacetParams.FACET_RANGE_START), start);
        solrQuery.add(String.format(Locale.ROOT, RANGFORMAT, field, FacetParams.FACET_RANGE_END), end);
        solrQuery.add(String.format(Locale.ROOT, RANGFORMAT, field, FacetParams.FACET_RANGE_GAP), gap);
        solrQuery.set(FacetParams.FACET, true);
    }

    /**
     * Uyarı metin mesajını döner.
     *
     * @return Uyarı metin mesajını
     */
    public String getWarningMessage() {
        return this.bundle.getString("main.warning");
    }

    /**
     * Boş arama uyarı mesajını döner.
     *
     * @return Boş arama uyarı mesajı
     */
    public String getEmptySearchMessage() {
        return this.bundle.getString("main.search.emptysearch");
    }

    /**
     * Ayarlanabilir genel arama seçenekleri listesini virgülle ayrılmış değerler olarak döner.
     *
     * @return Ayarlanabilir genel arama seçenekleri listesi
     */
    public String getPossibleSearchFields() {
        return this.bundle.getString("search.possiblefields");
    }

    /**
     * Minimum arama uzunluğu mesajını döner.
     *
     * @return minimum arama uzunluğu mesajı
     */
    public String getMinimumCharacterMessage() {
        return MessageFormat.format(this.bundle.getString("main.search.minimumlength"), SearchConstants.MIN_SEARCH_TEXT_LENGTH);
    }

    /**
     * Güvenlik seviyesi listesini virgülle ayrılmış değerler olarak döner.
     *
     * @return güvenlik seviyesi listesi
     */
    public String getSecurityList() {
        return this.bundle.getString("main.securitylist");
    }

    /**
     * Arama sonucu mesajını döner.
     *
     * @return arama sonucu mesajı
     */
    public String getSearchResultText() {
        return this.bundle.getString("search.resulttext");
    }

    public String getSelectOnlyOneCriterionWarningText() {
        return this.bundle.getString("search.detailed.onlyonecriterionwarning");
    }

    public String getSelectCriterionWarningText() {
        return this.bundle.getString("search.detailed.selectcriterionwarning");
    }

    public String getSelectCriterionWarningTextForGrouping() {
        return this.bundle.getString("search.detailed.selectcriterionwarningforgrouping");
    }

    public String getCriterionAlreadyExistsText() {
        return this.bundle.getString("search.detailed.criterionalreadyexits");
    }

    public String getEnterValidValuesText() {
        return this.bundle.getString("search.detailed.entervalidvalues");
    }

    public String getNoOperatorSelectedText() {
        return this.bundle.getString("search.detailed.nooperatorselected");
    }

    public String getNoMetadataSelectedText() {
        return this.bundle.getString("search.detailed.nometadataselected");
    }

    public String getWronglyDefinedMetadataMessage() {
        return this.bundle.getString("search.detailed.incorrectlydefinedmetadata");
    }

    public String getNotOperatorCriterionWarningText() {
        return this.bundle.getString("search.detailed.NOToperationcriterionwarning");
    }

    public String getSelectCriterionCountWarningText() {
        return this.bundle.getString("search.detailed.selectedcriterioncountwarning");
    }

    public String getBookmarkAlreadyExistWithSameNameWarningText() {
        return this.bundle.getString("search.detailed.bookmarkalreadyexits");
    }

    public String getBookmarkDeletedText() {
        return this.bundle.getString("search.bookmarkdeleted");
    }

    public String getBookmarkCreatedText() {
        return this.bundle.getString("search.bookmarkcreated");
    }

    public String getOperatorGreaterThanSign() {
        return this.bundle.getString("search.operators.GREATER_THAN.sign");
    }

    public String getOperatorLessThanSign() {
        return this.bundle.getString("search.operators.LESS_THAN.sign");
    }

    public String getSolrListCore() {
        return this.parameters.get("solr.lists.core.url");
    }

    public static void constructSolrInputDoc(final String key, final Object val, final SolrInputDocument document) {

        if (val instanceof String) {

            final String string = val.toString();
            if (string.contains(SearchConstants.SEPERATION_CHARS)) {

                final String[] split = string.split(SearchConstants.SEPERATION_CHARS);
                document.addField(key, split);

            } else {
                document.addField(key, val);
            }

        } else if (val instanceof BigDecimal) {
            document.addField(key, val.toString());
        } else if ((val instanceof Date) || (val instanceof Timestamp)) {
            document.addField(key, DateUtil.toUtcDate(val));
        } else {
            document.addField(key, val);
        }
        
        //recursive sekilde extend edilmemis fieldlar da indeksleniyor. 
        if (key.endsWith("_ci") || key.endsWith("_cim")) {
            String baseKey;

            if (key.endsWith("_ci")) {
                baseKey = key.substring(0, key.length() - 3); // Remove '_ci'
            } else { // key.endsWith("_cim")
                baseKey = key.substring(0, key.length() - 4); // Remove '_cim'
            }

            // Construct the Solr field for the base key
            SearchUtil.constructSolrInputDoc(baseKey, val, document);
        }
    }

    /**
     * Gets the solr help core for help page.
     *
     * @return the solr help core
     */
    public Http2SolrClient getSolrHelpCore() {
        return new Http2SolrClient.Builder(this.parameters.get("solr.help.core.url")).build();
    }

    public String getSolrServerUrl() {
        return this.parameters.get(SolrEnum.ENVANTER.getCoreKey());
    }

    public String getSolrServerUrl(final SolrEnum solrEnum) {
        return this.parameters.get(solrEnum.getCoreKey());
    }

}
