package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ESER_ZIMMET")
@NamedQuery(name = "EserZimmet.validateBeforeDelete", query = "SELECT ez FROM EserZimmet ez JOIN FETCH ez.eser e JOIN FETCH ez.zimmetPersonel zp LEFT JOIN FETCH e.mudurluk WHERE e.aktif = true AND e.silinmis = false AND ez.aktif = true AND ez.silinmis = false AND zp.id = :id AND e.mudurluk = :mudurluk")
public class EserZimmet extends EserZimmetSuper {

    private static final long serialVersionUID = -5646334757253460716L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ZIMMET_PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Personel          zimmetPersonel;

    public EserZimmet() {
        // default constructor
    }

    @Override
    public String toString() {
        Optional.ofNullable(this.getAciklama()).orElse("" + this.getId());
        String eserTitle = "";
        if (this.getEser() != null) {
            eserTitle = this.getEser().getTitle();
        }
        return Joiner.on(" ").skipNulls().join(eserTitle, this.getZimmetPersonel().getTitle());
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.getZimmetPersonel().getTitle()).orElse("" + this.getId());
    }

    public Personel getZimmetPersonel() {
        return this.zimmetPersonel;
    }

    public void setZimmetPersonel(final Personel zimmetPersonel) {
        this.zimmetPersonel = zimmetPersonel;
    }

}
