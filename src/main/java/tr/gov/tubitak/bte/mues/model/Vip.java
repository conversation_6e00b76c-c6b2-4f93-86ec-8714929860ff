/*
 * 
 */
package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 * The Class Vip.
 *
 *
 */
@Entity
@Table(name = "ONEMLI_KISI")
@NamedQuery(name = "Vip.findEagerById", query = "SELECT v FROM Vip v WHERE v.id = :id")
@NamedQuery(name = "Vip.findAll", query = "SELECT v FROM Vip v ORDER BY v.silinmis, v.aktif DESC, v.ad")
@NamedQuery(name = "Vip.findActive", query = "SELECT v FROM Vip v WHERE v.aktif = true AND v.silinmis = false ORDER BY v.ad")
@NamedQuery(name = "Vip.findByNameAndAciklama", query = "SELECT v FROM Vip v WHERE v.aktif = true AND v.silinmis = false AND (v.ad LIKE :str OR v.aciklama LIKE :str) ORDER BY v.ad, v.aciklama")
@NamedNativeQuery(name = "Vip.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER WHERE SILINMIS = 0 AND (ESERI_YAPTIRAN_ONEMLI_KISI_ID = :id OR ESERI_BAGISLAYAN_ONEMLI_KISI_ID = :id OR ESERI_YAPAN_ONEMLI_KISI_ID = :id OR ESERI_KULLANACAK_ONEMLI_KISI_ID = :id OR ESERI_KULLANAN_ONEMLI_KISI_ID = :id))")

public class Vip extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -1881190034640525526L;

    @Size(max = 50)
    @Column(name = "AD", unique = true, length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Vip() {
        // default constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
