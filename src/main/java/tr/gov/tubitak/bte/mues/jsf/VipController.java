package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Vip;
import tr.gov.tubitak.bte.mues.session.VipFacade;

@Named
@ViewScoped
public class VipController extends AbstractController<Vip> {

    private static final long serialVersionUID = 6851708291807550877L;

    @Inject
    private VipFacade         facade;

    public VipController() {
        super(Vip.class);
    }

    public List<Vip> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public VipFacade getFacade() {
        return this.facade;
    }

}
