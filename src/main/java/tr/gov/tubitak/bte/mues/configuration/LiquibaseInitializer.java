package tr.gov.tubitak.bte.mues.configuration;


import liquibase.Contexts;
import liquibase.LabelExpression;
import liquibase.Liquibase;
import liquibase.database.Database;
import liquibase.database.jvm.JdbcConnection;
import liquibase.resource.ClassLoaderResourceAccessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.naming.Context;
import javax.naming.InitialContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;
import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.DriverManager;
import java.util.Properties;

@WebListener
public class LiquibaseInitializer implements ServletContextListener {

    private static final Logger logger = LoggerFactory.getLogger(LiquibaseInitializer.class);

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        logger.info("Liquibase starting...");

        //        String enabled = System.getenv("LIQUIBASE_RUN_ON_INIT");
        String enabled = "true";
        logger.info("LIQUIBASE_RUN_ON_INIT: " + enabled);
        if (!"true".equalsIgnoreCase(enabled)) {
            logger.info("Liquibase DISABLED. Set LIQUIBASE_RUN_ON_INIT=true to enable.");
            return;
        }

        String activeProfile = "test";

        try (InputStream envIn = getClass().getClassLoader().getResourceAsStream("env.properties")) {
            if (envIn != null) {
                Properties envProps = new Properties();
                envProps.load(envIn);
                activeProfile = envProps.getProperty("env", "test");
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        logger.info("Active profile: " + activeProfile);
        String propFile = "config-profiles/liquibase_" + activeProfile + ".properties";

        Properties props = new Properties();
        try (InputStream is = getClass().getClassLoader().getResourceAsStream(propFile)) {
            if (is == null) throw new RuntimeException("Property file not found: " + propFile);
            props.load(is);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        String jndiName = props.getProperty("liquibase.jndi");
        logger.info(jndiName);

        try {
            DataSource ds = (DataSource) new InitialContext().lookup(jndiName);

            try (Connection connection = ds.getConnection()) {
                Database database = new liquibase.database.core.MSSQLDatabase();
                database.setConnection(new JdbcConnection(connection));

                Liquibase liquibase = new Liquibase("db/changelog/db.changelog-master.xml",
                        new ClassLoaderResourceAccessor(), database);

                liquibase.update(new Contexts(), new LabelExpression());

                logger.info("Liquibase has run successfully!");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        logger.info("Uygulama kapatılıyor...");
    }
}
