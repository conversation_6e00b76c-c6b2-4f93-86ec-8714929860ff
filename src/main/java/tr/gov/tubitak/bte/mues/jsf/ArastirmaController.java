package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Arastirma;
import tr.gov.tubitak.bte.mues.session.ArastirmaFacade;

@Named
@ViewScoped
public class ArastirmaController extends AbstractController<Arastirma> {

    private static final long serialVersionUID = 5674238528961050396L;

    @Inject
    private ArastirmaFacade   facade;

    public ArastirmaController() {
        super(Arastirma.class);
    }

    public List<Arastirma> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public ArastirmaFacade getFacade() {
        return this.facade;
    }

}
