package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "ArtifactPhotoAnnotation")
public class ArtifactPhotoAnnotation extends ArtifactPhotoAnnotationSuper {

    private static final long serialVersionUID = -8865829377154252544L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "photoId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserFotograf      eserFotograf;

    public ArtifactPhotoAnnotation() {
        // blank constructor
    }

    public EserFotograf getEserFotograf() {
        return this.eserFotograf;
    }

    public void setEserFotograf(final EserFotograf eserFotograf) {
        this.eserFotograf = eserFotograf;
    }

}
