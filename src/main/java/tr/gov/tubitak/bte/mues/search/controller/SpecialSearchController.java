package tr.gov.tubitak.bte.mues.search.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.response.FieldStatsInfo;
import org.apache.solr.client.solrj.util.ClientUtils;
import org.apache.solr.common.SolrDocument;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.SelectEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazySolrDataModel;
import tr.gov.tubitak.bte.mues.search.ComparisonOperatorEnum;
import tr.gov.tubitak.bte.mues.search.CompoundCriterion;
import tr.gov.tubitak.bte.mues.search.CriterionEnum;
import tr.gov.tubitak.bte.mues.search.CriterionFactory;
import tr.gov.tubitak.bte.mues.search.CriterionModel;
import tr.gov.tubitak.bte.mues.search.ICriterion;
import tr.gov.tubitak.bte.mues.search.LogicalOperatorEnum;
import tr.gov.tubitak.bte.mues.search.SearchQualifier;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.search.TextCriterion;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

@Named
@ViewScoped
@SearchQualifier
public class SpecialSearchController extends AbstractSpecialSearchController<ArtifactsSolrModel> {

    private static final long                   serialVersionUID             = 6763905455861013371L;

    private static final Logger                 logger                       = LoggerFactory.getLogger(SpecialSearchController.class);

    private CriterionModel                      mudurluk;

    private CriterionModel                      bagliBirim;

    private CriterionModel                      bina;

    private CriterionModel                      alan;

    private CriterionModel                      alanKonumu;

    private CriterionModel                      kayitTarihi;

    private CriterionModel                      eserEserId;

    private CriterionModel                      eserEskiEnvanterNo;

    private CriterionModel                      eserTasinirMalYonId;

    private CriterionModel                      eserAltTur;

    private CriterionModel                      eserEserOzelAdi;

    private CriterionModel                      kullaniciKullaniciAdi;

    private CriterionModel                      aktif;

    private CriterionModel                      eserSilinmis;

    private CriterionModel                      eserGenelAciklama;

    private CriterionModel                      tasinirMalYonetmeligiKodAd;

    private CriterionModel                      eserHareketEserGelisSekliAd;

    private CriterionModel                      donemAd;

    private CriterionModel                      kaziAd;

    private CriterionModel                      sahisAdSoyad;

    private CriterionModel                      yapimTeknigiMalzeme;

    private boolean                             disableMudurlukChange;

    private CriterionModel                      zimmetPersoneliAdSoyad;

    private String                              additionalQuery              = null;

    public static final String                  HIERARCHICAL_FACET_SEPARATOR = "/";

    private List<Metadata>                      statisticsFields;

    @Inject
    @SearchQualifier
    private SearchCriterionDefinitionController searchCriterionDefinitionController;

    @Inject
    public void setSearchController(@SearchQualifier
    final SearchController searchController) {
        this.searchController = searchController;
    }

    @Override
    public void makeInquery() {

        this.searchController.setSearchResultsVisible(true);
        this.searchController.resetTable();

        String sql = this.composeCriterions().getSql();

        if (this.additionalQuery != null) {
            sql = sql + this.getAdditionalQuery();
        }
        final SolrQuery solrQuery = new SolrQuery(sql);
        this.solrSearcher.combineStatisticsFields(solrQuery, this.statisticsFields);

        solrQuery.setSort("eserEserId", ORDER.asc);
        this.solrSearcher.setSolrQuery(solrQuery);

        this.searchController.setLazyDataModel(new LazySolrDataModel<>(this.solrSearcher, ArtifactsSolrModel.class, this.searchController));
    }

    public void handleMuzeMudurluguSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.mudurluk.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.mudurluk.getMetadata().getName(), this.mudurluk);
        this.setMudurluk(selected);
    }

    public void assignMudurlukValue(final String mudurlukName) {
        final Metadata metadata = this.mudurluk.getMetadata();
        final CriterionModel criterionModel = new CriterionModel(metadata, mudurlukName);
        criterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);

        final ICriterion criterion1 = CriterionFactory.createCriterion(criterionModel);

        final List<ICriterion> arrayList = new ArrayList<>();
        arrayList.add(criterion1);

        this.mudurluk.setCriteriaList(arrayList);
        this.selectedMetaDataMap.put(this.mudurluk.getMetadata().getName(), this.mudurluk);
        this.setDisableMudurlukChange(true);
        this.getMudurluk().setTextValue1(mudurlukName);
    }

    public void assignBagliBirimValue(final String birimName) {

        final Metadata metadata = this.bagliBirim.getMetadata();
        final CriterionModel criterionModel = new CriterionModel(metadata, birimName);
        criterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);

        final ICriterion criterion1 = CriterionFactory.createCriterion(criterionModel);

        final List<ICriterion> arrayList = new ArrayList<>();
        arrayList.add(criterion1);

        this.bagliBirim.setCriteriaList(arrayList);
        this.selectedMetaDataMap.put(this.bagliBirim.getMetadata().getName(), this.bagliBirim);
        this.getBagliBirim().setTextValue1(birimName);
    }

    public void handleBagliBirimSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.bagliBirim.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.bagliBirim.getMetadata().getName(), this.bagliBirim);
        this.bagliBirim = selected;
    }

    public void handleBinaSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.bina.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.bina.getMetadata().getName(), this.bina);
        this.bina = selected;
    }

    public void handleAlanSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.alan.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1().split(" ")[0]);
        this.selectedMetaDataMap.put(this.alan.getMetadata().getName(), this.alan);
        this.alan = selected;
    }

    public void handleAlanKonumuSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.alanKonumu.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.alanKonumu.getMetadata().getName(), this.alanKonumu);
        this.alanKonumu = selected;
    }

    public void handleEserGrubuSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.eserTasinirMalYonId.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.eserTasinirMalYonId.getMetadata().getName(), this.eserTasinirMalYonId);
        this.eserTasinirMalYonId = selected;

    }

    public void handleEserAltTuruSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.eserAltTur.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.eserAltTur.getMetadata().getName(), this.eserAltTur);
        this.eserAltTur = selected;

    }

    public void handleEserHareketEserGelisSekliAdSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.eserHareketEserGelisSekliAd.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.eserHareketEserGelisSekliAd.getMetadata().getName(), this.eserHareketEserGelisSekliAd);
        this.eserHareketEserGelisSekliAd = selected;
    }

    public void handleKullaniciSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.kullaniciKullaniciAdi.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.kullaniciKullaniciAdi.getMetadata().getName(), this.kullaniciKullaniciAdi);
        this.kullaniciKullaniciAdi = selected;
    }

    public void handleDonemAdSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.donemAd.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.donemAd.getMetadata().getName(), this.donemAd);
        this.donemAd = selected;
    }

    public void handleKaziAdSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.kaziAd.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.kaziAd.getMetadata().getName(), this.kaziAd);
        this.kaziAd = selected;
    }

    public void handleSahisSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.sahisAdSoyad.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.sahisAdSoyad.getMetadata().getName(), this.sahisAdSoyad);
        this.sahisAdSoyad = selected;
    }

    public void handleYapimTeknigiMalzemeSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.yapimTeknigiMalzeme.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.yapimTeknigiMalzeme.getMetadata().getName(), this.yapimTeknigiMalzeme);
        this.yapimTeknigiMalzeme = selected;
    }

    public void handleZimmetPersoneliAdSoyadSelect(final SelectEvent<CriterionModel> event) {
        final CriterionModel selected = event.getObject();
        ((TextCriterion) this.zimmetPersoneliAdSoyad.getCriteriaList().get(0)).setTextValue1(selected.getTextValue1());
        this.selectedMetaDataMap.put(this.zimmetPersoneliAdSoyad.getMetadata().getName(), this.zimmetPersoneliAdSoyad);
        this.zimmetPersoneliAdSoyad = selected;
    }

    @Override
    public ICriterion composeCriterions() {

        final ICriterion compoundCriterion = super.composeCriterions();

        if (!this.searchController.isSilinmis()) {
            this.eserSilinmis.setBooleanValue(0);
        } else {
            this.aktif.setBooleanValue(0);
            this.eserSilinmis.setBooleanValue(1);
        }

        final ICriterion criterion1 = CriterionFactory.createCriterion(this.kayitTarihi);
        final ICriterion criterion2 = CriterionFactory.createCriterion(this.eserGenelAciklama);
        final ICriterion criterion3 = CriterionFactory.createCriterion(this.aktif);
        final ICriterion criterion4 = CriterionFactory.createCriterion(this.eserEskiEnvanterNo);
        this.eserEserId.setTextValue1((MuesUtil.decimalPointFormatter(this.eserEserId.getTextValue1())));
        final ICriterion criterion5 = CriterionFactory.createCriterion(this.eserEserId);
        final ICriterion criterion6 = CriterionFactory.createCriterion(this.eserEserOzelAdi);
        final ICriterion criterion7 = CriterionFactory.createCriterion(this.eserSilinmis);

        final CompoundCriterion compoundCriterion2 = new CompoundCriterion();
        final CompoundCriterion compoundCriterion3 = new CompoundCriterion();

        compoundCriterion2.setChildren(new ArrayList<>());
        compoundCriterion2.getChildren().add(criterion1);
        compoundCriterion2.getChildren().add(criterion2);
        compoundCriterion2.getChildren().add(criterion3);
        compoundCriterion2.getChildren().add(criterion4);
        compoundCriterion2.getChildren().add(criterion5);
        compoundCriterion2.getChildren().add(criterion6);
        compoundCriterion2.getChildren().add(criterion7);
        compoundCriterion2.setLogicalOperator(LogicalOperatorEnum.AND);

        compoundCriterion3.setChildren(new ArrayList<>());
        compoundCriterion3.setLogicalOperator(LogicalOperatorEnum.AND);
        compoundCriterion3.getChildren().add(compoundCriterion);
        compoundCriterion3.getChildren().add(compoundCriterion2);

        ICriterion compoundCriterion4 = this.searchController.addQuerySearchPermision(compoundCriterion3);

        compoundCriterion4 = ((SearchController) this.searchController).addQueryMyArtifacts(compoundCriterion4);

        return compoundCriterion4;
    }

    @Override
    @PostConstruct
    public void init() {

        // init olmasi icin kasti olarak eklenmistir.
        this.searchController.toString();
        Metadata metadata = this.solrSearcher.getMetaDataMap().get("eserYaratmaZamani");
        final List<Metadata> defaultColumns = new ArrayList<>();
        final List<Metadata> unnecessaryColumns = new ArrayList<>();

        this.kayitTarihi = new CriterionModel();
        this.kayitTarihi.setMetadata(metadata);
        this.kayitTarihi.setType(metadata.getDataTypeId());
        this.kayitTarihi.setCondition(ComparisonOperatorEnum.BETWEEN);

        metadata = this.solrSearcher.getMetaDataMap().get("eserEserId");
        this.eserEserId = new CriterionModel(metadata, "", "");
        this.eserEserId.setType(metadata.getDataTypeId());
        this.eserEserId.setCondition(ComparisonOperatorEnum.CONTAINS);

        metadata = this.solrSearcher.getMetaDataMap().get("eserEskiEnvanterNo");
        this.eserEskiEnvanterNo = new CriterionModel(metadata, "", "");
        this.eserEskiEnvanterNo.setType(metadata.getDataTypeId());
        this.eserEskiEnvanterNo.setCondition(ComparisonOperatorEnum.CONTAINS);
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("tasinirMalYonetmeligiKodAd");
        this.tasinirMalYonetmeligiKodAd = new CriterionModel(metadata, "", "");
        this.tasinirMalYonetmeligiKodAd.setType(metadata.getDataTypeId());
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("eserTurAd");
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("eserAltTurAd");
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("eserEserOzelAdi");
        this.eserEserOzelAdi = new CriterionModel(metadata, "", "");
        this.eserEserOzelAdi.setType(metadata.getDataTypeId());
        this.eserEserOzelAdi.setCondition(ComparisonOperatorEnum.CONTAINS);
        defaultColumns.add(metadata);

        if (!this.disableMudurlukChange) {
            metadata = this.solrSearcher.getMetaDataMap().get("muzeMudurluguAd");
            this.mudurluk = new CriterionModel(metadata, "", "");
            this.mudurluk.setType(metadata.getDataTypeId());

            final List<String> mudurlukList = this.sessionBean.fetchMudurlukByApplicationTypeAndPermission(ApplicationType.ENVANTER, "sorgu:eserEnvanter")
                                                              .stream()
                                                              .map(Mudurluk::getAd)
                                                              .collect(Collectors.toList());
            if ((mudurlukList != null) && (mudurlukList.size() == 1)) {
                this.mudurluk.setTextValue1(mudurlukList.get(0));
            }
        } else {
            this.selectedMetaDataMap.put(this.mudurluk.getMetadata().getName(), this.mudurluk);
        }
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("bagliBirimAd");
        this.bagliBirim = new CriterionModel(metadata, "", "");
        this.bagliBirim.setType(metadata.getDataTypeId());
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("kullaniciKullaniciAdi");
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("eserOnayZamani");
        defaultColumns.add(metadata);

        metadata = this.solrSearcher.getMetaDataMap().get("binaAd");
        this.bina = new CriterionModel(metadata, "", "");
        this.bina.setType(metadata.getDataTypeId());

        metadata = this.solrSearcher.getMetaDataMap().get("alanAd");
        this.alan = new CriterionModel(metadata, "", "");
        this.alan.setType(metadata.getDataTypeId());

        metadata = this.solrSearcher.getMetaDataMap().get("alanKonumuAd");
        this.alanKonumu = new CriterionModel(metadata, "", "");
        this.alanKonumu.setType(metadata.getDataTypeId());

        metadata = this.solrSearcher.getMetaDataMap().get("eserTasinirMalYonId");
        this.eserTasinirMalYonId = new CriterionModel(metadata, "", "");
        this.eserTasinirMalYonId.setType(metadata.getDataTypeId());

        metadata = this.solrSearcher.getMetaDataMap().get("kullaniciKullaniciAdi");
        this.kullaniciKullaniciAdi = new CriterionModel(metadata, "", "");
        this.kullaniciKullaniciAdi.setType(metadata.getDataTypeId());

        metadata = this.solrSearcher.getMetaDataMap().get("eserGenelAciklama");
        this.eserGenelAciklama = new CriterionModel(metadata);
        this.eserGenelAciklama.setType(metadata.getDataTypeId());
        this.eserGenelAciklama.setCondition(ComparisonOperatorEnum.CONTAINS);

        metadata = this.solrSearcher.getMetaDataMap().get("aktif");
        this.aktif = (new CriterionModel(CriterionEnum.BOOLEAN.getCode(), metadata, 0));
        this.aktif.setType(metadata.getDataTypeId());
        this.aktif.setBooleanValue(1);

        metadata = this.solrSearcher.getMetaDataMap().get("eserSilinmis");
        this.eserSilinmis = (new CriterionModel(CriterionEnum.BOOLEAN.getCode(), metadata, 0));
        this.eserSilinmis.setType(metadata.getDataTypeId());

        metadata = this.solrSearcher.getMetaDataMap().get("eserAltTurAd");
        this.eserAltTur = new CriterionModel(metadata, "", "");
        this.eserAltTur.setType(metadata.getDataTypeId());
        this.eserAltTur.setCondition(ComparisonOperatorEnum.STARTSWITH);

        metadata = this.solrSearcher.getMetaDataMap().get("donemAd");
        this.donemAd = new CriterionModel(metadata, "", "");
        this.donemAd.setType(metadata.getDataTypeId());
        this.donemAd.setCondition(ComparisonOperatorEnum.CONTAINS);

        metadata = this.solrSearcher.getMetaDataMap().get("kaziAd");
        this.kaziAd = new CriterionModel(metadata, "", "");
        this.kaziAd.setType(metadata.getDataTypeId());
        this.kaziAd.setCondition(ComparisonOperatorEnum.CONTAINS);

        metadata = this.solrSearcher.getMetaDataMap().get("sahisAdSoyad");
        this.sahisAdSoyad = new CriterionModel(metadata, "", "");
        this.sahisAdSoyad.setType(metadata.getDataTypeId());
        this.sahisAdSoyad.setCondition(ComparisonOperatorEnum.CONTAINS);

        metadata = this.solrSearcher.getMetaDataMap().get("yapimTeknigiMalzeme");
        this.yapimTeknigiMalzeme = new CriterionModel(metadata, "", "");
        this.kaziAd.setType(metadata.getDataTypeId());
        this.kaziAd.setCondition(ComparisonOperatorEnum.CONTAINS);

        metadata = this.solrSearcher.getMetaDataMap().get("eserHareketEserGelisSekliAd");
        this.eserHareketEserGelisSekliAd = new CriterionModel(metadata, "", "");
        this.eserHareketEserGelisSekliAd.setType(metadata.getDataTypeId());

        this.restoreColumns(defaultColumns, unnecessaryColumns);
        metadata = this.solrSearcher.getMetaDataMap().get("eserZimmetPersonelAdSoyad");
        this.zimmetPersoneliAdSoyad = new CriterionModel(metadata, "", "");
        this.zimmetPersoneliAdSoyad.setType(metadata.getDataTypeId());
        this.zimmetPersoneliAdSoyad.setCondition(ComparisonOperatorEnum.CONTAINS);

        this.statisticsFields = new ArrayList<>();
        this.statisticsFields.add(this.solrSearcher.getMetaDataMap().get("eserKiymet"));
    }

    public void restoreColumns(final List<Metadata> defaultColumns, final List<Metadata> unnecessaryColumns) {
        if ((this.searchCriterionDefinitionController.getPreviousSelectedList() != null) && !(this.searchCriterionDefinitionController.getPreviousSelectedList().isEmpty())) {
            // Load previous columns after clicking the reset button
            this.searchController.setSelectedList(this.searchCriterionDefinitionController.getPreviousSelectedList());
        } else {
            // Load the default column
            final LinkedList<Metadata> tempMetadataList = new LinkedList<>();
            this.searchController.setSelectedList(tempMetadataList);
            for (final Metadata metadata : defaultColumns) {
                this.searchController.getSelectedList().add(metadata);
                this.searchCriterionDefinitionController.getPreviousSelectedList().add(metadata);
            }
        }
        for (final Metadata metadata : unnecessaryColumns) {
            for (final Metadata metadataTemp : this.searchCriterionDefinitionController.getList()) {
                if (metadata.getId().equals(metadataTemp.getId())) {
                    this.searchCriterionDefinitionController.getList().remove(metadataTemp);
                    break;
                }
            }
        }
        this.searchCriterionDefinitionController.createDynamicColumns();
    }

    public List<CriterionModel> searchForMuzeMudurlugu(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "muzeMudurluguAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<SolrDocument> searchOnHelpDoc(final String query) throws SolrServerException, IOException {
        final SolrQuery solrQuery = new SolrQuery("menuItemContent:*" + ClientUtils.escapeQueryChars(query) + "*");
        final SolrSearcher solrSearcher = new SolrSearcher();
        solrSearcher.setServer(this.searchUtil.getSolrHelpCore());
        solrSearcher.setSolrQuery(solrQuery);

        return solrSearcher.fetchRecordFromSolrHelpCoreByQuery(solrQuery);
    }

    public List<CriterionModel> searchForEserAltTur(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "eserAltTurAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForBagliBirim(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "bagliBirimAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForBina(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "binaAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForAlan(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "alanAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForAlanKonumu(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "alanKonumuAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForEserAltTuru(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "eserTasinirMalYonId", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForEserTasinirMalYonId(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "tasinirMalYonetmeligiKodAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForEserKullaniciKullaniciAdi(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "kullaniciKullaniciAdi", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForEserHareketEserGelisSekliAd(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "eserHareketEserGelisSekliAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForDonemAd(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "donemAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForKaziAd(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "kaziAd", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForSahis(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "sahisAdSoyad", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForMalzemeAd(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "yapimTeknigiMalzeme", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    public List<CriterionModel> searchForZimmetPersoneliAdSoyad(final String query) throws IOException, SolrServerException {
        return this.autocompleteForMetadata(query, "eserZimmetPersonelAdSoyad", ComparisonOperatorEnum.CONTAINS, CriterionEnum.TEXT.getCode());
    }

    @Override
    public void resetSearchFields() {
        logger.debug("[resetSearchFields] : ");
        this.selectedMetaDataMap = new HashMap<>();
        this.init();
        this.searchController.resetSearchFields();
    }

    public void restoreSelectionMode() {
        if (!this.searchController.isSilinmis()) {
            this.aktif.setBooleanValue(1);
        }
    }

    public CriterionModel getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final CriterionModel mudurluk) {
        this.mudurluk = mudurluk;
    }

    public CriterionModel getBagliBirim() {
        return this.bagliBirim;
    }

    public void setBagliBirim(final CriterionModel bagliBirim) {
        this.bagliBirim = bagliBirim;
    }

    public CriterionModel getBina() {
        return this.bina;
    }

    public void setBina(final CriterionModel bina) {
        this.bina = bina;
    }

    public CriterionModel getAlan() {
        return this.alan;
    }

    public void setAlan(final CriterionModel alan) {
        this.alan = alan;
    }

    public CriterionModel getAlanKonumu() {
        return this.alanKonumu;
    }

    public void setAlanKonumu(final CriterionModel alanKonumu) {
        this.alanKonumu = alanKonumu;
    }

    public CriterionModel getKayitTarihi() {
        return this.kayitTarihi;
    }

    public void setKayitTarihi(final CriterionModel kayitTarihi) {
        this.kayitTarihi = kayitTarihi;
    }

    public CriterionModel getEserEskiEnvanterNo() {
        return this.eserEskiEnvanterNo;
    }

    public void setEserEskiEnvanterNo(final CriterionModel eserEskiEnvanterNo) {
        this.eserEskiEnvanterNo = eserEskiEnvanterNo;
    }

    public CriterionModel getEserAltTur() {
        return this.eserAltTur;
    }

    public void setEserAltTur(final CriterionModel eserAltTur) {
        this.eserAltTur = eserAltTur;
    }

    public CriterionModel getKullaniciKullaniciAdi() {
        return this.kullaniciKullaniciAdi;
    }

    public void setKullaniciKullaniciAdi(final CriterionModel kullaniciKullaniciAdi) {
        this.kullaniciKullaniciAdi = kullaniciKullaniciAdi;
    }

    public CriterionModel getEserEserId() {
        return this.eserEserId;
    }

    public void setEserEserId(final CriterionModel eserEserId) {
        this.eserEserId = eserEserId;
    }

    public CriterionModel getEserTasinirMalYonId() {
        return this.eserTasinirMalYonId;
    }

    public void setEserTasinirMalYonId(final CriterionModel eserTasinirMalYonId) {
        this.eserTasinirMalYonId = eserTasinirMalYonId;
    }

    public CriterionModel getEserEserOzelAdi() {
        return this.eserEserOzelAdi;
    }

    public void setEserEserOzelAdi(final CriterionModel eserEserOzelAdi) {
        this.eserEserOzelAdi = eserEserOzelAdi;
    }

    public CriterionModel getAktif() {
        return this.aktif;
    }

    public void setAktif(final CriterionModel aktif) {
        this.aktif = aktif;
    }

    public CriterionModel getEserSilinmis() {
        return this.eserSilinmis;
    }

    public void setEserSilinmis(final CriterionModel eserSilinmis) {
        this.eserSilinmis = eserSilinmis;
    }

    public CriterionModel getEserGenelAciklama() {
        return this.eserGenelAciklama;
    }

    public void setEserGenelAciklama(final CriterionModel eserGenelAciklama) {
        this.eserGenelAciklama = eserGenelAciklama;
    }

    public CriterionModel getTasinirMalYonetmeligiKodAd() {
        return this.tasinirMalYonetmeligiKodAd;
    }

    public void setTasinirMalYonetmeligiKodAd(final CriterionModel tasinirMalYonetmeligiKodAd) {
        this.tasinirMalYonetmeligiKodAd = tasinirMalYonetmeligiKodAd;
    }

    public CriterionModel getEserHareketEserGelisSekliAd() {
        return this.eserHareketEserGelisSekliAd;
    }

    public void setEserHareketEserGelisSekliAd(final CriterionModel eserHareketEserGelisSekliAd) {
        this.eserHareketEserGelisSekliAd = eserHareketEserGelisSekliAd;
    }

    public boolean isDisableMudurlukChange() {
        return this.disableMudurlukChange;
    }

    public void setDisableMudurlukChange(final boolean disableMudurlukChange) {
        this.disableMudurlukChange = disableMudurlukChange;
    }

    public CriterionModel getDonemAd() {
        return this.donemAd;
    }

    public void setDonemAd(final CriterionModel donemAd) {
        this.donemAd = donemAd;
    }

    public CriterionModel getKaziAd() {
        return this.kaziAd;
    }

    public void setKaziAd(final CriterionModel kaziAd) {
        this.kaziAd = kaziAd;
    }

    public CriterionModel getYapimTeknigiMalzeme() {
        return this.yapimTeknigiMalzeme;
    }

    public void setYapimTeknigiMalzeme(final CriterionModel yapimTeknigiMalzeme) {
        this.yapimTeknigiMalzeme = yapimTeknigiMalzeme;
    }

    public CriterionModel getSahisAdSoyad() {
        return this.sahisAdSoyad;
    }

    public void setSahisAdSoyad(final CriterionModel sahisAdSoyad) {
        this.sahisAdSoyad = sahisAdSoyad;
    }

    public String getAdditionalQuery() {
        return this.additionalQuery;
    }

    public void setAdditionalQuery(final String additionalQuery) {
        this.additionalQuery = additionalQuery;
    }

    public CriterionModel getZimmetPersoneliAdSoyad() {
        return this.zimmetPersoneliAdSoyad;
    }

    public void setZimmetPersoneliAdSoyad(final CriterionModel zimmetPersoneliAdSoyad) {
        this.zimmetPersoneliAdSoyad = zimmetPersoneliAdSoyad;
    }

    public Map<String, FieldStatsInfo> getStatisticsList() {
        return this.solrSearcher.getStatisticsList();
    }
}
