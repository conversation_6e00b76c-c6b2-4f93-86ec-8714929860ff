package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "EP_EserSerh")
public class EserProposalSerh extends EserSerhSuper {

    private static final long serialVersionUID = 5287644200337308054L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalSerh() {
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
