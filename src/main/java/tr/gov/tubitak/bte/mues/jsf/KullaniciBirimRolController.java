package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.shiro.SecurityUtils;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.DualListModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.rules.AbstractKullaniciBirimRolOperations;
import tr.gov.tubitak.bte.mues.session.KullaniciBirimRolFacade;
import tr.gov.tubitak.bte.mues.session.KullaniciFacade;
import tr.gov.tubitak.bte.mues.session.PersonelFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.DateUtil;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.audits.Audit;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

@Named
@ViewScoped
public class KullaniciBirimRolController extends AbstractController<KullaniciBirimRol> implements SingleFileUploadable {

    private static final long              serialVersionUID    = -6183170734493961347L;

    private static final Logger            logger              = LoggerFactory.getLogger(KullaniciBirimRolController.class);

    public static final int                TYPE_USER           = 1;

    public static final int                TYPE_PERSONEL       = 2;

    public static final int                TYPE_EXTERNAL_USER  = 3;

    @Inject
    private AbstractKullaniciBirimRolOperations kullaniciRolOperations;

    @Inject
    private KullaniciBirimRolFacade        facade;

    @Inject
    private FileUploadHelper               fileUploadHelper;

    @Inject
    private MudurlukController             mudurlukController;

    @Inject
    private PersonelFacade                 personelFacade;

    @Inject
    private SessionBean                    sessionBean;

    @Inject
    private KullaniciFacade                kullaniciFacade;

    @Inject
    private AbstractParameters             parameter;

    @Inject
    private RolController                          rolController;

    private Personel                       personel;

    private PersonelView                   personelview;

    private int                            from                = TYPE_USER;

    private boolean                        permanentAssignment = true;

    private DualListModel<Mudurluk>        muzeDualListModel;

    private transient List<Object[]>       deletedKbr;

    private Audit                          deletedAuditKBR;

    public KullaniciBirimRolController() {
        super(KullaniciBirimRol.class);
    }

    @Override
    public void newRecord() {
        super.newRecord();

        this.personel = null;
        this.from = TYPE_USER;
        this.permanentAssignment = true;

        if (SecurityUtils.getSubject().hasRole("sistem_birim_sorumlusu")) {
        	//sistem birim yoneticisi tarafindan yetki verilmis ise yetkinin mudurlugu kullanicin mudurlugu olabilir. 
            this.getModel().setMudurluk(this.sessionBean.filterByPermission("kullaniciyetki:listele"));
        }
    }

    @Override
    public void showDetail(final KullaniciBirimRol item) {
        this.personel = null;
        this.from = TYPE_USER;
        this.permanentAssignment = (item.getAssignmentType() == null);
        super.showDetail(item);
    }

    @Override
    public DBOperationResult create() {
    	
        DBOperationResult result = DBOperationResult.failure("");
        final List<AbstractEntity> entities = new ArrayList<>();
        
        
        this.getModel().setDateCreated(DateUtil.getCurrentDate());
        this.getModel().setCreatedBy(this.sessionBean.getCurrentUser().getPersonelView());
        
        
        
        if (renderMultyMuseum()) {

            for (final Mudurluk each : this.getMuzeDualListModel().getTarget()) {
                this.getModel().setMudurluk(each);
                this.getModel().setId(null);
                result = this.creationalDetails(entities);
                //TODO should be removed.
                this.from = TYPE_USER;
            }
            this.resetSelection();

        } else {
            result = this.creationalDetails(entities);
        }

        if (result.isSuccess()) {
            Optional.ofNullable("Kayıt başarıyla eklendi.").ifPresent(x -> MuesUtil.showMessage(x, FacesMessage.SEVERITY_INFO));
            logger.info("[create] : {}", "Kayıt başarıyla eklendi.");
        }

        return result;
    }

    public DBOperationResult updateDetails(final List<AbstractEntity> entities) {
    	// görev baslama zamani gelecek bir tarih ise kaydi pasif olarak olustur (sonradan timer ile aktif olacak)
        if ((this.getModel().getAssignmentStartTime() != null) && this.getModel().getAssignmentStartTime().after(DateUtil.trimDate(new Date()))) {
        	this.getModel().setAktif(false);
        	this.getModel().setSilinmis(true);
        }
        final Boolean avaliable = this.kullaniciRolOperations.modulSpecificCreationCheckAndDoCreationalDetails(this.getModel(),entities, from, personel);
        if (avaliable != Boolean.FALSE) {
        	this.writeToPermanentFolder();
        	final DBOperationResult result = this.facade.update(entities);
        	return this.afterUpdate(result, "Güncelleme gercekleçti");
        }
        return DBOperationResult.failure("");
    }
    
    public DBOperationResult creationalDetails(final List<AbstractEntity> entities) {
    	
    	// görev baslama zamani gelecek bir tarih ise kaydi pasif olarak olustur (sonradan timer ile aktif olacak)
        if ((this.getModel().getAssignmentStartTime() != null) && this.getModel().getAssignmentStartTime().after(DateUtil.trimDate(new Date()))) {
        	this.getModel().setAktif(false);
        	this.getModel().setSilinmis(true);
        }
    	
    	final Boolean avaliable = this.kullaniciRolOperations.modulSpecificCreationCheckAndDoCreationalDetails(this.getModel(),entities, from, personel);


        if (avaliable != Boolean.FALSE) {

        	this.writeToPermanentFolder();
            final DBOperationResult result = this.facade.update(entities);

            if (result.isSuccess()) {
                if (this.items != null) {
                    this.items.add(0, this.getFacade().findEagerById(this.getModel().getId()));

                    if (this.filteredValues != null) {
                        this.filteredValues.add(0, this.getFacade().findEagerById(this.getModel().getId()));
                    }
                }

            } else {
                MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
            }
            return result;
        }
        return DBOperationResult.failure("");
    }

    @Override
    public DBOperationResult update() {
        final List<AbstractEntity> entities = new ArrayList<>();
        
        if (permanentAssignment) {
        	this.getModel().setAssignmentType(null);
        	this.getModel().setAssignmentStartTime(null);
        	this.getModel().setAssignmentEndTime(null);
        }
        
        return this.updateDetails(entities);
    }

    @Override
    public void toggleActive(final KullaniciBirimRol entity) {
        this.setModel(entity);
        this.getModel().setAktif(!entity.getAktif());
        this.update();
        // değişiklik yapılan kullanıcının session'ı silinir.
        if (this.parameter.getLoginMap().containsKey(entity.getKullanici().getKullaniciAdi())) {
            this.parameter.getLoginMap().get(entity.getKullanici().getKullaniciAdi()).logout();
            this.parameter.getLoginMap().remove(entity.getKullanici().getKullaniciAdi());
        }
    }

    @Override
    public void delete() {
        this.setDeleteDescription(this.kullaniciRolOperations.delete(this.getModel(),this.getDeleteDescription()));
    }

    @Override
    public void undelete() {
        super.undelete();
        final List<KullaniciBirimRol> kbrByUser = this.facade.fetchKbrByUser(this.getModel().getKullanici());

        // kullanici tarafindan rolun tekrar aktive edildigini belirtmek için ilgili field'in set edilmesi

        if (kbrByUser.size() == 1) {
            final Kullanici kullanici = this.kullaniciFacade.findById(this.getModel().getKullanici().getId());
            kullanici.setAktif(true);
            kullanici.setSilinmis(false);

            if (!this.kullaniciFacade.update(kullanici).isSuccess()) {
                super.delete();
            }
        }
    }

    

    public boolean isDisabled(final AbstractEntity entity) {
        return entity.getSilinmis();
    }

    public void resetSelection() {
        if (this.muzeDualListModel != null) {
            this.getMuzeDualListModel().getTarget().clear();
            this.getMuzeDualListModel().getSource().clear();
        }
    }

    public boolean renderMultyMuseum() {
        return !this.isNewMode() && this.getModel().getRol() == null && this.getModel().getRol().isMultiple()&& this.getModel().getRol().isRenderMuseum();
    }
   
    public boolean renderMuseumSelect() {
    	return  (this.getModel().getRol() != null && ((this.getModel().getRol().isRenderMuseum())||(!this.getModel().getRol().isMultiple()&& !this.getModel().getRol().isRenderMuseum())));
    }
    

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setAprovePermitDocPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getAprovePermitDocPath() != null) {
            this.getModel().setAprovePermitDocPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getAprovePermitDocPath(), FolderType.OTHER));
        }
    }

    @SuppressWarnings("unchecked")
    public void assignDeletedKBRAudit(final KullaniciBirimRol kbr) {

        this.deletedKbr = AuditReaderFactory.get(this.getFacade().getEM())
                                            .createQuery()
                                            .forRevisionsOfEntity(KullaniciBirimRol.class, false, true)
                                            .add(AuditEntity.id().eq(kbr.getId()))
                                            .add(AuditEntity.revisionProperty("revType").eq(AuditEvent.KullaniciBirimRolSilme.getCode()))
                                            .setMaxResults(1)
                                            .getResultList();

        this.deletedAuditKBR = (Audit) this.deletedKbr.get(0)[1];

    }

    public List<PersonelView> filterByFullNameFromPersonelPool(final String query) {
        return this.kullaniciRolOperations.filterByFullNameFromPersonelPool(query);
    }

    public void fetchSelectedPersonelFromPool() {
        final ApplicationType applicationType = ApplicationType.parse(this.parameter.get(ApplicationType.class.getSimpleName()));

        this.personelview.setApplicationType(Integer.parseInt(applicationType.getCode()));
        this.personelview.setMudurluk(null);
        this.personelview.setMudurlukAd(null);
        final DBOperationResult result = this.personelFacade.update(this.personelview);
        if (result.isSuccess()) {
            Optional.ofNullable("Personel Havuzdan çekildi.").ifPresent(x -> MuesUtil.showMessage(x, FacesMessage.SEVERITY_INFO));
        } else {
            Optional.ofNullable(result.getMessage()).ifPresent(x -> MuesUtil.showMessage(x, FacesMessage.SEVERITY_ERROR));
        }

        this.personelview = null;

    }

    public List<Rol> filterRolForExternalUser(final String query) {
    	 if (this.from == TYPE_EXTERNAL_USER) {
            return this.rolController.filterRoleByNameAndRank(query).stream().filter(x -> (x.getRank() == 40) || (x.getRank() == 60)).collect(Collectors.toList());
        }
        return this.rolController.filterRoleByNameAndRank(query);
    }

    // getters and setters ....................................................

    @Override
    public KullaniciBirimRolFacade getFacade() {
        return this.facade;
    }

    @Override
    public List<KullaniciBirimRol> getItems() {
        if (this.items == null) {

            final boolean showRolesWithoutMuseums = (SecurityUtils.getSubject().hasRole("SUPERUSER")
                                     || SecurityUtils.getSubject().hasRole("sistem_merkez_sorumlusu")
                                     || SecurityUtils.getSubject().hasRole("KVMGM_merkez_yoneticisi")
                                     || SecurityUtils.getSubject().hasRole("muzeler_daire_baskani")
                                     || SecurityUtils.getSubject().hasRole("ktb_merkez_yoneticisi")
                                     || SecurityUtils.getSubject().hasRole("bakanlik"));

            final List<Mudurluk> mudurlukList = this.sessionBean.fetchMudurlukListByPermission("kullaniciyetki:listele");

            if ((mudurlukList != null) && !mudurlukList.isEmpty()) {
                this.items = this.getFacade().findByMudurluk(mudurlukList, showRolesWithoutMuseums);
            }
        }

        return this.items;
    }

    public DualListModel<Mudurluk> getMuzeDualListModel() {
        if (this.muzeDualListModel == null) {
            this.muzeDualListModel = new DualListModel<>();
        }
        if (this.muzeDualListModel.getSource().isEmpty()) {
            this.muzeDualListModel.getSource().addAll(this.mudurlukController.getItems());
        }
        return this.muzeDualListModel;
    }

    public void setMuzeDualListModel(final DualListModel<Mudurluk> muzeDualListModel) {
        this.muzeDualListModel = muzeDualListModel;
    }

    public Personel getPersonel() {
        return this.personel;
    }

    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public Integer getFrom() {
        return this.from;
    }

    public void setFrom(final Integer from) {
        this.from = from;
    }

    public Boolean getPermanentAssignment() {
        return this.permanentAssignment;
    }

    public void setPermanentAssignment(final Boolean permanentAssignment) {
        this.permanentAssignment = permanentAssignment;
    }

    public List<Object[]> getDeletedKbr() {
        return this.deletedKbr;
    }

    public void setDeletedKbr(final List<Object[]> deletedKbr) {
        this.deletedKbr = deletedKbr;
    }

    public Audit getDeletedAuditKBR() {
        return this.deletedAuditKBR;
    }

    public void setDeletedAuditKBR(final Audit deletedAuditKBR) {
        this.deletedAuditKBR = deletedAuditKBR;
    }

    public PersonelView getPersonelview() {
        return this.personelview;
    }

    public void setPersonelview(final PersonelView personelview) {
        this.personelview = personelview;
    }

}
