package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Measure;
import tr.gov.tubitak.bte.mues.session.MeasureFacade;

@Named
@ViewScoped
public class MeasureController extends AbstractController<Measure> {

    private static final long serialVersionUID = -4395431535926072896L;

    @Inject
    private MeasureFacade     facade;

    public MeasureController() {
        super(Measure.class);
    }

    // getters and setters ....................................................

    @Override
    public MeasureFacade getFacade() {
        return this.facade;
    }

}
