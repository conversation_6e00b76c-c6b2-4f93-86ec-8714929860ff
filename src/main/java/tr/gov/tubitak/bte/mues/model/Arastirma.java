package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ARASTIRMA")
@NamedQueries({
                @NamedQuery(name = "Arastirma.findEagerById", query = "SELECT a FROM Arastirma a JOIN FETCH a.arastirmaTur WHERE a.id = :id"),
                @NamedQuery(name = "Arastirma.findAll", query = "SELECT a FROM Arastirma a JOIN FETCH a.arastirmaTur t ORDER BY a.silinmis, a.aktif DESC,t.ad asc"),
                @NamedQuery(name = "Arastirma.findActive", query = "SELECT a FROM Arastirma a JOIN FETCH a.arastirmaTur t WHERE a.aktif = true AND a.silinmis = false ORDER BY t.ad asc"),
                @NamedQuery(name = "Arastirma.findByNameAndAciklama", query = "SELECT a FROM Arastirma a WHERE a.aktif = true AND a.silinmis = false AND (a.ad LIKE :str OR a.aciklama LIKE :str) ORDER BY a.ad, a.aciklama") })
@NamedNativeQuery(name = "Arastirma.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_HAREKET WHERE SILINMIS = 0 AND ARASTIRMA_ID = :id)")
public class Arastirma extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 4736832619543254841L;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ARASTIRMA_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private ArastirmaTur      arastirmaTur;

    public Arastirma() {
    }

    // getters and setters ....................................................

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public ArastirmaTur getArastirmaTur() {
        return this.arastirmaTur;
    }

    public void setArastirmaTur(final ArastirmaTur arastirmaTurId) {
        this.arastirmaTur = arastirmaTurId;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
