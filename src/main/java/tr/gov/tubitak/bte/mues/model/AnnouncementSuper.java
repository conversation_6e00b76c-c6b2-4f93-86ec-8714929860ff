package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "Announcement.findAll", query = "SELECT a FROM Announcement a LEFT JOIN FETCH a.rol r WHERE ('SUPERUSER' IN :rolList OR r.kod in :rolList) ORDER BY a.silinmis, a.aktif DESC")
@NamedQuery(name = "Announcement.findEagerById", query = "SELECT a FROM Announcement a JOIN FETCH a.rol WHERE a.id = :id")
@NamedQuery(name = "Announcement.findByMaxId", query = "SELECT a FROM Announcement a WHERE a.id = (SELECT MAX(an.id) FROM Announcement an)")

public class AnnouncementSuper extends AbstractEntity {

    private static final long serialVersionUID = 311096275077294371L;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "rol", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Rol               rol;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "documentPath", length = 150)
    private String            documentPath;

    public AnnouncementSuper() {
        // blank constructor
    }

    public Rol getRol() {
        return this.rol;
    }

    public void setRol(final Rol rol) {
        this.rol = rol;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String getTitle() {
        return this.rol.toString();
    }

    public String getDocumentPath() {
        return this.documentPath;
    }

    public void setDocumentPath(final String documentPath) {
        this.documentPath = documentPath;
    }

}
