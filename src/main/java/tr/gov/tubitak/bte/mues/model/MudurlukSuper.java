package tr.gov.tubitak.bte.mues.model;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.constraint.Email;
import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.MuseumCode;
import tr.gov.tubitak.bte.mues.constraint.ValidName;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Audited
@MappedSuperclass

@NamedQuery(name = "Mudurluk.findAll", query = "SELECT m FROM Mudurluk m JOIN FETCH m.il JOIN FETCH m.ilce ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "Mudurluk.findActive", query = "SELECT m FROM Mudurluk m JOIN FETCH m.il JOIN FETCH m.ilce WHERE m.aktif = true AND m.silinmis = false ORDER BY m.ad")
@NamedQuery(name = "Mudurluk.findByName", query = "SELECT m FROM Mudurluk m JOIN FETCH m.il JOIN FETCH m.ilce WHERE (m.ad LIKE :str OR m.aciklama LIKE :str) ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "Mudurluk.filterByFullNameAndAciklamaPreventDuplicate", query = "SELECT m FROM Mudurluk m LEFT JOIN FETCH m.il LEFT JOIN FETCH m.ilce WHERE m.id NOT IN :ids AND m.aktif = true AND m.silinmis = false AND (m.ad LIKE :str OR m.aciklama LIKE :str) ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "Mudurluk.findByKod", query = "SELECT m FROM Mudurluk m WHERE m.kod = :kod")

public class MudurlukSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -1121870182876513222L;

    @MuseumCode
    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String            kod;

    @ValidName
    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ADRES", length = 150)
    private String            adres;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_1", length = 25)
    private String            telefon1;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_2", length = 25)
    private String            telefon2;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_3", length = 25)
    private String            telefon3;

    @Size(max = 25)
    @Column(name = "FAKS_1", length = 25)
    private String            faks1;

    @Size(max = 25)
    @Column(name = "FAKS_2", length = 25)
    private String            faks2;

    @Pattern(regexp = "^[a-zA-Z0-9_\\.+-]+", message = "Geçerli bir e-posta giriniz!")
    @Size(max = 25)
    @Column(name = "EPOSTA_KTB", length = 25)
    private String            epostaKtb;

    @Email
    @Size(max = 50)
    @Column(name = "EPOSTA_KURUMSAL", length = 50)
    private String            epostaKurumsal;

    @Size(max = 50)
    @Column(name = "WEB_SAYFASI", length = 50)
    private String            webAdres;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "FOTOGRAF_PATH", length = 250)
    private String            fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Min(value = -90)
    @Max(value = 90)
    @Column(name = "ENLEM")
    private BigDecimal        enlem;

    @Min(value = -180)
    @Max(value = 180)
    @Column(name = "BOYLAM")
    private BigDecimal        boylam;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "pdfPath", length = 250)
    private String            pdfPath;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "dwgPath", length = 250)
    private String            dwgPath;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                il;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ILCE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Ilce              ilce;

    @Column(name = "totalArtifactCount")
    private Integer           totalArtifactCount;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "LOGO_PATH", length = 250)
    private String            logoPath;

    @Column(name = "isVisibleFromMap")
    private Boolean           isVisibleFromMap;

    @Column(name = "dateCreated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              dateCreated;

    @Column(name = "dateUpdated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              dateUpdated;

    private transient Integer artifactCount;

    private transient Integer commissionCount;

    public MudurlukSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public MudurlukSuper(final Integer id) {
        this.setId(id);
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAdres() {
        return this.adres;
    }

    public void setAdres(final String adres) {
        this.adres = adres;
    }

    public String getTelefon1() {
        return this.telefon1;
    }

    public void setTelefon1(final String telefon1) {
        this.telefon1 = telefon1;
    }

    public String getTelefon2() {
        return this.telefon2;
    }

    public void setTelefon2(final String telefon2) {
        this.telefon2 = telefon2;
    }

    public String getTelefon3() {
        return this.telefon3;
    }

    public void setTelefon3(final String telefon3) {
        this.telefon3 = telefon3;
    }

    public String getFaks1() {
        return this.faks1;
    }

    public void setFaks1(final String faks1) {
        this.faks1 = faks1;
    }

    public String getFaks2() {
        return this.faks2;
    }

    public void setFaks2(final String faks2) {
        this.faks2 = faks2;
    }

    public String getEpostaKtb() {
        return this.epostaKtb;
    }

    public void setEpostaKtb(final String epostaKtb) {
        if (epostaKtb != null) {
            this.epostaKtb = epostaKtb.toLowerCase();
        }
    }

    public String getEpostaKurumsal() {
        return this.epostaKurumsal;
    }

    public void setEpostaKurumsal(final String epostaKurumsal) {
        if (epostaKurumsal != null) {
            this.epostaKurumsal = epostaKurumsal.toLowerCase();
        }
    }

    public String getWebAdres() {
        return this.webAdres;
    }

    public void setWebAdres(final String webAdres) {
        this.webAdres = webAdres;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getPdfPath() {
        return this.pdfPath;
    }

    public void setPdfPath(final String pdfPath) {
        this.pdfPath = pdfPath;
    }

    public String getDwgPath() {
        return this.dwgPath;
    }

    public void setDwgPath(final String dwgPath) {
        this.dwgPath = dwgPath;
    }

    public BigDecimal getEnlem() {
        return this.enlem;
    }

    public void setEnlem(final BigDecimal enlem) {
        this.enlem = enlem;
    }

    public BigDecimal getBoylam() {
        return this.boylam;
    }

    public void setBoylam(final BigDecimal boylam) {
        this.boylam = boylam;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public Ilce getIlce() {
        return this.ilce;
    }

    public void setIlce(final Ilce ilce) {
        this.ilce = ilce;
    }

    public Integer getArtifactCount() {
        return this.artifactCount;
    }

    public void setArtifactCount(final Integer artifactCount) {
        this.artifactCount = artifactCount;
    }

    public Integer getTotalArtifactCount() {
        return this.totalArtifactCount;
    }

    public void setTotalArtifactCount(final Integer totalArtifactCount) {
        this.totalArtifactCount = totalArtifactCount;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.ad, MuesUtil.surroundWithParanthesis(this.kod), (!this.getAktif() && this.getSilinmis()) ? " (Silinmiş) " : "");
    }

    @Override
    public int hashCode() {
        return super.hashCode();
    }

    @Override
    public boolean equals(final Object obj) {
        if (!super.equals(obj)) {
            return false;
        }
        final Mudurluk mudurlugu = (Mudurluk) obj;

        return (mudurlugu.getAd() != null) && this.getAd().equals(mudurlugu.getAd());
    }

    public Integer getProgressRegardingMax() {
        if ((this.artifactCount == null) || (this.totalArtifactCount == null) || (this.artifactCount == 0) || (this.totalArtifactCount == 0)) {
            return 0;
        }
        int progressValue = (100 * this.artifactCount) / Optional.ofNullable(this.totalArtifactCount).orElse(this.artifactCount);
        if (progressValue > 100) {
            progressValue = 100;
        }
        return progressValue;
    }

    public Integer getCommissionCount() {
        return this.commissionCount;
    }

    public void setCommissionCount(final Integer commissionCount) {
        this.commissionCount = commissionCount;
    }

    public String getLogoPath() {
        return this.logoPath;
    }

    public void setLogoPath(final String logoPath) {
        this.logoPath = logoPath;
    }

    public Boolean getIsVisibleFromMap() {
        return this.isVisibleFromMap;
    }

    public void setIsVisibleFromMap(final Boolean isVisibleFromMap) {
        this.isVisibleFromMap = isVisibleFromMap;
    }

    public Date getDateCreated() {
        return this.dateCreated;
    }

    public void setDateCreated(final Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Date getDateUpdated() {
        return this.dateUpdated;
    }

    public void setDateUpdated(final Date dateUpdated) {
        this.dateUpdated = dateUpdated;
    }

}
