package tr.gov.tubitak.bte.mues.session;

import java.io.Serializable;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceException;
import javax.persistence.Query;
import javax.persistence.Table;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.MatchMode;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.search.SearchConstants;

public abstract class LazyLoadFromDBFacade<T> implements Serializable {

    private static final long        serialVersionUID = 2212930399323158577L;

    private String                   filterclause;

    protected final transient Logger logger;

    private final Class<T>           entityClass;

    // We expect this information to be filled in by the calling class to prevent
    // unauthorized data access.
    // If you want everything to be visible, an empty string should be sent.
    private String                   customQuery      = SearchConstants.SEPERATION_CHARS;

    protected LazyLoadFromDBFacade(final Class<T> entityClass) {
        this.entityClass = entityClass;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @SuppressWarnings("unchecked")
    public List<T> fetchData(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        final StringBuilder queryStr = new StringBuilder();

        final Table table = this.entityClass.getAnnotation(Table.class);

        queryStr.append("SELECT * FROM ").append(table.name()).append(" o ");

        queryStr.append(this.constructFilterClause(filterBy));

        queryStr.append(this.sortClause(sortBy));

        final Query nativeQuery = this.getEM().createNativeQuery(queryStr.toString(), this.entityClass.getSimpleName());

        filterBy.entrySet().stream().filter(x -> (x.getValue().getFilterValue() != null)).forEach(x -> this.setParameter(nativeQuery, x));

        List<T> result = nativeQuery.setFirstResult(first).setMaxResults(pageSize).getResultList();

        if (result == null) {
            result = Collections.emptyList();
        }
        return result;
    }

    public Integer count(final Map<String, FilterMeta> filters) {
        final StringBuilder queryStr = new StringBuilder();
        this.filterclause = null;

        final Table table = this.entityClass.getAnnotation(Table.class);
        if (table == null) {
            throw new IllegalStateException("Entity class is not annotated with @Table");
        }

        queryStr.append("SELECT COUNT(*) FROM ").append(table.name()).append(" o");

        queryStr.append(this.constructFilterClause(filters));

        final Query nativeQuery = this.getEM().createNativeQuery(queryStr.toString());
        filters.entrySet().stream().filter(x -> x.getValue().getFilterValue() != null).forEach(x -> this.setParameter(nativeQuery, x));

        try {
            return (Integer) nativeQuery.getSingleResult();
        } catch (final NoResultException e) {
            return 0;
        } catch (final PersistenceException e) {
            throw new RuntimeException("Error executing count query", e);
        }
    }

    public Integer count(final Map<String, FilterMeta> filters, final String tableName) {
        final StringBuilder queryStr = new StringBuilder();
        this.filterclause = null;
        queryStr.append("SELECT  COUNT(*) FROM ").append(tableName).append(" o ");
        queryStr.append(this.constructFilterClause(filters));
        final Query nativeQuery = this.getEM().createNativeQuery(queryStr.toString());
        filters.entrySet().stream().filter(x -> (x.getValue().getFilterValue() != null)).forEach(x -> this.setParameter(nativeQuery, x));
        return (Integer) nativeQuery.getSingleResult();
    }

    protected String appendCustomConditions(final String filterQuery) {

        if (filterQuery.isEmpty() && (this.customQuery.isEmpty() || this.customQuery.equals(SearchConstants.SEPERATION_CHARS))) {
            return SearchConstants.EMPTY;

        } else if (filterQuery.isEmpty() && !this.customQuery.isEmpty()) {
            return SearchConstants.WHERE + this.customQuery;

        } else if (this.customQuery.isEmpty() && !filterQuery.isEmpty()) {
            return SearchConstants.WHERE + filterQuery;

        } else {
            return SearchConstants.WHERE + this.customQuery + SearchConstants.AND + filterQuery;
        }
    }

    protected String constructFilterClause(final Map<String, FilterMeta> filters) {

        this.filterclause = filters.entrySet()
                                   .stream()
                                   .filter(x -> ((x.getValue().getFilterValue() != null) && !x.getValue().getFilterValue().toString().equals("[]")))
                                   .map(x -> this.appendLike(x.getKey(), x.getValue()))
                                   .collect(Collectors.joining(SearchConstants.AND));

        return this.appendCustomConditions(this.filterclause);

    }

    protected String sortClause(final Map<String, SortMeta> multiSortMeta) {

        final StringBuilder str = new StringBuilder();

        // TODO: halis.yilboga test et

        final String str2 = multiSortMeta.entrySet()
                                         .stream()
                                         .filter(x -> x.getValue() != null)
                                         .map(x -> x.getValue().getField() + (SortOrder.ASCENDING.name().equals(x.getValue().getOrder().name()) ? " ASC " : " DESC "))
                                         .collect(Collectors.joining(SearchConstants.COMMA_LITERAL, " ORDER BY ", ""));

        multiSortMeta.entrySet().stream().forEach(Map.Entry::getKey);

        for (final Iterator<String> it = multiSortMeta.keySet().iterator(); it.hasNext();) {
            final String sortProperty = it.next();
            final SortMeta sortValue = multiSortMeta.get(sortProperty);

            if (str.length() > 0) {
                str.append(",");
            }
            str.append(sortValue.getField());

            if (SortOrder.ASCENDING.name().equals(sortValue.getOrder().name())) {
                str.append(" ASC ");

            } else {
                str.append(" DESC ");
            }
        }

        str.insert(0, " ORDER BY ");
        this.logger.debug(str.toString());
        return str.toString();
    }

    protected String appendLike(final String param, final FilterMeta filterMeta) {
        final StringBuilder str = new StringBuilder();
        if (param.contains("Time")) {
            final String fromPart = filterMeta.getFilterValue().toString().substring(0, filterMeta.getFilterValue().toString().indexOf("~"));
            final String toPart = filterMeta.getFilterValue().toString().substring(filterMeta.getFilterValue().toString().indexOf("~") + 1);

            if (!fromPart.isEmpty()) {
                str.append(param + ">=CONVERT(DATETIME, ").append(":fromPart, 105)");
            }
            if (!fromPart.isEmpty() && !toPart.isEmpty()) {
                str.append(" AND ");
            }
            if (!toPart.isEmpty()) {
                str.append(param + "<=CONVERT(DATETIME, ").append(":toPart, 105)");
            }
        } else if (filterMeta.getMatchMode() == MatchMode.IN) {
            if (!((List<?>) filterMeta.getFilterValue()).isEmpty()) {
                str.append("o." + param).append(" IN :").append(param);
            }
        } else if (param.contains("Id")) {
            str.append(String.format("o.%s=:%s", param, param));
        } else {
            if (filterMeta.getMatchMode() == MatchMode.EQUALS) {
                str.append("o." + param).append("=:").append(param);
            } else {
                str.append("o." + param).append(" LIKE :").append(param);
            }
        }

        return str.toString();
    }

    protected void setParameter(final Query nativeQuery, final Entry<String, FilterMeta> x) {
        final String param = x.getKey();
        final String filterValue = x.getValue().getFilterValue().toString();

        if (param.contains("Time")) {
            final String fromPart = filterValue.substring(0, filterValue.indexOf("~"));
            final String toPart = filterValue.substring(filterValue.indexOf("~") + 1);
            if (!fromPart.isEmpty()) {
                nativeQuery.setParameter("fromPart", fromPart);
            }
            if (!toPart.isEmpty()) {
                nativeQuery.setParameter("toPart", toPart);
            }
        } else if (x.getValue().getMatchMode() == MatchMode.IN) {
            if (!((List<?>) x.getValue().getFilterValue()).isEmpty()) {
                nativeQuery.setParameter(x.getKey(), x.getValue().getFilterValue());
            }
        } else if (param.contains("Id")) {
            nativeQuery.setParameter(x.getKey(), filterValue);
        } else {
            if (x.getValue().getMatchMode() == MatchMode.EQUALS) {
                nativeQuery.setParameter(x.getKey(), filterValue);
            } else {
                nativeQuery.setParameter(x.getKey(), "%" + filterValue + "%");
            }
        }

    }

    protected abstract EntityManager getEM();

    public String getCustomQuery() {
        return this.customQuery;
    }

    public void setCustomQuery(final String customQuery) {
        this.customQuery = customQuery;
    }
}
