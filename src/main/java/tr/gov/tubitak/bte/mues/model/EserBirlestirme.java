package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "Eser_Birlestirme")
@NamedQuery(name = "EserBirlestirme.findEagerById", query = "SELECT eb FROM EserBirlestirme eb JOIN FETCH eb.birlestirme LEFT JOIN FETCH eb.eser e LEFT JOIN FETCH e.anaFotograf ef  WHERE eb.id = :id")
@NamedQuery(name = "EserBirlestirme.findAll", query = "SELECT x FROM EserBirlestirme x JOIN FETCH x.birlestirme JOIN FETCH x.eser e LEFT JOIN FETCH e.anaFotograf ef JOIN FETCH x.birlestirme ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "EserBirlestirme.findActive", query = "SELECT x FROM EserBirlestirme x JOIN FETCH x.birlestirme JOIN FETCH x.eser e LEFT JOIN FETCH e.anaFotograf ef JOIN FETCH x.birlestirme WHERE x.aktif = true AND x.silinmis = false")

public class EserBirlestirme extends AbstractEntity {

    private static final long serialVersionUID = -6285893522539661683L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "birlestirmeId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Birlestirme       birlestirme;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public EserBirlestirme() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Birlestirme getBirlestirme() {
        return this.birlestirme;
    }

    public void setBirlestirme(final Birlestirme birlestirme) {
        this.birlestirme = birlestirme;
    }

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    @Override
    public String toString() {
        return "Birlestirme: " + this.birlestirme + "; Eser: " + this.eser;
    }

    @Override
    public String getTitle() {
        return this.birlestirme.toString();
    }

}
