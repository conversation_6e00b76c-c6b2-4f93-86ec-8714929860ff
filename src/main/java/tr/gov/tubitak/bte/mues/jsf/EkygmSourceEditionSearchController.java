package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.StringJoiner;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.shaded.json.JSONArray;
import org.primefaces.shaded.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.session.LiteraturFacade;
import tr.gov.tubitak.bte.mues.util.DateUtil;

@Named
@ViewScoped
public class EkygmSourceEditionSearchController extends AbstractController<Literatur> {

    private static final long                     serialVersionUID = 9179945691688700637L;

    @Inject
    private LiteraturFacade                       literaturFacade;

    @Inject
    private MuesParameters                            parameters;

    @Inject
    private EserKaynakLiteraturController         eserKaynakLiteraturController;

    @Inject
    private EserYayinLiteraturController          eserYayinLiteraturController;

    @Inject
    private EserProposalKaynakLiteraturController eserProposalKaynakLiteraturController;

    @Inject
    private EserProposalYayinLiteraturController  eserProposalYayinLiteraturController;

    private static final Logger                   logger           = LoggerFactory.getLogger(EkygmSourceEditionSearchController.class);

    private Set<Literatur>                        literatures;

    private String                                kaynakAd;

    private String                                isbnNo;

    private String                                issnNo;

    private String                                host;

    private String                                isbnNameOrIsbnNumberUrl;

    private String                                issnNameOrIssnNumberUrl;

    protected EkygmSourceEditionSearchController() {
        super(Literatur.class);
    }

    @PostConstruct
    public void init() {
        this.host = this.parameters.get("ekygm.service.url");
        this.isbnNameOrIsbnNumberUrl = this.host + "/" + this.parameters.get("ekygm.isbnNameOrIsbnNumber.service") + "?";
        this.issnNameOrIssnNumberUrl = this.host + "/" + this.parameters.get("ekygm.issnNameOrIssnNumber.service") + "?";
    }

    public void fetchLiteraturesFromEKYGM() throws IOException {
        this.literatures = null;
        if (this.isbnNo != null) {
            this.getLiteratures()
                .addAll(this.parseJsonDataToLiteraturList(this.getDataFromApiService(this.isbnNameOrIsbnNumberUrl + "materialName=" + this.kaynakAd + "&isbnNumber=" + this.isbnNo), "ISBN"));
        } else if (this.issnNo != null) {
            this.getLiteratures()
                .addAll(this.parseJsonDataToLiteraturList(this.getDataFromApiService(this.issnNameOrIssnNumberUrl + "materialName=" + this.kaynakAd + "&issnNumber=" + this.issnNo), "ISSN"));
        } else {
            this.getLiteratures().addAll(this.parseJsonDataToLiteraturList(this.getDataFromApiService(this.isbnNameOrIsbnNumberUrl + "materialName=" + this.kaynakAd), "ISBN"));

            this.getLiteratures().addAll(this.parseJsonDataToLiteraturList(this.getDataFromApiService(this.issnNameOrIssnNumberUrl + "materialName=" + this.kaynakAd), "ISSN"));
        }

    }

    @Override
    public void showDetail(final Literatur item) {
        // will first query from the db, if there is no db, it will query from the service
        final List<Literatur> literatur = this.literaturFacade.findByIsbnOrIssn(item.getIsbn(), item.getIssn());
        if ((literatur == null) || literatur.isEmpty()) {
            if (item.getIsbn() != null) {
                this.setModel(this.fetchLiteraturesIsbnNumber(item.getIsbn()));
            } else {
                this.setModel(this.fetchLiteraturesIssnNumber(item.getIssn()));
            }
            this.setNewMode(true);
        } else {
            this.setModel(literatur.get(0));
        }
    }

    public void saveKaynak() {
        this.update();
        this.eserKaynakLiteraturController.getModel().setLiteratur(this.literaturFacade.findByIsbnOrIssn(this.getModel().getIsbn(), this.getModel().getIssn()).get(0));
    }

    public void saveProposalKaynak() {
        this.update();
        this.eserProposalKaynakLiteraturController.getModel().setLiteratur(this.literaturFacade.findByIsbnOrIssn(this.getModel().getIsbn(), this.getModel().getIssn()).get(0));
    }

    public void saveYayin() {
        this.update();
        this.eserYayinLiteraturController.getModel().setLiteratur(this.literaturFacade.findByIsbnOrIssn(this.getModel().getIsbn(), this.getModel().getIssn()).get(0));
    }

    public void saveProposalYayin() {
        this.update();
        this.eserProposalYayinLiteraturController.getModel().setLiteratur(this.literaturFacade.findByIsbnOrIssn(this.getModel().getIsbn(), this.getModel().getIssn()).get(0));
    }

    public Literatur fetchLiteraturesIsbnNumber(final String isbn) {
        return this.parseIsbnJsonDataToLiteratur(this.getDataFromApiService(this.host + "/" + this.parameters.get("ekygm.isbnAttributes.service") + "?" + "isbnNumber=" + isbn));
    }

    public Literatur fetchLiteraturesIssnNumber(final String issn) {
        return this.parseIssnJsonDataToLiteratur(this.getDataFromApiService(this.host + "/" + this.parameters.get("ekygm.issnAttributes.service") + "?" + "issnNumber=" + issn));
    }

    public Set<Literatur> parseJsonDataToLiteraturList(String jsonData, final String type) throws IOException {
        final Set<Literatur> literaturResultList = new LinkedHashSet<>();
        if ((jsonData != null) && !jsonData.isEmpty() && (!jsonData.equals("null"))) {
            jsonData = jsonData.trim();

            final JSONArray jsonArray = new JSONArray(jsonData);
            for (int i = 0; i < jsonArray.length(); i++) {
                final JSONObject jsonObject = jsonArray.getJSONObject(i);
                final Object isbnNo = jsonObject.get("IsbnNumarasi");
                if ((isbnNo != null) && (isbnNo != JSONObject.NULL)) {
                    final String eserAdi = jsonObject.getString("EserAdi");
                    final Literatur literatur = new Literatur();
                    literatur.setAd(eserAdi);

                    if (type.equals("ISBN")) {
                        literatur.setIsbn(isbnNo.toString());
                    } else if (type.equals("ISSN")) {
                        literatur.setIssn(isbnNo.toString());
                    }
                    literaturResultList.add(literatur);
                }
            }
        }
        return literaturResultList;
    }

    public Literatur parseIsbnJsonDataToLiteratur(final String jsonData) {
        final Literatur literatur = new Literatur();
        final JSONObject jsonObject = new JSONObject(jsonData);
        literatur.setAd(jsonObject.getString("EserAdi"));
        literatur.setIsbn(jsonObject.getString("IsbnNumarasi"));
        literatur.setLiteraturDate(DateUtil.getDateDDMMYYYYString(jsonObject.getString("OnaylanmaTarihi").replace("-", ".")));

        final StringJoiner sj = new StringJoiner(", ");
        final JSONArray yazarlarJsonArray = jsonObject.getJSONArray("Yazarlar");
        for (int i = 0; i < yazarlarJsonArray.length(); i++) {
            final JSONObject yazarJsonObject = yazarlarJsonArray.getJSONObject(i);
            sj.add(yazarJsonObject.getString("Name"));
        }
        literatur.setYazar(sj.toString());
        return literatur;
    }

    public Literatur parseIssnJsonDataToLiteratur(final String jsonData) {
        final Literatur literatur = new Literatur();
        final JSONObject jsonObject = new JSONObject(jsonData);
        literatur.setAd(jsonObject.getString("SureliYayininAdi"));
        literatur.setIssn(jsonObject.getString("IssnNumarasi"));
        literatur.setLiteraturDate(DateUtil.getDateDDMMYYYYString(jsonObject.getString("OnaylanmaTarihi").replace("-", ".")));
        literatur.setYazar(jsonObject.getString("YayimciAdi"));
        return literatur;
    }

    public String getDataFromApiService(final String uri) {
        HttpResponse<String> response = null;
        try {
            final HttpClient httpClient = HttpClient.newBuilder().version(HttpClient.Version.HTTP_2).build();

            final HttpRequest request = HttpRequest.newBuilder(URI.create(uri.replace(" ", "%20")))
                                                   .header("kullaniciadi", this.parameters.get("ekygm.service.username"))
                                                   .header("sifre", this.parameters.get("ekygm.service.password"))
                                                   .GET()
                                                   .build();

            // Send HTTP GET request and get response
            response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            if (response.statusCode() == 200) {
                return response.body();
            } else {
                FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Hata!", "Hata Kodu: " + response.statusCode()));
            }

        } catch (final Exception e) {
            FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, "Hata!", "Hata Kodu: " + response.statusCode()));
            logger.debug("[EKYGM api service response]", e.getMessage());
        }
        return "";
    }

    public void resetSearchFields() {
        this.literatures = null;
        this.kaynakAd = null;
        this.isbnNo = null;
        this.issnNo = null;
    }

    @Override
    public LiteraturFacade getFacade() {
        return this.literaturFacade;
    }

    public LiteraturFacade getLiteraturFacade() {
        return this.literaturFacade;
    }

    public void setLiteraturFacade(final LiteraturFacade literaturFacade) {
        this.literaturFacade = literaturFacade;
    }

    public Set<Literatur> getLiteratures() {
        if (this.literatures == null) {
            this.literatures = new HashSet<>();
        }
        return this.literatures;
    }

    public void setLiteraturesa(final Set<Literatur> literatures) {
        this.literatures = literatures;
    }

    public String getKaynakAd() {
        return this.kaynakAd;
    }

    public void setKaynakAd(final String kaynakAd) {
        this.kaynakAd = kaynakAd;
    }

    public String getIsbnNo() {
        return this.isbnNo;
    }

    public void setIsbnNo(final String isbnNo) {
        this.isbnNo = isbnNo;
    }

    public String getIssnNo() {
        return this.issnNo;
    }

    public void setIssnNo(final String issnNo) {
        this.issnNo = issnNo;
    }

    public String getIsbnNameOrIsbnNumberUrl() {
        return this.isbnNameOrIsbnNumberUrl;
    }

    public void setIsbnNameOrIsbnNumberUrl(final String isbnNameOrIsbnNumberUrl) {
        this.isbnNameOrIsbnNumberUrl = isbnNameOrIsbnNumberUrl;
    }

    public String getIssnNameOrIssnNumberUrl() {
        return this.issnNameOrIssnNumberUrl;
    }

    public void setIssnNameOrIssnNumberUrl(final String issnNameOrIssnNumberUrl) {
        this.issnNameOrIssnNumberUrl = issnNameOrIssnNumberUrl;
    }

}
