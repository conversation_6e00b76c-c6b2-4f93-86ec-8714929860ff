package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Cag;
import tr.gov.tubitak.bte.mues.model.Donem;
import tr.gov.tubitak.bte.mues.model.Kronoloji;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class DonemFacade extends AbstractFacade<Donem> {

    public DonemFacade() {
        super(Donem.class);
    }

    public List<Cag> filterByNameAndKronoloji(final String value, final Kronoloji kronoloji) {
        return this.em.createNamedQuery("Cag.findByNameAndKronoloji", Cag.class)
                      .setParameter("ad", "%" + value + "%")
                      .setParameter("krono", kronoloji)
                      .getResultList();
    }

    public List<Donem> filterByNameAndCag(final String value, final Cag cag) {
        return this.em.createNamedQuery("Donem.findByNameAndCag", Donem.class)
                      .setParameter("ad", "%" + value + "%")
                      .setParameter("cag", cag)
                      .getResultList();
    }

}
