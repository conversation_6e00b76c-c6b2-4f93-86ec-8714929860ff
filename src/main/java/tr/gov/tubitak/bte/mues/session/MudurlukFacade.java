package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.NoResultException;
import javax.transaction.Transactional;

import org.apache.shiro.SecurityUtils;

import tr.gov.tubitak.bte.mues.model.AlanKonumu;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.mapping.MudurlukIzin;
import tr.gov.tubitak.bte.mues.util.ApplicationSpecificQueries;

/**
 *
*
 */
@RequestScoped
@Transactional
public class MudurlukFacade extends AbstractFacade<Mudurluk> {

    @Inject
    ApplicationSpecificQueries applicationSpecificQueries;

    public MudurlukFacade() {
        super(Mudurluk.class);
    }

    @SuppressWarnings("unchecked")
    public List<MudurlukIzin> fetchAllPermittedMudurluks() {
        return this.em.createNativeQuery(this.applicationSpecificQueries.getAllPermittedMudurluks(), "MudurlukBazliIzin").setParameter(1, SecurityUtils.getSubject().getPrincipal()).getResultList();
    }

    public Integer getTeshirSalonuSayisi() {
        return ((Integer) this.em.createNativeQuery("SELECT count(*) from ALAN_KONUMU").getSingleResult()).intValue();
    }

    public List<AlanKonumu> findByNameAndMuseumDirectorate(final Integer mudurlukId) {
        return this.getEM().createNamedQuery("Eser.findByNameAndMuseumDirectorate", AlanKonumu.class).setParameter("id", mudurlukId).getResultList();
    }

    public List<Mudurluk> filterByName(final String query) {
        return this.em.createNamedQuery("Mudurluk.findByName", Mudurluk.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public Mudurluk findByKod(final String kod) {
        try {
            return this.em.createNamedQuery("Mudurluk.findByKod", Mudurluk.class).setParameter("kod", kod).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }

    public void toggleActiveSelfAndDescendants(final Mudurluk mudurluk) {

        this.getEM().createNativeQuery(this.applicationSpecificQueries.getAktiveSelfDescendants()).setParameter(1, !mudurluk.getAktif()).setParameter(2, mudurluk.getId()).executeUpdate();
    }

    public List<Mudurluk> filterByFullNameAndAciklamaPreventDuplicate(final String query, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("Mudurluk.filterByFullNameAndAciklamaPreventDuplicate", Mudurluk.class).setParameter("str", "%" + query + "%").setParameter("ids", excludedIds).getResultList();
    }

    public List<Mudurluk> findByType(final int type) {
        return this.em.createNamedQuery("Mudurluk.findByType", Mudurluk.class).setParameter("type", type).getResultList();
    }

}
