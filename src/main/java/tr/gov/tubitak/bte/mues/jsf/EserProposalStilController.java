package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalStil;
import tr.gov.tubitak.bte.mues.model.Stil;
import tr.gov.tubitak.bte.mues.session.EserProposalStyleFacade;

@Named
@ViewScoped
public class EserProposalStilController extends AbstractController<EserProposalStil> {

    private static final long       serialVersionUID = -4465419352459118582L;

    @Inject
    private EserProposalStyleFacade facade;

    private List<Stil>              selectionList;

    private List<EserProposalStil>  eserStilList;

    public EserProposalStilController() {
        super(EserProposalStil.class);
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.getSelectionList().clear();
        this.getEserStilList().clear();
    }

    public List<EserProposalStil> getSelectedEserStils() {
        for (final Stil stil : this.getSelectionList()) {
            final EserProposalStil eserStil = new EserProposalStil();
            eserStil.setStil(stil);
            this.getEserStilList().add(eserStil);
        }
        return this.getEserStilList();
    }

    // getters and setters ....................................................

    @Override
    public EserProposalStyleFacade getFacade() {
        return this.facade;
    }

    public List<Stil> getSelectionList() {
        if (this.selectionList == null) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;
    }

    public void setSelectionList(final List<Stil> selectionList) {
        this.selectionList = selectionList;
    }

    public List<EserProposalStil> getEserStilList() {
        if (this.eserStilList == null) {
            this.eserStilList = new ArrayList<>();
        }
        return this.eserStilList;
    }

    public void setEserStilList(final List<EserProposalStil> eserStilList) {
        this.eserStilList = eserStilList;
    }

}
