package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "ResearcherRequestArtifact")
@NamedQuery(name = "ResearcherRequestArtifact.findRequestAssignedArtifact", query = "SELECT distinct rra FROM ResearcherRequestArtifact rra LEFT JOIN FETCH rra.researcherRequest rr LEFT JOIN FETCH rra.artifact a WHERE rr.endDate > :date and rr.startDate <= :date and a.id in :ids and rr.researcher != :researcher")
public class ResearcherRequestArtifact extends AbstractEntity implements EditPermissible {

    private static final long   serialVersionUID = 2700746296707991733L;

    @JoinColumn(name = "researcherRequestId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private RequestOfResearcher researcherRequest;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "artifactId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser                artifact;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String              aciklama;

    public ResearcherRequestArtifact() {
        // default constructor.
    }

    // getters and setters ....................................................

    public RequestOfResearcher getResearcherRequest() {
        return this.researcherRequest;
    }

    public void setResearcherRequest(final RequestOfResearcher researcherRequest) {
        this.researcherRequest = researcherRequest;
    }

    public Eser getArtifact() {
        return this.artifact;
    }

    public void setArtifact(final Eser artifact) {
        this.artifact = artifact;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public Integer getUserIdentifier() {
        return this.getId();
    }

    @Override
    public String getTitle() {
        return null;
    }

}
