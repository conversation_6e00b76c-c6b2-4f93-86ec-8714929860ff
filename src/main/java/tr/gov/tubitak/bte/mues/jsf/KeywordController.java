package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Keyword;
import tr.gov.tubitak.bte.mues.session.KeywordFacade;

@Named
@ViewScoped
public class KeywordController extends AbstractController<Keyword> {

    private static final long serialVersionUID = 6607377072277568419L;

    @Inject
    private KeywordFacade     facade;

    public KeywordController() {
        super(Keyword.class);
    }

    // getters and setters ....................................................

    @Override
    public KeywordFacade getFacade() {
        return this.facade;
    }

}
