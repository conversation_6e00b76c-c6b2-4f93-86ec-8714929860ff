package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@MappedSuperclass
@NamedQuery(name = "EserKaynakLiteratur.findEagerById", query = "SELECT x FROM EserKaynakLiteratur x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.literatur WHERE x.id = :id")
@NamedQuery(name = "EserKaynakLiteratur.findAll", query = "SELECT x FROM EserKaynakLiteratur x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.literatur ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "EserKaynakLiteratur.findActive", query = "SELECT x FROM EserKaynakLiteratur x WHERE x.aktif = true AND x.silinmis = false")
public class EserKaynakLiteraturSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -4924530238332485943L;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "literaturId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Literatur         literatur;

    public EserKaynakLiteraturSuper() {
        // default consturactor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Literatur getLiteratur() {
        return this.literatur;
    }

    public void setLiteratur(final Literatur literatur) {
        this.literatur = literatur;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.literatur.getTitle()).orElse("" + this.getId());
    }

}
