package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Menu;

/**
 *
*
 */
@RequestScoped
public class MenuFacade extends AbstractFacade<Menu> {

    public MenuFacade() {
        super(Menu.class);
    }

    public List<Menu> fetchMenuByHierarchy() {
        return this.getEM().createNamedQuery("Menu.fetchMenuByHierarchy", Menu.class).getResultList();
    }

    public List<Menu> filterRootsByName(final String query) {
        return this.em.createNamedQuery("Menu.findRootsByName", Menu.class).setParameter("ad", "%" + query + "%").getResultList();
    }

}
