package tr.gov.tubitak.bte.mues.model;

import java.lang.reflect.Field;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.validation.ValidationException;

import org.hibernate.envers.Audited;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

@Audited
@MappedSuperclass
public abstract class AbstractEntity implements IEntity<Integer> {

    private static final long serialVersionUID = -8410815683631177713L;
    
    protected final transient Logger                     logger           = LoggerFactory.getLogger(AbstractEntity.class);

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer           id;

    @Column(name = "AKTIF")
    private Boolean           aktif;

    @Column(name = "SILINMIS")
    private Boolean           silinmis;

    // end global fields ......................................................

    protected AbstractEntity() {
        this.aktif = true;
        this.silinmis = false;
    }

    // getters and setters ....................................................

    @Override
    public Integer getId() {
        return this.id;
    }

    public void setId(final Integer id) {
        this.id = id;
    }

    @Override
    public Boolean getAktif() {
        return this.aktif;
    }

    @Override
    public void setAktif(final Boolean aktif) {
        // if an item is activated, it has to be undeleted
        if (Boolean.TRUE.equals(aktif)) {
            this.silinmis = false;
        }

        this.aktif = aktif;
    }

    public Boolean getSilinmis() {
        return this.silinmis;
    }

    @Override
    public void setAciklama(final String aciklama) {
        // implement this in the child classes
    }

    @Override
    public void setSilinmis(final Boolean silinmis) {
        // if an item is deleted, it has to be inactivated
        // if you delete the logic here, make sure to make this.aktif = false everywhere that use this setter
        if (Boolean.TRUE.equals(silinmis)) {
            this.aktif = false;
        }
        this.silinmis = silinmis;
    }

    @PrePersist
    @PreUpdate
    public void validateBeforePersist() {
        for (Field field : this.getClass().getDeclaredFields()) {
            if (field.isAnnotationPresent(FilePathCheck.class)) {
                validateFilePath(field);
            }
        }
    }

    private void validateFilePath(Field field) {
        field.setAccessible(true);
        
        try {
            String filePath = (String) field.get(this);
            if (filePath != null && filePath.contains("wildfly-")) {
            	
                throw new ValidationException("Invalid file path: " + filePath);
            }
        } catch (IllegalAccessException e) {
            // Log the exception for debugging
        	logger.error("Error accessing field annotated with @FilePathCheck in entity {} with ID {}", 
                    this.getClass().getCanonicalName(), this.getId(), e);
        }
    }

    
    // hashCode and equals ....................................................


    public int getRowKey() {
        return this.hashCode(); 
    }
    
    @Override
    public int hashCode() {
        if (this.id == null) {
            return super.hashCode();
        }
        return this.id.hashCode();
    }

    @Override
    public boolean equals(final Object object) {
        if (this == object) {
            return true;
        }
        if (object == null) {
            return false;
        }
        if (this.getClass() != object.getClass()) {
            return false;
        }
        return this.hashCode() == object.hashCode();
    }
}
