package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.KadroDurum;
import tr.gov.tubitak.bte.mues.session.KadroDurumFacade;

@Named
@ViewScoped
public class KadroDurumController extends AbstractController<KadroDurum> {

    private static final long serialVersionUID = -7598984032406714129L;

    @Inject
    private KadroDurumFacade  facade;

    public KadroDurumController() {
        super(KadroDurum.class);
    }

    @Override
    public KadroDurumFacade getFacade() {
        return this.facade;
    }

}
