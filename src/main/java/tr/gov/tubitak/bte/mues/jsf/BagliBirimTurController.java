package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.BagliBirimTur;
import tr.gov.tubitak.bte.mues.session.BagliBirimTurFacade;

@Named
@ViewScoped
public class BagliBirimTurController extends AbstractController<BagliBirimTur> {

    private static final long   serialVersionUID = 1601660358989137130L;

    @Inject
    private BagliBirimTurFacade facade;

    public BagliBirimTurController() {
        super(BagliBirimTur.class);
    }

    @Override
    public BagliBirimTurFacade getFacade() {
        return this.facade;
    }

}
