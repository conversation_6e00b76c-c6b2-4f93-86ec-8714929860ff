package tr.gov.tubitak.bte.mues.session;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import javax.enterprise.context.RequestScoped;
import javax.transaction.Transactional;

import tr.gov.tubitak.bte.mues.util.audits.Audit;

@RequestScoped
public class UtilOperationsFacade extends AbstractFacade<Audit> {

    public UtilOperationsFacade() {
        super(Audit.class);
    }

    @Transactional
    public void insertValues(final Object[] enumValues, final String name) {

        try {
            String deleteSql = "DELETE FROM " + name + ";";
            System.out.println(deleteSql); // Log the DELETE SQL for Liquibase copy-paste
            this.em.createNativeQuery("DELETE FROM " + name).executeUpdate();
            for (final Object enumValue : enumValues) {
                final Method getValueMethod = enumValue.getClass().getMethod("getCode");
                final Method getNameMethod = enumValue.getClass().getMethod("getTitle");

                final int value = (int) getValueMethod.invoke(enumValue);
                final String title = (String) getNameMethod.invoke(enumValue);

                String sql = "INSERT INTO " + name + " (enumCode, enumValue) VALUES (" + value + ", '" + title.replace("'", "''") + "');";
                System.out.println(sql); // Log the SQL for Liquibase copy-paste

                this.em
                       .createNativeQuery("INSERT INTO "
                                          + name
                                          + " (enumCode, enumValue) VALUES (:enumCode, :enumValue)")
                       .setParameter("enumCode", value)
                       .setParameter("enumValue", title)
                       .executeUpdate();
            }

        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            e.printStackTrace();
        }
    }

}
