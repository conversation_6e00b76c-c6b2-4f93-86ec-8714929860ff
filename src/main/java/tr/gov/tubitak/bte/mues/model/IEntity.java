package tr.gov.tubitak.bte.mues.model;

import java.io.Serializable;

/**
 * Entity'ler icin identifiable interface. DAO ve servis gecisleri icin kullanilir.
 * 
 * <AUTHOR> YILBOGA
 *
 * @param <T>
 */
public interface IEntity<T extends Serializable> extends Identifiable<T> {

    public String getTitle();

    public void setSilinmis(Boolean b);

    public void setAktif(Boolean b);

    public Boolean getAktif();

    public void setAciklama(final String aciklama);

}
