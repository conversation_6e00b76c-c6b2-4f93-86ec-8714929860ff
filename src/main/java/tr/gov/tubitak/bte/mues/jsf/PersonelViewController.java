package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.session.PersonelViewFacade;
import tr.gov.tubitak.bte.mues.util.CurrentUser;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

@Named
@ViewScoped
public class PersonelViewController extends AbstractController<PersonelView> {

    private static final long  serialVersionUID = 6767457728064281536L;

    @Inject
    private PersonelViewFacade facade;

    @Inject
    @CurrentUser
    private Kullanici          me;

    public PersonelViewController() {
        super(PersonelView.class);
    }

    /***
     * Filters upon all personnel
     * 
     * @param query filter
     * @return result list
     */
    public List<PersonelView> filterByFullNameAndAciklamaAndMuseumDirectorate(final String query) {
        final FacesContext context = FacesContext.getCurrentInstance();
        final Mudurluk mudurluk = (Mudurluk) UIComponent.getCurrentComponent(context).getAttributes().get("mudurluk");

        return this.facade.filterByFullNameAndAciklamaAndMuseumDirectorates(query, List.of(mudurluk));
    }

    /***
     * Filters personel by given application type
     * 
     * @param query filter
     * @return result list
     */
    public List<PersonelView> filterByNameAndMudurlukAndAppType(final String query) {
        final FacesContext context = FacesContext.getCurrentInstance();
        final Mudurluk mudurluk = (Mudurluk) UIComponent.getCurrentComponent(context).getAttributes().get("mudurluk");
        final ApplicationType appType = (ApplicationType) UIComponent.getCurrentComponent(context).getAttributes().get("appType");

        return this.facade.filterByNameAndMudurlukAndAppType(query, List.of(mudurluk), appType);
    }

    public List<PersonelView> findPersonelViewByAppType(final ApplicationType appType) {
        return this.facade.findPersonelViewByAppType(appType);
    }

    public List<PersonelView> findAllPersonnel() {
        return this.facade.findAllPersonnel();
    }

    public void assignCurrentPersonel() {
        this.setModel(this.facade.findEagerById(this.me.getPersonelView().getId()));
        this.setNewMode(false);
    }

    @Override
    public List<PersonelView> getItems() {
        if (this.items == null) {
            this.items = new ArrayList<>();
        }
        return this.items;
    }

    // getter .................................................................

    @Override
    public PersonelViewFacade getFacade() {
        return this.facade;
    }

}
