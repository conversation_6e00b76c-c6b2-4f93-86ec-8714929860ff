package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Rol;

/**
 *
*
 */
@RequestScoped
public class RolFacade extends AbstractFacade<Rol> {

    public RolFacade() {
        super(Rol.class);
    }

    public List<Rol> filterRoleByNameAndRank(final String query, final Integer userId) {
        return this.em.createNamedQuery("Rol.findByNameAndRank", Rol.class).setParameter("ad", "%" + query + "%").setParameter("userId", userId).getResultList();
    }

    // TODO: halis.yilboga kontrol baska sekilde halledilmesi gereken bir konu. En azindan isimi degilde codu kullanilabilir.
    public List<Rol> findByCode(final String kod) {
        return this.em.createNamedQuery("Rol.findByCode", Rol.class).setParameter("kod", kod).getResultList();
    }

}
