package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.ValidArtifactJoinable;
import tr.gov.tubitak.bte.mues.constraint.validator.ArtifactJoinGroup;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "Birlestirme")
@ValidArtifactJoinable(groups = ArtifactJoinGroup.class)
public class Birlestirme extends BirlestirmeSuper {

    private static final long serialVersionUID = 563634505387948672L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "muzeMudurluguId")
    private Mudurluk          mudurluk;

    public Birlestirme() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }
}
