package tr.gov.tubitak.bte.mues.model.mapping;

import java.io.Serializable;
import java.util.UUID;

import javax.persistence.Table;

@Table(name = "TimeSpectrum")
public class TimeSpectrum implements Serializable {

    private static final long serialVersionUID = -2485599189732661226L;

    private String            id;

    private Integer           kronolojiId;

    private String            kronolojiAdi;

    private Integer           cagId;

    private String            cagAdi;

    private Integer           donemId;

    private String            donemAdi;

    private Integer           uygarlikId;

    private String            uygarlikAdi;

    private String            hukumdarAdi;

    private Integer           hukumdarId;

    private Integer           yearStart;

    private Integer           yearEnd;

    public TimeSpectrum() {
    }

    public TimeSpectrum(final Integer kronolojiId,
                        final String kronolojiAdi,
                        final Integer cagId,
                        final String cagAdi,
                        final Integer donemId,
                        final String donemAdi,
                        final Integer uygarlikId,
                        final String uygarlikAdi,
                        final String hukumdarAdi,
                        final Integer hukumdarId,
                        final Integer yearStart,
                        final Integer yearEnd) {
        this.kronolojiId = kronolojiId;
        this.kronolojiAdi = kronolojiAdi;
        this.cagId = cagId;
        this.cagAdi = cagAdi;
        this.donemId = donemId;
        this.donemAdi = donemAdi;
        this.uygarlikId = uygarlikId;
        this.uygarlikAdi = uygarlikAdi;
        this.hukumdarAdi = hukumdarAdi;
        this.hukumdarId = hukumdarId;
        this.yearStart = yearStart;
        this.yearEnd = yearEnd;
    }

    // getters and setters ....................................................

    public Integer getKronolojiId() {
        return this.kronolojiId;
    }

    public void setKronolojiId(final int kronolojiId) {
        this.kronolojiId = kronolojiId;
    }

    public String getKronolojiAdi() {
        return this.kronolojiAdi;
    }

    public void setKronolojiAdi(final String kronolojiAdi) {
        this.kronolojiAdi = kronolojiAdi;
    }

    public Integer getCagId() {
        return this.cagId;
    }

    public void setCagId(final Integer cagId) {
        this.cagId = cagId;
    }

    public String getCagAdi() {
        return this.cagAdi;
    }

    public void setCagAdi(final String cagAdi) {
        this.cagAdi = cagAdi;
    }

    public Integer getDonemId() {
        return this.donemId;
    }

    public void setDonemId(final Integer donemId) {
        this.donemId = donemId;
    }

    public String getDonemAdi() {
        return this.donemAdi;
    }

    public void setDonemAdi(final String donemAdi) {
        this.donemAdi = donemAdi;
    }

    public Integer getUygarlikId() {
        return this.uygarlikId;
    }

    public void setUygarlikId(final Integer uygarlikId) {
        this.uygarlikId = uygarlikId;
    }

    public String getUygarlikAdi() {
        return this.uygarlikAdi;
    }

    public void setUygarlikAdi(final String uygarlikAdi) {
        this.uygarlikAdi = uygarlikAdi;
    }

    public String getHukumdarAdi() {
        return this.hukumdarAdi;
    }

    public void setHukumdarAdi(final String hukumdarAdi) {
        this.hukumdarAdi = hukumdarAdi;
    }

    public Integer getHukumdarId() {
        return this.hukumdarId;
    }

    public void setHukumdarId(final Integer hukumdarId) {
        this.hukumdarId = hukumdarId;
    }

    public Integer getYearStart() {
        return this.yearStart;
    }

    public void setYearStart(final Integer yearStart) {
        this.yearStart = yearStart;
    }

    public Integer getYearEnd() {
        return this.yearEnd;
    }

    public void setYearEnd(final Integer yearEnd) {
        this.yearEnd = yearEnd;
    }

    public Serializable getId() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString();
        }
        return this.id;
    }

}
