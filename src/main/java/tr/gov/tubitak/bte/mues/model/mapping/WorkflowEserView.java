package tr.gov.tubitak.bte.mues.model.mapping;

import java.io.Serializable;
import java.text.DecimalFormat;
import java.util.Date;

import javax.persistence.Table;

@Table(name = "WorkflowEserView")
public class WorkflowEserView implements Serializable {

    private static final long          serialVersionUID = -8004973212516422376L;

    private static final String        TR_M             = "TR.M.%s";

    private static final DecimalFormat df               = new DecimalFormat("000,000,000");

    private Integer                    eserID;

    private Integer                    permanentId;

    private String                     assetID;

    private String                     eserEnvanterNo;

    private String                     tempID;

    private String                     anaFotografPath;

    private String                     eserTurAd;

    private String                     eserAltTurAd;

    private String                     eserTurAltTurAd;

    private String                     eserDonemAd;

    private String                     eserGrubuAd;

    private String                     eserKonumu;

    private String                     mudurlukAd;

    private Integer                    mudurlukId;

    private String                     bagliBirimAd;

    private Integer                    bagliBirimId;

    private String                     binaAd;

    private Integer                    binaId;

    private String                     alanAd;

    private Integer                    alanId;

    private String                     alanKonumuAd;

    private Integer                    alanKonumuId;

    private Integer                    reviewId;

    private String                     qrCode;

    private Date                       envanterTarihi;

    private String                     eserAdi;

    private String                     eserTranskripsiyonMonogramPath;
    
    public WorkflowEserView() {
        // constructor
    }

    public WorkflowEserView(final Integer eserID,
                            final Integer permanentId,
                            final String assetID,
                            final String eserEnvanterNo,
                            final String tempID,
                            final String anaFotografPath,
                            final String eserTurAd,
                            final String eserAltTurAd,
                            final String eserTurAltTurAd,
                            final String eserDonemAd,
                            final String eserGrubuAd,
                            final String eserKonumu,
                            final String mudurlukAd,
                            final Integer mudurlukId,
                            final String bagliBirimAd,
                            final Integer bagliBirimId,
                            final String binaAd,
                            final Integer binaId,
                            final String alanAd,
                            final Integer alanId,
                            final String alanKonumuAd,
                            final Integer alanKonumuId,
                            final Integer reviewId,
                            final String qrCode,
                            final Date envanterTarihi,
                            final String eserAdi,
                            final String eserTranskripsiyonMonogramPath) {
        this.eserID = eserID;
        this.permanentId = permanentId;
        this.assetID = assetID;
        this.eserEnvanterNo = eserEnvanterNo;
        this.tempID = tempID;
        this.anaFotografPath = anaFotografPath;
        this.eserTurAd = eserTurAd;
        this.eserAltTurAd = eserAltTurAd;
        this.eserTurAltTurAd = eserTurAltTurAd;
        this.eserDonemAd = eserDonemAd;
        this.eserGrubuAd = eserGrubuAd;
        this.eserKonumu = eserKonumu;
        this.mudurlukAd = mudurlukAd;
        this.mudurlukId = mudurlukId;
        this.bagliBirimAd = bagliBirimAd;
        this.bagliBirimId = bagliBirimId;
        this.binaAd = binaAd;
        this.binaId = binaId;
        this.alanAd = alanAd;
        this.alanId = alanId;
        this.alanKonumuAd = alanKonumuAd;
        this.alanKonumuId = alanKonumuId;
        this.reviewId = reviewId;
        this.qrCode = qrCode;
        this.envanterTarihi = envanterTarihi;
        this.eserAdi = eserAdi;
        this.eserTranskripsiyonMonogramPath = eserTranskripsiyonMonogramPath;
    }

    // getters and setters ....................................................

    public Integer getEserID() {
        return this.eserID;
    }

    public void setEserID(final Integer eserID) {
        this.eserID = eserID;
    }

    public Integer getPermanentId() {
        return this.permanentId;
    }

    public void setPermanentId(final Integer permanentId) {
        this.permanentId = permanentId;
    }

    public void setAssetID(final String assetID) {
        this.assetID = assetID;
    }

    public String getEserEnvanterNo() {
        return this.eserEnvanterNo;
    }

    public void setEserEnvanterNo(final String eserEnvanterNo) {
        this.eserEnvanterNo = eserEnvanterNo;
    }

    public String getTempID() {
        return this.tempID;
    }

    public void setTempID(final String tempID) {
        this.tempID = tempID;
    }

    public String getAnaFotografPath() {
        return this.anaFotografPath;
    }

    public void setAnaFotografPath(final String anaFotografPath) {
        this.anaFotografPath = anaFotografPath;
    }

    public String getAssetID() {
        return this.assetID;
    }

    public String getFormattedAssetID() {
        if (this.permanentId == null) {
            return null;
        }
        return String.format(TR_M, df.format(this.permanentId).replace(",", "."));
    }
    
    public String getEserTurAd() {
        return this.eserTurAd;
    }

    public void setEserTurAd(final String eserTurAd) {
        this.eserTurAd = eserTurAd;
    }

    public String getEserAltTurAd() {
        return this.eserAltTurAd;
    }

    public void setEserAltTurAd(final String eserAltTurAd) {
        this.eserAltTurAd = eserAltTurAd;
    }

    public String getEserTurAltTurAd() {
        return this.eserTurAltTurAd;
    }

    public void setEserTurAltTurAd(final String eserTurAltTurAd) {
        this.eserTurAltTurAd = eserTurAltTurAd;
    }

    public String getEserDonemAd() {
        return this.eserDonemAd;
    }

    public void setEserDonemAd(final String eserDonemAd) {
        this.eserDonemAd = eserDonemAd;
    }

    public String getEserGrubuAd() {
        return this.eserGrubuAd;
    }

    public void setEserGrubuAd(final String eserGrubuAd) {
        this.eserGrubuAd = eserGrubuAd;
    }

    public String getEserKonumu() {
        return this.eserKonumu;
    }

    public void setEserKonumu(final String eserKonumu) {
        this.eserKonumu = eserKonumu;
    }

    public String getMudurlukAd() {
        return this.mudurlukAd;
    }

    public void setMudurlukAd(final String mudurlukAd) {
        this.mudurlukAd = mudurlukAd;
    }

    public String getBagliBirimAd() {
        return this.bagliBirimAd;
    }

    public void setBagliBirimAd(final String bagliBirimAd) {
        this.bagliBirimAd = bagliBirimAd;
    }

    public String getBinaAd() {
        return this.binaAd;
    }

    public void setBinaAd(final String binaAd) {
        this.binaAd = binaAd;
    }

    public String getAlanAd() {
        return this.alanAd;
    }

    public void setAlanAd(final String alanAd) {
        this.alanAd = alanAd;
    }

    public String getAlanKonumuAd() {
        return this.alanKonumuAd;
    }

    public void setAlanKonumuAd(final String alanKonumuAd) {
        this.alanKonumuAd = alanKonumuAd;
    }

    public Integer getMudurlukId() {
        return this.mudurlukId;
    }

    public void setMudurlukId(final Integer mudurlukId) {
        this.mudurlukId = mudurlukId;
    }

    public Integer getBagliBirimId() {
        return this.bagliBirimId;
    }

    public void setBagliBirimId(final Integer bagliBirimId) {
        this.bagliBirimId = bagliBirimId;
    }

    public Integer getBinaId() {
        return this.binaId;
    }

    public void setBinaId(final Integer binaId) {
        this.binaId = binaId;
    }

    public Integer getAlanId() {
        return this.alanId;
    }

    public void setAlanId(final Integer alanId) {
        this.alanId = alanId;
    }

    public Integer getAlanKonumuId() {
        return this.alanKonumuId;
    }

    public void setAlanKonumuId(final Integer alanKonumuId) {
        this.alanKonumuId = alanKonumuId;
    }

    public Integer getReviewId() {
        return this.reviewId;
    }

    public void setReviewId(final Integer reviewId) {
        this.reviewId = reviewId;
    }

    public String getQrCode() {
        return this.qrCode;
    }

    public void setQrCode(final String qrCode) {
        this.qrCode = qrCode;
    }

    public Date getEnvanterTarihi() {
        return this.envanterTarihi;
    }

    public void setEnvanterTarihi(final Date envanterTarihi) {
        this.envanterTarihi = envanterTarihi;
    }

    public String getEserAdi() {
        return this.eserAdi;
    }

    public void setEserAdi(final String eserAdi) {
        this.eserAdi = eserAdi;
    }

    public String[] listEserTranskripsiyonMonogramPath() {
		return eserTranskripsiyonMonogramPath!=null ? eserTranskripsiyonMonogramPath.split(","):null;
	}
	public String getEserTranskripsiyonMonogramPath() {
		return eserTranskripsiyonMonogramPath;
	}

	public void setEserTranskripsiyonMonogramPath(String eserTranskripsiyonMonogramPath) {
		this.eserTranskripsiyonMonogramPath = eserTranskripsiyonMonogramPath;
	}

}
