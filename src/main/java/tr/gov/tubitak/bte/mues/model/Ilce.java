package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "ILCE")
@NamedQuery(name = "Ilce.findEagerById", query = "SELECT i FROM Ilce i JOIN FETCH i.il WHERE i.id = :id")
@NamedQuery(name = "Ilce.findAll", query = "SELECT i FROM Ilce i JOIN FETCH i.il m ORDER BY i.silinmis, i.aktif DESC, m.ad")
@NamedQuery(name = "Ilce.findActive", query = "SELECT i FROM Ilce i JOIN FETCH i.il WHERE i.aktif = true AND i.silinmis = false ORDER BY i.ad")
@NamedQuery(name = "Ilce.findByNameAndIl", query = "SELECT i FROM Ilce i WHERE i.il = :il AND i.ad LIKE :ad AND i.aktif = true AND i.silinmis = false ORDER BY i.ad")
@NamedNativeQuery(name = "Ilce.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM KAZI WHERE SILINMIS = 0 AND ILCE_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM MUZE_MUDURLUGU WHERE SILINMIS = 0 AND ILCE_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM SAHIS WHERE SILINMIS = 0 AND ILCE_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM TUZEL_KISI WHERE SILINMIS = 0 AND ILCE_ID = :id) +"
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_Collectioner WHERE SILINMIS = 0 AND countyId = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_Laboratory WHERE SILINMIS = 0 AND ILCE_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_LaboratoryDirectorate WHERE SILINMIS = 0 AND ILCE_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_CommissionMember WHERE SILINMIS = 0 AND ILCE_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_PrivateMuseum WHERE SILINMIS = 0 AND countyId = :id)")
public class Ilce extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 7233728469988434234L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                il;

    public Ilce() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    @Override
    public String toString() {
        return this.getAd();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
