package tr.gov.tubitak.bte.mues.jsf.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

/**
 * Created with IntelliJ IDEA. User: sceviker1 Date: 12/18/12 Time: 4:31 PM
 * <p>
 * </p>
 * ToDo: change this
 */
@FacesConverter("converterPhone")
public class ConverterPhone implements Converter {

    private static Pattern patternDahili   = Pattern.compile("\\(([0-9][0-9][0-9])\\)[ ]([0-9][0-9][0-9])\\-([0-9][0-9][0-9][0-9])[ ][x]([0-9]+)");

    private static Pattern patternStandart = Pattern.compile("\\(([0-9][0-9][0-9])\\)[ ]([0-9][0-9][0-9])\\-([0-9][0-9][0-9][0-9])");

    // private static char[] removeCharacterArray = {'-','(',')','x'};

    @Override
    public Object getAsObject(final FacesContext context, final UIComponent component, String value) {
        if (value == null) {
            return null;
        }

        Matcher matcherIban = patternDahili.matcher(value);
        if (matcherIban.find()) {
            value = "";
            value += matcherIban.group(1);
            value += matcherIban.group(2);
            value += matcherIban.group(3);
            value += matcherIban.group(4);
        } else {
            matcherIban = patternStandart.matcher(value);
            if (matcherIban.find()) {
                value = "";
                value += matcherIban.group(1);
                value += matcherIban.group(2);
                value += matcherIban.group(3);
            }
        }

        /*for(char removeChar : removeCharacterArray)
        {
            value = value.replace("" + removeChar, "");
        }*/

        return value;
    }

    @Override
    public String getAsString(final FacesContext context, final UIComponent component, final Object value) {
        if ((value == null) || ((String) value).trim().equals("") || (((String) value).length() < 10)) {
            return null;
        }

        String formattedValue = "(";
        formattedValue += ((String) value).substring(0, 3);
        formattedValue += ") ";
        formattedValue += ((String) value).substring(3, 6);
        formattedValue += "-";
        formattedValue += ((String) value).substring(6, 10);
        if (((String) value).length() > 10) {
            formattedValue += " x";
            formattedValue += ((String) value).substring(10, ((String) value).length());
        }

        return formattedValue;
        // return (String)getAsObject(context, component, (String)value);
    }

}
