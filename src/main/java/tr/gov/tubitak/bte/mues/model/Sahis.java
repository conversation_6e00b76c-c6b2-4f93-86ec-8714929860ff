package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

/**
 *
*
 */
@Entity
@Table(name = "SAHIS")
@NamedNativeQuery(name = "Sahis.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_HAREKET WHERE SILINMIS = 0 AND TESLIM_EDEN_SAHIS_ID = :id)")
public class Sahis extends SahisSuper {

    private static final long serialVersionUID = 2450526640902372096L;

    public Sahis() {
        // blank constructor
    }

}
