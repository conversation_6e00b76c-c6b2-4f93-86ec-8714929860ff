package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.YaziTipi;
import tr.gov.tubitak.bte.mues.session.YaziTipiFacade;

@Named
@ViewScoped
public class YaziTipiController extends AbstractController<YaziTipi> {

    private static final long       serialVersionUID = 929771085741670483L;

    @Inject
    private YaziTipiFacade          facade;
    
    private Dil                     dil;

    public YaziTipiController() {
        super(YaziTipi.class);
    }

    public List<YaziTipi> filterByNameAndDil(final String query) {
        return this.getFacade().filterByNameAndDil(query, this.dil);
    }

    public List<YaziTipi> filterByNameAndAciklama(final String query) {
        return this.facade.filterByNameAndAciklama(query);
    }

    // getters and setter .....................................................

    @Override
    public YaziTipiFacade getFacade() {
        return this.facade;
    }

    public Dil getDil() {
        return dil;
    }

    public void setDil(Dil dil) {
        this.dil = dil;
    }
    
}
