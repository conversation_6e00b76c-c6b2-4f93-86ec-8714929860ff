package tr.gov.tubitak.bte.mues.constraint;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.ReportAsSingleViolation;

import tr.gov.tubitak.bte.mues.constraint.validator.EserValidator;

@ReportAsSingleViolation
@Constraint(validatedBy = { EserValidator.class })
@Documented
@Target({ ElementType.TYPE, ElementType.METHOD, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidEser {

    String message() default "{tr.gov.tubitak.bte.mues.constraint.Eser}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
