package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.UretimBolgesi;
import tr.gov.tubitak.bte.mues.session.UretimBolgesiFacade;

@Named
@ViewScoped
public class UretimBolgesiController extends AbstractController<UretimBolgesi> {

    private static final long   serialVersionUID = 2215886861273694263L;

    @Inject
    private UretimBolgesiFacade facade;

    public UretimBolgesiController() {
        super(UretimBolgesi.class);
    }

    public List<UretimBolgesi> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public UretimBolgesiFacade getFacade() {
        return this.facade;
    }

}
