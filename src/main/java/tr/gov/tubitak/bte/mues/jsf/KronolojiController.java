package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Kronoloji;
import tr.gov.tubitak.bte.mues.session.KronolojiFacade;

@Named
@ViewScoped
public class KronolojiController extends AbstractController<Kronoloji> {

    private static final long serialVersionUID = 1673286460908390185L;

    @Inject
    private KronolojiFacade   facade;

    public KronolojiController() {
        super(Kronoloji.class);
    }

    // getters and setters ....................................................

    @Override
    public KronolojiFacade getFacade() {
        return this.facade;
    }

}
