package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "BAGLI_BIRIM")
@NamedNativeQuery(name = "BagliBirim.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM BINA WHERE SILINMIS = 0 AND BAGLI_BIRIM_ID = :id)")
public class BagliBirim extends BagliBirimSuper {

    private static final long serialVersionUID = -2348343102784137703L;

    public BagliBirim() {
        // blank constructor
    }

}
