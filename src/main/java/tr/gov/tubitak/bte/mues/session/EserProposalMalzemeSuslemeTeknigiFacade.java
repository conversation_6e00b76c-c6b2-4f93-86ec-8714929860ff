package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalMalzemeSuslemeTeknigi;
import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.SuslemeTeknigi;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class EserProposalMalzemeSuslemeTeknigiFacade extends AbstractFacade<EserProposalMalzemeSuslemeTeknigi> {

    public EserProposalMalzemeSuslemeTeknigiFacade() {
        super(EserProposalMalzemeSuslemeTeknigi.class);
    }

    public List<SuslemeTeknigi> findByNameAndAciklamaAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("SuslemeTeknigi.findByNameAndAciklamaAndMalzeme", SuslemeTeknigi.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("malzeme", malzeme)
                      .getResultList();
    }

}
