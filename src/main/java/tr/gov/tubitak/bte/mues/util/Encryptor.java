package tr.gov.tubitak.bte.mues.util;

import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Optional;
import java.util.stream.IntStream;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Encryptor {

    private static final String key        = "MUE$Key5BA7tgZ!2";                      // 128 bit key

    private static String       initVector = "MUE$In!tV3ct0r..";                      // 16 bytes IV

    private static final Logger logger     = LoggerFactory.getLogger(Encryptor.class);

    public static Optional<String> encrypt(final String value) {
        try {
            return Optional.of(encrypt(key, initVector, value));
        } catch (InvalidKeyException | UnsupportedEncodingException | NoSuchAlgorithmException | NoSuchPaddingException
                | InvalidAlgorithmParameterException | IllegalBlockSizeException | BadPaddingException e) {
            logger.error("[encrypt] : Hata - ID: {}, {}", value, e.getMessage(), e);
        }
        return Optional.empty();
    }

    private static String encrypt(final String key, final String initVector, final String value) throws UnsupportedEncodingException,
            NoSuchAlgorithmException,
            NoSuchPaddingException,
            InvalidKeyException,
            InvalidAlgorithmParameterException,
            IllegalBlockSizeException,
            BadPaddingException {
        final IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
        final SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

        final Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);

        final byte[] encrypted = cipher.doFinal(value.getBytes());
        return Base64.getEncoder().encodeToString(encrypted);
    }

    public static String decrypt(final String value) throws InvalidKeyException,
            UnsupportedEncodingException,
            NoSuchAlgorithmException,
            NoSuchPaddingException,
            InvalidAlgorithmParameterException,
            IllegalBlockSizeException,
            BadPaddingException {
        return decrypt(key, initVector, value);
    }

    private static String decrypt(final String key, final String initVector, final String encrypted) throws UnsupportedEncodingException,
            NoSuchAlgorithmException,
            NoSuchPaddingException,
            InvalidKeyException,
            InvalidAlgorithmParameterException,
            IllegalBlockSizeException,
            BadPaddingException {
        final IvParameterSpec iv = new IvParameterSpec(initVector.getBytes("UTF-8"));
        final SecretKeySpec skeySpec = new SecretKeySpec(key.getBytes("UTF-8"), "AES");

        final Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);

        final byte[] original = cipher.doFinal(Base64.getDecoder().decode(encrypted));

        return new String(original);
    }

    public static void main(final String[] args) throws InvalidKeyException,
            UnsupportedEncodingException,
            NoSuchAlgorithmException,
            NoSuchPaddingException,
            InvalidAlgorithmParameterException,
            IllegalBlockSizeException,
            BadPaddingException {
        final long delta = System.currentTimeMillis();

        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World1")));
        System.out.println(encrypt(key,
                                   initVector,
                                   "Hellsdo World1").matches("^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$"));
        IntStream.rangeClosed(1, 273).forEach(e -> System.out.printf("%d\t%s\n", e, encrypt(String.valueOf(e)).get()));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World2")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World3")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World4")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World5")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World6")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World7")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World8")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "Hellsdo World9")));
        System.out.println(decrypt(key, initVector, encrypt(key, initVector, "3")));
        System.out.println(encrypt(key, initVector, "3"));
        System.out.println("Delta : " + (System.currentTimeMillis() - delta));
    }
}
