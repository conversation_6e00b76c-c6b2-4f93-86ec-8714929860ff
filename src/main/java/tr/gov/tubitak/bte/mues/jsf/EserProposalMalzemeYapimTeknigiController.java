package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.EserProposalMalzemeYapimTeknigi;
import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.MalzemeYapimTeknigi;
import tr.gov.tubitak.bte.mues.model.Renk;
import tr.gov.tubitak.bte.mues.model.YapimTeknigi;
import tr.gov.tubitak.bte.mues.session.EserProposalMalzemeYapimTeknigiFacade;
import tr.gov.tubitak.bte.mues.session.MalzemeFacade;
import tr.gov.tubitak.bte.mues.session.MalzemeYapimTeknigiFacade;

@Named
@ViewScoped
public class EserProposalMalzemeYapimTeknigiController extends AbstractController<EserProposalMalzemeYapimTeknigi>
{

    private static final long                     serialVersionUID = 1035909197431936381L;

    @Inject
    private EserProposalMalzemeYapimTeknigiFacade facade;

    @Inject
    private MalzemeYapimTeknigiFacade             malzemeYapimTeknigiFacade;

    @Inject
    private MalzemeFacade                         malzemeFacade;
    
    @Inject
    private RenkController                        renkController;

    private List<EserProposalMalzemeYapimTeknigi> eserMalzemeTeknikList;

    private List<MalzemeYapimTeknigi>             malzemeYapimTeknigiList;

    private EserProposalMalzemeYapimTeknigi       itemForColor;

    private Integer                               itemIndex;

    public EserProposalMalzemeYapimTeknigiController() {
        super(EserProposalMalzemeYapimTeknigi.class);
    }

    public void handleMalzemeSelect(final SelectEvent<Malzeme> event) {
        this.getModel().setMalzeme(event.getObject());
        this.getModel().setYapimTeknigi(null);
    }
    
    public void addMultiColour() {

        this.itemForColor.getRenks().clear();

        for (final Renk tempRenk : this.renkController.getSelectionList()) {
            if (!(this.getModel()
                      .getRenks()
                      .stream()
                      .anyMatch(x -> x.getId().equals(tempRenk.getId())))) {

                this.getModel().getRenks().add(tempRenk);
            }
        }
        this.getModel()
            .getRenks()
            .removeIf(x -> this.renkController.getSelectionList()
                                              .stream()
                                              .noneMatch(s -> s.getId().equals(x.getId())));
        this.itemForColor.getRenks().addAll(this.getModel().getRenks());

        if (this.getItemIndex() != null) {
            PrimeFaces.current().ajax().update(":formEserMultipleSusleme:multipleDataTableSusleme:" + this.itemIndex + ":renkSecimSusleme");
        }

    }

    public List<YapimTeknigi> filterByNameAndAciklamaAndMalzeme(final String query) {
        return this.facade.filterByNameAndAciklamaAndMalzeme(query, this.getModel().getMalzeme());
    }

    public void handleColorSelect(final Renk renk) {
        this.getModel().getRenks().add(renk);
        if (this.itemForColor != null) {
            this.itemForColor.getRenks().add(renk);
        }
        // updating the row that is selected for color determination
        PrimeFaces.current().ajax().update(":formEserMultipleYapim:multipleDataTableYapim:" + this.itemIndex + ":renkSecimYapim");
    }

    public List<MalzemeYapimTeknigi> filterByMalzeme(final Malzeme malzeme) {
        if (malzeme != null) {
            return this.getMalzemeYapimTeknigiList().stream().filter(x -> x.getMalzeme().getAd().equals(malzeme.getAd())).collect(Collectors.toList());
        }
        return this.getMalzemeYapimTeknigiList();
    }

    public List<EserProposalMalzemeYapimTeknigi> getSelectedEserMalzemeYapimTeknigis() {
        return this.getEserMalzemeTeknikList()
                   .stream()
                   .filter(x -> ((x.getMalzeme() != null) && (x.getMalzeme().getAd() != null) && !x.getMalzeme().getAd().equals(""))
                                && ((x.getYapimTeknigi() != null) && !x.getYapimTeknigi().toString().equals("") && ((x.getYapimTeknigi().getAd() != null) && !x.getYapimTeknigi().getAd().equals(""))))
                   .collect(Collectors.toList());
    }

    public void selectItemForColor(final EserProposalMalzemeYapimTeknigi itemForColor, final Integer itemIndex) {
        this.setItemForColor(itemForColor);
        this.setItemIndex(itemIndex);
    }

    public void resetInputFieldsLists() {
        this.eserMalzemeTeknikList = null;
        super.setModel(null);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalMalzemeYapimTeknigiFacade getFacade() {
        return this.facade;
    }

    public List<MalzemeYapimTeknigi> getMalzemeYapimTeknigiList() {
        if (this.malzemeYapimTeknigiList == null) {
            this.malzemeYapimTeknigiList = this.malzemeYapimTeknigiFacade.findActive();
        }
        return this.malzemeYapimTeknigiList;
    }

    public void setMalzemeYapimTeknigiList(final List<MalzemeYapimTeknigi> malzemeYapimTeknigiList) {
        this.malzemeYapimTeknigiList = malzemeYapimTeknigiList;
    }

    public List<EserProposalMalzemeYapimTeknigi> getEserMalzemeTeknikList() {
        if (this.eserMalzemeTeknikList == null) {
            this.eserMalzemeTeknikList = new ArrayList<>();
            for (final Malzeme malzeme : this.malzemeFacade.findActive()) {
                final EserProposalMalzemeYapimTeknigi eserMalzemeYapimTeknigi = new EserProposalMalzemeYapimTeknigi();
                eserMalzemeYapimTeknigi.setMalzeme(malzeme);
                this.eserMalzemeTeknikList.add(eserMalzemeYapimTeknigi);
            }
        }
        return this.eserMalzemeTeknikList;
    }

    public void setEserMalzemeTeknikList(final List<EserProposalMalzemeYapimTeknigi> eserMalzemeTeknikList) {
        this.eserMalzemeTeknikList = eserMalzemeTeknikList;
    }

    public EserProposalMalzemeYapimTeknigi getItemForColor() {
        return this.itemForColor;
    }

    public void setItemForColor(final EserProposalMalzemeYapimTeknigi itemForColor) {
        this.itemForColor = itemForColor;
    }

    public Integer getItemIndex() {
        return this.itemIndex;
    }

    public void setItemIndex(final Integer itemIndex) {
        this.itemIndex = itemIndex;
    }
}
