package tr.gov.tubitak.bte.mues.search;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Basit kriterler için ortak özellik ve davranışların bulunduğu sınıf.
 */
@MappedSuperclass
public abstract class AbstractSimpleCriterion extends AbstractCriterion {

    private static final long   serialVersionUID = -856751063337615855L;

    private static final Logger logger           = LoggerFactory.getLogger(AbstractSimpleCriterion.class);

    /**
     * Yapıcı metot.
     */
    AbstractSimpleCriterion() {
    }

    /**
     * Yapıcı metot.
     */
    AbstractSimpleCriterion(final Integer id) {
        this.setRowId(id);
    }

    /**
     * Birinci değeri döner.
     *
     * @return birinci değer
     */
    @Transient
    public abstract String getFirstValue();

    /**
     * İkinci değeri döner.
     *
     * @return ikinci değer
     */
    @Transient
    public abstract String getSecondValue();

    /**
     * Birinci değerin metnini döner.
     *
     * @return birinci değer metni
     */
    @Transient
    public abstract String getFirstValueText();

    /**
     * İkinci değerin metnini döner.
     *
     * @return ikinci değer metni
     */
    @Transient
    public abstract String getSecondValueText();

    /**
     * Üst sınıfa kriter modelini atar. AbstractCriterionFactory sınıfında, CriteriaRegistry'den ilgili kriter alınarak yeni nesnesi oluşturulmakta,
     * sonrasında da ilk değerleri atamak için bu metot çağrılmaktadır.
     *
     * @param model kriter modeli
     */
    public void setSuperModel(final CriterionModel model) {
        this.setRowId(model.getRowId());
        this.setMetadata(model.getMetadata());
        this.setComparisonOperator(model.getCondition());
        // TODO dil gibi çok değerli ögeler için değişiklik yapılması gerekli
    }

    @Override
    @Transient
    public String getText() {
        final StringBuilder sb = new StringBuilder();
        sb.append(this.getMetadata().getIndexFaceName());
        sb.append(SearchConstants.SPACE_LITERAL);
        sb.append(this.getComparisonOperatorEnum().getConditionalText());
        sb.append(SearchConstants.SPACE_LITERAL);

        if (this.getComparisonOperatorEnum().getValueCount() > 0) {
            sb.append(this.getFirstValueText());
        }

        if (this.getComparisonOperatorEnum().getValueCount() > 1) {
            sb.append(SearchConstants.SPACE_LITERAL);
            sb.append(this.getComparisonOperatorEnum().getConnectorText());
            sb.append(SearchConstants.SPACE_LITERAL);
            sb.append(this.getSecondValueText());
        }
        return sb.toString();
    }

    /* (non-Javadoc)
     * @see tr.gov.tubitak.bte.ebelgem.search.ICriterion#getSql()
     */

    @Override
    @Transient
    public String getSql() {
        // solr query cümleciği oluşturuluyor.
        final StringBuilder sb = new StringBuilder();

        if (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.ISNULL)
            || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_EQUALS)
            || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_CONTAINS_IN)
            || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_CONTAINS)) {
            sb.append(SearchConstants.LEFT_PARANTHESIS + SearchConstants.ALL_QUERY + SearchConstants.SPACE_LITERAL);
            sb.append("-");
        }

        if (((this.getFirstValue() != null) && (this.getFirstValue().length() > 0))) {
            sb.append(this.getMetadata().getName());

            if (CriterionEnum.TEXT.getCode().equals(this.getMetadata().getDataTypeId())
                && (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.CONTAINS) || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_CONTAINS))) {

                sb.append("_ci");
            } else if (CriterionEnum.MULTI_LIST.getCode().equals(this.getMetadata().getDataTypeId())
                       && (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.CONTAINS) || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_CONTAINS))) {
                sb.append("_cim");
            }
            sb.append(SearchConstants.SOLREQUALSSIGN);
            sb.append(this.getComparisonOperatorEnum().getPrefix(this));

            sb.append(SearchConstants.sanitize(this.getFirstValue()));

            sb.append(this.getComparisonOperatorEnum().getSuffix(this));

        } else if (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.ISNULL) || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOTNULL)) {
            sb.append(this.getMetadata().getName());
            sb.append(SearchConstants.SOLREQUALSSIGN);
            sb.append(this.getComparisonOperatorEnum().getPrefix(this));

            sb.append(this.getComparisonOperatorEnum().getSuffix(this));

        }

        if (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.ISNULL)
            || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_EQUALS)
            || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_CONTAINS_IN)
            || this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.NOT_CONTAINS)) {
            sb.append(SearchConstants.RIGHT_PARANTHESIS);
        }

        return sb.toString();
    }

    @Override
    @Transient
    public String getFacet() {
        // TODO facelet için gerekli json üretilecektir.
        final StringBuilder sb = new StringBuilder("json.facet");
        final StringBuilder content = new StringBuilder();

        try {

            content.append(this.getMetadata().getName());
            content.append(SearchConstants.SOLREQUALSSIGN);
            content.append(SearchConstants.LEFT_CURLY_BRACE);

            if ((this.getMetadata().getDataTypeId() == 1) || (this.getMetadata().getDataTypeId() == 4)) {
                content.append("type:terms,");
            } else if (this.getMetadata().getDataTypeId() == 3) {
                content.append("type : range,");
            } else { // TODO: 7 dışında başka değerler de terms olmasında bir sıkıntı varmı olmasa gerek.
                content.append("type:terms,");

            }

            if ((this.getSecondValue() != null) && (this.getSecondValue().length() > 0)) {
                // content.append("prefix :" + this.getSecondValue() + SearchConstants.COMMA_LITERAL);
                content.append(Stream.of("prefix :", this.getSecondValue(), SearchConstants.COMMA_LITERAL).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining("")));
            }

            content.append(SearchConstants.FIELD);
            content.append(SearchConstants.SOLREQUALSSIGN);
            content.append(this.getMetadata().getName());

            content.append(",limit:-1,sort:index");
            content.append(SearchConstants.RIGHT_CURLY_BRACE);

            sb.append(SearchConstants.EQUALS_LETERAL);
            sb.append(SearchConstants.LEFT_CURLY_BRACE);
            sb.append(URLEncoder.encode(content.toString(), "UTF-8"));

            sb.append(SearchConstants.RIGHT_CURLY_BRACE);
            return sb.toString();
        } catch (final UnsupportedEncodingException e) {
            logger.error("[getFacet] : Hata : {}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    @Transient
    public boolean isValid() {
        if ((this.getComparisonOperatorEnum().getValueCount() > 0) && (this.getFirstValue() == null)) {
            return false;
        }
        // TODO operasyonların kontrolü burda yapılabilir (yapılmaya da bilir).
        return !((this.getComparisonOperatorEnum().getValueCount() > 1) && (this.getSecondValue() == null));

    }

    @Override
    public String toString() {
        return this.getText();
    }

}