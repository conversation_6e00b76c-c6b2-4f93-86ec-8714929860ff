package tr.gov.tubitak.bte.mues.jsf;

import java.util.Collections;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.SelectEvent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.AlanKonumu;
import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.EserDepo;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.session.AlanKonumuFacade;
import tr.gov.tubitak.bte.mues.session.EserDepoFacade;
import tr.gov.tubitak.bte.mues.session.EserFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class EserDepoController extends AbstractController<EserDepo> {

    private static final long    serialVersionUID = -1349611815551735998L;

    private static final Logger  logger           = LoggerFactory.getLogger(EserDepoController.class);

    @Inject
    private EserDepoFacade       facade;

    @Inject
    private AlanKonumuFacade     alanKonumuFacade;

    @Inject
    private transient EserFacade eserFacade;

    @Inject
    private SessionBean          sessionBean;

    private Mudurluk             mudurluk;

    private BagliBirim           birim;

    private Bina                 bina;

    private Alan                 alan;

    private boolean              disableLabDirectorate;

    public EserDepoController() {
        super(EserDepo.class);
    }

    @PostConstruct
    private void init() {
        this.mudurluk = this.sessionBean.filterByPermission("eser:ekle");
        this.newRecord(); // raporlamada model null olmasın diye eklendi
        this.changeUnitIfOnlyOne();
    }

    public void handleMudurlukChange(final SelectEvent<?> event) {
        this.mudurlukChange();
        logger.debug("Muze Mudurlugu Change {}", event.getSource());
    }

    public void mudurlukChange() {
        this.setBirim(null);
        this.setBina(null);
        this.setAlan(null);
        if (this.getModel() != null) {
            this.getModel().setAlanKonumu(null);
        }
        this.changeUnitIfOnlyOne();
    }

    public void handleBagliBirimChange(final SelectEvent<BagliBirim> event) {
        this.changeBagliBirim(this.birim);
        logger.debug("Bagli Birim Change {} ", event.getSource());
    }

    public void handleBinaChange(final SelectEvent<Bina> event) {
        this.changeBina(event.getObject());
    }

    public void handleAlanChange(final SelectEvent<AlanKonumu> event) {
        this.changeAlan(this.alan);
        logger.debug("Alan Change {} ", event.getSource());
    }

    public void changeBagliBirim(final BagliBirim bagliBirim) {
        this.setBirim(bagliBirim);
        this.setBina(null);
        this.setAlan(null);
        this.getModel().setAlanKonumu(null);
    }

    public void changeBina(final Bina bina) {
        this.bina = bina;
        this.setAlan(null);
        this.getModel().setAlanKonumu(null);
    }

    public void changeAlan(final Alan alan) {
        this.alan = alan;
        this.getModel().setAlanKonumu(null);
    }

    // Sets unit by mudurluk if only one
    public void changeUnitIfOnlyOne() {
        if (this.mudurluk != null) {
            final List<BagliBirim> bagliBirimList = this.filterByNameAndMudurluk("");
            if (bagliBirimList.size() == 1) {
                this.setBirim(bagliBirimList.iterator().next());
            }
        }
    }

    public List<BagliBirim> filterByNameAndMudurluk(final String value) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndMudurluk(value, this.getMudurluk());
    }

    public List<Bina> filterByNameAndBagliBirim(final String value) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndBagliBirim(value, this.birim);
    }

    public List<Alan> filterByNameAndBina(final String query) {
        final List<Alan> alanList = this.alanKonumuFacade.findByNameAndAciklamaAndBina(query, this.bina);
        Collections.sort(alanList, MuesUtil.comparing(Alan::getAd));
        return alanList;
    }

    public List<AlanKonumu> filterByNameAndAlan(final String query) {
        final List<AlanKonumu> alanKonumuList = this.alanKonumuFacade.findByNameAndAciklamaAndAlan(query, this.alan);
        Collections.sort(alanKonumuList, MuesUtil.comparing(AlanKonumu::getAd));
        return alanKonumuList;

    }

    public void restoreFields(final EserDepo eserDepo) {
        if (eserDepo != null) {
            super.setModel(eserDepo);

            if (eserDepo.getAlanKonumu() != null) {
                this.setAlan(eserDepo.getAlanKonumu().getAlan());

                if (eserDepo.getAlanKonumu().getAlan() != null) {
                    this.setBina(eserDepo.getAlanKonumu().getAlan().getBina());

                    if (eserDepo.getAlanKonumu().getAlan().getBina() != null) {
                        this.setBirim(eserDepo.getAlanKonumu().getAlan().getBina().getBagliBirim());

                        if (eserDepo.getAlanKonumu().getAlan().getBina().getBagliBirim() != null) {
                            this.setMudurluk(eserDepo.getAlanKonumu().getAlan().getBina().getBagliBirim().getMudurluk());
                        }
                    }
                }
            }
        }
    }

    public Mudurluk fetchMuzeMudurlugu(final EserDepo eserDepo) {
        if ((eserDepo != null)
            && (eserDepo.getAlanKonumu() != null)
            && (eserDepo.getAlanKonumu().getAlan() != null)
            && (eserDepo.getAlanKonumu().getAlan().getBina() != null)
            && (eserDepo.getAlanKonumu().getAlan().getBina().getBagliBirim() != null)) {

            return eserDepo.getAlanKonumu().getAlan().getBina().getBagliBirim().getMudurluk();
        }
        return null;
    }

    // getters and setters .....................................................

    @Override
    public EserDepoFacade getFacade() {
        return this.facade;
    }

    public EserFacade getEserFacade() {
        return this.eserFacade;
    }

    public void setEserFacade(final EserFacade eserFacade) {
        this.eserFacade = eserFacade;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public BagliBirim getBirim() {
        return this.birim;
    }

    public void setBirim(final BagliBirim birim) {
        this.birim = birim;
    }

    public Bina getBina() {
        return this.bina;
    }

    public void setBina(final Bina bina) {
        this.bina = bina;
    }

    public Alan getAlan() {
        return this.alan;
    }

    public void setAlan(final Alan alan) {
        this.alan = alan;
    }

    public boolean isDisableLabDirectorate() {
        return this.disableLabDirectorate;
    }

    public void setDisableLabDirectorate(final boolean disableLabDirectorate) {
        this.disableLabDirectorate = disableLabDirectorate;
    }

}
