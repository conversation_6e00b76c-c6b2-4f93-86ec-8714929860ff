package tr.gov.tubitak.bte.mues.constraint.validator;

import java.lang.reflect.InvocationTargetException;

import org.omnifaces.util.copier.Copier;
import org.primefaces.PrimeFaces;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.ArtifactJoinable;
import tr.gov.tubitak.bte.mues.util.MuesException;

public class ArtifactJoinableCopier implements Copier {

    private static final Logger logger = LoggerFactory.getLogger(ArtifactJoinableCopier.class);

    @Override
    public ArtifactJoinable copy(final Object object) {
        final ArtifactJoinable original = (ArtifactJoinable) object;

        try {
            final ArtifactJoinable copy = original.getClass().getDeclaredConstructor().newInstance();

            copy.setCombinedArtifacts(original.getCombinedArtifacts());
            return copy;

        } catch (final InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException | SecurityException e) {
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary': "
                                     + "'Birleştirme için eser olmaması hatası'"
                                     + ", 'detail': "
                                     + "'En az 2 eser seçiniz.'"
                                     + ", 'severity':'Error'})");

            logger.error("[copy] : Hata : {}", e.getMessage(), e);

            throw new MuesException("");
        }
    }

}
