package tr.gov.tubitak.bte.mues.solr.util;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.solr.parse.JsonParsing;

public class HttpRequestProcessor {

    private static final Logger logger = LoggerFactory.getLogger(HttpRequestProcessor.class);

    public static InputStream getInputStream(final String queryUrl) throws IOException {
        logger.debug(queryUrl);
        final URL urlObject = new URL(queryUrl);
        final HttpURLConnection con = (HttpURLConnection) urlObject.openConnection();
        con.setConnectTimeout(5000);
        con.setReadTimeout(8000);

        // optional default is GET
        con.setRequestMethod("GET");

        // add request header
        con.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; en-GB;     rv:1.9.2.13) Gecko/20101203 Firefox/3.6.13 (.NET CLR 3.5.30729)");

        return con.getInputStream();
    }

    public static InputStream getInputStreamWithPost(final String url) throws IOException {

        final URL obj = new URL(url);
        final HttpURLConnection con = (HttpURLConnection) obj.openConnection();

        con.setConnectTimeout(2000);
        con.setReadTimeout(5000);

        // optional default is POST
        con.setRequestMethod("POST");

        // add request header
        con.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows; U; Windows NT 6.1; en-GB;     rv:1.9.2.13) Gecko/20101203 Firefox/3.6.13 (.NET CLR 3.5.30729)");

        return con.getInputStream();

    }

    public static void main(final String[] args) throws IOException, InterruptedException {
        final String jsonData = "json.facet=" + "{" + URLEncoder.encode("'x' : 'unique(eserEserOzelAdi)'", "UTF-8") + "}";

        final JsonParsing jsonParsing = new JsonParsing();
        int count = 0;
        while (true) { // jrebel fast development
            final String str = "http://10.1.37.181:8983/solr/Eser/select?indent=on&q=*:*&wt=json&fl=id" + "&" + jsonData;
            try (InputStream inputStream = HttpRequestProcessor.getInputStream(str)) {
                logger.debug("[main] : {}", str);
                logger.debug("[main] : {}", jsonParsing.parseFacets(inputStream, "eserEserOzelAdi"));
            } catch (final Exception e) {
                logger.error("[main] : Hata : {}", e.getMessage(), e);
            }
            Thread.sleep(1000);
            if (count == 10000000) {
                break;
            }
            count++;
        }

    }
}
