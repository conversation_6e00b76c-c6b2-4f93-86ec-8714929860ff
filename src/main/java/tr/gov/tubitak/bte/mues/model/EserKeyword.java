package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

@Entity
@Table(name = "Eser_Keyword")
public class EserKeyword extends EserKeywordSuper {

    private static final long serialVersionUID = 5262644200337308054L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              eser;

    public EserKeyword() {

    }

    public EserKeyword(final Eser eser, final Keyword keyword) {
        this.eser = eser;
        this.setKeyword(keyword);
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    @Override
    public String toString() {
        if ((this.eser == null) || (this.getKeyword() == null)) {
            return "both are null";
        }
        return "{Eser: " + this.eser.getEserOzelAdi() + ", Keyword: " + this.getKeyword().getAd() + "}";
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.eser, this.getKeyword());
    }

}
