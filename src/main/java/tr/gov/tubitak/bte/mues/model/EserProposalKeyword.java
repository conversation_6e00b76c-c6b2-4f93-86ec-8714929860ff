package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

@Entity
@Table(name = "EP_Eser_Keyword")
@NamedQuery(name = "EserProposalKeyword.findEagerById", query = "SELECT x FROM EserProposalKeyword x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.keyword WHERE x.id = :id")
@NamedQuery(name = "EserProposalKeyword.findAll", query = "SELECT s FROM EserProposalKeyword s")
@NamedQuery(name = "EserProposalKeyword.findActive", query = "SELECT s FROM EserProposalKeyword s WHERE s.aktif = true AND s.silinmis = false")

public class EserProposalKeyword extends EserKeywordSuper {

    private static final long serialVersionUID = 5262644200337390054L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalKeyword() {

    }

    public EserProposalKeyword(final EserProposal eser, final Keyword keyword) {
        this.eser = eser;
        this.setKeyword(keyword);
    }

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
