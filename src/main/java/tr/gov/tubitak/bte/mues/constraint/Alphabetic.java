package tr.gov.tubitak.bte.mues.constraint;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.ReportAsSingleViolation;
import javax.validation.constraints.Pattern;

@ReportAsSingleViolation
@Pattern(regexp = "[A-Za-zçğıöşüÇĞİÖŞÜ][A-Za-zçğıöşüÇĞİÖŞÜ ]*")
@Constraint(validatedBy = {})
@Documented
@Target({ ElementType.METHOD, ElementType.FIELD, ElementType.ANNOTATION_TYPE, ElementType.CONSTRUCTOR, ElementType.PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
public @interface Alphabetic {

    String message() default "{tr.gov.tubitak.bte.mues.constraint.Alphabetic}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
