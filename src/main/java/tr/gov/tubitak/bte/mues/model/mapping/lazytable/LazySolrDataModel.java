package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;

import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrQuery.SortClause;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrException;
import org.primefaces.PrimeFaces;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.IRankedArtifact;
import tr.gov.tubitak.bte.mues.model.Identifiable;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.search.AbstractSearchController;
import tr.gov.tubitak.bte.mues.search.AbstractSimpleCriterion;
import tr.gov.tubitak.bte.mues.search.ComparisonOperatorEnum;
import tr.gov.tubitak.bte.mues.search.CriterionEnum;
import tr.gov.tubitak.bte.mues.search.CriterionFactory;
import tr.gov.tubitak.bte.mues.search.CriterionModel;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

public class LazySolrDataModel<T extends Identifiable<?>> extends LazyDataModel<T> {

    private static final long      serialVersionUID = -4739489425974372483L;

    private final transient Logger logger;

    protected final SolrSearcher   solrSearcher;

    private final Class<T>         clazz;

    protected boolean              firstTime;

    private List<T>                data;

    private List<String>           imageSimilarIDs;

    AbstractSearchController<T>    searchController;

    public LazySolrDataModel(final SolrSearcher solrSearcher, final Class<T> clazz, final AbstractSearchController<T> searchController) {
        this.clazz = clazz;
        this.solrSearcher = solrSearcher;
        this.firstTime = true;
        this.logger = LoggerFactory.getLogger(LazySolrDataModel.class);
        this.searchController = searchController;
    }

    public LazySolrDataModel(final SolrSearcher solrSearcher, final Class<T> clazz, final Class<?> originClass) {
        this.clazz = clazz;
        this.solrSearcher = solrSearcher;
        this.firstTime = true;
        this.logger = LoggerFactory.getLogger(originClass);
    }

    /**
     * Constructor for image similarity
     */
    public LazySolrDataModel(final SolrSearcher solrSearcher, final Class<T> clazz, final AbstractSearchController<T> searchController, final List<String> imageSimilarIDs) {
        this.clazz = clazz;
        this.solrSearcher = solrSearcher;
        this.searchController = searchController;
        this.firstTime = true;
        this.imageSimilarIDs = imageSimilarIDs;
        this.logger = LoggerFactory.getLogger(LazySolrDataModel.class);
    }

    @Override
    public List<T> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        if ((filterBy != null)) {
            this.solrSearcher.getQuery().setFilterQueries();

            for (final Map.Entry<String, FilterMeta> entry : filterBy.entrySet()) {

                final String filterProperty = entry.getKey();

                final Object filterValue = entry.getValue().getFilterValue();

                if (filterValue != null) {
                    final Metadata metaData = this.solrSearcher.getMetaDataMap().get(filterProperty);

                    final CriterionModel criterionModel = new CriterionModel();
                    criterionModel.setType(metaData.getDataTypeId());
                    criterionModel.setMetadata(metaData);

                    if (criterionModel.getType() == 3) { // if type is date and filter by text needed optimize edilebilir mi.
                        criterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);
                        criterionModel.setType(CriterionEnum.TEXT.getCode());
                        metaData.setDataTypeId(CriterionEnum.TEXT.getCode());

                    } else if (criterionModel.getType() != 2) { // if type is not integer
                        criterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);
                    } else {
                        criterionModel.setCondition(ComparisonOperatorEnum.EQUALS);
                    }

                    criterionModel.setTextValue1(filterValue.toString());

                    final AbstractSimpleCriterion abstractSimpleCriterion = (AbstractSimpleCriterion) CriterionFactory.createCriterion(criterionModel);
                    final String sql = abstractSimpleCriterion.getSql();
                    this.solrSearcher.getQuery().addFilterQuery(sql);
                }
            }
        }

        if (this.solrSearcher != null) {
            if (this.firstTime) {
                PrimeFaces.current().executeScript("PF('searchTableWidget').paginator.setPage(0)");
            }
            return this.solrLoad(first, pageSize, sortBy);
        } else {
            return Collections.emptyList();

        }

    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
        return 0; // see the javadoc of this method
    }

    private List<T> solrLoad(final int first, final int pageSize, final Map<String, SortMeta> sortBy) {

        final List<SortClause> sortClauses = new ArrayList<>();

        if ((sortBy != null) && !sortBy.isEmpty()) {
            for (final Iterator<Entry<String, SortMeta>> it = sortBy.entrySet().iterator(); it.hasNext();) {

                final Map.Entry<String, SortMeta> pair = it.next();
                final SortMeta sortMeta = pair.getValue();

                final Metadata metaData = this.solrSearcher.getMetaDataMap().get(sortMeta.getField());

                ORDER order = ORDER.desc;

                if (SortOrder.ASCENDING.name().equals(sortMeta.getOrder().name())) {
                    order = ORDER.asc;
                }

                if ((metaData != null) && (metaData.getDataTypeId() == 1)) {
                    sortClauses.add(new SortClause(sortMeta.getField() + "_tr", order));
                } else {
                    sortClauses.add(new SortClause(sortMeta.getField(), order));
                }

            }
            this.solrSearcher.getQuery().setSorts(sortClauses);
        }

        return this.solrLoad(first, pageSize);
    }

    private List<T> solrLoad(final int first, final int pageSize) {
        this.solrSearcher.getQuery().setStart(first);
        this.solrSearcher.getQuery().setRows(pageSize);

        try {
            if (this.logger.isDebugEnabled()) {
                this.logger.debug(this.solrSearcher.getQuery().toString());
            }

            final QueryResponse solrResponse = this.solrSearcher.makeSearch();

            this.fixSolrChildMappingFix(solrResponse);

            final List<T> listEser = solrResponse.getBeans(this.clazz);

            final SolrDocumentList results = solrResponse.getResults();
            this.setRowCount((int) results.getNumFound());
            this.recalculateFirst(first, pageSize, this.getRowCount());

            if (this.firstTime) {
                this.solrSearcher.setTotalCount(results.getNumFound());
                this.solrSearcher.setDuration(solrResponse.getElapsedTime());
                this.firstTime = false;
            }

            this.solrSearcher.setStatisticsList(Optional.ofNullable(solrResponse.getFieldStatsInfo())
                                                        .orElse(Collections.emptyMap())
                                                        .entrySet()
                                                        .stream()
                                                        .filter(x -> x.getValue().getCount() > 0)
                                                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
            this.data = listEser;

            // image similarity ranking for the first page
            if ((this.imageSimilarIDs != null) && !this.imageSimilarIDs.isEmpty() && (first == 0)) {
                this.rankWithImageContent(pageSize);
            }

            return this.data;

        } catch (final SolrServerException | SolrException | IOException e) {

            MuesUtil.showMessage("Solr Sunucusuna Ulaşmada Hata", FacesMessage.SEVERITY_ERROR);
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Solr Sunucusuna Ulaşmada Hata'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine Başvurunuz. '"
                                     + ", 'severity':'error'})");
            this.logger.error("[solrLoad] : Hata : {}", e.getMessage(), e);
            return Collections.emptyList();
        }

    }

    protected void fixSolrChildMappingFix(final QueryResponse solrResponse) {
        // do nothing if there is no child document.

    }

    /***
     * Manipulate the lazy solr data to set similarity ranking set rank for top 100 records
     */
    @SuppressWarnings("unchecked")
    private void rankWithImageContent(final int pageSize) {
        int i = 1;
        for (final String s : this.imageSimilarIDs) {
            final IRankedArtifact<?> art = ((List<IRankedArtifact<?>>) this.data).stream().filter(a -> a.getUid().toString().equals(s)).findAny().orElse(null);
            if (art != null) {
                art.setRank(i);
                i++;
            }
            if ((i == (pageSize + 1)) || (i > 100)) {
                break;
            }
        }
    }

    @Override
    public T getRowData(final String rowKey) {
        // Ekranda yapilan secilimlerin gorunmesi icin yapilmistir.
        for (final T row : this.searchController.getSelectionList()) {
            if (row.getId().toString().equals(rowKey)) {
                return row;
            }
        }

        for (final T row : this.data) {
            if (row.getId().toString().equals(rowKey)) {
                return row;
            }
        }

        return null;
    }

    @Override
    public String getRowKey(final T rowRecord) {
        return rowRecord.getId().toString();
    }

    public List<T> getData() {
        return this.data;
    }

    public void setData(final List<T> data) {
        this.data = data;
    }

    public SolrSearcher getSolrSearcher() {
        return this.solrSearcher;
    }

    public Class<T> getClazz() {
        return this.clazz;
    }
}
