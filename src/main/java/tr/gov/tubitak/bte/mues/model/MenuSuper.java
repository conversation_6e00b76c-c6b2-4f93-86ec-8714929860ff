package tr.gov.tubitak.bte.mues.model;

import java.util.List;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
 */

@MappedSuperclass

@NamedQuery(name = "Menu.findEagerById", query = "SELECT m FROM Menu m WHERE m.id = :id")
@NamedQuery(name = "Menu.findAll", query = "SELECT m FROM Menu m ORDER BY m.secilebilir, m.parent, m.menuSirasi")
@NamedQuery(name = "Menu.findRootsByName", query = "SELECT m FROM Menu m WHERE m.aktif = true AND m.silinmis = false AND m.parent IS NULL AND m.ad LIKE :ad ORDER BY m.menuSirasi")
@NamedQuery(name = "Menu.findActive", query = "SELECT m FROM Menu m WHERE m.aktif = true AND m.silinmis = false ORDER BY m.secilebilir, m.menuSirasi")
@NamedQuery(name = "Menu.fetchMenuByHierarchy", query = "SELECT DISTINCT(m) FROM Menu m LEFT JOIN FETCH m.children c WHERE m.aktif = true AND m.silinmis = false AND c.aktif = true AND c.silinmis = false AND m.secilebilir = false AND m.parent IS NULL ORDER BY m.menuSirasi, c.menuSirasi")
public class MenuSuper extends AbstractEntity {

    private static final long serialVersionUID = -1094719437907436172L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "BUNDLE_TITLE", length = 50)
    private String            bundleTitle;

    @Size(max = 50)
    @Column(name = "PAGE_ADDRESS", length = 50)
    private String            pageAddress;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "HELP_CONTENT_PATH", length = 150)
    private String            helpContentPath;

    @Size(max = 50)
    @Column(name = "ICON", length = 50)
    private String            icon;

    @Size(max = 50)
    @Column(name = "REQUIRED_PERMISSION", length = 50)
    private String            requiredPermission;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Column(name = "SECILEBILIR")
    private Boolean           secilebilir;

    @Column(name = "MENU_SIRASI")
    private Integer           menuSirasi;

    @ManyToOne
    @JoinColumn(name = "parentMenuId")
    private Menu              parent;

    @OneToMany(mappedBy = "parent")
    private List<Menu>        children;

    public MenuSuper() {
        // default constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getBundleTitle() {
        return this.bundleTitle;
    }

    public void setBundleTitle(final String bundleTitle) {
        this.bundleTitle = bundleTitle;
    }

    public String getPageAddress() {
        return this.pageAddress;
    }

    public void setPageAddress(final String pageAddress) {
        this.pageAddress = pageAddress;
    }

    public String getHelpContentPath() {
        return this.helpContentPath;
    }

    public void setHelpContentPath(final String helpContentPath) {
        this.helpContentPath = helpContentPath;
    }

    public String getIcon() {
        return this.icon;
    }

    public void setIcon(final String icon) {
        this.icon = icon;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Boolean getSecilebilir() {
        return this.secilebilir;
    }

    public void setSecilebilir(final Boolean secilebilir) {
        this.secilebilir = secilebilir;
    }

    public Integer getMenuSirasi() {
        return this.menuSirasi;
    }

    public void setMenuSirasi(final Integer menuSirasi) {
        this.menuSirasi = menuSirasi;
    }

    public String getRequiredPermission() {
        return this.requiredPermission;
    }

    public void setRequiredPermission(final String requiredPermission) {
        this.requiredPermission = requiredPermission;
    }

    public Menu getParent() {
        return this.parent;
    }

    public void setParent(final Menu parent) {
        this.parent = parent;
    }

    public List<Menu> getChildren() {
        return this.children;
    }

    public void setChildren(final List<Menu> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("---");
    }

}
