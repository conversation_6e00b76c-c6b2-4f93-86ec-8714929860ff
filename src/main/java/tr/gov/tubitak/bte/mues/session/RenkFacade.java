package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Renk;

/**
 *
*
 */
@RequestScoped
public class RenkFacade extends AbstractFacade<Renk> {

    public RenkFacade() {
        super(Renk.class);
    }

    public List<Renk> filterByNameAndDescription(final String query) {
        return this.em.createNamedQuery("Renk.findByNameAndDescription", Renk.class).setParameter("ad", "%" + query + "%").getResultList();
    }

}
