package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardLanguage;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardLanguageFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class VirtualKeyboardLanguageController extends AbstractController<VirtualKeyboardLanguage>  implements SingleFileUploadable {

    private static final long             serialVersionUID = 5315937074953322347L;

    @Inject
    private VirtualKeyboardLanguageFacade facade;
    
    @Inject
    private FileUploadHelper       fileUploadHelper;
    
    public VirtualKeyboardLanguageController() {   	
        super(VirtualKeyboardLanguage.class);
    }
    
    @Override
    public VirtualKeyboardLanguageFacade getFacade() {
        return this.facade;
    }
    
    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath((this.fileUploadHelper.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK)));
        }
    }
    
    public void handleDilChange(final SelectEvent<Dil> event) {
        this.getModel().setDil(event.getObject());
    }
    
    public boolean isDilSelected() {
    	return this.getModel().getDil() == null ? false : true;
    }

    public List<VirtualKeyboardLanguage> filterByLangName(final String query) {
        return this.facade.filterByLangName(query);
    }

    public String redirectToVirtualKeyboardCharacterPage(final Integer dilId) {
        return "/liste/sanal-klavye-harf?faces-redirect=true&dil=" + dilId;
    }
    
	public FileUploadHelper getFileUploadHelper() {
		return fileUploadHelper;
	}

	public void setFileUploadHelper(final FileUploadHelper fileUploadHelper) {
		this.fileUploadHelper = fileUploadHelper;
	}  

}
