package tr.gov.tubitak.bte.mues.model;

import java.text.SimpleDateFormat;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.stream.Collectors;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.ValidEserHareket;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ESER_HAREKET")
@ValidEserHareket
public class EserHareket extends EserHareketSuper {

    private static final long     serialVersionUID = -4077839579035405418L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser                  eser;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_GELIS_SEKLI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MuesPick              eserGelisSekli;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "MUZE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk              mudurluk;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "TESLIM_ALAN_PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Personel              teslimAlanPersonel;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "TESLIM_EDEN_PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Personel              teslimEdenPersonel;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eserHareket", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserHareketSahis> teslimEdenSahis;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "TESLIM_EDEN_TUZEL_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    protected TuzelKisi           teslimEdenTuzelKisi;

    public EserHareket() {
        // default Constructor
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    public MuesPick getEserGelisSekli() {
        return this.eserGelisSekli;
    }

    public void setEserGelisSekli(final MuesPick eserGelisSekli) {
        this.eserGelisSekli = eserGelisSekli;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    @Override
    public Personel getTeslimAlanPersonel() {
        return this.teslimAlanPersonel;
    }

    public void setTeslimAlanPersonel(final Personel teslimAlanPersonel) {
        this.teslimAlanPersonel = teslimAlanPersonel;
    }

    @Override
    public Personel getTeslimEdenPersonel() {
        return this.teslimEdenPersonel;
    }

    public void setTeslimEdenPersonel(final Personel teslimEdenPersonel) {
        this.teslimEdenPersonel = teslimEdenPersonel;
    }

    @Override
    public String toString() {
        final StringBuilder str = new StringBuilder();

        str.append(this.eserGelisSekli.getAd());
        str.append(": ");

        if ("Kazı".equals(this.eserGelisSekli.getAd())) {
            str.append(this.kazi.getAd());
        } else if ("Araştırma".equals(this.eserGelisSekli.getAd())) {
            str.append(this.arastirma.getAd());
        }

        return str.toString();

    }

    public Set<EserHareketSahis> getTeslimEdenSahis() {
        if (this.teslimEdenSahis == null) {
            this.teslimEdenSahis = new LinkedHashSet<>();
        }
        return this.teslimEdenSahis;
    }

    public void setTeslimEdenSahis(final Set<EserHareketSahis> teslimEdenSahis) {
        this.teslimEdenSahis = teslimEdenSahis;
    }

    public TuzelKisi getTeslimEdenTuzelKisi() {
        return this.teslimEdenTuzelKisi;
    }

    public void setTeslimEdenTuzelKisi(final TuzelKisi teslimEdenTuzelKisi) {
        this.teslimEdenTuzelKisi = teslimEdenTuzelKisi;
    }

    @Override
    public String getTitle() {

        if (this.arastirma != null) {
            return this.arastirma.getTitle();
        } else if (this.kazi != null) {
            return this.kazi.getTitle();
        } else if (this.mudurluk != null) {
            return "Devreden müzesi " + this.mudurluk.getTitle() + " olan Devir";
        } else if (this.teslimEdenTuzelKisi != null) {
            return this.teslimEdenTuzelKisi.getTitle();
        } else if (this.teslimEdenPersonel != null) {
            return this.teslimEdenPersonel.getTitle();
        } else if (this.getTeslimEdenSahis() != null) {
            return this.getTeslimEdenSahis().stream().map(EserHareketSahis::getTitle).collect(Collectors.joining(", "));
        } else if (this.teslimAlanPersonel != null) {
            return this.teslimAlanPersonel.getTitle();
        } else if (this.onayTarihi != null) {
            final SimpleDateFormat dateFormat = new SimpleDateFormat("dd.MM.yyyy");
            return dateFormat.format(this.onayTarihi) + " komisyon tarihli Sayım Fazlası";
        } else if (this.onaySayisi != null) {
            return this.onaySayisi + " onay sayılı Sayım Fazlası";
        } else if (this.iadeEdenUlke != null) {
            return "İade eden ülkesi " + this.iadeEdenUlke.getTitle() + " olan Yurtdışından İade";
        }
        return this.envanterNo;
    }

}
