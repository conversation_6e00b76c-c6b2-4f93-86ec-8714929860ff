package tr.gov.tubitak.bte.mues.session;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.enterprise.context.RequestScoped;
import javax.persistence.PersistenceException;
import javax.transaction.Transactional;
import javax.validation.ConstraintViolationException;

import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.EserVersion;
import tr.gov.tubitak.bte.mues.model.EserZimmet;
import tr.gov.tubitak.bte.mues.model.LiabilityOperationEnum;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class EserZimmetFacade extends AbstractFacade<EserZimmet> {

    static final int BATCH_SIZE = 1000;

    public EserZimmetFacade() {
        super(EserZimmet.class);
    }

    public List<EserZimmet> findApprovedEserByUserId(final Personel personel, final Integer version) {
        return this.getEM().createNamedQuery("EserZimmet.findApprovedEserByUserId", EserZimmet.class).setParameter("zimmetPersonel", personel).setParameter("version", version).getResultList();
    }

    public List<EserZimmet> findbyEserId(final Integer eserId) {
        return this.getEM().createNamedQuery("EserZimmet.findbyEserId", EserZimmet.class).setParameter("id", eserId).getResultList();
    }

    public List<EserZimmet> validateBeforeDelete(final Integer id, final Mudurluk mudurluk) {
        return this.em.createNamedQuery("EserZimmet.validateBeforeDelete", EserZimmet.class).setParameter("id", id).setParameter("mudurluk", mudurluk).getResultList();
    }

    /***
     * Coklu zimmet devrinde (personelin tum eserleri icin) bulk islem yapan metot
     * 
     * @param transferer
     * @param review
     * @param assigneeIdList
     * @param liabilityDate
     * @param aciklama
     * @param isRemoved
     * @return the DBOperationResult
     */
    @Transactional
    public DBOperationResult bulkUpdateForLiability(final int transferer,
                                                    final int review,
                                                    final List<Integer> assigneeIdList,
                                                    final Date liabilityDate,
                                                    final String aciklama,
                                                    final Integer stateCode,
                                                    final List<Integer> excludeEserIdList) {
        try {

            final Date date = new Date();

            // update WORKFLOW (zimmetli eserlerinin id'sine göre update eder)

            final List<?> list = this.getEM()
                                     .createNativeQuery("(SELECT (case when ESER.permanentId is null then 'G.' + CAST(ESER_ID as VARCHAR) else FORMAT( ESER.permanentId, 'TR\\.M\\.000\\.000\\.000') end)  AS eserEserId FROM ESER_ZIMMET LEFT JOIN ESER on ESER_ZIMMET.ESER_ID = ESER.ID WHERE ZIMMET_PERSONEL_ID = :transferer "
                                                        + " AND ESER_ZIMMET.AKTIF=1 AND ESER_ZIMMET.SILINMIS=0 AND ESER_ID not in (:eserIds) AND  ESER.updateInProgress != 1 AND  ESER.SILINMIS = 0) ")
                                     .setParameter("transferer", transferer)
                                     .setParameter("eserIds", excludeEserIdList)
                                     .getResultList();

            final String message = list.stream()
                                       .limit(10)
                                       .map(String::valueOf)
                                       .collect(Collectors.joining(", ", "", list.size() > 10 ? "..." : ""));

            this.getEM()
                .createNativeQuery("UPDATE Workflow SET dateModified=:date, review=:review WHERE AKTIF=1 AND SILINMIS=0  AND eserId IN "
                                   + "(SELECT ESER_ID FROM ESER_ZIMMET LEFT JOIN ESER on ESER_ZIMMET.ESER_ID = ESER.ID WHERE ZIMMET_PERSONEL_ID=:transferer "
                                   + "AND ESER_ZIMMET.AKTIF=1 AND ESER_ZIMMET.SILINMIS=0 AND ESER_ID not in (:eserIds) AND  ESER.updateInProgress != 1 AND  ESER.SILINMIS = 0) ")
                .setParameter("transferer", transferer)
                .setParameter("date", date)
                .setParameter("eserIds", excludeEserIdList)
                .setParameter("review", review)
                .executeUpdate();

            // insert ESER_ZIMMET (kendine zaten zimmetli olmayan eserler için insert yapar)
            // zimmetten dusme kayitlari icin OR :transferer = :assignee eklenmistir. Kendinde olan zimmet kayitlari listeden dusulecek zimmetlisi
            // sadece kendisi olanlar istisna.
            for (final int assigneeId : assigneeIdList) {
                this.getEM()
                    .createNativeQuery("insert into ESER_ZIMMET_TEMP (ESER_ID, DEVRALAN_ID, DEVREDEN_ID, ZIMMET_TARIHI, ACIKLAMA, AKTIF, SILINMIS, STATE) "
                                       + " select distinct ESER_ID, :assignee, :transferer, :date, :aciklama, ESER_ZIMMET.AKTIF, ESER_ZIMMET.SILINMIS, :state from ESER_ZIMMET "
                                       + "LEFT JOIN ESER on ESER_ZIMMET.ESER_ID = ESER.ID WHERE ZIMMET_PERSONEL_ID=:transferer  AND ESER_ZIMMET.AKTIF=1 AND ESER_ZIMMET.SILINMIS=0 AND  ESER.updateInProgress != 1 AND  ESER.SILINMIS = 0  "
                                       + " and ( ESER_ID not in (select distinct ESER_ID from ESER_ZIMMET WHERE ZIMMET_PERSONEL_ID=:assignee and AKTIF=1 and SILINMIS=0) OR :transferer = :assignee ) AND ESER_ID not in (:eserIds) ")

                    .setParameter("transferer", transferer)
                    .setParameter("assignee", assigneeId)
                    .setParameter("date", liabilityDate)
                    .setParameter("aciklama", aciklama)
                    .setParameter("eserIds", excludeEserIdList)
                    .setParameter("state", stateCode)
                    .executeUpdate();
            }

            // FLUSH ALL
            this.getEM().flush();
            return DBOperationResult.success(message);

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
    }

    /***
     * Coklu zimmet islemi icin tablodan yapılan secime göre batch islem yapan metot
     * 
     * @param review
     * @param eserZimmetTuples
     * @param aciklama
     * @param assigneeIdList
     * @param liabilityDate
     * @return the DBOperationResult
     */
    @Transactional
    public DBOperationResult batchUpdateForLiability(final Integer review,
                                                     final List<EserZimmet> eserZimmetTuples,
                                                     final String aciklama,
                                                     final List<Integer> assigneeIdList,
                                                     final Date liabilityDate,
                                                     final Integer liablityOperationCode) {
        try {
            final int n = eserZimmetTuples.size();
            final StringBuilder message = new StringBuilder("");
            for (int i = 0; i < n; i += EserZimmetFacade.BATCH_SIZE) {
                final List<EserZimmet> subList = new ArrayList<>(eserZimmetTuples.subList(i, Math.min(n, i + EserZimmetFacade.BATCH_SIZE)));
                final List<Integer> eserIDs = new ArrayList<>();
                subList.stream().forEach(ez -> eserIDs.add(ez.getEser().getId()));
                final List<Integer> eserZimmetIDs = new ArrayList<>();
                subList.stream().forEach(ez -> eserZimmetIDs.add(ez.getId()));

                // update WORKFLOW
                this.getEM()
                    .createNativeQuery("update Workflow set dateModified=:date, review=:review where AKTIF=1 and SILINMIS=0 and eserId in (:eserIDs)")
                    .setParameter("eserIDs", eserIDs)
                    .setParameter("date", new Date())
                    .setParameter("review", review)
                    .executeUpdate();


                // insert ESER_ZIMMET for new assignees

                for (final int assigneeId : assigneeIdList) {
                    this.getEM()
                        .createNativeQuery("insert into ESER_ZIMMET_TEMP (AKTIF, SILINMIS, ACIKLAMA, ZIMMET_TARIHI, DEVRALAN_ID, DEVREDEN_ID, ESER_ID, STATE)"
                                           + " select distinct 1, 0, :aciklama, :date, :assignee, ZIMMET_PERSONEL_ID, ESER_ID, :state from ESER_ZIMMET"
                                           + " where ID in (:eserZimmetIDs) AND AKTIF=1 and SILINMIS=0")
                        .setParameter("eserZimmetIDs", eserZimmetIDs)
                        .setParameter("aciklama", aciklama)
                        .setParameter("date", liabilityDate)
                        .setParameter("assignee", assigneeId)
                        .setParameter("state", liablityOperationCode)
                        .executeUpdate();
                }
                message.append(subList.stream()
                                      .limit(15)
                                      .map(EserZimmet::getEser)
                                      .map(Eser::getOneId)
                                      .collect(Collectors.joining(", ", "{ ", " }")));

                if (i > 15) {
                    message.append("...");
                }

            }

            // FLUSH ALL
            this.getEM().flush();
            return DBOperationResult.success(message.toString());

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
    }

    /***
     * Draft eserler icin zimmet devrini bulk olarak (personelin tüm kayitlari icin) yapan metot
     * 
     * @param assignee
     * @param user
     * @param states
     * @return the DBOperationResult
     */
    @Transactional
    public DBOperationResult bulkUpdateForDrafts(final int assignee, final int user, final List<Integer> states) {
        try {

            this.getEM()
                .createNativeQuery("update ESER set YARATMA_KULLANICI_ID=:assignee, DUZENLEME_KULLANICI_ID=:assignee WHERE AKTIF=1 and SILINMIS=0 and ID in "
                                   + "(select eserId from Workflow where modifier=:user and review IN :types and AKTIF=1 and SILINMIS=0)")
                .setParameter("user", user)
                .setParameter("assignee", assignee)
                .setParameter("types", states)
                .executeUpdate();

            this.getEM()
                .createNativeQuery("update Workflow set dateModified=:date, modifier=:assignee WHERE review IN :types and modifier=:user "
                                   + "and AKTIF=1 and SILINMIS=0 and eserId in (select ID from ESER where AKTIF=1 and SILINMIS=0)")
                .setParameter("user", user)
                .setParameter("date", new Date())
                .setParameter("assignee", assignee)
                .setParameter("types", states)
                .executeUpdate();

            // FLUSH ALL
            this.getEM().flush();

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    /***
     * Draft eserler icin personelin tablodan secilen eserlerine batch islem yapan metot
     * 
     * @param user
     * @param aciklama
     * @param eserIDs
     * @param checkIfAssignOnes
     * @return the DBOperationResult
     */
    @Transactional
    public DBOperationResult batchUpdateForDrafts(final Integer user, final List<Integer> eserIDs) {
        try {
            final int n = eserIDs.size();
            for (int i = 0; i < n; i += EserZimmetFacade.BATCH_SIZE) {
                final List<Integer> subList = new ArrayList<>(eserIDs.subList(i, Math.min(n, i + EserZimmetFacade.BATCH_SIZE)));

                this.getEM()
                    .createNativeQuery("update Workflow set dateModified=:date, modifier=:user, AKTIF=1, SILINMIS=0 where eserId in (:eserIDs)")
                    .setParameter("eserIDs", subList)
                    .setParameter("date", new Date())
                    .setParameter("user", user)
                    .executeUpdate();

                this.getEM()
                    .createNativeQuery("update ESER set DUZENLEME_KULLANICI_ID=:user, YARATMA_KULLANICI_ID=:user where ID in (:eserIDs)")
                    .setParameter("eserIDs", subList)
                    .setParameter("user", user)
                    .executeUpdate();
            }

            // FLUSH ALL
            this.getEM().flush();

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    /***
     * Zimmetten düşme için personelin tüm kayitlarini zimmetten dusen ve bulk islem yapan metot. Eser sadece o kisiye zimmetliyse (aynı zamanda
     * başkasına da zimmetli değilse) o eserler icin zimmetten dusma yapmaz, sql sorgulari buna göre yazilmistir
     * 
     * 
     * @param personelId
     * @param aciklama
     * @return the DBOperationResult
     */
    @Transactional
    public DBOperationResult bulkRemoveForLiabiliy(final int personelId, final String aciklama, final List<Integer> excludeList) {
        return this.bulkUpdateForLiability(personelId,
                                           EserVersion.LIABILITY_DROP_PENDING_REVIEW.getCode(),
                                           Arrays.asList(personelId),
                                           new Date(),
                                           aciklama,
                                           LiabilityOperationEnum.LIABILITY_DROP.getCode(),
                                           excludeList);
    }

    /***
     * Zimmetten dusme icin tablodan secilen eserlerin zimmet dusumunu batch olarak yapan metot. Eser sadece o kisiye zimmetliyse (aynı zamanda
     * başkasına da zimmetli değilse) o eserler icin zimmetten dusme yapmaz, sql sorgulari buna göre yazilmistir
     * 
     * @param personelId
     * @param aciklama
     * @param eserZimmetTuples
     * @return the DBOperationResult
     */
    @Transactional
    public DBOperationResult batchRemoveForLiability(final int personelId, final String aciklama, final List<EserZimmet> eserZimmetTuples) {

        // not: yukardaki kodu degistirmemek icin dusme(drop) durumda personel olarak kendisini devir personel listesi olarak veriyoruz.
        return this.batchUpdateForLiability(EserVersion.LIABILITY_DROP_PENDING_REVIEW.getCode(),
                                            eserZimmetTuples,
                                            aciklama,
                                            Arrays.asList(personelId),
                                            new Date(),
                                            LiabilityOperationEnum.LIABILITY_DROP.getCode());
    }

    // COUNT queries
    // ------------------------------------------------------------

    /***
     * Personelin sadece kendisine zimmetli olan eserlerini doner
     * 
     * @param personelId
     * @return eserID list
     */
    public List<Integer> fetchArtifactFromArtifactLiabilityPersonel(final Integer personelId) {

        return this.fetchArtifactFromArtifactLiabilityPersonel(personelId, Collections.emptyList());
    }

    public List<String> fetchArtifactFromArtifactLiabilityPersonelWithFormatted(final Integer personelId) {

        return this.fetchArtifactFromArtifactLiabilityPersonelWithFormatted(personelId, Collections.emptyList());
    }

    /***
     * Personelin sadece kendisine zimmetli olan eserlerini döner
     * 
     * @param personelId
     * @param eserIDs
     * @return eserID list
     */
    @SuppressWarnings("unchecked")
    public List<Integer> fetchArtifactFromArtifactLiabilityPersonel(final Integer personelId, final List<Integer> eserIDs) {

        final String query = "select distinct COALESCE(ESER_ZIMMET.ESER_ID, ESER.permanentId) " +
                "from ESER_ZIMMET, " +
                "     ESER " +
                "where ESER_ZIMMET.ESER_ID = ESER.ID " +
                "  and ESER.AKTIF = 1 " +
                "  and ESER.SILINMIS = 0 " +
                "  and ESER_ZIMMET.ESER_ID in " +
                "      (select ESER_ID " +
                "       from ESER_ZIMMET " +
                "       where ESER_ZIMMET.AKTIF = 1 " +
                "         and ESER_ZIMMET.SILINMIS = 0 " +
                "         and ESER_ZIMMET.ZIMMET_PERSONEL_ID = :personelId " +
                "         AND (ESER_ZIMMET.ESER_ID in (:eserIds) OR COALESCE(:eserIds, NULL) is NULL)) " +
                "  and ESER_ZIMMET.AKTIF = 1 " +
                "  and ESER_ZIMMET.SILINMIS = 0 " +
                "GROUP BY COALESCE(ESER_ZIMMET.ESER_ID, ESER.permanentId) " +
                "having count(COALESCE(ESER_ZIMMET.ESER_ID, ESER.permanentId)) = 1";

        if (eserIDs.isEmpty()) {
            return this.getEM().createNativeQuery(query).setParameter("eserIds", null).setParameter("personelId", personelId).getResultList();
        }


        final List<Integer> result = new ArrayList<>();
        final int n = eserIDs.size();

        for (int i = 0; i < n; i += EserZimmetFacade.BATCH_SIZE) {
            final List<Integer> subList = new ArrayList<>(eserIDs.subList(i, Math.min(n, i + EserZimmetFacade.BATCH_SIZE)));

            final List<Integer> batchResult = this.getEM().createNativeQuery(query).setParameter("eserIds", subList).setParameter("personelId", personelId).getResultList();
            if (!batchResult.isEmpty()) {
                result.addAll(batchResult);
            }
        }

        return result;

    }

    public List<String> fetchArtifactFromArtifactLiabilityPersonelWithFormatted(final Integer personelId, final List<Integer> eserIDs) {

        final String query = "select distinct " +
                "    case " +
                "        when ESER.permanentId is not null then  " +
                "            concat('TR.M.',  " +
                "                   format(cast(ESER.permanentId as int), '000000000')) " +
                "            else concat('G.', ESER_ZIMMET.ESER_ID)" +
                "    end as formattedEserId " +
                "from ESER_ZIMMET, " +
                "     ESER " +
                "where ESER_ZIMMET.ESER_ID = ESER.ID " +
                "  and ESER.AKTIF = 1 " +
                "  and ESER.SILINMIS = 0 " +
                "  and ESER_ZIMMET.ESER_ID in " +
                "      (select ESER_ID " +
                "       from ESER_ZIMMET " +
                "       where ESER_ZIMMET.AKTIF = 1 " +
                "         and ESER_ZIMMET.SILINMIS = 0 " +
                "         and ESER_ZIMMET.ZIMMET_PERSONEL_ID = :personelId " +
                "         AND (ESER_ZIMMET.ESER_ID in (:eserIds) OR COALESCE(:eserIds, NULL) is NULL)) " +
                "  and ESER_ZIMMET.AKTIF = 1 " +
                "  and ESER_ZIMMET.SILINMIS = 0 " +
                "group by " +
                "    case " +
                "        when ESER.permanentId is not null then " +
                "            concat('TR.M.', format(cast(ESER.permanentId as int), '000000000')) " +
                "        else " +
                "            concat('G.', ESER_ZIMMET.ESER_ID) " +
                "    end " +
                "having count(*) = 1";

        if (eserIDs.isEmpty()) {
            return this.getEM().createNativeQuery(query).setParameter("eserIds", null).setParameter("personelId", personelId).getResultList();
        }


        final List<String> result = new ArrayList<>();
        final int n = eserIDs.size();

        for (int i = 0; i < n; i += EserZimmetFacade.BATCH_SIZE) {
            final List<Integer> subList = new ArrayList<>(eserIDs.subList(i, Math.min(n, i + EserZimmetFacade.BATCH_SIZE)));

            final List<String> batchResult = this.getEM().createNativeQuery(query).setParameter("eserIds", subList).setParameter("personelId", personelId).getResultList();
            if (!batchResult.isEmpty()) {
                result.addAll(batchResult);
            }
        }

        return result;

    }

}
