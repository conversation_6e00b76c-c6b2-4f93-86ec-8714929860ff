package tr.gov.tubitak.bte.mues.model;

import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

/**
 *
*
 */
@Audited
@Entity
@DiscriminatorValue(value = ApplicationType.Values.ENVANTER)
@NamedNativeQuery(name = "Personel.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL_GOREV WHERE SILINMIS = 0 AND PERSONEL_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM KULLANICI WHERE SILINMIS = 0 AND PERSONEL_ID = :id)")
public class Personel extends AbstractPersonel {

    private static final long serialVersionUID = -2431886886124326713L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "mudurlukId", referencedColumnName = "ID")
    @ManyToOne(optional = true, fetch = FetchType.LAZY)
    private Mudurluk          mudurluk;

    public Personel() {
        // default Constructor
    }

    // getter setters
    @Deprecated
    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

}
