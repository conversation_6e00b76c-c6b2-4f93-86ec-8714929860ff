/*TUBITAK-BILGEM BTE,Gebze-Kocaeli,2017©*/
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.List;
import java.util.ResourceBundle;

import javax.faces.component.UIInput;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.primefaces.PrimeFaces;

import tr.gov.tubitak.bte.mues.constraint.ValidEser;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Workflow;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/***
 * 
*
 *
 */
public class EserValidator implements ConstraintValidator<ValidEser, Eser> {

    @Inject
    private ResourceBundle  bundle;

    @Inject
    protected EntityManager em;

    public EserValidator() {
        // default constructor
    }

    /* (non-Javadoc)
    * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
    */
    @Override
    public void initialize(final ValidEser constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final Eser eser, final ConstraintValidatorContext context) {

        boolean result = true;
        if (MuesUtil.isEmptyOrNull(eser.getEserFotografs())) {
            this.raiseFlag(this.bundle.getString("file.photo.required"), context);
            this.addCssErrorClassToComponent("eserForm:eserFotografDataTable");
            result = false;
        }
        if (MuesUtil.isEmptyOrNull(eser.getEserMeasures())) {
            this.raiseFlag(this.bundle.getString("valid.eser.olcu"), context);
            this.addCssErrorClassToComponent("eserForm:eserMeasureTable");
            result = false;
        }
        if (MuesUtil.isEmptyOrNull(eser.getEserZimmets())) {
            this.raiseFlag(this.bundle.getString("valid.eser.zimmet"), context);
            this.addCssErrorClassToComponent("eserForm:eserZimmetTable");
            result = false;
        }

        if ((eser.getId() == null) && (eser.getEnvanterNo() != null) && !eser.getEnvanterNo().isEmpty()) {
            final UIInput ui = (UIInput) FacesContext.getCurrentInstance().getViewRoot().findComponent(":eserForm:mudurluk");
            final Mudurluk mudurluk = (Mudurluk) ui.getValue();
            final List<Workflow> resultList = this.em.createNamedQuery("Workflow.findByEnvanterNoAndMudurluk", Workflow.class)
                                                     .setParameter("envanterNo", eser.getEnvanterNo())
                                                     .setParameter("mudurluk", mudurluk)
                                                     .setParameter("eserId", eser.getId())
                                                     .getResultList();
            if (!resultList.isEmpty()) {
                this.raiseFlag(this.bundle.getString("valid.eser.envanterNo"), context);
                this.addCssErrorClassToComponent("eserForm:envanterNo");
                result = false;
            }
        }

        return result;
    }

    private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

    private void addCssErrorClassToComponent(final String componentId) {
        PrimeFaces.current().executeScript("$(PrimeFaces.escapeClientId(\"" + componentId + "\")).addClass('required-input-field error')");
    }

}
