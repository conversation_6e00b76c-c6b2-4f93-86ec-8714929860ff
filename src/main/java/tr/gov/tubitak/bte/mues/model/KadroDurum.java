package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Entity
@Table(name = "KADRO_DURUM")
@NamedQuery(name = "KadroDurum.findEagerById", query = "SELECT k FROM KadroDurum k WHERE k.id = :id")
@NamedQuery(name = "KadroDurum.findAll", query = "SELECT k FROM KadroDurum k ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "KadroDurum.findActive", query = "SELECT k FROM KadroDurum k WHERE k.aktif = true AND k.silinmis = false ORDER BY k.ad")
@NamedNativeQuery(name = "KadroDurum.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL_GOREV WHERE SILINMIS = 0 AND KADRO_DURUMU_ID = :id) +"
                                                                    + "(SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL WHERE SILINMIS = 0 AND kadroDurum = :id)")
public class KadroDurum extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 6146845897465708819L;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public KadroDurum() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
