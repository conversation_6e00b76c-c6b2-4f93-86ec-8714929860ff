/***********************************************************
 * ObjectTypeFacade.java - mues Projesi
 *
 * Kullanılan JRE: 1.8.0_91
 *
 * halis.yilboga - 22.Tem.2016
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2012©
 ***********************************************************/

package tr.gov.tubitak.bte.mues.configuration;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.context.RequestScoped;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceException;
import javax.persistence.Query;

import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

/**
 * ObjectTypeFacade sınıfının amacı...
 *
 */
@ApplicationScoped
public class MetadataFacade extends AbstractFacade<Metadata> {

    private static final Logger logger = LoggerFactory.getLogger(MetadataFacade.class);

    /**
     * Yapıcı metotdur.
     */
    public MetadataFacade() {
        super(Metadata.class);
    }

    /**
     * Creates the object type.
     *
     * @param metadatas the object type
     * @param metadatas the metadatas
     * @return true, if successful
     */
    public boolean createMetadata(final Metadata metadatas) {
        if (logger.isDebugEnabled()) {
            logger.debug("[createMetadata] : [{}]", metadatas);
        }
        try {

            this.create(metadatas);
            return true;
        } catch (final PersistenceException e) {
            logger.error("[createMetadata] : Hata : {}", e.getMessage());
            return false;
        }
    }

    public List<Map<String, Object>> exceuteQueryResponse(final String queryString) {

        final Query query = this.em.createNativeQuery(queryString);
        final NativeQueryImpl nativeQuery = (NativeQueryImpl) query;
        nativeQuery.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
        final List<Map<String, Object>> result = nativeQuery.getResultList();
        return result;

    }

    /**
     * Updates object type.
     *
     * @param objectType the object type
     * @param metadatas the metadatas
     * @return true, if successful
     */
    public boolean updateMetadata(final Metadata entity) {
        if (logger.isDebugEnabled()) {
            logger.debug("[updateMetadata] : [{}]", entity);
        }
        try {
            this.update(entity);

            return true;
        } catch (final PersistenceException e) {
            logger.error("[updateMetadata] : Hata : {}", e.getMessage());
            return false;
        }
    }

    // finders ......................................................................................................
    /**
     * Finds all object types.
     *
     * @return the list
     */
    public List<Metadata> findAllMetadatas() {
        if (logger.isDebugEnabled()) {
            logger.debug("[findAllMetadatas] : ");
        }
        try {
            return this.em.createNamedQuery("allMetadatas", Metadata.class).getResultList();
        } catch (final NoResultException e) {
            logger.error("[findAllMetadatas] : {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    public List<Metadata> findMetadataByType(final SolrEnum solrEnum) {
        if (logger.isDebugEnabled()) {
            logger.debug("[findMetadataByType] : by {}", solrEnum.getCoreKey());
        }
        try {
            return this.em.createNamedQuery("allMetadataBySolrCoreId", Metadata.class).setParameter("solrCoreId", solrEnum.getCode()).getResultList();
        } catch (final NoResultException e) {
            logger.error("[findMetadataByType] : {}", e.getMessage());
            return Collections.emptyList();
        }
    }
}
