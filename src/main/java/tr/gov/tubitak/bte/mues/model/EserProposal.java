package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * ali.kelle
 */

@Entity
@Table(name = "EP_ESER")

public class EserProposal extends EserProposalSuper {

    private static final long serialVersionUID = 5814980331509052387L;

    @JoinColumn(name = "mudurlukId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk          eserOneriMudurluk;

    /**
     * Instantiates a new eser.
     */
    public EserProposal() {
        // empty constructor for generation of model
    }

    public Mudurluk getEserOneriMudurluk() {
        return this.eserOneriMudurluk;
    }

    public void setEserOneriMudurluk(final Mudurluk eserOneriMudurluk) {
        this.eserOneriMudurluk = eserOneriMudurluk;
    }

}
