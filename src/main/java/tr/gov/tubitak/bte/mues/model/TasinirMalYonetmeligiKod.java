package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.MKSCode;
import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Entity
@Table(name = "TASINIR_MAL_YONETMELIGI_KOD")
@NamedQuery(name = "TasinirMalYonetmeligiKod.findEagerById", query = "SELECT t FROM TasinirMalYonetmeligiKod t WHERE t.id = :id")
@NamedQuery(name = "TasinirMalYonetmeligiKod.findAll", query = "SELECT t FROM TasinirMalYonetmeligiKod t WHERE t.aktif = true AND t.silinmis = false ORDER BY t.kod")
@NamedQuery(name = "TasinirMalYonetmeligiKod.findActive", query = "SELECT t FROM TasinirMalYonetmeligiKod t WHERE t.aktif = true AND t.silinmis = false ORDER BY t.ad")
@NamedQuery(name = "TasinirMalYonetmeligiKod.findByNameCodeAndAciklama", query = "SELECT t FROM TasinirMalYonetmeligiKod t WHERE t.aktif = true AND t.silinmis = false AND (t.ad LIKE :str OR t.aciklama LIKE :str OR t.kod LIKE :str) ORDER BY t.kod, t.ad, t.aciklama")
@NamedNativeQuery(name = "TasinirMalYonetmeligiKod.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER WHERE SILINMIS = 0 AND TASINIR_MAL_YON_ID = :id)")
public class TasinirMalYonetmeligiKod extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 1384364610684065078L;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "FOTOGRAF_PATH", length = 250)
    private String            fotografPath;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @MKSCode
    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Column(name = "APPLICATION_TYPE")
    private Integer           applicationType;

    public TasinirMalYonetmeligiKod() {
    }

    // getters and setters ....................................................

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Integer getApplicationType() {
        return this.applicationType;
    }

    public void setApplicationType(final Integer applicationType) {
        this.applicationType = applicationType;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
