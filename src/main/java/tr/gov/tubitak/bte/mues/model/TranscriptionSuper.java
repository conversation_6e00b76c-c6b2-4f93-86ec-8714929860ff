package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
 * ali.kelle
 */

@Audited
@MappedSuperclass
public class TranscriptionSuper extends AbstractEntity {

    private static final long serialVersionUID = -1257253912454192664L;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "transcriptionPath", length = 250)
    private String            transcriptionPath;
    
    @Size(max = 15000)
    // @Column(name = "metaphrase", length = 15000)
    @Column(name = "translation", length = 15000)
    private String            metaphrase;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "yaziDili", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Dil               yaziDili;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "yaziTipi", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private YaziTipi          yaziTipi;
    
    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "monogram", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Monogram               monogram;

    public TranscriptionSuper() {
    	//default constructor
    }

    // getters and setters ....................................................

    public String getTranscriptionPath() {
        return this.transcriptionPath;
    }

    public void setTranscriptionPath(final String transcriptionPath) {
        this.transcriptionPath = transcriptionPath;
    }

    public String getMetaphrase() {
        return this.metaphrase;
    }

    public void setMetaphrase(final String metaphrase) {
        this.metaphrase = metaphrase;
    }

    public Dil getYaziDili() {
        return this.yaziDili;
    }

    public void setYaziDili(final Dil yaziDili) {
        this.yaziDili = yaziDili;
    }

    public YaziTipi getYaziTipi() {
        return this.yaziTipi;
    }

    public void setYaziTipi(final YaziTipi yaziTipi) {
        this.yaziTipi = yaziTipi;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        // uzun çeviri-transkripsiyonlarda ilk 40 karakter getirilir.
        if ((this.metaphrase != null) && (this.metaphrase.length() > 40)) {
            return Optional.ofNullable(this.metaphrase.substring(0, 40) + "...").orElse("" + this.getId());
        } else {
            return Optional.ofNullable(this.metaphrase).orElse("" + this.getId());
        }
    }

    public Monogram getMonogram() {
        return monogram;
    }

    public void setMonogram(Monogram monogram) {
        this.monogram = monogram;
    }

}
