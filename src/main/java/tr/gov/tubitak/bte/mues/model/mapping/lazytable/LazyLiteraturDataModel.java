package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.JpaLazyDataModel;
import org.primefaces.model.SortMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.session.LiteraturLazyLoadFacade;

/**
 *
 * <AUTHOR>
 */
public class LazyLiteraturDataModel extends JpaLazyDataModel<Literatur> {

    private static final long             serialVersionUID = -4739489425974372483L;

    private static final Logger           logger           = LoggerFactory.getLogger(LazyLiteraturDataModel.class);

    private final LiteraturLazyLoadFacade literaturFacade;

    private List<Literatur>               data;

    public LazyLiteraturDataModel(final LiteraturLazyLoadFacade literaturFacade) {
        this.literaturFacade = literaturFacade;
    }

    @Override
    public List<Literatur> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        this.data = this.literaturFacade.fetchData(first, pageSize, sortBy, filterBy);
        logger.debug("filters: {} _ multiSortMeta Length, {}", sortBy, filterBy);

        return this.data;
    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
        return this.literaturFacade.count(filterBy);
    }

    @Override
    public Literatur getRowData(final String rowKey) {
        for (final Literatur each : this.data) {
            if (each.getId().equals(Integer.valueOf(rowKey))) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final Literatur literatur) {
        return literatur.getId().toString();
    }

}
