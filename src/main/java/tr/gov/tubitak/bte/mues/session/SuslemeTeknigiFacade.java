package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.SuslemeTeknigi;

/**
 *
*
 */
@RequestScoped
public class SuslemeTeknigiFacade extends AbstractFacade<SuslemeTeknigi> {

    public SuslemeTeknigiFacade() {
        super(SuslemeTeknigi.class);
    }

    public List<SuslemeTeknigi> filterByNameAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("SuslemeTeknigi.findByNameAndMalzeme", SuslemeTeknigi.class).setParameter("ad", "%" + query + "%").setParameter("malzeme", malzeme).getResultList();
    }

    public List<SuslemeTeknigi> filterByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("SuslemeTeknigi.findByNameAndAciklama", SuslemeTeknigi.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<SuslemeTeknigi> findByNameAndAciklamaAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("SuslemeTeknigi.findByNameAndAciklamaAndMalzeme", SuslemeTeknigi.class).setParameter("str", "%" + query + "%").setParameter("malzeme", malzeme).getResultList();
    }

}
