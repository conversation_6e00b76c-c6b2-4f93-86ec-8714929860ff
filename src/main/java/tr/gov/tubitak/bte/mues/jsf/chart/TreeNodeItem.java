package tr.gov.tubitak.bte.mues.jsf.chart;

import java.io.Serializable;

import javax.faces.view.ViewScoped;
import javax.inject.Named;

import tr.gov.tubitak.bte.mues.model.Cag;
import tr.gov.tubitak.bte.mues.model.Donem;
import tr.gov.tubitak.bte.mues.model.Hukumdar;
import tr.gov.tubitak.bte.mues.model.Kronoloji;
import tr.gov.tubitak.bte.mues.model.Uygarlik;

/**
 * ali.kelle
 */

@Named("treeNodeItem")
@ViewScoped
public class TreeNodeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String            classType;

    private Integer           minDate;

    private Integer           maxDate;

    private transient Object  obj;

    private Integer           id;

    public TreeNodeItem(final String classType, final Integer minDate, final Integer maxDate, final Object obj, final Integer id) {
        this.classType = classType;
        this.minDate = minDate;
        this.maxDate = maxDate;
        this.obj = obj;
        this.id = id;
    }

    public String getClassType() {
        return this.classType;
    }

    public void setClassType(final String classType) {
        this.classType = classType;
    }

    public Integer getMinDate() {
        return this.minDate;
    }

    public void setMinDate(final Integer minDate) {
        this.minDate = minDate;
    }

    public Integer getMaxDate() {
        return this.maxDate;
    }

    public void setMaxDate(final Integer maxDate) {
        this.maxDate = maxDate;
    }

    public Object getObj() {
        return this.obj;
    }

    public void setObj(final Object obj) {
        this.obj = obj;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(final Integer id) {
        this.id = id;
    }

    @Override
    public String toString() {

        if (this.getClassType().equals("Kronoloji")) {
            final Kronoloji koronoloji = (Kronoloji) this.getObj();
            return koronoloji.getAd();
        }
        if (this.getClassType().equals("Cag")) {
            final Cag cag = (Cag) this.getObj();
            return cag.getAd();
        }
        if (this.getClassType().equals("Donem")) {
            final Donem donem = (Donem) this.getObj();
            return donem.getAd();
        }
        if (this.getClassType().equals("Uygarlik")) {
            final Uygarlik uygarlik = (Uygarlik) this.getObj();
            return uygarlik.getAd();
        }
        if (this.getClassType().equals("Hukumdar")) {
            final Hukumdar hukumdar = (Hukumdar) this.getObj();
            return hukumdar.getAd();
        }
        return null;
    }

}