package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@MappedSuperclass

@NamedQuery(name = "EserDepo.findEagerById", query = "SELECT e FROM EserDepo e LEFT JOIN FETCH e.alanKonumu LEFT JOIN FETCH e.eser WHERE e.id = :id")
@NamedQuery(name = "EserDepo.findAll", query = "SELECT e FROM EserDepo e ORDER BY e.silinmis, e.aktif DESC")
@NamedQuery(name = "EserDepo.findActive", query = "SELECT e FROM EserDepo e WHERE e.aktif = true AND e.silinmis = false")

public abstract class EserDepoSuper extends AbstractEntity {

    private static final long serialVersionUID = -5646334757253460716L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ALAN_KONUMU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private AlanKonumu        alanKonumu;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public EserDepoSuper() {
        // default constructor
    }

    public AlanKonumu getAlanKonumu() {
        return this.alanKonumu;
    }

    public void setAlanKonumu(final AlanKonumu alanKonumu) {
        this.alanKonumu = alanKonumu;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

}
