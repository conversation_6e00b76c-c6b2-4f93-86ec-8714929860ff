package tr.gov.tubitak.bte.mues.search;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Metadata;

/**
 * Bir detaylı aramada belirtilen kriterleri tutmak için model sınıfıdır.
 */
public class CriterionModel extends AbstractEntity {

    private static final long      serialVersionUID = -453344764062414133L;

    private Integer                id;

    private Integer                rowId;

    private Integer                type;

    private String                 metadataName;

    private String                 metadataDbName;

    private Metadata               metadata;

    private String                 textValue1;

    private String                 textValue2;

    private Date                   dateValue1;

    private Date                   dateValue2;

    private String                 listValue;

    private String                 listValueLabel;

    private int                    booleanValue;

    private ComparisonOperatorEnum condition;

    private List<ICriterion>       criteriaList;

    private List<CriterionModel>   subCriteriaModelList;

    private LogicalOperatorEnum    operator;

    private final boolean          childField;

    /**
     * Yapıcı metot.
     */
    public CriterionModel() {
        this.subCriteriaModelList = new ArrayList<>();
        this.childField = false;
    }

    public CriterionModel(final Metadata metadata) {

        this.metadata = metadata;
        this.childField = metadata.isChildField();

    }

    public CriterionModel(final Metadata metadata, final String value1, final String value2) {
        this.type = CriterionEnum.TEXT.getCode();
        this.metadata = metadata;
        this.textValue1 = value1;
        this.textValue2 = value2;
        this.childField = metadata.isChildField();
    }

    public CriterionModel(final Metadata metadata, final String value) {
        this.type = metadata.getDataTypeId();
        this.metadata = metadata;
        this.textValue1 = value;
        this.childField = metadata.isChildField();
    }

    public CriterionModel(final Integer type, final Metadata metadata, final int value) {
        this.type = type;
        this.metadata = metadata;
        this.booleanValue = value;
        this.childField = metadata.isChildField();
    }

    public CriterionModel(final Metadata metadata, final Date value1, final Date value2) {
        this.type = CriterionEnum.DATE.getCode();
        this.metadata = metadata;
        this.dateValue1 = value1;
        this.dateValue2 = value2;
        this.childField = metadata.isChildField();
    }

    public Integer getType() {
        return this.type;
    }

    public void setType(final Integer type) {
        this.type = type;
    }

    public String getMetadataName() {
        return this.metadataName;
    }

    public void setMetadataName(final String metadataName) {
        this.metadataName = metadataName;
    }

    public String getMetadataDbName() {
        return this.metadataDbName;
    }

    public void setMetadataDbName(final String metadataDbName) {
        this.metadataDbName = metadataDbName;
    }

    public String getTextValue1() {
        return this.textValue1;
    }

    public void setTextValue1(final String textValue1) {
        this.textValue1 = textValue1;
    }

    public String getTextValue2() {
        return this.textValue2;
    }

    public void setTextValue2(final String textValue2) {
        this.textValue2 = textValue2;
    }

    public Date getDateValue1() {
        return this.dateValue1;
    }

    public void setDateValue1(final Date dateValue1) {
        this.dateValue1 = dateValue1;
    }

    public Date getDateValue2() {
        return this.dateValue2;
    }

    public void setDateValue2(final Date dateValue2) {
        this.dateValue2 = dateValue2;
    }

    public String getListValue() {
        return this.listValue;
    }

    public void setListValue(final String listValue) {
        this.listValue = listValue;
    }

    public String getListValueLabel() {
        return this.listValueLabel;
    }

    public void setListValueLabel(final String listValueLabel) {
        this.listValueLabel = listValueLabel;
    }

    public int getBooleanValue() {
        return this.booleanValue;
    }

    public void setBooleanValue(final int booleanValue) {
        this.booleanValue = booleanValue;
    }

    public ComparisonOperatorEnum getCondition() {
        return this.condition;
    }

    public void setCondition(final ComparisonOperatorEnum condition) {
        this.condition = condition;
    }

    public List<ICriterion> getCriteriaList() {
        return this.criteriaList;
    }

    public void setCriteriaList(final List<ICriterion> criteriaList) {
        this.criteriaList = criteriaList;
    }

    public LogicalOperatorEnum getOperator() {
        return this.operator;
    }

    public void setOperator(final LogicalOperatorEnum operator) {
        this.operator = operator;
    }

    public List<CriterionModel> getSubCriteriaModelList() {
        return this.subCriteriaModelList;
    }

    public void setSubCriteriaModelList(final List<CriterionModel> subCriteriaModelList) {
        this.subCriteriaModelList = subCriteriaModelList;
    }

    public Metadata getMetadata() {
        return this.metadata;
    }

    public void setMetadata(final Metadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public Integer getId() {
        return this.id;
    }

    @Override
    public void setId(final Integer id) {
        this.id = id;
    }

    public Integer getRowId() {
        return this.rowId;
    }

    public void setRowId(final Integer rowId) {
        this.rowId = rowId;
    }

    @Override
    public String getTitle() {
        // Auto-generated method stub
        return null;
    }

    @Override
    public void setSilinmis(final Boolean b) {
        // Auto-generated method stub

    }

    @Override
    public void setAktif(final Boolean b) {
        // Auto-generated method stub

    }

    public boolean isChildField() {
        return childField;
    }

}
