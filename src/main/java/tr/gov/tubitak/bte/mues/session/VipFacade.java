package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Vip;

/**
 *
*
 */
@RequestScoped
public class VipFacade extends AbstractFacade<Vip> {

    public VipFacade() {
        super(Vip.class);
    }

    public List<Vip> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Vip.findByNameAndAciklama", Vip.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
