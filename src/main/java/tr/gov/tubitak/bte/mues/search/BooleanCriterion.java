package tr.gov.tubitak.bte.mues.search;

import java.util.Objects;
import java.util.ResourceBundle;

import javax.faces.context.FacesContext;
import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * Mantıksal de<PERSON> kriterleri tutan sınıf.
 */
@Entity
@DiscriminatorValue("6")
public class BooleanCriterion extends AbstractSimpleCriterion {

    private static final long        serialVersionUID = 7036444630704387131L;

    @Transient
    private transient ResourceBundle bundle;

    @Column(name = "value")
    private int                      value;

    /**
     * Yapıcı metot.
     */
    public BooleanCriterion() {
        // blank constructor
    }

    @Override
    public ICriterion setModel(final CriterionModel model) {
        super.setSuperModel(model);
        this.value = model.getBooleanValue();
        return this;
    }

    @Override
    @Transient
    public String getFirstValue() {
        return this.value == 1 ? "true" : "false";
    }

    @Override
    @Transient
    public String getSecondValue() {
        return "";
    }

    @Override
    @Transient
    public String getFirstValueText() {
        if (this.value == MuesUtil.TRUE) {
            return this.getBundle().getString("search.yes");
        }
        return this.getBundle().getString("search.no");
    }

    @Override
    @Transient
    public String getSecondValueText() {
        return "";
    }

    public int getBooleanValue() {
        return this.value;
    }

    public void setBooleanValue(final int booleanValue) {
        this.value = booleanValue;
    }

    private ResourceBundle getBundle() {
        if (this.bundle == null) {
            this.bundle = ResourceBundle.getBundle("/labels", FacesContext.getCurrentInstance().getViewRoot().getLocale());
        }
        return this.bundle;
    }

    @Override
    public String getSql() {
        if (this.getBooleanValue() != 2) {
            return super.getSql();
        }
        return SearchConstants.ALL_QUERY + SearchConstants.SPACE_LITERAL;

    }

    @Override
    public int hashCode() {
        return Objects.hash(this.value);
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final BooleanCriterion other = (BooleanCriterion) obj;
        return Objects.equals(this.value, other.value);
    }

    @Override
    public String getTitle() {
        return null;
    }

}
