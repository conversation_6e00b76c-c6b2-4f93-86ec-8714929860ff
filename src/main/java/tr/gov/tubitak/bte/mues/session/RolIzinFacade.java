/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.session;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Izin;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.model.RolIzin;

/**
 *
*
 */
@RequestScoped
public class RolIzinFacade {

    @Inject
    private EntityManager       em;

    private static final Logger logger = LoggerFactory.getLogger(RolIzinFacade.class);

    public Map<Izin, Map<Rol, Boolean>> fetchAllRolIzin() {
        final Map<Izin, Map<Rol, Boolean>> izinRolMap = new HashMap<>();

        final List<Izin> permitions = this.findAll();

        for (final Izin izin : permitions) {
            final Collection<RolIzin> rolIzinCollection = izin.getRolIzinCollection();

            if ((rolIzinCollection == null) || rolIzinCollection.isEmpty()) {
                this.getRolMap(izinRolMap, izin);
            } else {
                for (final RolIzin rolIzin : rolIzinCollection) {
                    this.getRolMap(izinRolMap, rolIzin.getIzin()).put(rolIzin.getRol(), true);
                }
            }

        }
        return izinRolMap;
    }

    private Map<Rol, Boolean> getRolMap(final Map<Izin, Map<Rol, Boolean>> mainMap, final Izin key) {
        Map<Rol, Boolean> map = mainMap.get(key);

        if (map == null) {
            map = new HashMap<>();
            mainMap.put(key, map);
        }
        return map;
    }

    @Transactional
    public boolean updateIzinRols(final Izin izin, final Map<Rol, Boolean> rolMap) {
        try {
            final Izin iz = this.em.merge(izin);
            iz.getRolIzinCollection().stream().forEach(r -> this.em.remove(r));
            this.em.flush();

            final List<RolIzin> newRolIzins = rolMap.entrySet()
                                                    .stream()
                                                    .filter(e -> e.getValue())
                                                    .map(e -> new RolIzin(e.getKey().getId(), izin.getId()))
                                                    .collect(Collectors.toList());

            newRolIzins.stream().forEach(r -> this.em.persist(r));
        } catch (final javax.persistence.PersistenceException e) {
            logger.error("[updateIzinRols] : Hata : {}", e.getMessage(), e);
            return false;
        }
        return true;
    }

    private List<Izin> findAll() {
        return this.em.createNamedQuery("Izin.findAllWithRols", Izin.class).getResultList();
    }

}
