package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@MappedSuperclass
// TODO: halis.yilboga sonradan test edilerek kaldirilabilir.
@NamedQuery(name = "EserHareketSahis.findEagerById", query = "SELECT m FROM EserHareketSahis m LEFT JOIN FETCH m.sahis LEFT JOIN FETCH m.eserHareket mm WHERE m.id = :id")
@NamedQuery(name = "EserHareketSahis.findAll", query = "SELECT m FROM EserHareketSahis m LEFT JOIN FETCH m.sahis LEFT JOIN FETCH m.eserHareket mm ORDER BY mm.silinmis, mm.aktif DESC")
@NamedQuery(name = "EserHareketSahis.findByName", query = "SELECT m FROM EserHareketSahis m LEFT JOIN FETCH m.sahis LEFT JOIN FETCH m.eserHareket mm WHERE (mm.aciklama LIKE :str) ORDER BY mm.silinmis, mm.aktif DESC")
@NamedQuery(name = "EserHareketSahis.findActive", query = "SELECT m FROM EserHareketSahis m LEFT JOIN FETCH m.sahis LEFT JOIN FETCH m.eserHareket mm WHERE mm.aktif = true AND mm.silinmis = false")

public class EserHareketSahisSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 8582999553642405009L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "SAHIS", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Sahis             sahis;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_HAREKET", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserHareket       eserHareket;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public EserHareketSahisSuper() {
        // default constructor.
    }

    // getters and setters ....................................................

    public Sahis getSahis() {
        return this.sahis;
    }

    public void setSahis(final Sahis sahis) {
        this.sahis = sahis;
    }

    public EserHareket getEserHareket() {
        return this.eserHareket;
    }

    public void setEserHareket(final EserHareket eserHareket) {
        this.eserHareket = eserHareket;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        if (this.getSahis() != null) {
            return Optional.ofNullable(this.getSahis().getTitle()).orElse("" + this.getId());
        }
        return "";
    }

    @Override
    public String getTitle() {
        return this.toString();
    }

}
