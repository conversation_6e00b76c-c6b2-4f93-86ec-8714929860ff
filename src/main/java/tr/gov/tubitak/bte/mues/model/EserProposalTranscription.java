package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 *
 * ali.kelle
 */
@Entity
@Table(name = "EP_Transcription")
public class EserProposalTranscription extends TranscriptionSuper {

    private static final long serialVersionUID = -1257259124984192664L;

    @JoinColumn(name = "eser", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalTranscription() {
        // blank constructor
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
