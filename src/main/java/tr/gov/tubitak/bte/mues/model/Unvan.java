package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "UNVAN")
@NamedQuery(name = "Unvan.findEagerById", query = "SELECT u FROM Unvan u WHERE u.id = :id")
@NamedQuery(name = "Unvan.findAll", query = "SELECT u FROM Unvan u ORDER BY u.silinmis, u.aktif DESC, u.ad")
@NamedQuery(name = "Unvan.findActive", query = "SELECT u FROM Unvan u WHERE u.aktif = true AND u.silinmis = false ORDER BY u.ad")
@NamedNativeQuery(name = "Unvan.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL_GOREV WHERE SILINMIS = 0 AND UNVAN_ID = :id) + "
                                                               + "(SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL WHERE SILINMIS = 0 AND unvan = :id)")
public class Unvan extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 7610247686303389882L;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Unvan() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
