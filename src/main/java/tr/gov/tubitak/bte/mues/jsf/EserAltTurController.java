package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserAltTur;
import tr.gov.tubitak.bte.mues.session.EserAltTurFacade;

@Named
@ViewScoped
public class EserAltTurController extends AbstractController<EserAltTur> {

    private static final long serialVersionUID = -3183554119166131551L;

    @Inject
    private EserAltTurFacade  facade;

    public EserAltTurController() {
        super(EserAltTur.class);
    }

    @Override
    public List<EserAltTur> filterByName(final String query) {
        return this.getFacade().filterByName("ad", query);
    }

    public List<EserAltTur> filterByNameAndAciklamaAndUstTurName(final String query) {
        final List<EserAltTur> filterByName = this.filterByName(query);
        final List<EserAltTur> list = this.getFacade().findByNameAndAciklamaAndUstTurName(query);

        for (final EserAltTur eserAltTur : list) {
            if (!filterByName.contains(eserAltTur)) {
                filterByName.add(eserAltTur);
            }
        }

        return filterByName;
    }

    // getters and setters ...................................................

    @Override
    public EserAltTurFacade getFacade() {
        return this.facade;
    }

}
