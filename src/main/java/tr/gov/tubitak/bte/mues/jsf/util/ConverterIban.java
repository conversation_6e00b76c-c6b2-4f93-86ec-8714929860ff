package tr.gov.tubitak.bte.mues.jsf.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

@FacesConverter("converterIban")
public class ConverterIban implements Converter<String> {

    private static Pattern patternIban = Pattern.compile("(\\d{4})\\-(\\d{4})\\-(\\d{4})\\-(\\d{4})\\-(\\d{4})\\-(\\d{4})");

    @Override
    public String getAsObject(final FacesContext context, final UIComponent component, String value) {
        if (value == null) {
            return null;
        }

        final Matcher matcherIban = patternIban.matcher(value);
        if (matcherIban.find()) {
            value = "";
            value += matcherIban.group(1);
            value += matcherIban.group(2);
            value += matcherIban.group(3);
            value += matcherIban.group(4);
            value += matcherIban.group(5);
            value += matcherIban.group(6);
        }

        return value;
    }

    @Override
    public String getAsString(final FacesContext context, final UIComponent component, final String value) {

        if ((value == null) || "".equals(value.trim()) || (value.length() < 16)) {
            return null;
        }

        final StringBuilder sb = new StringBuilder();
        sb.append("TR");
        sb.append(value.substring(0, 4)).append("-");
        sb.append(value.substring(4, 8)).append("-");
        sb.append(value.substring(8, 12)).append("-");
        sb.append(value.substring(12, 16)).append("-");
        sb.append(value.substring(16, 20)).append("-");
        sb.append(value.substring(20, 24));

        return sb.toString();
    }

}
