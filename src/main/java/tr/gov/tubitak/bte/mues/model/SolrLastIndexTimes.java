package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.envers.Audited;

@Entity
@Table(name = "SolrLastIndexTimes")
@NamedQuery(name = "SolrLastIndexTimes.findBySolrCore", query = "SELECT s FROM SolrLastIndexTimes s WHERE s.solrCoreCode = :solrCoreCode ")
public class SolrLastIndexTimes extends AbstractEntity {

    private static final long serialVersionUID = -6209243971512737327L;

    // SolrEnum
    @Column(name = "solrCoreCode")
    private Integer           solrCoreCode;

    @Column(name = "lastIndexTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              lastIndexTime;

    // getters and setters ....................................................

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }

    public Integer getSolrCoreCode() {
        return this.solrCoreCode;
    }

    public void setSolrCoreCode(final Integer solrCoreCode) {
        this.solrCoreCode = solrCoreCode;
    }

    public Date getLastIndexTime() {
        return this.lastIndexTime;
    }

    public void setLastIndexTime(final Date lastIndexTime) {
        this.lastIndexTime = lastIndexTime;
    }

}
