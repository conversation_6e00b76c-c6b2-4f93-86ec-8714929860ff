/***********************************************************
 * ContentServer.java - MUES Projesi
 *
 * Kullanılan JRE: 1.7.0_03
 *
 * halis.yilboga - 12.Ara.2012
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2011©
 ***********************************************************/
package tr.gov.tubitak.bte.mues.jsf.util;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.PrintWriter;
import java.nio.file.Files;
import java.nio.file.Paths;

import javax.servlet.ServletOutputStream;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Servlet implementation class ContentServer
 *
 * Dosya içeriklerini kendsini parametreler ile çağıran elemana stream olarak sunar.
 *
 * <AUTHOR>
 */

@WebServlet("/ContentServer")
public class ContentServer extends HttpServlet {

    private static final long serialVersionUID    = 4519323064950981087L;

    private static final int  DEFAULT_BUFFER_SIZE = 10240;

    private String            dosyaTipi           = null;

    private static Logger     logger              = LoggerFactory.getLogger(ContentServer.class);

    /**
     * Instantiates a new content server.
     */
    public ContentServer() {
        // default constructor
    }

    /**
     * Do get.
     *
     * @param request the request
     * @param response the response
     * @throws IOException
     * @see HttpServlet#doGet(HttpServletRequest request, HttpServletResponse response)
     */
    @Override
    protected void doGet(final HttpServletRequest request, final HttpServletResponse response) throws IOException {

        final String requestParameter;
        String fileName = "envanter";

        try {
            final String dataId = request.getParameter("id");
            this.dosyaTipi = request.getParameter("tip");
            requestParameter = (String) request.getSession().getAttribute(dataId);
            fileName = dataId;

        } catch (final IllegalArgumentException | NullPointerException e) {
            logger.warn("[ContentServer ] beklenen Id Gönderilmedi {}", e.getMessage());
            showErrorMessage(response, "Kullanıcıdan id Alınamadı Belge Gösterilemeyecek");
            return;
        }

        if ((requestParameter == null) || !Paths.get(requestParameter).toFile().exists()) {
            showErrorMessage(response, "dosya yolu alınamadı");
            logger.error("[doGet] : Hata : dosya yolu alınamadı {}", requestParameter);
            return;
        }

        final File doc = new File(requestParameter);

        try (final ServletOutputStream outputstream = response.getOutputStream(); InputStream io = new FileInputStream(doc); InputStream buferedInput = new BufferedInputStream(io);) {

            final String probeContentType = Files.probeContentType(Paths.get(doc.getAbsolutePath()));
            response.addHeader("Content-Type", probeContentType);
            response.setHeader("Content-Length", String.valueOf(doc.length()));
            response.addHeader("Content-Disposition", "inline; filename=\"" + fileName + "\"");

            final byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
            int length;
            while ((length = buferedInput.read(buffer)) > 0) {
                outputstream.write(buffer, 0, length);
            }
            outputstream.flush();

        } catch (final IOException e) {
            logger.error("[ContentServer ] <<<<<<<<<<<< Data Göndermede Hata {}", e.getMessage());
            showErrorMessage(response, "!.. Dosya disk sisteminde bulunamadı");
        }
    }

    public static void showErrorMessage(final HttpServletResponse response, final String hata) throws IOException {

        response.setContentType("text/html; charset=UTF-8");
        response.setCharacterEncoding("UTF-8");

        final StringBuilder str = new StringBuilder();

        str.append("<!DOCTYPE html><html><head><title>Hata</title></head>");
        str.append("<body style=\"background: #F0F0F0; text-align: center; padding-top: 2em;\">");
        str.append("<h1 style=\"color: red;\">Hata İçerik</h1><h2 style=\"color: #0d4f90;\">Dosya yolu bulunamadı!</h2>");
        str.append("<h3 style=\"color: #7e7eb1;\">Ayrıntı için log kayıtlarına bakınız.</h3></body></html>");

        final PrintWriter out = response.getWriter();
        out.println(str.toString());
        out.close();

        logger.error(hata);
    }

}
