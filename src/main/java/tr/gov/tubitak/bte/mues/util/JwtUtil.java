package tr.gov.tubitak.bte.mues.util;

import java.io.Serializable;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;

import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.jsf.SessionBean;

/**
 * 
 */
@Named
@ViewScoped
public class JwtUtil implements Serializable {

    private static final long  serialVersionUID = 821753267941800892L;

    @Inject
    private SessionBean        sessionBean;

    @Inject
    private AbstractParameters parameters;

    public void redirect(final String urlKey) {
        final JWTGuard jwtGuard = new JWTGuard();
        final String token = jwtGuard.generateTokenFromUsername(this.sessionBean.getCurrentUser().getKullaniciAdi());
        PrimeFaces.current().executeScript("window.open('" + this.parameters.get(urlKey) + "?t=" + token + "', '_blank');");
    }

    public void redirectWithParameter(final String urlKey, final String parameters, final String module) {
        final JWTGuard jwtGuard = new JWTGuard();
        final String token = jwtGuard.generateTokenFromUsername(this.sessionBean.getCurrentUser().getKullaniciAdi());
        if(module.equals("LAB"))
            PrimeFaces.current().executeScript("window.open('" + this.parameters.get(urlKey) + "?e=" + parameters + "&t=" + token + "', '_blank');");
        else
            PrimeFaces.current().executeScript("window.open('" + this.parameters.get(urlKey) + "?c=" + parameters + "&t=" + token + "', '_blank');");
    }
    
    public void redirectWithParameterRedirectCurrentPage(final String urlKey, final Integer id, final String redirectURL) {
        this.redirectWithParameterRedirectCurrentPage(urlKey, true, "c=" + id, "url=" + redirectURL);
    }

    public void redirectWithParameterRedirectCurrentPage(final String urlKey, final boolean self, final String... params) {
        final JWTGuard jwtGuard = new JWTGuard();
        final String token = jwtGuard.generateTokenFromUsername(this.sessionBean.getCurrentUser().getKullaniciAdi());

        final StringBuilder urlBuilder = new StringBuilder(this.parameters.get(urlKey));
        urlBuilder.append("?")
                  .append("&t=")
                  .append(token);

        // Append additional parameters
        for (final String param : params) {
            urlBuilder.append("&").append(param);
        }

        PrimeFaces.current().executeScript("window.open('" + urlBuilder.toString() + "', '" + (self ? "_self" : "") + "');");

    }

    public JwtUtil() {
        // default constructor
    }

}
