package tr.gov.tubitak.bte.mues.search;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.faces.context.FacesContext;
import javax.inject.Inject;

public enum ComparisonOperatorEnum {

    /** Büyüktür. */
    GREATER_THAN(1, 1, ">", "[", " TO *]"),

    /** Küçüktür. */
    LESS_THAN(2, 1, "<", "[* TO ", "]"),

    /** Eşittir. */
    EQUALS(3, 1, "", "\"", "\""),

    /** Eşittir. */
    NOT_EQUALS(3, 1, "", "\"", "\""),

    /** Arasında. */
    BETWEEN(4, 2, "BETWEEN", "ile"),

    /** Liste içinde içerir. */
    CONTAINS_IN(5, 1, "IN", "", ""),

    /** Liste içinde içermez. */
    NOT_CONTAINS_IN(5, 1, "NOT IN", "", ""),

    /** Kelime içerir. */
    CONTAINS(6, 1, "LIKE", "(*", "*)"),

    /** Kelime içermez */
    NOT_CONTAINS(6, 1, "NOT LIKE", "(*", "*)"),

    // /** Boş. */
    // EMPTY(0, " = '' "),

    /** Null. */
    ISNULL(7, 0, "IS NULL", "[*", " TO *]"),

    /** Not Null. */
    NOTNULL(8, 0, "NOT NULL", "[*", " TO *]"),

    STARTSWITH(9, 1, "LIKE", "(", "*)"),

    /** Operasyon yok. */
    NOP(0, 0, "", "", "");

    private static Map<Integer, ComparisonOperatorEnum> map = new HashMap<>();
    static {
        for (final ComparisonOperatorEnum each : ComparisonOperatorEnum.values()) {
            map.put(each.getCode(), each);
        }
    }

    @Inject
    private ResourceBundle                            bundle;

    private final int                                 code;

    private final int                                 valueCount;

    private final String                              conditionalText;

    private String                                    connectorText = "";

    private String                                    label;

    private final String                              prefix;

    private final String                              suffix;

    private static final List<ComparisonOperatorEnum> VALUE_LIST    = Stream.of(values()).collect(Collectors.collectingAndThen(Collectors.toList(), Collections::unmodifiableList));

    private ComparisonOperatorEnum(final int code, final int valueCount, final String conditionalText, final String prefix, final String suffix) {
        this.code = code;
        this.valueCount = valueCount;
        this.conditionalText = conditionalText;
        this.label = "search.operators." + this.name();
        this.prefix = prefix;
        this.suffix = suffix;
    }

    private ComparisonOperatorEnum(final int type, final int valueCount, final String conditionalText, final String connectorText) {
        this(type, valueCount, conditionalText, "", "");
        this.connectorText = connectorText;
    }

    /**
     * Kodu verilen kıyaslama operatörünün <code>enum</code> değerini döner.
     *
     * @param code kıyaslama operatörü kodu
     * @return kıyaslama operatörü
     */
    public static ComparisonOperatorEnum parse(final int code) {
        return map.get(code);
    }

    // getters and setters ....................................................

    public int getValueCount() {
        return this.valueCount;
    }

    public String getConditionalText() {
        return this.getBundle().getString(this.label + ".sign");
    }

    public String getConditionalSql() {
        return this.conditionalText;
    }

    public String getConnectorText() {
        return this.connectorText;
    }

    public String getConnectorSql() {
        return this.connectorText;
    }

    public String getPrefix(final ICriterion criterion) {
        return (criterion instanceof DateCriterion) && this.equals(EQUALS) ? "" : this.prefix;
    }

    public String getSuffix(final ICriterion criterion) {
        return (criterion instanceof DateCriterion) && this.equals(EQUALS) ? "" : this.suffix;
    }

    public String getLabel() {
        return this.label;
    }

    public void setLabel(final String label) {
        this.label = label;
    }

    public int getCode() {
        return this.code;
    }

    private ResourceBundle getBundle() {
        if (this.bundle == null) {
            final FacesContext ctx = FacesContext.getCurrentInstance();
            if (ctx != null) {
                this.bundle = ResourceBundle.getBundle("/labels", ctx.getViewRoot().getLocale());
            }
        }
        return this.bundle;
    }

    public static List<ComparisonOperatorEnum> valuesAsList() {
        return VALUE_LIST;
    }

}
