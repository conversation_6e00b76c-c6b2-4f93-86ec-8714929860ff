package tr.gov.tubitak.bte.mues.session;

import java.util.Map;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.SortMeta;

import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.search.SearchConstants;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class LiteraturLazyLoadFacade extends LazyLoadFromDBFacade<Literatur> {

	private static final long serialVersionUID = 6674734777223438853L;

	@Inject
    protected EntityManager em;

    public LiteraturLazyLoadFacade() {
        super(Literatur.class);
        this.setCustomQuery(SearchConstants.EMPTY);
    }

    @Override
    protected String appendLike(final String param, final FilterMeta filterMeta) {
        if ("globalFilter".equals(param)) {
            return "(LOWER(o.ad) LIKE LOWER(:globalFilter) OR LOWER(o.aciklama) LIKE LOWER(:globalFilter))";
        }
        return super.appendLike(param, filterMeta);
    }

    @Override
    protected String sortClause(final Map<String, SortMeta> multiSortMeta) {

        final StringBuilder str = new StringBuilder();

        if ((multiSortMeta != null) && !multiSortMeta.isEmpty()) {

            return super.sortClause(multiSortMeta);

        } else {
            str.append("ad");
            str.insert(0, " ORDER BY ");
            return str.toString();
        }
    }

    @Override
    protected EntityManager getEM() {
        return this.em;
    }

}
