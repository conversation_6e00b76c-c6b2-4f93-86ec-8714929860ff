package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalKaynakLiteratur;
import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.session.EserProposalKaynakLiteraturFacade;

@Named
@ViewScoped
public class EserProposalKaynakLiteraturController extends AbstractController<EserProposalKaynakLiteratur> {

    private static final long                 serialVersionUID = -8692714386176081472L;

    @Inject
    private EserProposalKaynakLiteraturFacade facade;

    public EserProposalKaynakLiteraturController() {
        super(EserProposalKaynakLiteratur.class);
    }

    public void handleLiteraturSelect(final Literatur literatur) {
        this.getModel().setLiteratur(literatur);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalKaynakLiteraturFacade getFacade() {
        return this.facade;
    }

}
