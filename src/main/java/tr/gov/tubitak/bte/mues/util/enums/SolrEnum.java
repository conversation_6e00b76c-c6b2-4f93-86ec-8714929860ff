/***********************************************************
 * SolrEnum.java - mues Projesi
 *
 * Kullanılan JRE: 1.8.0_91
 *
 * ufuk.tul - 19.01.2021
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2022©
 ***********************************************************/

package tr.gov.tubitak.bte.mues.util.enums;

public enum SolrEnum {

    ENVANTER(0, "solr.eser.core.url"),

    MBS(1, "solr.mbs.core.url"),

    PERSONEL(2, "solr.personel.core.url"),

    LABORATUVAR(3, "solr.lab.core.url"),

    KAM_ARTIFACT(4, "solr.kam.artifact.core.url"),

    KAM_INCIDENT(4, "solr.kam.incident.core.url"),

    KMS_COMMISSION(5, "solr.kms.commission.core.url"),

    KMS_OBJECT(5, "solr.kms.object.core.url"),

    KMS_TGA(6, "solr.kms.tga.core.url"),

    OMK_ARTIFACT(7, "solr.omk.eser.core.url");

    Integer code;

    String  coreKey;

    private SolrEnum(final Integer code, final String coreKey) {
        this.code = code;
        this.coreKey = coreKey;
    }

    public static SolrEnum parse(final String code) {
        for (final SolrEnum each : SolrEnum.values()) {
            if (code.equals(each.name())) {
                return each;
            }
        }

        return SolrEnum.ENVANTER;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getCoreKey() {
        return this.coreKey;
    }

}
