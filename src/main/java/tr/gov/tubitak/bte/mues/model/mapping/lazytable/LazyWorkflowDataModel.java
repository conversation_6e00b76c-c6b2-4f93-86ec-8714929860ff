package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.mapping.WorkflowView;
import tr.gov.tubitak.bte.mues.session.WorkflowViewLazyLoadFacade;

public class LazyWorkflowDataModel extends LazyDataModel<WorkflowView> {

	private static final long serialVersionUID = -4739489425974372483L;

	private static final Logger logger = LoggerFactory.getLogger(LazyWorkflowDataModel.class);

	private final transient WorkflowViewLazyLoadFacade facade;

	private List<WorkflowView> data;

	public LazyWorkflowDataModel(final WorkflowViewLazyLoadFacade facade) {
		this.facade = facade;
	}

	@Override
	public List<WorkflowView> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy,
			final Map<String, FilterMeta> filterBy) {
		this.data = this.facade.fetchData(first, pageSize, sortBy, filterBy);
		logger.debug("filters: {} _ multiSortMeta Length,  {}", sortBy, filterBy);
		return this.data;
	}

	@Override
	public int count(final Map<String, FilterMeta> filterBy) {
		return this.facade.count(filterBy);
	}

	@Override
	public WorkflowView getRowData(final String rowKey) {
		if (this.data != null) {
			for (final WorkflowView each : this.data) {
				if (each.getWorkflowRowKey().equals(rowKey)) {
					return each;
				}
			}
		}
		return null;
	}

	@Override
	public String getRowKey(final WorkflowView w) {
		return w.getWorkflowRowKey();
	}

}
