package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Arastirma;

@RequestScoped
public class ArastirmaFacade extends AbstractFacade<Arastirma> {

    public ArastirmaFacade() {
        super(Arastirma.class);
    }

    public List<Arastirma> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Arastirma.findByNameAndAciklama", Arastirma.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
