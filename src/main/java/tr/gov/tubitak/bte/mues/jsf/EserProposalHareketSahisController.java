package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalHareketSahis;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

/**
 * <AUTHOR>
 *
 */
@Named
@ViewScoped
public class EserProposalHareketSahisController extends AbstractController<EserProposalHareketSahis> {

    private static final long serialVersionUID = -4790994858737662781L;

    public EserProposalHareketSahisController() {
        super(EserProposalHareketSahis.class);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalHareketSahis getModel() {
        if (super.getModel() == null) {
            this.setModel(new EserProposalHareketSahis());
        }
        return super.getModel();
    }

    @Override
    public AbstractFacade<EserProposalHareketSahis> getFacade() {
        return null;
    }

}
