package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.ValidPersonelYetkiTime;

/**
 *
*
 */

@Audited
@Entity
@Table(name = "KULLANICI_BIRIM_ROL", uniqueConstraints = { @UniqueConstraint(columnNames = { "KULLANICI_ID", "MUZE_MUDURLUGU_ID", "ROL_ID" }) })
@ValidPersonelYetkiTime
public class KullaniciBirimRol extends KullaniciBirimRolSuper {

    private static final long serialVersionUID = 181231918226418523L;

    public KullaniciBirimRol() {
        // default constructor
    }

}
