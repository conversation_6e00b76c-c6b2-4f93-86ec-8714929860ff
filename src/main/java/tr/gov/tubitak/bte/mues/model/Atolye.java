package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "Atolye")
@NamedQuery(name = "Atolye.findEagerById", query = "SELECT a FROM Atolye a WHERE a.id = :id")
@NamedQuery(name = "Atolye.findAll", query = "SELECT a FROM Atolye a  ORDER BY a.silinmis, a.aktif DESC")
@NamedQuery(name = "Atolye.findActive", query = "SELECT a FROM Atolye a WHERE a.aktif = true AND a.silinmis = false")
@NamedQuery(name = "Atolye.findByNameAndAciklama", query = "SELECT a FROM Atolye a WHERE a.aktif = true AND a.silinmis = false AND (a.ad LIKE :str OR a.aciklama LIKE :str) ORDER BY a.ad, a.aciklama")

public class Atolye extends AbstractEntity {

    private static final long serialVersionUID = 7097486972591276865L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "note", length = 150)
    private String            aciklama;

    public Atolye() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
