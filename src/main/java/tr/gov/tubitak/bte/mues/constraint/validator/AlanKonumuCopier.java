package tr.gov.tubitak.bte.mues.constraint.validator;

import org.omnifaces.util.copier.Copier;

import tr.gov.tubitak.bte.mues.model.AlanKonumu;

public class AlanKonumuCopier implements Copier {
	@Override
    public AlanKonumu copy(final Object object) {
        final AlanKonumu original = (AlanKonumu) object;
        final AlanKonumu copy = new AlanKonumu();

        copy.setKod(original.getKod());
                
        return copy;
    }
}
