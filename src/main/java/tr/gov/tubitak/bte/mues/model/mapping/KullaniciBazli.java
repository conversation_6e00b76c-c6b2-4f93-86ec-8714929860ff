package tr.gov.tubitak.bte.mues.model.mapping;

public class KullaniciBazli {

    private Integer mudurlukId;

    private String  mudurluk;

    private String  fotografPath;

    private Integer kullaniciId;

    private String  kullanici;

    private Integer artifactsPendingApprovalCount;

    private Integer artifactsDraftCount;

    private Integer artifactsRejectedCount;

    private Integer eserSayisi;

    public KullaniciBazli(final Integer mudurlukId,
                          final String mudurluk,
                          final String fotografPath,
                          final Integer kullaniciId,
                          final String kullanici,
                          final Integer eserSayisi,
                          final Integer artifactsPendingApprovalCount,
                          final Integer artifactsDraftCount,
                          final Integer artifactsRejectedCount) {
        this.mudurlukId = mudurlukId;
        this.mudurluk = mudurluk;
        this.fotografPath = fotografPath;
        this.kullaniciId = kullaniciId;
        this.kullanici = kullanici;
        this.eserSayisi = eserSayisi;
        this.artifactsPendingApprovalCount = artifactsPendingApprovalCount;
        this.artifactsDraftCount = artifactsDraftCount;
        this.artifactsRejectedCount = artifactsRejectedCount;
    }

    // getters and setters ....................................................

    public Integer getMudurlukId() {
        return this.mudurlukId;
    }

    public void setMudurlukId(final Integer mudurlukId) {
        this.mudurlukId = mudurlukId;
    }

    public void setMudurluk(final String mudurluk) {
        this.mudurluk = mudurluk;
    }

    public String getMudurluk() {
        return this.mudurluk;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setKullanici(final String kullanici) {
        this.kullanici = kullanici;
    }

    public String getKullanici() {
        return this.kullanici;
    }

    public void setEserSayisi(final Integer eserSayisi) {
        this.eserSayisi = eserSayisi;
    }

    public Integer getEserSayisi() {
        return this.eserSayisi;
    }

    public Integer getKullaniciId() {
        return this.kullaniciId;
    }

    public void setKullaniciId(final Integer kullaniciId) {
        this.kullaniciId = kullaniciId;
    }

    public Integer getArtifactsPendingApprovalCount() {
        return this.artifactsPendingApprovalCount;
    }

    public void setArtifactsPendingApprovalCount(final Integer artifactsPendingApprovalCount) {
        this.artifactsPendingApprovalCount = artifactsPendingApprovalCount;
    }

    public Integer getArtifactsDraftCount() {
        return this.artifactsDraftCount;
    }

    public void setArtifactsDraftCount(final Integer artifactsDraftCount) {
        this.artifactsDraftCount = artifactsDraftCount;
    }

    public Integer getArtifactsRejectedCount() {
        return this.artifactsRejectedCount;
    }

    public void setArtifactsRejectedCount(final Integer artifactsRejectedCount) {
        this.artifactsRejectedCount = artifactsRejectedCount;
    }

}
