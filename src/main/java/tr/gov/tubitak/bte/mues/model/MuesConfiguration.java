package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

@Audited
@Entity
@Table(name = "CONFIGURATION")
@NamedQuery(name = "MuesConfiguration.findAll", query = "SELECT c FROM MuesConfiguration c ORDER BY c.silinmis, c.aktif DESC, c.clue")

public class MuesConfiguration extends ConfigurationSuper {

    private static final long serialVersionUID = 1019478583266366160L;

    public MuesConfiguration() {
        // Default Constructor
    }

}
