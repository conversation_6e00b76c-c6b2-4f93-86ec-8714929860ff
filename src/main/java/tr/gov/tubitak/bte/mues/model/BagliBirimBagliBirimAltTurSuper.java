package tr.gov.tubitak.bte.mues.model;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "BagliBirimBagliBirimAltTur.findEagerById", query = "SELECT x FROM BagliBirimBagliBirimAltTur x LEFT JOIN FETCH x.bagliBirim LEFT JOIN FETCH x.bagliBirimAltTur xx LEFT JOIN FETCH xx.bagliBirimTur WHERE x.id = :id")
@NamedQuery(name = "BagliBirimBagliBirimAltTur.findAll", query = "SELECT b FROM BagliBirimBagliBirimAltTur b")
@NamedQuery(name = "BagliBirimBagliBirimAltTur.findActive", query = "SELECT b FROM BagliBirimBagliBirimAltTur b WHERE b.aktif = true AND b.silinmis = false")

public class BagliBirimBagliBirimAltTurSuper extends AbstractEntity {

    private static final long serialVersionUID = 1035253373119984286L;

    @JoinColumn(name = "bagliBirimId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private BagliBirim        bagliBirim;

    @JoinColumn(name = "bagliBirimAltTurId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private BagliBirimAltTur  bagliBirimAltTur;

    @Size(max = 150)
    @Column(name = "description", length = 150)
    private String            aciklama;

    public BagliBirimBagliBirimAltTurSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public BagliBirim getBagliBirim() {
        return this.bagliBirim;
    }

    public void setBagliBirim(final BagliBirim bagliBirim) {
        this.bagliBirim = bagliBirim;
    }

    public BagliBirimAltTur getBagliBirimAltTur() {
        return this.bagliBirimAltTur;
    }

    public void setBagliBirimAltTur(final BagliBirimAltTur bagliBirimAltTur) {
        this.bagliBirimAltTur = bagliBirimAltTur;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return "{Birim: " + this.getBagliBirim().getAd() + ", Alttur: " + this.getBagliBirimAltTur().getAd() + "}";
    }

    @Override
    public String getTitle() {
        return Stream.of(this.getBagliBirim().getAd(), MuesUtil.surroundWithParanthesis(this.getBagliBirimAltTur().getAd())).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }

}
