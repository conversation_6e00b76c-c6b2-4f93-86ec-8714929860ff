package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "BAGLI_BIRIM_ALT_TUR")
@NamedQuery(name = "BagliBirimAltTur.findEagerById", query = "SELECT b FROM BagliBirimAltTur b JOIN FETCH b.bagliBirimTur WHERE b.id = :id")
@NamedQuery(name = "BagliBirimAltTur.findAll", query = "SELECT b FROM BagliBirimAltTur b JOIN FETCH b.bagliBirimTur t ORDER BY b.silinmis, b.aktif DESC, t.ad")
@NamedQuery(name = "BagliBirimAltTur.findActive", query = "SELECT b FROM BagliBirimAltTur b JOIN FETCH b.bagliBirimTur WHERE b.aktif = true AND b.silinmis = false ORDER BY b.ad")
@NamedQuery(name = "BagliBirimAltTur.filterByNameAndTur", query = "SELECT b FROM BagliBirimAltTur b WHERE b.aktif = true AND b.silinmis = false AND b.bagliBirimTur = :tur AND b.ad LIKE :ad AND b NOT IN :bagliBirimAltTur ORDER BY b.ad")
@NamedQuery(name = "BagliBirimAltTur.findByNameAndTur", query = "SELECT b FROM BagliBirimAltTur b WHERE b.aktif = true AND b.silinmis = false AND b.bagliBirimTur = :tur AND b.ad LIKE :ad ORDER BY b.ad")
@NamedNativeQuery(name = "BagliBirimAltTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM BagliBirim_BagliBirimAltTur WHERE SILINMIS = 0 AND bagliBirimAltTurId = :id)")

public class BagliBirimAltTur extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -6681579384160277628L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "BAGLI_BIRIM_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private BagliBirimTur     bagliBirimTur;

    public BagliBirimAltTur() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public BagliBirimTur getBagliBirimTur() {
        return this.bagliBirimTur;
    }

    public void setBagliBirimTur(final BagliBirimTur bagliBirimTur) {
        this.bagliBirimTur = bagliBirimTur;//
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
