package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "KAZI_TUR")
@NamedQuery(name = "KaziTur.findEagerById", query = "SELECT k FROM KaziTur k WHERE k.id = :id")
@NamedQuery(name = "KaziTur.findAll", query = "SELECT k FROM KaziTur k ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "KaziTur.findActive", query = "SELECT k FROM KaziTur k WHERE k.aktif = true AND k.silinmis = false ORDER BY k.ad")
@NamedNativeQuery(name = "KaziTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM KAZI WHERE SILINMIS = 0 AND KAZI_TUR_ID = :id)")
public class KaziTur extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -1941160868904648972L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @OneToMany(mappedBy = "kaziTur")
    private Collection<Kazi>  kaziCollection;

    public KaziTur() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<Kazi> getKaziCollection() {
        return this.kaziCollection;
    }

    public void setKaziCollection(final Collection<Kazi> kaziCollection) {
        this.kaziCollection = kaziCollection;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
