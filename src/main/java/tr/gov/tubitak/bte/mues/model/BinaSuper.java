package tr.gov.tubitak.bte.mues.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "Bina.findEagerById", query = "SELECT b FROM Bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE b.id = :id")
@NamedQuery(name = "Bina.findAll", query = "SELECT b FROM Bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk ORDER BY b.silinmis, b.aktif DESC, b.ad")
@NamedQuery(name = "Bina.findByMudurluk", query = "SELECT b FROM Bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk m WHERE bb.mudurluk in :muzeler ORDER BY b.silinmis, b.aktif DESC, m.ad ASC ")
@NamedQuery(name = "Bina.findActive", query = "SELECT b FROM Bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE b.aktif = true AND b.silinmis = false ORDER BY b.ad")
@NamedQuery(name = "Bina.findByNameAndAciklamaAndBirim", query = "SELECT b FROM Bina b WHERE b.aktif = true AND b.silinmis = false AND (b.ad LIKE :str OR b.aciklama LIKE :str) AND b.bagliBirim = :birim ORDER BY b.ad")
@NamedQuery(name = "Bina.findByNameAndAciklamaAndBirimPreventDuplicate", query = "SELECT b FROM Bina b WHERE b.id NOT IN :ids AND b.aktif = true AND b.silinmis = false AND (b.ad LIKE :str OR b.aciklama LIKE :str) AND b.bagliBirim = :birim ORDER BY b.ad")
public class BinaSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 6333391550779912072L;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "pdfPath", length = 250)
    private String            pdfPath;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "dwgPath", length = 250)
    private String            dwgPath;

    @JoinColumn(name = "BAGLI_BIRIM_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private BagliBirim        bagliBirim;

    @Min(value = -90)
    @Max(value = 90)
    @Column(name = "enlem")
    private BigDecimal        enlem;

    @Min(value = -180)
    @Max(value = 180)
    @Column(name = "boylam")
    private BigDecimal        boylam;

    @Size(max = 15)
    @Column(name = "pafta")
    private String            pafta;

    @Min(value = 0)
    @Max(value = 10000000)
    @Column(name = "ada")
    private Integer           ada;

    @Min(value = 0)
    @Max(value = 10000000)
    @Column(name = "parsel")
    private Integer           parsel;

    public BinaSuper() {
    }

    // getters and setters ....................................................

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getPdfPath() {
        return this.pdfPath;
    }

    public void setPdfPath(final String pdfPath) {
        this.pdfPath = pdfPath;
    }

    public String getDwgPath() {
        return this.dwgPath;
    }

    public void setDwgPath(final String dwgPath) {
        this.dwgPath = dwgPath;
    }

    public BagliBirim getBagliBirim() {
        return this.bagliBirim;
    }

    public void setBagliBirim(final BagliBirim bagliBirim) {
        this.bagliBirim = bagliBirim;
    }

    public BigDecimal getEnlem() {
        return this.enlem;
    }

    public void setEnlem(final BigDecimal enlem) {
        this.enlem = enlem;
    }

    public BigDecimal getBoylam() {
        return this.boylam;
    }

    public void setBoylam(final BigDecimal boylam) {
        this.boylam = boylam;
    }

    public String getPafta() {
        return this.pafta;
    }

    public void setPafta(final String pafta) {
        this.pafta = pafta;
    }

    public Integer getAda() {
        return this.ada;
    }

    public void setAda(final Integer ada) {
        this.ada = ada;
    }

    public Integer getParsel() {
        return this.parsel;
    }

    public void setParsel(final Integer parsel) {
        this.parsel = parsel;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
