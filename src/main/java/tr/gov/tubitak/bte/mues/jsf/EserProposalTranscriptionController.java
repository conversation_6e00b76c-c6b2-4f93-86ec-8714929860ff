package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;
import javax.json.Json;
import javax.json.JsonArrayBuilder;
import javax.json.JsonObject;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.EserProposalTranscription;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardCharacter;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardCharacterFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class EserProposalTranscriptionController extends AbstractController<EserProposalTranscription> implements SingleFileUploadable {

    private static final long              serialVersionUID = -7905785918797792754L;

    @Inject
    private VirtualKeyboardCharacterFacade virtualKeyboardCharacterFacade;

    @Inject
    private YaziTipiController             yaziTipiController;

    @Inject
    private FileUploadHelper               fileUploadHelper;

    private Boolean                        isKeyboardSet;

    public EserProposalTranscriptionController() {
        super(EserProposalTranscription.class);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setTranscriptionPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getTranscriptionPath() != null) {
            this.getModel().setTranscriptionPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getTranscriptionPath(), FolderType.TRANSCRIPT));
        }
    }

    public void makeFileRelatedOperations(final EserProposalTranscription transcription) {
        this.setModel(transcription);
        this.writeToPermanentFolder();
    }

    public List<String> buildFilePathFromModel(final EserProposalTranscription model) {
        return this.fileUploadHelper.constructMainCopyImagePath(model.getTranscriptionPath(), FolderType.TRANSCRIPT);
    }

    public void handleLanguageChange(final SelectEvent<Dil> event) {
        final Dil language = event.getObject();
        this.yaziTipiController.setDil(language);
        this.getModel().setYaziTipi(null);

        final List<VirtualKeyboardCharacter> letterList = this.virtualKeyboardCharacterFacade.findByDilId(language.getId());

        final JsonArrayBuilder outerJa = Json.createArrayBuilder();

        for (int i = 0; i < letterList.size(); i++) {
            if ((i == 0) || ((i % 11) == 0)) {
                final JsonArrayBuilder ja = Json.createArrayBuilder();
                ja.add(letterList.get(i).getCharacter()).add(letterList.get(i).getCharacter()).add(1).add(0).add(true);
                outerJa.add(ja);
            } else {
                final JsonArrayBuilder ja = Json.createArrayBuilder();
                ja.add(letterList.get(i).getCharacter()).add(letterList.get(i).getCharacter()).add(1).add(0).add(false);
                outerJa.add(ja);
            }
        }

        if (!letterList.isEmpty()) {
            final JsonArrayBuilder ja = Json.createArrayBuilder();
            ja.add("⟵").add("8").add(8).add(9).add(true);
            outerJa.add(ja);
            ja.add("__").add("32").add(32).add(9).add(false);
            outerJa.add(ja);

            final JsonObject jo = Json.createObjectBuilder().add("layout", Json.createArrayBuilder().add(Json.createArrayBuilder().add(outerJa))).build();
            PrimeFaces.current().executeScript("charSet =" + jo + " ;");

            this.setIsKeyboardSet(true);
        } else {
            this.setIsKeyboardSet(false);
            PrimeFaces.current().executeScript("delete charSet ;");
        }

    }

    // getters and setters ....................................................

    @Override
    public AbstractFacade<EserProposalTranscription> getFacade() {
        return null;
    }

    public Boolean getIsKeyboardSet() {
        return this.isKeyboardSet;
    }

    public void setIsKeyboardSet(final Boolean isKeyboardSet) {
        this.isKeyboardSet = isKeyboardSet;
    }

}
