/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.jsf;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.enterprise.context.ApplicationScoped;
import javax.faces.context.FacesContext;
import javax.faces.event.PhaseId;
import javax.inject.Inject;
import javax.inject.Named;
import javax.servlet.ServletContext;

import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.coobird.thumbnailator.Thumbnails;
import tr.gov.tubitak.bte.mues.model.MuesConfiguration;
import tr.gov.tubitak.bte.mues.model.MuesPick;
import tr.gov.tubitak.bte.mues.model.MuesPickGroup;
import tr.gov.tubitak.bte.mues.request.FileDownloadController;
import tr.gov.tubitak.bte.mues.session.MuesConfigurationFacade;
import tr.gov.tubitak.bte.mues.session.MuesPickFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesException;

/**
 * ali.kelle
 *
 */
@Named
@ApplicationScoped
public class MuesParameters implements Serializable {

    private static final long                                 serialVersionUID            = 2079681704742799706L;

    private static final Logger                               logger                      = LoggerFactory.getLogger(MuesParameters.class);

    @Inject
    private MuesConfigurationFacade                           configurationFacade;

    @Inject
    private FileDownloadController                            fileDownloadController;

    @Inject
    private transient MuesPickFacade                          secenekFacade;

    @Inject
    private ServletContext                                    servletContext;

    private Map<String, String>                               params;

    public static final Integer                               GELIS_SEKLI_SATIN_ALMA_HIBE = 11;

    public static final Integer                               GELIS_SEKLI_SAYIM_FAZLASI   = 8;

    public static final Integer                               GELIS_SEKLI_SATIN_ALMA      = 4;

    private static Map<MuesPickGroup, Map<Integer, MuesPick>> picks;

    @PostConstruct
    public void loadInitialParameters() {
        logger.debug("[MuesParameters] : Entered");
        this.populateConfigParams();
        this.populatePicks();
        logger.info("[loadInitialParameters] : Parameters loaded from the database");
    }

    private synchronized void populateConfigParams() {
        this.params = this.configurationFacade.findAll().stream().collect(Collectors.toMap(MuesConfiguration::getClue, MuesConfiguration::getValue));
    }

    private synchronized void populatePicks() {
        final List<MuesPick> seceneks = this.secenekFacade.findActive();
        picks = seceneks.stream().collect(Collectors.groupingBy(MuesPick::getGrup, Collectors.toMap(MuesPick::getRank, x -> x)));
    }

    public MuesParameters() {
        // blank constructor
    }

    public StreamedContent getImage() {

        if (FacesContext.getCurrentInstance().getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
            // So, we're rendering the HTML. Return a stub StreamedContent so that it will generate right URL.
            return new DefaultStreamedContent();

        } else {
            // So, browser is requesting the image. Return a real StreamedContent with the image bytes.
            final String file = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("file");

            if ((file != null) && (file.length() > 0)) {
                return DefaultStreamedContent.builder().contentType("image/png").stream(() ->
                    {

                        try {
                            return new FileInputStream(this.getImageUsageCopyPath(file));

                        } catch (final Exception e) {
                            logger.debug("Ilgili Foto bulunamadi");
                        }
                        return null;

                    }).build();
            }
            return null;
        }
    }

    public List<MuesPick> filterPickByEserGelisSekli(final String query, final List<Integer> exludeRanks) {
        return Optional.ofNullable(getPicksByGroupCode(1).values())
                       .orElse(Collections.emptyList())
                       .stream()
                       .filter(x -> (x.getAd().toLowerCase().contains(query.toLowerCase()) || Optional.ofNullable(x.getAciklama()).orElse("").toLowerCase().contains(query.toLowerCase()))
                                    && !exludeRanks.contains(x.getRank()))
                       .collect(Collectors.toList());
    }

    public List<MuesPick> filterPickByNameAndIlgiliYuz(final String query) {
        return this.filterPickByName(getPicksByGroupCode(3), query);
    }

    public List<MuesPick> filterPickByNameAndKondisyonDurumu(final String query) {
        return this.filterPickByName(getPicksByGroupCode(5), query);
    }

    public List<MuesPick> filterPickByNameAndGorevlendirmeTipi(final String query) {
        return this.filterPickByName(getPicksByGroupCode(6), query);
    }

    public List<MuesPick> filterPickByNameAndLanguageLevel(final String query) {
        return this.filterPickByName(getPicksByGroupCode(36), query);
    }

    public static Map<Integer, MuesPick> getPicksByGroupCode(final Integer number) {
        return picks.get(picks.keySet().stream().filter(x -> Objects.equals(x.getKod(), number)).findFirst().get());
    }

    public List<MuesPick> filterPickByNameAndEserGelisSekliTemporaryAdmissionReceipt(final String query) {
        final List<MuesPick> aList = this.filterPickByName(getPicksByGroupCode(1), query);
        Optional.of(MuesParameters.getPicksByGroupCode(1).values())
                .ifPresent(b -> aList.removeAll(b.stream()
                                                 .filter(x -> (MuesParameters.GELIS_SEKLI_SAYIM_FAZLASI.equals(x.getRank())) || (MuesParameters.GELIS_SEKLI_SATIN_ALMA_HIBE.equals(x.getRank())))
                                                 .collect(Collectors.toList())));
        return aList;
    }

    private List<MuesPick> filterPickByName(final Map<Integer, MuesPick> picks, final String query) {
        return Optional.ofNullable(picks.values())
                       .orElse(Collections.emptyList())
                       .stream()
                       .filter(x -> x.getAd().toLowerCase().contains(query.toLowerCase()) || Optional.ofNullable(x.getAciklama()).orElse("").toLowerCase().contains(query.toLowerCase()))
                       .collect(Collectors.toList());
    }

    public String getImageUsageCopyPath(final String filePath) {
        return this.get(AbstractParameters.IMAGES_BASE_FOLDER) + File.separator + FolderType.IMAGE_DK.getFolderPath() + File.separator + filePath + AbstractParameters.IMAGE_EXTENSION;
    }

    public void showImage() {
        final Map<String, String> paramMap = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap();
        this.showMainImage(paramMap.get("file"));
    }

    public void showMainImage(final String file) {
        logger.debug("image Changed {}", file);
        this.fileDownloadController.showImage(file, FolderType.IMAGE_AK, this.get(AbstractParameters.IMAGES_BASE_FOLDER));

    }

    public void handlePdfShow(final String path, final FolderType folderType) {
        this.fileDownloadController.handlePdfShow(path, folderType, "contentContainer", this.get(AbstractParameters.IMAGES_BASE_FOLDER));
    }

    public String getImageMainCopyUrl(final String url) {
        return this.getImageURL(url, FolderType.IMAGE_AK.getFolderPath());
    }

    public String getImageUsageCopyUrl(final String url) {
        return this.getImageURL(url, FolderType.IMAGE_DK.getFolderPath(), AbstractParameters.IMAGE_EXTENSION);
    }

    public String getImageURL(final String url, final String copyType, final String extension) {
        return this.getImageURL(url, copyType) + extension;
    }

    public String getImageURL(final String url, final String copyType) {
        final String baseURL = this.params.get(AbstractParameters.IMAGES_BASE_URL);
        if (url.startsWith(AbstractParameters.SLASH)) {
            return baseURL + copyType + url;

        } else {
            return baseURL + copyType + AbstractParameters.SLASH + url;
        }
    }

    public String get(final String key) {
        return this.params.get(key);
    }

    public Path getAbsolutePath(final String filePath, final FolderType type) {
        return Paths.get(this.get(AbstractParameters.IMAGES_BASE_FOLDER), type.getFolderPath(), filePath);
    }

    public void showPdf(final String file, final FolderType type) {
        this.fileDownloadController.handlePdfShow(file, type, "contentContainer", this.get(AbstractParameters.IMAGES_BASE_FOLDER));
    }
    
    public void showRestorationPdf(final String file, final FolderType type) {
        this.fileDownloadController.handlePdfShow(file, type, "contentContainer", this.get(AbstractParameters.LAB_IMAGES_BASE_URL));
    }

    public void showPdf(final String file, final FolderType type, final String container) {
        this.fileDownloadController.handlePdfShow(file, type, container, this.get(AbstractParameters.IMAGES_BASE_FOLDER));
    }

    public String writeMainCopyToFile(final String tempFile, final FolderType type) {

        if (!tempFile.startsWith(this.getTempDir())) {
            return tempFile;
        }

        final Path tempFile1 = Paths.get(tempFile);
        if (tempFile1.getFileName() == null) {
            throw new MuesException("writeMainCopyToFile method called with null filename");
        }
        final Path finalAbsolutePath = this.generatePath(tempFile1.getFileName().toString(), type.getFolderPath());
        final String filePath = finalAbsolutePath.subpath(finalAbsolutePath.getNameCount() - 4, finalAbsolutePath.getNameCount()).toString().replace("\\", "/");
        try {
            this.createMainCopyWithFolders(tempFile1, type, finalAbsolutePath);
        } catch (final IOException e) {
            logger.error("[writeMainCopyToFile] : Hata : {}", e.getMessage(), e);
        } finally {
            try {
                Files.deleteIfExists(tempFile1);
            } catch (final IOException e) {
                logger.error("[writeMainCopyToFile] : Hata : {}", e.getMessage(), e);
            }
        }
        return filePath;
    }

    public Path generatePath(final String fileName, final String copyType) {
        final Path root = Paths.get(this.get(AbstractParameters.IMAGES_BASE_FOLDER) + File.separator + copyType);
        final Path relativeFolderHierarchy = Paths.get(fileName.substring(0, 1), fileName.substring(1, 2), fileName.substring(2, 3));
        final Path absoluteFolderHierarchy = root.resolve(relativeFolderHierarchy);
        return absoluteFolderHierarchy.resolve(fileName);
    }

    public void createMainCopyWithFolders(final Path tempFile, final FolderType type, final Path finalAbsolutePath) throws IOException {
        Files.createDirectories(finalAbsolutePath.getParent());
        Files.copy(tempFile, finalAbsolutePath);
        if (type == FolderType.IMAGE_AK) {
            this.generateImageUsageCopy(tempFile);
        }
        logger.info("[createMainCopyWithFolders] : {}", finalAbsolutePath);
    }

    private void generateImageUsageCopy(final Path tempFile) {
        if (tempFile.getFileName() == null) {
            throw new MuesException("generateUsageCopy method called with null filename");
        }
        final Path finalAbsoluteUCPath = this.generatePath(tempFile.getFileName().toString(), FolderType.IMAGE_DK.getFolderPath());

        try {
            this.createUsageCopyWithFolders(tempFile, finalAbsoluteUCPath);
        } catch (final IOException e) {
            logger.error("[generateUsageCopy] : Hata : {}", e.getMessage(), e);
        }
    }

    private void createUsageCopyWithFolders(final Path tempFile, final Path finalAbsoluteUCPath) throws IOException {
        Files.createDirectories(finalAbsoluteUCPath.getParent());
        Thumbnails.of(tempFile.toFile()).size(128, 128).outputFormat("png").toFile(finalAbsoluteUCPath.toString());
        logger.info("[generateUsageCopy] : {}", finalAbsoluteUCPath);
    }

    public String getTempDir() {
        return this.servletContext.getAttribute(ServletContext.TEMPDIR).toString();
    }

}