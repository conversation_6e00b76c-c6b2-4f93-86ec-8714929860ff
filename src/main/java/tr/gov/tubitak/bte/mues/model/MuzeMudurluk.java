package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "MUZE_MUDURLUGU", uniqueConstraints = { @UniqueConstraint(columnNames = { "kod", "ad" }) })

@NamedQuery(name = "MuzeMudurluk.findEagerById", query = "SELECT m FROM MuzeMudurluk m LEFT JOIN FETCH m.laboratoryDirectorate LEFT JOIN FETCH m.il LEFT JOIN FETCH m.ilce WHERE m.id = :id")
@NamedQuery(name = "MuzeMudurluk.findAll", query = "SELECT m FROM MuzeMudurluk m LEFT JOIN FETCH m.il JOIN FETCH m.ilce LEFT JOIN FETCH m.laboratoryDirectorate ORDER BY m.ad")
@NamedQuery(name = "MuzeMudurluk.findByIdsAndName", query = "SELECT m FROM MuzeMudurluk m LEFT JOIN FETCH m.il LEFT JOIN FETCH m.ilce WHERE m.id in :ids AND m.ad LIKE :str ORDER BY m.ad ")
@NamedQuery(name = "MuzeMudurluk.findByName", query = "SELECT m FROM MuzeMudurluk m WHERE m.ad LIKE :str ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "MuzeMudurluk.findActive", query = "SELECT m FROM MuzeMudurluk m LEFT JOIN FETCH m.laboratoryDirectorate LEFT JOIN FETCH m.il LEFT JOIN FETCH m.ilce WHERE m.aktif = true AND m.silinmis = false ORDER BY m.ad")
@NamedQuery(name = "MuzeMudurluk.findMuseumDirectorateByCityCode", query = "SELECT m FROM MuzeMudurluk m LEFT JOIN FETCH m.laboratoryDirectorate LEFT JOIN FETCH m.il LEFT JOIN FETCH m.ilce WHERE m.aktif = true AND m.silinmis = false AND m.il.id = :cityCode ORDER BY m.ad")
@NamedQuery(name = "MuzeMudurluk.filterByNameAndEmptyLab", query = "SELECT m FROM MuzeMudurluk m WHERE laboratoryDirectorate is null AND (m.ad LIKE :str) ORDER BY m.ad")
@NamedQuery(name = "MuzeMudurluk.filterByLaboratoryDirectorateAndName", query = "SELECT m FROM MuzeMudurluk m LEFT JOIN FETCH m.laboratoryDirectorate ml WHERE m.ad LIKE :str AND ml.id IN :ids ORDER BY m.silinmis, m.aktif DESC, m.ad")

public class MuzeMudurluk extends MudurlukSuper {

    private static final long serialVersionUID = -1121870182876513222L;

    @JoinColumn(name = "LAB_DIRECTORATE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk          laboratoryDirectorate;

    public MuzeMudurluk() {
        // default Constructor
    }

    public MuzeMudurluk(final Integer id, final String ad, final String aciklama) {
        this.setId(id);
        this.setAd(ad);
        this.setAciklama(aciklama);
    }

    public MuzeMudurluk(final Integer integer) {
        super(integer);
    }

    public Mudurluk getLaboratoryDirectorate() {
        return this.laboratoryDirectorate;
    }

    public void setLaboratoryDirectorate(final Mudurluk laboratoryDirectorate) {
        this.laboratoryDirectorate = laboratoryDirectorate;
    }

    // getters and setters ....................................................

    @Override
    public boolean equals(final Object obj) {
        if (!this.isEqual(obj)) {
            return false;
        }
        final MuzeMudurluk mudurlugu = (MuzeMudurluk) obj;
        if (this.getAd().equals(mudurlugu.getAd())) { // added fields are tested
            return true;
        }
        return false;
    }

    // https://stackoverflow.com/questions/11935895/java-how-to-call-super-super-in-overridden-method-grandparent-method
    public boolean isEqual(final Object object) {
        if (this == object) {
            return true;
        }
        if (object == null) {
            return false;
        }
        if (this.getClass() != object.getClass()) {
            return false;
        }
        return this.hashCode() == object.hashCode();
    }
}