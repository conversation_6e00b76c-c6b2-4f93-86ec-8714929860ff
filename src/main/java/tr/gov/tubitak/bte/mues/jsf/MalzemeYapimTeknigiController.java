package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.MalzemeYapimTeknigi;
import tr.gov.tubitak.bte.mues.model.YapimTeknigi;
import tr.gov.tubitak.bte.mues.session.MalzemeYapimTeknigiFacade;

@Named
@ViewScoped
public class MalzemeYapimTeknigiController extends AbstractController<MalzemeYapimTeknigi> {

    private static final long         serialVersionUID = -6435763503005446879L;

    @Inject
    private MalzemeYapimTeknigiFacade facade;

    private List<Malzeme>             malzemes;

    private List<YapimTeknigi>        yapimTeknigis;

    private List<MalzemeYapimTeknigi> malzemeYapimTeknigis;

    private boolean                   malzemeBased     = true;

    public MalzemeYapimTeknigiController() {
        super(MalzemeYapimTeknigi.class);
    }

    // getters and setters ....................................................

    @Override
    public MalzemeYapimTeknigiFacade getFacade() {
        return this.facade;
    }

    public List<MalzemeYapimTeknigi> getMalzemeYapimTeknigis() {
        if (this.malzemeYapimTeknigis == null) {
            this.malzemeYapimTeknigis = this.getFacade().findActiveGroupByMalzeme();
        }
        return this.malzemeYapimTeknigis;
    }

    public void setMalzemeYapimTeknigis(final List<MalzemeYapimTeknigi> malzemeYapimTeknigis) {
        this.malzemeYapimTeknigis = malzemeYapimTeknigis;
    }

    public List<Malzeme> getMalzemes() {
        return this.malzemes;
    }

    public void setMalzemes(final List<Malzeme> malzemes) {
        this.malzemes = malzemes;
    }

    public List<YapimTeknigi> getYapimTeknigis() {
        return this.yapimTeknigis;
    }

    public void setYapimTeknigis(final List<YapimTeknigi> yapimTeknigis) {
        this.yapimTeknigis = yapimTeknigis;
    }

    public boolean isMalzemeBased() {
        return this.malzemeBased;
    }

    public void setMalzemeBased(final boolean malzemeBased) {
        this.malzemeBased = malzemeBased;
    }

}
