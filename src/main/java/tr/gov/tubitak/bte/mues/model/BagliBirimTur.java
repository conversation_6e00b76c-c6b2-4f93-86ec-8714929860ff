package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "BAGLI_BIRIM_TUR")
@NamedQuery(name = "BagliBirimTur.findEagerById", query = "SELECT b FROM BagliBirimTur b WHERE b.id = :id")
@NamedQuery(name = "BagliBirimTur.findAll", query = "SELECT b FROM BagliBirimTur b ORDER BY b.silinmis, b.aktif DESC, b.ad")
@NamedQuery(name = "BagliBirimTur.findActive", query = "SELECT b FROM BagliBirimTur b WHERE b.aktif = true AND b.silinmis = false ORDER BY b.ad")
@NamedNativeQuery(name = "BagliBirimTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM BAGLI_BIRIM_ALT_TUR WHERE SILINMIS = 0 AND BAGLI_BIRIM_TUR_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM BAGLI_BIRIM WHERE SILINMIS = 0 AND BAGLI_BIRIM_TUR_ID = :id)")
public class BagliBirimTur extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 6620192718970143558L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public BagliBirimTur() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }
}
