package tr.gov.tubitak.bte.mues.session;

import java.util.Map;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.primefaces.model.SortMeta;

import tr.gov.tubitak.bte.mues.model.mapping.AuditView;
import tr.gov.tubitak.bte.mues.search.SearchConstants;

@RequestScoped
public class AuditViewFacade extends LazyLoadFromDBFacade<AuditView> {

    private static final long serialVersionUID = -3002813483041648739L;

    @Inject
    protected EntityManager   em;

    public AuditViewFacade() {
        super(AuditView.class);
        this.setCustomQuery(SearchConstants.EMPTY);
    }

    @Override
    protected String sortClause(final Map<String, SortMeta> multiSortMeta) {

        final StringBuilder str = new StringBuilder();

        if ((multiSortMeta != null) && !multiSortMeta.isEmpty()) {

            return super.sortClause(multiSortMeta);

        } else {
            str.append("id DESC");
            str.insert(0, " ORDER BY ");
            return str.toString();
        }
    }

    @Override
    protected EntityManager getEM() {
        return this.em;
    }

}
