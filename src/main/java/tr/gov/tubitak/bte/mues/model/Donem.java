package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;

/**
 *
*
 */
@Entity
@Table(name = "Donem")
@NamedQuery(name = "Donem.findEagerById", query = "SELECT d FROM Donem d JOIN FETCH d.cag c JOIN FETCH c.kronoloji WHERE d.id = :id")
@NamedQuery(name = "Donem.findAll", query = "SELECT d FROM Donem d JOIN FETCH d.cag c JOIN FETCH c.kronoloji k ORDER BY d.silinmis, d.aktif DESC, k.ad")
@NamedQuery(name = "Donem.findActive", query = "SELECT d FROM Donem d JOIN FETCH d.cag WHERE d.aktif = true AND d.silinmis = false ORDER BY d.ad")
@NamedQuery(name = "Donem.findByNameAndCag", query = "SELECT d FROM Donem d WHERE d.cag = :cag AND d.ad LIKE :ad AND d.aktif = true AND d.silinmis = false ORDER BY d.ad")
@NamedNativeQuery(name = "Donem.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM UYGARLIK_DONEM WHERE SILINMIS = 0 AND DONEM_ID = :id)")

public class Donem extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 5726440021146484600L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String            fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "cagId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Cag               cag;

    @Transient
    private Integer           termStart;

    @Transient
    private Integer           termEnd;

    @Transient
    private Integer           signumStart;

    @Transient
    private Integer           signumEnd;

    public Donem() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Cag getCag() {
        return this.cag;
    }

    public void setCag(final Cag cag) {
        this.cag = cag;
    }

    public Integer getTermStart() {
        return this.termStart;
    }

    public void setTermStart(final Integer termStart) {
        this.termStart = termStart;
    }

    public Integer getTermEnd() {
        return this.termEnd;
    }

    public void setTermEnd(final Integer termEnd) {
        this.termEnd = termEnd;
    }

    public Integer getSignumStart() {
        if ((this.signumStart == null) && (this.termStart != null)) {
            if (this.termStart > 0) {
                this.signumStart = 1;
            } else {
                if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumStart = 2;
                } else {
                    this.signumStart = 3;
                }
            }
        }
        return this.signumStart;
    }

    public void setSignumStart(final Integer signumStart) {
        this.signumStart = signumStart;
    }

    public Integer getSignumEnd() {
        if ((this.signumEnd == null) && (this.termEnd != null)) {
            if (this.termEnd > 0) {
                this.signumEnd = 1;
            } else {
                if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumEnd = 2;
                } else {
                    this.signumEnd = 3;
                }
            }
        }
        return this.signumEnd;
    }

    public void setSignumEnd(final Integer signumEnd) {
        this.signumEnd = signumEnd;
    }

    public Integer getSignificantStart() {
        return this.termStart == null ? null : this.termStart < 0 ? -this.termStart : this.termStart;
    }

    public void setSignificantStart(final Integer significantStart) {
        if (this.getSignumStart() != null) {
            // it is already positive after Christ
            if (this.getSignumStart() == 1) {
                this.termStart = significantStart;

            } else {
                // set negative if before Christ
                this.termStart = -significantStart;
            }
        }
    }

    public Integer getSignificantEnd() {
        return this.termEnd == null ? null : this.termEnd < 0 ? -this.termEnd : this.termEnd;
    }

    public void setSignificantEnd(final Integer significantEnd) {
        if (this.getSignumEnd() != null) {
            // it is already positive after Christ
            if (this.getSignumEnd() == 1) {
                this.termEnd = significantEnd;

            } else {
                // set negative if before Christ
                this.termEnd = -significantEnd;
            }
        }
    }

    public String getTermStartTitle() {
        if (this.termStart == null) {
            return null;
        }
        if (this.termStart < 0) {
            if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                return "M.Ö. " + -this.termStart;
            }
            return "G.Ö. " + -this.termStart;
        }
        return "M.S. " + this.termStart;
    }

    public String getTermEndTitle() {
        if (this.termEnd == null) {
            return null;
        }
        if (this.termEnd < 0) {
            if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                return "M.Ö. " + -this.termEnd;
            }
            return "G.Ö. " + -this.termEnd;
        }
        return "M.S. " + this.termEnd;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
