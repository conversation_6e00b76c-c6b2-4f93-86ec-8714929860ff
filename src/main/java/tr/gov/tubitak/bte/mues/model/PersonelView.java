package tr.gov.tubitak.bte.mues.model;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "PERSONEL")
@NamedQuery(name = "PersonelView.findEagerById", query = "SELECT p FROM PersonelView p "
                                                         + "LEFT JOIN FETCH p.meslek "
                                                         + "LEFT JOIN FETCH p.unvan "
                                                         + "LEFT JOIN FETCH p.kadroDurum "
                                                         + "LEFT JOIN FETCH p.personelUzmanlikAlani pu "
                                                         + "LEFT JOIN FETCH pu.uzmanlik<PERSON>lani "
                                                         + "LEFT JOIN FETCH p.personelYabanciDil py "
                                                         + "LEFT JOIN FETCH py.dil "
                                                         + "LEFT JOIN FETCH py.languageLevel WHERE p.id = :id")
@NamedQuery(name = "PersonelView.findByIds", query = "SELECT p FROM PersonelView p WHERE p.aktif = true AND p.silinmis = false AND p.id IN :ids ORDER BY p.ad, p.soyad")
@NamedQuery(name = "PersonelView.findAllPersonnel", query = "SELECT p FROM PersonelView p LEFT JOIN FETCH p.unvan ORDER BY p.ad, p.soyad")
@NamedQuery(name = "PersonelView.findByFullNameAndAciklamaAndMuseumDirectorates", query = "SELECT p FROM PersonelView p LEFT JOIN FETCH p.unvan WHERE p.aktif = true AND p.silinmis = false "
                                                                                          + "AND p.inspector = false "
                                                                                          + " AND p.mudurluk IN :muzes AND (REPLACE(concat(p.ad, p.soyad), ' ', '')"
                                                                                          + " LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad, p.soyad")
@NamedQuery(name = "PersonelView.findByNameAndMudurlukAndAppType", query = "SELECT p FROM PersonelView p LEFT JOIN FETCH p.unvan WHERE p.aktif = true AND p.silinmis = false"
                                                                           + " AND p.mudurluk IN :muzes AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str)"
                                                                           + " AND p.applicationType = :appType AND p.inspector = false "
                                                                           + " ORDER BY p.ad, p.soyad")
@NamedQuery(name = "PersonelView.findPersonelViewByAppType", query = "SELECT p FROM PersonelView p LEFT JOIN FETCH p.unvan WHERE p.aktif = true AND p.silinmis = false"
                                                                     + " AND p.applicationType = :appType AND p.inspector = false "
                                                                     + " ORDER BY p.ad, p.soyad")
public class PersonelView extends PersonelSuper {

    private static final long serialVersionUID = -2431886886124326713L;

    @Column(name = "applicationType")
    private Integer           applicationType;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @Column(name = "mudurlukId")
    private Integer           mudurluk;

    public PersonelView() {
        // default Constructor
    }

    public PersonelView(final PersonelSuper p) {
        this.setId(p.getId());
        this.setAd(p.getAd());
        this.setSoyad(p.getSoyad());
    }

    // getter setters
    public Integer getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Integer mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Integer getApplicationType() {
        return this.applicationType;
    }

    public void setApplicationType(final Integer applicationType) {
        this.applicationType = applicationType;
    }

    @Override
    public String getTitle() {
        return Stream.of(this.getAd(), this.getSoyad(), MuesUtil.surroundWithParanthesis(this.getSicilNo())).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }
}
