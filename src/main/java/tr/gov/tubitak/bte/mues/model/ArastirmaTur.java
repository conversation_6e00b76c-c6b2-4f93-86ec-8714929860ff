package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "ARASTIRMA_TUR")
@NamedQuery(name = "ArastirmaTur.findEagerById", query = "SELECT a FROM ArastirmaTur a WHERE a.id = :id")
@NamedQuery(name = "ArastirmaTur.findAll", query = "SELECT a FROM ArastirmaTur a ORDER BY a.silinmis, a.aktif DESC, a.ad")
@NamedQuery(name = "ArastirmaTur.findActive", query = "SELECT a FROM ArastirmaTur a WHERE a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedNativeQuery(name = "ArastirmaTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ARASTIRMA WHERE SILINMIS = 0 AND ARASTIRMA_TUR_ID = :id)")
public class ArastirmaTur extends AbstractEntity implements DeleteValidatable {

    private static final long     serialVersionUID = 4253013404043877960L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                aciklama;

    @OneToMany(mappedBy = "arastirmaTur")
    private Collection<Arastirma> arastirmaCollection;

    public ArastirmaTur() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<Arastirma> getArastirmaCollection() {
        return this.arastirmaCollection;
    }

    public void setArastirmaCollection(final Collection<Arastirma> arastirmaCollection) {
        this.arastirmaCollection = arastirmaCollection;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
