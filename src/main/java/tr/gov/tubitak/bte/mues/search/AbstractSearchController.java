package tr.gov.tubitak.bte.mues.search;

import java.io.IOException;
import java.io.Serializable;
import java.text.Collator;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import javax.enterprise.context.Dependent;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ValueChangeEvent;
import javax.inject.Inject;

import org.apache.shiro.SecurityUtils;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.Http2SolrClient;
import org.apache.solr.client.solrj.response.PivotField;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.util.NamedList;
import org.omnifaces.util.Utils;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.SelectEvent;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.event.data.PageEvent;
import org.primefaces.model.LazyDataModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.SessionBean;
import tr.gov.tubitak.bte.mues.model.Identifiable;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazySolrDataModel;
import tr.gov.tubitak.bte.mues.search.controller.ImageSearchUtil;
import tr.gov.tubitak.bte.mues.session.SolrLastIndexTimesFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.SearchUtil;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

/**
 *
 *
 */
@Dependent
public abstract class AbstractSearchController<T extends Identifiable<?>> implements Serializable {

    private static final long                  serialVersionUID      = -568096064186647607L;

    private static final Logger                logger                = LoggerFactory.getLogger(AbstractSearchController.class);

    private static final String                AKTIF                 = "Aktif";

    private static final String                SILINMISCONST         = "Silinmis";

    private static final String                ACIKLAMA              = "Aciklama";

    private Integer                            monthRangeValue       = 1;

    @Inject
    protected SolrSearcher                     solrSearcher;

    @Inject
    protected transient SearchUtil             searchUtil;

    @Inject
    private SessionBean                        sessionBean;

    @Inject
    private ImageSearchUtil                    imageSearchUtil;

    @Inject
    protected AbstractSolrSearcherIndexingUtil searcherIndexingUtil;

    @Inject
    protected SolrLastIndexTimesFacade         solrLastIndexTimesFacade;

    private final transient List<ICriterion>   criteriaList          = new ArrayList<>();

    private Metadata                           selectedMetadata;

    private List<Integer>                      selectedCriteriaList  = new ArrayList<>();

    private ComparisonOperatorEnum             searchCondition       = ComparisonOperatorEnum.NOP;

    private transient CriterionModel           criterionModel        = new CriterionModel();

    private boolean                            freeInput             = true;

    private boolean                            exactMatch            = false;

    private boolean                            defaultFilterForAktifField;

    private boolean                            searchResultsVisible;

    private int                                activeIndex;

    private String                             globalSearchText;

    protected LogicalOperatorEnum              logicalOperator       = LogicalOperatorEnum.AND;

    private LogicalOperatorEnum                globalLogicalOperator = LogicalOperatorEnum.AND;

    private final Class<T>                     modelClass;

    private String                             modelName;

    private boolean                            silinmis;

    private boolean                            eserFotoGoster;

    private boolean                            eserKarsilastirmaGoster;

    private List<Metadata>                     selectedList;

    protected transient List<T>                selectionList;

    protected LazyDataModel<T>                 lazyDataModel;

    // used for child searches.
    private LazyDataModel<T>                   childSearchLazyDataModel;

    private boolean                            childSearchResultsVisible;

    private Boolean                            renderSayi            = false;

    private final SolrEnum                     core;
    // guncelleme sirasinda eskisinin kaldirilmasi amaci ile kullanilmistir.
    private AbstractCriterion                  criterion;

    public void init() {

        this.initSolr();
    }

    public void selectedOptionsChanged() {

        if ((this.getSelectedCriteriaList() != null) && !this.getSelectedCriteriaList().isEmpty()) {
            final Integer integer = this.getSelectedCriteriaList().get(this.getSelectedCriteriaList().size() - 1);
            this.criterion = (AbstractCriterion) this.criteriaList.get(integer);
            this.selectedMetadata = this.criterion.getMetadata();
            this.searchCondition = this.criterion.getComparisonOperatorEnum();

            if (this.criterion instanceof BooleanCriterion) {
                this.criterionModel.setBooleanValue(((BooleanCriterion) this.criterion).getBooleanValue());

            } else if ((this.criterion instanceof DateCriterion)) {
                this.criterionModel.setDateValue1(((DateCriterion) this.criterion).getDateValue1());
                this.criterionModel.setDateValue2(((DateCriterion) this.criterion).getDateValue2());

            } else {
                this.criterionModel.setTextValue1(((AbstractSimpleCriterion) this.criterion).getFirstValue());
                this.criterionModel.setTextValue2(((AbstractSimpleCriterion) this.criterion).getSecondValue());

            }

        }
    }

    // set core and fetch metadatas
    private void initSolr() {
        this.solrSearcher.setUrlString(this.searchUtil.getSolrServerUrl(this.core));
        this.solrSearcher.setServer(new Http2SolrClient.Builder(this.solrSearcher.getUrlString()).build());

        final List<Metadata> allMetadatas = this.solrSearcher.getMetadataFacade().findMetadataByType(this.core);

        this.solrSearcher.setMetaDataMap(new LinkedHashMap<>());

        for (final Metadata metaData : allMetadatas) {
            this.solrSearcher.getMetaDataMap().put(metaData.getName(), metaData);
        }

        this.solrSearcher.setRtmmList(allMetadatas);
    }

    protected AbstractSearchController(final Class<T> modelClass, final SolrEnum core) {
        this.modelClass = modelClass;
        this.core = core;
    }

    protected AbstractSearchController(final Class<T> modelClass, final String modelNameL, final SolrEnum core) {
        this.modelClass = modelClass;
        this.modelName = modelNameL;
        this.core = core;
    }

    public Class<T> getModelClass() {
        return this.modelClass;
    }

    public abstract ICriterion addQuerySearchPermision(final ICriterion criterion);

    protected ICriterion addQuerySearchPermision(final ICriterion criterion, final String permission, final ApplicationType applicationType, final String permissionFieldName) {

        final List<String> mudurlukList = this.getSessionBean().fetchMudurlukByApplicationTypeAndPermission(applicationType, permission).stream().map(Mudurluk::getAd).collect(Collectors.toList());

        if ((mudurlukList == null) || mudurlukList.isEmpty()) {
            return criterion;
        }

        final Metadata metadata = this.getSolrSearcher().getMetaDataMap().get(permissionFieldName);
        final CompoundCriterion compoundCriterion0 = new CompoundCriterion();
        compoundCriterion0.setChildren(new ArrayList<>());
        compoundCriterion0.setLogicalOperator(LogicalOperatorEnum.OR);

        for (final String muzeAd : mudurlukList) {
            final CriterionModel muzeMudurlugu = new CriterionModel(metadata, muzeAd);
            muzeMudurlugu.setCondition(ComparisonOperatorEnum.CONTAINS);
            final ICriterion criterion1 = CriterionFactory.createCriterion(muzeMudurlugu);
            compoundCriterion0.getChildren().add(criterion1);
        }

        final CompoundCriterion compoundCriterion1 = new CompoundCriterion();
        compoundCriterion1.setChildren(new ArrayList<>());
        compoundCriterion1.setLogicalOperator(LogicalOperatorEnum.AND);
        compoundCriterion1.getChildren().add(compoundCriterion0);
        compoundCriterion1.getChildren().add(criterion);
        return compoundCriterion1;
    }

    public void resetSearchFields() {
        this.imageSearchUtil.removePhoto();
        this.imageSearchUtil.getModel().setFotografPath(null);
        this.setSearchResultsVisible(false);
        this.getCriterionModel().setTextValue1(null);
        this.getSelectionList().clear();
        this.resetTable();
    }

    public void resetTable() {
        final DataTable dataTable = (DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(":sorgulamaFormListing:searchResultsDataTable");
        if (dataTable != null) {
            dataTable.reset();
        }
    }

    public List<CriterionModel> autocompleteForMetadata(final String query,
                                                        final String metadataName,
                                                        final ComparisonOperatorEnum comparisonOperatorEnum,
                                                        final Integer criterionCode,
                                                        final String filter)
            throws IOException,
            SolrServerException {

        final Metadata metadata = this.solrSearcher.getMetaDataMap().get(metadataName);

        final CriterionModel tempCriterionModel = new CriterionModel();

        tempCriterionModel.setType(criterionCode);
        tempCriterionModel.setCondition(comparisonOperatorEnum);
        tempCriterionModel.setTextValue1(query);
        tempCriterionModel.setMetadata(metadata);

        return this.autocompleteForMetadata(metadata, tempCriterionModel, filter);

    }

    public List<CriterionModel> autocompleteForMetadata(final Metadata metadata, final CriterionModel criterionModel, final String filter) throws SolrServerException, IOException {

        final ICriterion criterion1 = CriterionFactory.createCriterion(criterionModel);

        final String queryString = criterion1.getSql();

        final SolrQuery query = new SolrQuery((queryString != null) && (queryString.length() > 0) ? queryString : "*:*");
        query.setFilterQueries(filter);

        query.set("fl", "id");
        query.setFacet(true);

        // Converting List to Array of strings
        final String facetPivotFields = metadata.getName();

        query.addFacetPivotField(facetPivotFields);
        this.getSolrSearcher().setSolrQuery(query);

        final QueryResponse solrResponse = this.getSolrSearcher().makeSearch();

        final NamedList<List<PivotField>> facetPivot = solrResponse.getFacetPivot();
        final List<PivotField> list = facetPivot.get(facetPivotFields);

        final List<CriterionModel> criterionModels = new ArrayList<>();

        for (final PivotField pivotField : list) {
            final CriterionModel criterionModel2 = new CriterionModel();
            criterionModel2.setTextValue1(pivotField.getValue().toString());

            criterionModel2.setTextValue2(pivotField.getCount() + " ");

            criterionModel2.setMetadata(metadata);
            final List<ICriterion> arrayList = new ArrayList<>();
            arrayList.add(criterion1);
            criterionModel2.setCriteriaList(arrayList);

            // for those with multiple value
            if ((metadata.getDataTypeId() != 5) || pivotField.getValue().toString().toLowerCase().contains(criterionModel.getTextValue1().toLowerCase())) {
                criterionModels.add(criterionModel2);
            }
        }

        final Collator collator = Collator.getInstance(Locale.forLanguageTag("tr_TR"));
        criterionModels.sort((final CriterionModel h1, final CriterionModel h2) -> collator.compare(h1.getTextValue1().toUpperCase(), h2.getTextValue1().toUpperCase()));

        return criterionModels;

    }

    // ------------------------------------------------------------------------------------------------------------------------------
    // Image similarity methods
    // ------------------------------------------------------------------------------------------------------------------------------
    public void searchBoostedUIDs(final List<String> ids) {
        this.resetTable();
        final String uidsStr = this.imageSearchUtil.addBoostForSolr(ids);
        final String sql = "uid : (" + uidsStr + ")";
        if (logger.isDebugEnabled()) {
            logger.debug("Similarity Search Query : {}", sql);
        }
        final SolrQuery solrQuery = new SolrQuery(sql);
        solrQuery.setParam("q.op", LogicalOperatorEnum.OR.toString());
        this.solrSearcher.setSolrQuery(solrQuery);

        this.setLazyDataModel(new LazySolrDataModel<>(this.getSolrSearcher(), this.modelClass, this, ids));
        this.setSearchResultsVisible(true);
    }

    // ------------------------------------------------------------------------------------------------------------------------------
    // Global search methods
    // ------------------------------------------------------------------------------------------------------------------------------
    public void globalSearch() {
        this.setSearchResultsVisible(true);
        this.resetTable();

        String sql = this.composeGlobalSearchCriterions().getSql();

        if (logger.isDebugEnabled()) {
            logger.debug("Arama ayarları : {} ", sql);
        }

        final SolrQuery solrQuery = new SolrQuery(sql);
        solrQuery.setParam("q.op", this.logicalOperator.name());
        this.solrSearcher.setSolrQuery(solrQuery);
        this.setLazyDataModel(new LazySolrDataModel<>(this.getSolrSearcher(), this.modelClass, this));
        this.setActiveIndex(2);
    }

    public String redirectToEditPage() {
        return "/eser/eser-sorgulama?faces-redirect=true&global=" + Utils.encodeURI(this.getGlobalSearchText());
    }

    public ICriterion composeGlobalSearchCriterions() {

        final Metadata metadata = new Metadata();

        metadata.setDataTypeId(CriterionEnum.FREETEXT.getCode());

        final CriterionModel gloabalSearchCriterionModel = new CriterionModel(metadata, Optional.ofNullable(this.getGlobalSearchText()).orElse("").trim());

        if (this.exactMatch) {
            metadata.setName("globalSearchFieldExactMatch");
            gloabalSearchCriterionModel.setCondition(ComparisonOperatorEnum.EQUALS);
        } else {
            metadata.setName("globalSearchField");
            gloabalSearchCriterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);
        }

        final ICriterion criterion2 = CriterionFactory.createCriterion(gloabalSearchCriterionModel);
        return this.addQuerySearchPermision(criterion2);

    }

    public void resetGlobalSearchFields() {
        logger.debug("[resetSearchFields] : ");
        this.globalSearchText = null;
        this.resetSearchFields();
    }

    public String getGlobalSearchText() {
        return this.globalSearchText;
    }

    public void setGlobalSearchText(final String globalSearchText) {
        this.globalSearchText = globalSearchText;
    }

    public void globalValueChanged(final ValueChangeEvent valueChangeEvent) {
        this.globalSearchText = (String) valueChangeEvent.getNewValue();
        this.globalSearch();
    }

    // ------------------------------------------------------------------------------------------------------------------------------
    // Dynamic search methods
    // ------------------------------------------------------------------------------------------------------------------------------
    public void makeDetailedSearch() {

        logger.debug("{} ", "makeDetailedSearch");

        final SolrQuery solrQuery;

        if (this.getCriteriaList().isEmpty()) {
            solrQuery = new SolrQuery(SearchConstants.ALL_QUERY);
        } else {

            solrQuery = this.composeDetailedSearchQuery();
        }

        this.solrSearcher.setSolrQuery(solrQuery);
        logger.debug("[makeDetailedSearch] : Solr sorgusu {}", solrQuery);

        // for image similarity ranking
        if (this.getImageSearchUtil().getModel().getFotografPath() != null) {
            this.searchImageBasedSimilarityWithSolrQuery();
        } else {
            this.setLazyDataModel(new LazySolrDataModel<>(this.getSolrSearcher(), this.modelClass, this));
        }

        this.setSearchResultsVisible(true);
    }

    public void searchImageBasedSimilarity() {
        try {
            final StringBuilder uidStrBuilder = new StringBuilder();
            List<String> uids;
            // bu metot sadece görsel arama yapar diğer filtrelere bakmaz ve eğer süperuser sorgulama yapmıyor ise yetkiye göre id çekilir ama
            // superuser ise idsiz search işlemi yapılır
            if (SecurityUtils.getSubject().hasRole("SUPERUSER")) {
                uids = this.getImageSearchUtil().searchEserIdsByImageSimilarity(null, this.getCore().getCode());
            } else {
                final SolrQuery solrQuery = new SolrQuery(SearchConstants.ALL_QUERY);
                solrQuery.setRows(10000000);
                this.getSolrSearcher().setSolrQuery(solrQuery);
                this.getSolrSearcher().makeSearch().getResults().stream().forEach(x -> uidStrBuilder.append(x.getFirstValue("uid").toString() + ","));
                uids = this.getImageSearchUtil().searchEserIdsByImageSimilarity(uidStrBuilder.toString(), this.getCore().getCode());
            }
            this.resetTable();
            if (!uids.isEmpty()) {
                this.searchBoostedUIDs(uids);
                this.setEserFotoGoster(true);
            }
        } catch (SolrServerException | IOException e) {
            logger.error("[searchImageBasedSimilarity] : ", e);
        }
    }

    public void searchImageBasedSimilarityWithSolrQuery() {
        try {
            if ((this.getSolrSearcher().getQuery() == null) || this.getSolrSearcher().getQuery().toString().equals("q=*:*")) {
                this.searchImageBasedSimilarity();
            } else {
                final StringBuilder uidStrBuilder = new StringBuilder();
                final SolrQuery solrQuery = this.getSolrSearcher().getQuery();
                solrQuery.setRows(10000000);
                this.getSolrSearcher().setSolrQuery(solrQuery);
                this.getSolrSearcher().makeSearch().getResults().stream().forEach(x -> uidStrBuilder.append(x.getFirstValue("uid").toString() + ","));
                final List<String> uids = this.getImageSearchUtil().searchEserIdsByImageSimilarity(uidStrBuilder.toString(), this.getCore().getCode());
                this.resetTable();
                if (!uids.isEmpty()) {
                    this.searchBoostedUIDs(uids);
                    this.setEserFotoGoster(true);
                }
            }
        } catch (SolrServerException | IOException e) {
            logger.error("[searchImageBasedSimilarity] : ", e);
        }

    }

    protected List<Integer> composeSearchQuery(final Predicate<ICriterion> predicate) {
        final List<Integer> lst = new ArrayList<>();

        if (!this.getSelectedCriteriaList().isEmpty()) {
            for (int i = 0; i < this.getSelectedCriteriaList().size(); i++) {
                final int index = this.getSelectedCriteriaList().get(i);
                final ICriterion criterion1 = this.getCriteriaList().get(index);

                if (predicate.test(criterion1)) {
                    lst.add(index);
                }

                if (criterion1.getMetadata().getName().equals(this.getAktifFieldName())) {
                    this.setDefaultFilterForAktifField(false);
                }
            }
        } else {
            for (int i = 0; i < this.getCriteriaList().size(); i++) {
                final ICriterion criterion1 = this.getCriteriaList().get(i);

                if (predicate.test(criterion1)) {
                    lst.add(i);
                }

                if (criterion1.getMetadata().getName().equals(this.getAktifFieldName())) {
                    this.setDefaultFilterForAktifField(false);
                }
            }
        }

        return lst;
    }

    protected SolrQuery composeChildQueryDetailedSearchQuery() {
        final List<Integer> lst = this.composeSearchQuery(criterionp -> criterionp.getMetadata().isChildField());
        final ICriterion cc = this.makeLogicalOperation(lst, this.logicalOperator);
        return this.composeSolrQuery(cc);
    }

    protected SolrQuery composeDetailedSearchQuery() {
        final List<Integer> lst = this.composeSearchQuery(criterionp -> !criterionp.getMetadata().isChildField());
        final ICriterion cc = this.makeLogicalOperation(lst, this.logicalOperator);
        return this.composeSolrQuery(cc);
    }

    public void deleteCriterion() {
        if (!this.selectedCriteriaList.isEmpty()) {
            final List<ICriterion> itemsToBeDeleted = new ArrayList<>();
            for (final Integer index : this.selectedCriteriaList) {
                itemsToBeDeleted.add(this.getCriteriaList().get(index));
            }
            this.getCriteriaList().removeAll(itemsToBeDeleted);
            for (int i = 0; i < this.getCriteriaList().size(); i++) {
                this.getCriteriaList().get(i).setRowId(i);
            }
            this.selectedMetadata = null;
            this.searchCondition = null;
        }
    }

    public void setDefaultFilterForAktifField(final boolean b) {
        this.defaultFilterForAktifField = b;
    }

    public SolrQuery composeSolrQuery(final ICriterion criterion) {

        final String queryText = criterion.getSql();

        return new SolrQuery(queryText);
    }

    /**
     * Detaylı arama ekranındaki arama alanlarını temizler.
     */

    public void resetDetailedSearchFields() {
        logger.debug("[resetDetailedSearchFields] : ");
        this.getCriteriaList().clear();
        this.setSearchCondition(ComparisonOperatorEnum.NOP);
        this.logicalOperator = LogicalOperatorEnum.AND;

        this.selectedMetadata = null;

        this.resetSearchFields();
    }

    public void updateSelectedRows(final PageEvent event) {
        // intentially left blank
    }

    /**
     * Operatörleri döndürür.
     *
     * @return operatörler
     */
    public List<ComparisonOperatorEnum> getSearchConditions() {
        return CriterionEnum.parse(this.selectedMetadata.getDataTypeId()).getConditions();
    }

    public void handleMetadataSelectDetailedSearch(final SelectEvent<Metadata> event) {

        this.setSelectedMetadata(event.getObject());
    }

    public void handleAutoCompleteSelect(final SelectEvent<String> event) {

        final String selected = event.getObject();
        this.criterionModel.setTextValue1(selected);
    }

    public List<Metadata> fetchMetadataSuggestionList(final String query) {
        final List<Metadata> suggestions = new ArrayList<>();

        for (final String each : this.solrSearcher.getMetaDataMap().keySet()) {
            final Metadata md = this.solrSearcher.getMetaDataMap().get(each);

            if (md.getIndexFaceName().toLowerCase(MuesUtil.LOCALE_TR).contains(query.toLowerCase(MuesUtil.LOCALE_TR))) {
                suggestions.add(md);
            }
        }

        return suggestions;
    }

    public void addCriterion() {

        if (this.selectedMetadata == null) {
            MuesUtil.showMessage(this.searchUtil.getNoMetadataSelectedText(), FacesMessage.SEVERITY_WARN);
            return;
        }
        if (this.searchCondition == ComparisonOperatorEnum.NOP) {
            MuesUtil.showMessage(this.searchUtil.getNoOperatorSelectedText(), FacesMessage.SEVERITY_WARN);
            return;
        }

        this.criteriaList.remove(this.criterion);

        this.criterionModel.setType(this.selectedMetadata.getDataTypeId());
        this.criterionModel.setMetadata(this.selectedMetadata);
        this.criterionModel.setCondition(this.searchCondition);
        this.criterionModel.setRowId(this.getCriteriaList().size());
        // null checkneeded

        final ICriterion criterion1 = CriterionFactory.createCriterion(this.criterionModel);

        if (!criterion1.isValid()) {
            MuesUtil.showMessage(this.searchUtil.getEnterValidValuesText(), FacesMessage.SEVERITY_WARN);
        } else if (this.getCriteriaList().contains(criterion1)) {
            MuesUtil.showMessage(this.searchUtil.getCriterionAlreadyExistsText(), FacesMessage.SEVERITY_WARN);
        } else {
            this.getCriteriaList().add(criterion1);
            this.selectedCriteriaList.clear();
            this.resetCriterionFields();
            this.criterion = null;
        }
    }

    public List<String> autoCompleteTextArea(final String query) throws IOException, SolrServerException {
        String filter = SearchConstants.ALL_QUERY + SearchConstants.SPACE_LITERAL;

        if (this.defaultFilterForAktifField) {
            filter = this.composeAktifFieldCriterion();
        }

        final Metadata metadata = this.solrSearcher.getMetaDataMap().get(this.selectedMetadata.getName());

        final CriterionModel tempCriterionModel = new CriterionModel();

        tempCriterionModel.setType(metadata.getDataTypeId());
        tempCriterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);
        tempCriterionModel.setTextValue1(query);
        tempCriterionModel.setTextValue2(query);
        tempCriterionModel.setMetadata(metadata);

        return this.autocompleteForMetadata(this.selectedMetadata, tempCriterionModel, filter).stream().map(CriterionModel::getTextValue1).collect(Collectors.toList());
    }

    public List<String> autoCompleteFields(final String query) throws IOException, SolrServerException {
        String filter = SearchConstants.ALL_QUERY + SearchConstants.SPACE_LITERAL;

        if (this.defaultFilterForAktifField) {
            filter = this.composeAktifFieldCriterion();
        }

        final Metadata metadata = this.solrSearcher.getMetaDataMap().get(this.selectedMetadata.getName());

        final CriterionModel tempCriterionModel = new CriterionModel();

        tempCriterionModel.setType(metadata.getDataTypeId());
        tempCriterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);
        tempCriterionModel.setTextValue1(query);
        tempCriterionModel.setMetadata(metadata);

        return this.autocompleteForMetadata(this.selectedMetadata, tempCriterionModel, filter)
                   .stream()
                   // MUES-5409 icin yapilmis bir yama. gecici cozum. Multifield dialoglarda facete calismasinda olusan hata.
                   .filter(x -> x.getTextValue1().toLowerCase(MuesUtil.LOCALE_TR).contains(query.toLowerCase(MuesUtil.LOCALE_TR)))
                   .map(CriterionModel::getTextValue1)
                   .collect(Collectors.toList());
    }

    public String composeAktifFieldCriterion() {

        final Metadata metadata = this.solrSearcher.getMetaDataMap().get(this.getAktifFieldName());
        final CriterionModel tempCriterionModel = new CriterionModel(CriterionEnum.BOOLEAN.getCode(), metadata, 0);
        tempCriterionModel.setType(metadata.getDataTypeId());
        tempCriterionModel.setBooleanValue(1);

        final ICriterion criterion1 = CriterionFactory.createCriterion(tempCriterionModel);

        return criterion1.getSql();
    }

    public void resetCriterionFields() {
        this.criterionModel = new CriterionModel();
        this.selectedMetadata = null;
        this.searchCondition = ComparisonOperatorEnum.NOP;
    }

    public ICriterion makeLogicalOperation(final List<Integer> indices, final LogicalOperatorEnum operator) {

        final List<ICriterion> newCriteriaList = new ArrayList<>();
        final CriterionModel tempCriterionModel = new CriterionModel();

        tempCriterionModel.setCriteriaList(newCriteriaList);
        tempCriterionModel.setType(CriterionEnum.COMPOUND.getCode());
        tempCriterionModel.setRowId(this.getCriteriaList().size());
        tempCriterionModel.setOperator(operator);

        for (int i = 0; i < indices.size(); i++) {
            newCriteriaList.add(this.getCriteriaList().get(indices.get(i)));
        }

        final ICriterion parentCriterion = CriterionFactory.createCriterion(tempCriterionModel);
        parentCriterion.setChildren(newCriteriaList);

        return parentCriterion;

    }

    public LazyDataModel<T> getLazyDataModel() {
        return this.lazyDataModel;
    }

    public void setLazyDataModel(final LazyDataModel<T> lazyDataModel) {
        this.lazyDataModel = lazyDataModel;
    }

    public List<T> getSelectionList() {
        if (this.selectionList == null) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;
    }

    // lazyloadda sayfalar arasinda secili nesneler tutulmadigi icin operate uzerinden ilerleniyor. Selection list uzerindeki degisiklikler de shift
    // key kullanilabildigi icin operate yansitmak icin bu islem yapilmistir.
    public void setSelectionList(final List<T> selectionList) {

        this.selectionList = selectionList;
    }

    public String getResultCountString() {
        return MessageFormat.format(this.getSearchUtil().getSearchResultText(), this.getSolrSearcher().getTotalCount(), this.getSearchDurationText());
    }

    private String getSearchDurationText() {
        return String.valueOf(this.getSolrSearcher().getDuration() / SearchConstants.SECOND_DIVIDER);
    }

    public void activeTabChanged(final TabChangeEvent<?> event) {
        this.setActiveIndex(((TabView) event.getComponent()).getActiveIndex());
    }

    public int getActiveIndex() {
        return this.activeIndex;
    }

    public void setActiveIndex(final int activeIndex) {
        this.activeIndex = activeIndex;
    }

    public boolean isSearchResultsVisible() {
        return this.searchResultsVisible;
    }

    public void setSearchResultsVisible(final boolean searchResultsVisible) {
        this.searchResultsVisible = searchResultsVisible;
    }

    public SolrSearcher getSolrSearcher() {
        return this.solrSearcher;
    }

    public void setSolrSearcher(final SolrSearcher solrSearcher) {
        this.solrSearcher = solrSearcher;
    }

    public SearchUtil getSearchUtil() {
        return this.searchUtil;
    }

    public void setSearchUtil(final SearchUtil searchUtil) {
        this.searchUtil = searchUtil;
    }

    public SessionBean getSessionBean() {
        return this.sessionBean;
    }

    public void setSessionBean(final SessionBean sessionBean) {
        this.sessionBean = sessionBean;
    }

    public ImageSearchUtil getImageSearchUtil() {
        return this.imageSearchUtil;
    }

    public Integer getMetadataValueType() {
        return this.selectedMetadata.getDataTypeId();
    }

    public Integer getLogicalOperatorValue() {
        return this.logicalOperator.getCode();
    }

    public void setLogicalOperatorValue(final Integer logicalOperator) {
        if (logicalOperator != null) {
            this.logicalOperator = LogicalOperatorEnum.parse(logicalOperator);
        }
    }

    public Integer getGlobalLogicalOperatorValue() {
        return this.globalLogicalOperator.getCode();
    }

    public void setGlobalLogicalOperatorValue(final Integer globalLogicalOperator) {
        if (globalLogicalOperator != null) {
            this.globalLogicalOperator = LogicalOperatorEnum.parse(globalLogicalOperator);
        }
    }

    public List<ICriterion> getCriteriaList() {
        return this.criteriaList;
    }

    public Metadata getSelectedMetadata() {
        return this.selectedMetadata;
    }

    public void setSelectedMetadata(final Metadata selectedMetadata) {
        this.selectedMetadata = selectedMetadata;
    }

    public ComparisonOperatorEnum getSearchCondition() {
        return this.searchCondition;
    }

    public void setSearchCondition(final ComparisonOperatorEnum searchCondition) {
        this.searchCondition = searchCondition;
    }

    public List<Integer> getSelectedCriteriaList() {
        return this.selectedCriteriaList;
    }

    public void setSelectedCriteriaList(final List<Integer> selectedCriteriaList) {
        this.selectedCriteriaList = selectedCriteriaList;
    }

    public CriterionModel getCriterionModel() {
        return this.criterionModel;
    }

    public void setCriterionModel(final CriterionModel criterionModel) {
        this.criterionModel = criterionModel;
    }

    public boolean getFreeInput() {
        return this.freeInput;
    }

    public void setFreeInput(final boolean freeInput) {
        this.freeInput = freeInput;
    }

    public boolean isExactMatch() {
        return this.exactMatch;
    }

    public void setExactMatch(final boolean exactMatch) {
        this.exactMatch = exactMatch;
    }

    public String getAktifFieldName() {
        return this.modelName + AKTIF;
    }

    public String getSilinmis() {
        return this.modelName + SILINMISCONST;
    }

    public String getAciklama() {
        return this.modelName + ACIKLAMA;
    }

    public LazyDataModel<T> getChildSearchLazyDataModel() {
        return this.childSearchLazyDataModel;
    }

    public void setChildSearchLazyDataModel(final LazyDataModel<T> childSearchLazyDataModel) {
        this.childSearchLazyDataModel = childSearchLazyDataModel;
    }

    public boolean isChildSearchResultsVisible() {
        return this.childSearchResultsVisible;
    }

    public void setChildSearchResultsVisible(final boolean childSearchResultsVisible) {
        this.childSearchResultsVisible = childSearchResultsVisible;
    }

    public boolean isSilinmis() {
        return this.silinmis;
    }

    public void setSilinmis(final boolean silinmis) {
        this.silinmis = silinmis;
    }

    public List<Metadata> getSelectedList() {
        if (this.selectedList == null) {
            this.selectedList = new LinkedList<>();
        }
        return this.selectedList;
    }

    public void setSelectedList(final List<Metadata> selectedList) {
        this.selectedList = selectedList;
    }

    public Integer getMonthRangeValue() {
        return this.monthRangeValue;
    }

    public void setMonthRangeValue(final Integer monthRangeValue) {
        this.monthRangeValue = monthRangeValue;
    }

    public SolrEnum getCore() {
        return this.core;
    }

    public boolean isEserKarsilastirmaGoster() {
        return this.eserKarsilastirmaGoster;
    }

    public void setEserKarsilastirmaGoster(final boolean eserKarsilastirmaGoster) {
        this.eserKarsilastirmaGoster = eserKarsilastirmaGoster;
    }

    public boolean isEserFotoGoster() {
        return this.eserFotoGoster;
    }

    public void setEserFotoGoster(final boolean eserFotoGoster) {
        this.eserFotoGoster = eserFotoGoster;
    }

    public Boolean getRenderSayi() {
        return this.renderSayi;
    }

    public void setRenderSayi(final Boolean renderSayi) {
        this.renderSayi = renderSayi;
    }
}
