package tr.gov.tubitak.bte.mues.util;

import org.hibernate.boot.Metadata;
import org.hibernate.boot.MetadataSources;
import org.hibernate.boot.registry.StandardServiceRegistry;
import org.hibernate.boot.registry.StandardServiceRegistryBuilder;
import org.hibernate.tool.hbm2ddl.SchemaExport;
import org.hibernate.tool.schema.TargetType;
import org.reflections.Reflections;

import javax.persistence.Table;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class HibernateSchemaExport {

    public static void main(String[] args) {

        Map<String, String> settings = new HashMap<>();
        settings.put("hibernate.connection.driver_class", "com.microsoft.sqlserver.jdbc.SQLServerDriver");
        settings.put("hibernate.connection.url", "***********************************************************************************************;");
        settings.put("hibernate.connection.username", "sa");
        settings.put("hibernate.connection.password", "Aa12345678");
        settings.put("hibernate.dialect", "org.hibernate.dialect.SQLServer2012Dialect");

        StandardServiceRegistry serviceRegistry = new StandardServiceRegistryBuilder()
                .applySettings(settings)
                .build();

        MetadataSources metadataSources = new MetadataSources(serviceRegistry);

        // Reflections ile tüm Entity classlarını bul ve ekle
        Reflections reflections = new Reflections("tr.gov.tubitak");

        Set<Class<?>> entityClasses = reflections.getTypesAnnotatedWith(Table.class);

        for (Class<?> entityClass : entityClasses) {
            System.out.println("Entity bulundu: " + entityClass.getName());
            metadataSources.addAnnotatedClass(entityClass);
        }

        Metadata metadata = metadataSources.buildMetadata();

        SchemaExport schemaExport = new SchemaExport();
        schemaExport.setOutputFile("schema.sql");
        schemaExport.setDelimiter(";");
        schemaExport.setFormat(true);

        // Hem dosyaya hem console'a yazdırıyoruz
        schemaExport.createOnly(
                EnumSet.of(TargetType.SCRIPT, TargetType.STDOUT),
                metadata
        );

        StandardServiceRegistryBuilder.destroy(serviceRegistry);
    }
}
