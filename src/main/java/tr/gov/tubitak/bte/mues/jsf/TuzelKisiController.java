package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.faces.context.FacesContext;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.TuzelKisi;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyTuzelKisiDataModel;
import tr.gov.tubitak.bte.mues.session.TuzelKisiFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

@Named
@ViewScoped
public class TuzelKisiController extends AbstractController<TuzelKisi> {

    private static final long    serialVersionUID = -9115980376759227564L;

    @Inject
    private TuzelKisiFacade      facade;

    @Inject
    private IlceController       ilceController;

    private DataModel<TuzelKisi> lazyTuzelKisiDataModel;

    private List<TuzelKisi>      selectionList;

    public TuzelKisiController() {
        super(TuzelKisi.class);
    }

    @Override
    public DBOperationResult create() {
        return super.create();
    }

    public void showDetail(final Integer id) {
        this.setModel(this.getFacade().findEagerById(id));
        this.setNewMode(false);
    }

    @Override
    public TuzelKisiFacade getFacade() {
        return this.facade;
    }

    @Override
    public List<TuzelKisi> filterByName(final String value) {
        return this.getFacade().filterByName("ticariUnvan", value);
    }

    public void onIlSelected(final SelectEvent<Il> event) {
        this.ilceController.setIl(event.getObject());
        this.getModel().setIlce(null);
    }

    public void loadDataTable() {
        this.resetTable();
        this.setLazyTuzelKisiDataModel(new LazyTuzelKisiDataModel(TuzelKisi.class, () -> this.getFacade().getEM(), this));
    }

    public void resetTable() {
        this.lazyTuzelKisiDataModel = null;
        ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("tuzelKisiDialog:multiTuzelKisiForm:tuzelKisiTableId")).reset();
    }

    public List<TuzelKisi> getSelectionList() {
        if ((this.selectionList == null)) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;
    }

    public void setSelectionList(final List<TuzelKisi> selectionList) {
        this.selectionList = selectionList;
    }

    public DataModel<TuzelKisi> getLazyTuzelKisiDataModel() {
        return this.lazyTuzelKisiDataModel;
    }

    public void setLazyTuzelKisiDataModel(final DataModel<TuzelKisi> lazyTuzelKisiDataModel) {
        this.lazyTuzelKisiDataModel = lazyTuzelKisiDataModel;
    }

}
