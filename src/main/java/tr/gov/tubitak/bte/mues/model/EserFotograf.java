package tr.gov.tubitak.bte.mues.model;

import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ESER_FOTOGRAF")
@NamedQuery(name = "EserFotograf.findEagerById", query = "SELECT e FROM EserFotograf e LEFT JOIN FETCH e.ilgiliYuz LEFT JOIN FETCH e.annotations WHERE e.id = :id")
public class EserFotograf extends EserFotografSuper {

    private static final long            serialVersionUID = -1257253912454192664L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ILGILI_YUZ", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MuesPick                         ilgiliYuz;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser                         eser;

    @Column(name = "ANNOTATED")
    private Boolean                      annotated;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(mappedBy = "eserFotograf", fetch = FetchType.LAZY, cascade = { CascadeType.MERGE, CascadeType.PERSIST }, orphanRemoval = true)
    @OrderBy("ID")
    private Set<ArtifactPhotoAnnotation> annotations;

    public EserFotograf() {
        this.setAnaFotograf(false);
    }

    // getters and setters ....................................................
    public MuesPick getIlgiliYuz() {
        return this.ilgiliYuz;
    }

    public void setIlgiliYuz(final MuesPick ilgiliYuz) {
        this.ilgiliYuz = ilgiliYuz;
    }

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    public Boolean getAnnotated() {
        return this.annotated;
    }

    public void setAnnotated(final Boolean annotated) {
        this.annotated = annotated;
    }

    public Set<ArtifactPhotoAnnotation> getAnnotations() {
        return this.annotations;
    }

    public void setAnnotations(final Set<ArtifactPhotoAnnotation> annotations) {
        this.annotations = annotations;
    }

}
