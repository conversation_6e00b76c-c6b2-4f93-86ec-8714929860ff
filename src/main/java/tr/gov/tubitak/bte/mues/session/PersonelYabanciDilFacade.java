package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.transaction.Transactional;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.PersonelYabanciDil;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
@Transactional
public class PersonelYabanciDilFacade extends AbstractFacade<PersonelYabanciDil> {

    public PersonelYabanciDilFacade() {
        super(PersonelYabanciDil.class);
    }

    public List<PersonelYabanciDil> filterByName(final String query) {
        return this.em.createNamedQuery("PersonelYabanciDil.findByName", PersonelYabanciDil.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<PersonelYabanciDil> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("PersonelYabanciDil.findByNameAndAciklama", PersonelYabanciDil.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<Dil> filterByFullNameAndAciklamaPreventDuplicate(final String query, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("Dil.filterByFullNameAndAciklamaPreventDuplicate", Dil.class).setParameter("str", "%" + query + "%").setParameter("ids", excludedIds).getResultList();
    }
}
