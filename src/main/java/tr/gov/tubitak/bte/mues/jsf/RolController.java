package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.session.RolFacade;
import tr.gov.tubitak.bte.mues.util.CurrentUser;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class RolController extends AbstractController<Rol> {

    private static final long serialVersionUID = -2432066315763665173L;

    @Inject
    @CurrentUser
    private Kullanici         kullanici;

    @Inject
    private RolFacade         facade;

    public RolController() {
        super(Rol.class);
    }

    @Override
    public void delete() {
        if (this.getModel().isSistem()) {
            MuesUtil.showMessage("Sistem rolleri silinemez!", FacesMessage.SEVERITY_ERROR);
        } else {
            super.delete();
        }
    }

    @Override
    public RolFacade getFacade() {
        return this.facade;
    }

    public List<Rol> filterRoleByNameAndRank(final String query) {
        return this.getFacade().filterRoleByNameAndRank(query, this.kullanici.getId());
    }

}
