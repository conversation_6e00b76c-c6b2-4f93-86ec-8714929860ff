package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Entity
@Table(name = "ESER_TUR")
@NamedQuery(name = "EserTur.findEagerById", query = "SELECT e FROM EserTur e WHERE e.id = :id")
@NamedQuery(name = "EserTur.findAll", query = "SELECT e FROM EserTur e ORDER BY e.silinmis, e.aktif DESC, e.ad")
@NamedQuery(name = "EserTur.findActive", query = "SELECT e FROM EserTur e WHERE e.aktif = true AND e.silinmis = false ORDER BY e.ad")
@NamedNativeQuery(name = "EserTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_ALT_TUR WHERE SILINMIS = 0 AND ESER_TUR_ID = :id)")
public class EserTur extends AbstractEntity implements DeleteValidatable {

    private static final long      serialVersionUID = -7142635080903512466L;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                 ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                 deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                 aciklama;

    @OneToMany(mappedBy = "eserTur")
    private Collection<EserAltTur> eserAltTurCollection;

    public EserTur() {
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<EserAltTur> getEserAltTurCollection() {
        return this.eserAltTurCollection;
    }

    public void setEserAltTurCollection(final Collection<EserAltTur> eserAltTurCollection) {
        this.eserAltTurCollection = eserAltTurCollection;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.ad).orElse("Eser Türü");
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
