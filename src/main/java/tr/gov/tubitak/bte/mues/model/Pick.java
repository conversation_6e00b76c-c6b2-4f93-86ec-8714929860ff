package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "Pick", uniqueConstraints = { @UniqueConstraint(columnNames = { "grup", "rank" }) })
public class Pick extends PickSuper {

    private static final long serialVersionUID = 2920113423714474511L;

    @JoinColumn(name = "grup", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PickGroup         grup;

    public Pick() {
        // blank constructor
    }

    public PickGroup getGrup() {
        return grup;
    }

    public void setGrup(PickGroup grup) {
        this.grup = grup;
    }

}
