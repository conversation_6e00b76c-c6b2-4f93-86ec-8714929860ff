package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.AlanKonumu;
import tr.gov.tubitak.bte.mues.model.AlanKonumuTur;
import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.session.AlanKonumuFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class AlanKonumuController extends AbstractController<AlanKonumu> implements SingleFileUploadable {

    private static final long serialVersionUID = 7980007778131931021L;

    @Inject
    private AlanKonumuFacade  alanKonumuFacade;

    @Inject
    private FileUploadHelper  fileUploadHelper;

    @Inject
    private SessionBean       sessionBean;

    private Mudurluk          mudurluk;

    private BagliBirim        birim;

    private Bina              bina;

    private Alan              alan;

    private String            dwgFileToDelete;

    public AlanKonumuController() {
        super(AlanKonumu.class);
    }

    @PostConstruct
    private void init() {
        this.mudurluk = this.sessionBean.filterByPermission("alankonumu:guncelle");
        this.dwgFileToDelete = "";
    }

    @Override
    public DBOperationResult update() {
        final DBOperationResult result = super.update();
        if ((this.getDwgFileToDelete() != null) && !this.getDwgFileToDelete().isBlank() && result.isSuccess()) {
            this.fileUploadHelper.deleteDwgFilePermanently(this.dwgFileToDelete);
        }
        return result;
    }

    @Override
    public List<AlanKonumu> getItems() {

        if (this.items == null) {
            final List<Mudurluk> mudurluguListByPermissionName = this.sessionBean.fetchMudurlukListByPermission("alankonumu:listele");

            if (mudurluguListByPermissionName != null) {
                this.items = this.getFacade().findByMudurluk(mudurluguListByPermissionName);
            }
        }
        return this.items;
    }

    public void handleMudurlukChange(final SelectEvent<Mudurluk> event) {
        this.setMudurluk(event.getObject());
        this.setBirim(null);
        this.changeUnitIfOnlyOne();
        this.setBina(null);
        this.getModel().setAlan(null);
        this.getModel().setAlanKonumuTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);

    }

    public void handleBirimChange(final SelectEvent<BagliBirim> event) {
        this.setBirim(event.getObject());
        this.setBina(null);
        this.getModel().setAlan(null);
        this.getModel().setAlanKonumuTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);

    }

    public void handleBinaChange(final SelectEvent<Bina> event) {
        this.setBina(event.getObject());
        this.getModel().setAlan(null);
        this.getModel().setAlanKonumuTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);
    }

    public void handleAlanChange(final SelectEvent<Alan> event) {
        this.setAlan(event.getObject());
        this.getModel().setAlanKonumuTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);
    }

    public void handleKonumTurChange(final SelectEvent<AlanKonumuTur> event) {
        this.getModel().setAlanKonumuTur(event.getObject());
        this.getModel().setKod(this.getModel().getAlanKonumuTur().getKod());
        this.getModel().setAd(this.getModel().getAlanKonumuTur().getAd());
        this.getModel().setAciklama(null);
    }

    // Sets unit by mudurluk if only one
    public void changeUnitIfOnlyOne() {
        if (this.mudurluk != null) {
            final List<BagliBirim> bagliBirimList = this.filterByNameAndMudurluk("");
            this.setBirim(null);
            if (bagliBirimList.size() == 1) {
                this.setBirim(bagliBirimList.iterator().next());
            }
        }
    }

    public List<BagliBirim> filterByNameAndMudurluk(final String query) {
        return this.getFacade().findByNameAndAciklamaAndMudurluk(query, this.mudurluk);
    }

    public List<Bina> filterByNameAndBagliBirim(final String query) {
        return this.getFacade().findByNameAndAciklamaAndBagliBirim(query, this.birim);
    }

    public List<Alan> filterByNameAndBina(final String query) {
        return this.getFacade().findByNameAndAciklamaAndBina(query, this.bina);
    }

    @Override
    public void showDetail(final AlanKonumu item) {
        super.showDetail(item);
        this.alan = this.getModel().getAlan();
        this.bina = this.getModel().getAlan().getBina();
        this.birim = this.getModel().getAlan().getBina().getBagliBirim();
        this.mudurluk = this.getModel().getAlan().getBina().getBagliBirim().getMudurluk();
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.birim = null;
        this.bina = null;
        this.getModel().setAlan(null);
        this.getModel().setAlanKonumuTur(null);
        this.mudurluk = this.sessionBean.filterByPermission("alankonumu:guncelle");
        this.changeUnitIfOnlyOne();
    }

    public void uploadDocumentToTempFolder(final FileUploadEvent event) {
        this.getModel().setPdfPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDwgDocumentToTempFolder(final FileUploadEvent event) {
        if ((this.getModel().getDwgPath() != null) && !this.getModel().getDwgPath().isBlank()) {
            this.setDwgFileToDelete(this.getModel().getDwgPath());
        }
        this.getModel().setDwgPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void deleteDwgFileFromModel() {
        this.setDwgFileToDelete(this.getModel().getDwgPath());
        this.getModel().setDwgPath(null);
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getPdfPath() != null) {
            this.getModel().setPdfPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getPdfPath(), FolderType.OTHER));
        }
        if (this.getModel().getDwgPath() != null) {
            this.getModel().setDwgPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDwgPath(), FolderType.DWG));
        }
    }

    // getters and setters ....................................................

    @Override
    public AlanKonumuFacade getFacade() {
        return this.alanKonumuFacade;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public BagliBirim getBirim() {
        return this.birim;
    }

    public void setBirim(final BagliBirim birim) {
        this.birim = birim;
    }

    public Bina getBina() {
        return this.bina;
    }

    public void setBina(final Bina bina) {
        this.bina = bina;
    }

    public Alan getAlan() {
        return this.alan;
    }

    public void setAlan(final Alan alan) {
        this.alan = alan;
    }

    public String getDwgFileToDelete() {
        return this.dwgFileToDelete;
    }

    public void setDwgFileToDelete(final String dwgFile) {
        this.dwgFileToDelete = dwgFile;
    }
}
