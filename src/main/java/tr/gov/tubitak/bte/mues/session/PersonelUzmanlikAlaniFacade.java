package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.PersonelUzmanlikAlani;
import tr.gov.tubitak.bte.mues.model.UzmanlikAlani;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class PersonelUzmanlikAlaniFacade extends AbstractFacade<PersonelUzmanlikAlani> {

    public PersonelUzmanlikAlaniFacade() {
        super(PersonelUzmanlikAlani.class);
    }

    public List<PersonelUzmanlikAlani> findByPersonelId(final Integer personelId) {
        return this.getEM().createNamedQuery("PersonelUzmanlikAlani.findByPersonelId", PersonelUzmanlikAlani.class).setParameter("id", personelId).getResultList();
    }

    public List<UzmanlikAlani> filterByFullNameAndAciklamaPreventDuplicate(final String query, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("UzmanlikAlani.filterByFullNameAndAciklamaPreventDuplicate", UzmanlikAlani.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("ids", excludedIds)
                      .getResultList();
    }

}
