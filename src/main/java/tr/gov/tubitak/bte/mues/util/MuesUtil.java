/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.util;

import java.io.Serializable;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.Normalizer;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Year;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.enterprise.context.ApplicationScoped;
import javax.faces.application.FacesMessage;
import javax.faces.application.FacesMessage.Severity;
import javax.faces.context.FacesContext;
import javax.faces.model.SelectItem;
import javax.inject.Named;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.commons.io.FilenameUtils;
import org.jsoup.Jsoup;
import org.omnifaces.util.Faces;
import org.omnifaces.util.Messages;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;

/**
 *
 *
 */
@Named
@ApplicationScoped
public class MuesUtil implements Serializable {

    private static final long          serialVersionUID   = -2367513187535215357L;

    private static final Logger        logger             = LoggerFactory.getLogger(MuesUtil.class);

    public static final char[]         NUMBERS            = "0123456789".toCharArray();

    public static final char[]         LOWER_LETTERS      = { 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z' };

    public static final char[]         UPPER_LETTERS      = { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };

    public static final char[]         SYMBOLS            = { ',', '-', '*', '/', '!', '+', '%', '(', ')', '_', '?', '=', ';', ':', '@' };

    public static final char[]         ALL_CHARACTERS     = new char[MuesUtil.NUMBERS.length + MuesUtil.LOWER_LETTERS.length + MuesUtil.UPPER_LETTERS.length + MuesUtil.SYMBOLS.length];

    /** Represents an empty String. */
    public static final String         BLANK_STR          = "";

    /** Varsayılan locale değişkenini tutar. */
    public static final String         DEFAULT_LOCALE_STR = "tr";

    /** Locale değişkenini tutar. */
    public static final Locale         LOCALE_TR          = new Locale(MuesUtil.DEFAULT_LOCALE_STR);

    /** Used to imitate boolean primitive type for the database since SQL does not have boolean primitive types: 1. */
    public static final int            TRUE               = 1;

    /** Used to imitate boolean primitive type for the database since SQL does not have boolean primitive types: 0. */
    public static final int            FALSE              = 0;

    /** This is used to represent no selection from a list of choices: -1L. */
    public static final long           NOSELECTION_LONG   = -1L;

    /** The Constant zero as string: "0". */
    public static final String         ZERO_STR           = "0";

    private static final String        SEPARATOR          = ", ";

    private static final int           FILE_NAME_MAX_SIZE = 50;

    private static final String        TR_M               = "TR.M.%s";

    private static final DecimalFormat df                 = new DecimalFormat("000,000,000");

    static {
        int index = 0;
        for (int i = 0; i < MuesUtil.NUMBERS.length; i++) {
            MuesUtil.ALL_CHARACTERS[index] = MuesUtil.NUMBERS[i];
            index++;
        }
        for (int i = 0; i < MuesUtil.LOWER_LETTERS.length; i++) {
            MuesUtil.ALL_CHARACTERS[index] = MuesUtil.LOWER_LETTERS[i];
            index++;
        }
        for (int i = 0; i < MuesUtil.UPPER_LETTERS.length; i++) {
            MuesUtil.ALL_CHARACTERS[index] = MuesUtil.UPPER_LETTERS[i];
            index++;
        }
        for (int i = 0; i < MuesUtil.SYMBOLS.length; i++) {
            MuesUtil.ALL_CHARACTERS[index] = MuesUtil.SYMBOLS[i];
            index++;
        }
    }

    public MuesUtil() {
        // blank constructor
    }

    // returns password digest of username and password
    public static String hashPassword(final String username, final String password) {
        try {
            return Base64.getEncoder().encodeToString(MessageDigest.getInstance("SHA1").digest((username + ":" + password).getBytes()));

        } catch (final NoSuchAlgorithmException e) {
            MuesUtil.logger.error("[hashPassword] : Hata : {}", e.getMessage(), e);
            return null;
        }
    }

    public static String surroundWithParanthesis(final String value) {
        if (MuesUtil.isEmptyOrNull(value)) {
            return MuesUtil.BLANK_STR;
        }
        return "(" + value + ")";
    }

    public static Integer toTimeline(final Integer sign, final Integer date) {
        if (sign == 1) {
            return date;
        }
        return -date;
    }

    /**
     * Boolean değeri integer değere çevirir.
     *
     * @param value çevrilmek istenen boolean değeri
     * @return 1 eğer boolean değeri true ise, 0 eğer boolean değeri false ise
     */
    public static int toInt(final boolean value) {
        if (value) {
            return 1;
        }
        return 0;
    }

    public static boolean toBoolean(final int value) {
        return value != 0;
    }

    /**
     * Verilen nesnenin boş ve null olup olmadığını kontrol eder. Şu an için sadece String ve List türünden nesnelere göre özelleştirilmiştir.
     *
     * @param object nesne
     * @return true, eğer null veya boş ise
     */
    public static boolean isEmptyOrNull(final Object object) {
        if (object == null) {
            return true;
        }
        if (object instanceof String) {
            return MuesUtil.BLANK_STR.equals(((String) object).trim());
        }
        if (object instanceof Collection) {
            return ((Collection<?>) object).isEmpty();
        }
        return false;
    }

    public static void showMessage(final String summary) {
        // "null" states that this is a global message
        FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(summary));
    }

    /**
     * Girilen mesaj metnini belirtilen seviyede yayınlar. Kullanıcıya sadece özet kısmı gösterilir.
     *
     * @param summary yayınlanacak mesaj
     * @param severity mesaj seviyesi
     */
    public static void showMessage(final String summary, final Severity severity) {
        // "null" states that this is a global message
        if (FacesContext.getCurrentInstance() != null) {
            FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(severity, summary, MuesUtil.BLANK_STR));
        }
    }

    public static void showMessage(final Severity severity, final String... summary) {
        // "null" states that this is a global message
        FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(severity, summary[0], summary.length > 1 ? summary[1] : MuesUtil.BLANK_STR));
    }

    /**
     * Girilen mesaj metnini belirtilen seviyede yayınlar. Kullanıcıya hem özet kısmı hem de detay kısmı gösterilir.
     *
     * @param summary yayınlanacak mesajın özeti
     * @param detail yayınlanacak mesajın detayları
     * @param severity mesaj seviyesi
     */
    public static void showMessage(final String summary, final String detail, final Severity severity) {
        // "null" states that this is a global message
        FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(severity, summary, detail));
    }

    public static void showMessage(final String clientId, final String summary, final String detail, final Severity severity) {
        FacesContext.getCurrentInstance().addMessage(clientId, new FacesMessage(severity, summary, detail));
    }

    public static void showMessageWithClientId(final String clientId, final String summary) {
        FacesContext.getCurrentInstance().addMessage(clientId, new FacesMessage(summary));
    }

    public static void showFlashMessage(final Severity severity, final String summary, final String detail) {
        Messages.addFlashGlobal(new FacesMessage(severity, summary, detail));
    }

    public static String fetchSessionId() {
        return (((HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(false)).getId());
    }

    public static String fetchUserIp() {
        final HttpServletRequest request = (HttpServletRequest) FacesContext.getCurrentInstance().getExternalContext().getRequest();
        String ipAddress = request.getHeader("X-FORWARDED-FOR");
        if (ipAddress == null) {
            ipAddress = request.getRemoteAddr();
            ipAddress = ipAddress.replaceFirst(",.*", "");
        }
        return ipAddress;
    }

    /**
     * Checks if two strings are equals ignoring case.
     *
     * @param str1 the str1
     * @param str2 the str2
     * @return true, if two strings are equal
     */
    public static boolean equalsIgnoreCase(final String str1, final String str2) {
        if ((str1 == null) || (str2 == null)) {
            return false;
        }
        return str1.toLowerCase(MuesUtil.LOCALE_TR).equals(str2.toLowerCase(MuesUtil.LOCALE_TR));
    }

    public static String unaccent(final String src) {
        return Normalizer.normalize(src.replaceAll("ı", "i"), Normalizer.Form.NFD).replaceAll("[^\\p{ASCII}]", "");
    }

    public static SelectItem[] getSelectItems(final List<?> entities, final boolean selectOne) {
        final int size = selectOne ? entities.size() + 1 : entities.size();
        final SelectItem[] items = new SelectItem[size];
        int i = 0;
        if (selectOne) {
            items[0] = new SelectItem("", "---");
            i++;
        }
        for (final Object x : entities) {
            items[i] = new SelectItem(x, x.toString());
            i++;
        }
        return items;
    }

    public static boolean isValidationFailed() {
        return FacesContext.getCurrentInstance().isValidationFailed();
    }

    public static void addErrorMessage(final Exception exception, final String defaultMessage) {
        final String msg = exception.getLocalizedMessage();
        if ((msg != null) && !msg.isEmpty()) {
            MuesUtil.addErrorMessage(msg);
        } else {
            MuesUtil.addErrorMessage(defaultMessage);
        }
    }

    public static void addErrorMessages(final List<String> messages) {
        for (final String each : messages) {
            MuesUtil.addErrorMessage(each);
        }
    }

    public static void addErrorMessage(final String msg) {
        MuesUtil.addErrorMessage(msg, msg);
    }

    public static void addErrorMessage(final String title, final String msg) {
        FacesContext.getCurrentInstance().addMessage(null, new FacesMessage(FacesMessage.SEVERITY_ERROR, title, msg));
    }

    public static void addSuccessMessage(final String msg) {
        FacesContext.getCurrentInstance().addMessage("successInfo", new FacesMessage(FacesMessage.SEVERITY_INFO, msg, msg));
    }

    public static void setSessionMapParameter(final String key, final Object value) {
        FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put(key, value);
    }

    public static void removeSessionMapParameter(final String key) {
        Faces.removeSessionAttribute(key);
    }

    public static Object getSessionMapParameter(final String key) {
        return Faces.getSessionAttribute(key);
    }

    public static String getRequestParameter(final String key) {
        return Faces.getRequestParameter(key);
    }

    public static void setRequestParameter(final String key, final Object value) {
        FacesContext.getCurrentInstance().getExternalContext().getRequestMap().put(key, value);
    }

    public static String escapeBackslash(final String text) {
        return text.replace("\\\\", "\\\\\\\\");
    }

    public enum PersistAction {

        CREATE,

        DELETE,

        UPDATE

    }

    public static String readableFileSize(long bytes) {
        int u = 0;
        for (; bytes > (1024 * 1024); bytes >>= 10) {
            u++;
        }
        if (bytes > 1024) {
            u++;
        }
        return String.format("%.1f %cB", bytes / 1024f, " kMGTPE".charAt(u));
    }

    private static final boolean isDigit(final char ch) {
        return (ch >= 48) && (ch <= 57);
    }

    /** Length of string is passed in for improved efficiency (only need to calculate it once) **/
    private static final String getChunk(final String s, int slength, int marker) {
        final StringBuilder chunk = new StringBuilder();

        char c = s.charAt(marker);
        chunk.append(c);
        marker++;
        if (MuesUtil.isDigit(c)) {
            // Integer max limitinin aşılmaması için length'de kırpma yapılıyor, tümü numericse
            // sadece ilk 9 karakter sortlanıyor
            if (slength > 9) {
                slength = 9;
            }
            while (marker < slength) {
                c = s.charAt(marker);
                if (!MuesUtil.isDigit(c)) {
                    break;
                }
                chunk.append(c);
                marker++;
            }
        } else {
            while (marker < slength) {
                c = s.charAt(marker);
                if (MuesUtil.isDigit(c)) {
                    break;
                }
                chunk.append(c);
                marker++;
            }
        }
        return chunk.toString();
    }

    public static <V, U extends Comparable<? super U>> Comparator<V> comparing(final Function<? super V, ? extends U> keyExtractor) {
        Objects.requireNonNull(keyExtractor);
        return (c1, c2) -> MuesUtil.compare(keyExtractor.apply(c1), (keyExtractor.apply(c2)));
    }

    public static int compare(final Object o1, final Object o2) {
        // Null value'ların sort'u bozmasını önlemek için eklendi.
        if ((o1 == null) && (o2 == null)) {
            return 0;
        }
        if (o1 == null) {
            return 1;
        }
        if (o2 == null) {
            return -1;
        } else if (!(o1 instanceof String) || !(o2 instanceof String)) {
            return 0;
        }
        final String s1 = (String) o1;
        final String s2 = (String) o2;

        int thisMarker = 0;
        int thatMarker = 0;
        final int s1Length = s1.length();
        final int s2Length = s2.length();

        while ((thisMarker < s1Length) && (thatMarker < s2Length)) {
            final String thisChunk = MuesUtil.getChunk(s1, s1Length, thisMarker);
            thisMarker += thisChunk.length();

            final String thatChunk = MuesUtil.getChunk(s2, s2Length, thatMarker);
            thatMarker += thatChunk.length();

            // If both chunks contain numeric characters, sort them numerically
            int result = 0;
            if (MuesUtil.isDigit(thisChunk.charAt(0)) && MuesUtil.isDigit(thatChunk.charAt(0))) {
                // Simple chunk comparison by length.

                result = Integer.parseInt(thisChunk) - Integer.parseInt(thatChunk);
                // If equal, the first different number counts

                if (result != 0) {
                    return result;
                }

            } else {
                result = thisChunk.compareTo(thatChunk);
            }

            if (result != 0) {
                return result;
            }
        }

        return s1Length - s2Length;
    }

    public static <V extends AbstractEntity> List<Integer> toIds(final Collection<V> objects) {
        final List<Integer> ids;
        if ((objects == null) || objects.isEmpty()) {
            ids = new ArrayList<>(1);
            ids.add(0);
        } else {
            ids = new ArrayList<>(objects.size());
            objects.stream().forEach(x -> ids.add(x.getId()));
        }
        return ids;
    }

    public static <V extends AbstractEntity> String getIdsInExp(final List<V> list) {
        if ((list != null) && !list.isEmpty()) {
            final StringBuilder stringBuilder = new StringBuilder("(");
            list.stream().forEach(x -> stringBuilder.append(x.getId()).append(","));
            stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(","));
            stringBuilder.append(")");
            return stringBuilder.toString();
        }
        return "(0)";
    }

    public static List<Integer> toIds(List<Integer> objects) {
        if ((objects == null)) {
            objects = new ArrayList<>(1);
        }
        if (objects.isEmpty()) {
            objects.add(0);
        }
        return objects;
    }

    public static List<Integer> emptyIdList() {
        return Arrays.asList(0);
    }

    public static Integer toId(final Integer id) {
        if (id == null) {
            return Integer.valueOf(0);
        }
        return id;
    }

    public static String extractFileName(final String fileName) {
        if (fileName.length() > MuesUtil.FILE_NAME_MAX_SIZE) {
            return FilenameUtils.getName(fileName).substring(0, MuesUtil.FILE_NAME_MAX_SIZE);
        }
        return FilenameUtils.getName(fileName);
    }

    public static String extractFileName(final String fileName, final int strSize) {
        if (fileName.length() > strSize) {
            return fileName.substring(0, strSize);
        }
        return fileName;
    }

    public static Integer getThisYear() {
        return Year.now().getValue();
    }

    public static String join(final List<Object> listToConvert) {
        return Optional.ofNullable(listToConvert).orElse(Collections.emptyList()).stream().map(Object::toString).collect(Collectors.joining(MuesUtil.SEPARATOR));
    }

    public static List<String> convertTrueFalseList(final List<Boolean> list) {
        final List<String> tempList = new ArrayList<>();
        if (list == null) {
            return Collections.emptyList();
        }

        for (final Boolean value : list) {
            if ((value != null) && value) {
                tempList.add("Evet");
            } else {
                tempList.add("Hayır");
            }
        }
        return tempList;
    }

    public static String convertTrueFalse(final Boolean boolValue) {

        if ((boolValue == null)) {
            return "";
        }
        if (Boolean.FALSE.equals(boolValue)) {
            return "Hayır";
        }

        return "Evet";

    }

    public static String convertMultiDate(final List<Object> listZimmetTarihi) {

        final List<Object> dateList = new ArrayList<>();

        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("EE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);

        if (listZimmetTarihi != null) {
            for (final Object dateObject : listZimmetTarihi) {
                try {
                    final Date parsedDate = simpleDateFormat.parse(dateObject.toString());
                    final SimpleDateFormat printedDate = new SimpleDateFormat("yyyy-MM-dd");
                    dateList.add(printedDate.format(parsedDate));
                } catch (final ParseException e) {
                    e.printStackTrace();
                }
            }
        }

        return Optional.ofNullable(dateList).orElse(Collections.emptyList()).stream().map(Object::toString).collect(Collectors.joining(MuesUtil.SEPARATOR));
    }

    public static String html2text(final String html) {
        if (html != null) {
            return Jsoup.parse(html).text();
        }
        return html;

    }

    public static String truncate(String value, final int length) {
        if ((value != null) && (value.length() > length)) {
            value = value.substring(0, length);
        }
        return value;
    }

    public String truncateGenelAciklama(String aciklama) {
        if (aciklama != null) {
            aciklama = MuesUtil.html2text(aciklama);
            if (aciklama.length() > 50) {
                aciklama = aciklama.substring(0, 50) + "...";
            }
        }
        return aciklama;
    }

    public static String getWrappedAssetId(final int eserId) {
        return String.format(MuesUtil.TR_M, MuesUtil.df.format(eserId).replace(",", "."));
    }

    public static Integer getUnWrappedAssetId(final String eserId) {
        try {
            final String replace = eserId.replace("TR.M.", "");
            return Integer.valueOf(replace.replace(".", ""));
        } catch (final NumberFormatException e) {
            return null;
        }
    }

    public static Integer getUnWrappedOMKAssetId(final String eserId) {
        try {
            final String replace = eserId.replace("OMK", "");
            return Integer.valueOf(replace.replace(".", ""));
        } catch (final NumberFormatException e) {
            return null;
        }
    }

    public static String getWrappedKamAssetId(final String eserId) {
        try {
            final DecimalFormat df = new DecimalFormat("000000000");
            return String.format("%s", df.format(MuesUtil.getUnWrappedKamId(eserId)));
        } catch (final NumberFormatException e) {
            return null;
        }
    }

    public static Integer getUnWrappedKamId(final String eserId) {
        try {
            return Integer.valueOf(eserId.replaceAll("[^\\d]", ""));
        } catch (final NumberFormatException e) {
            return null;
        }
    }

    public static String decimalPointFormatter(final String value) {
        try {
            return String.format("%,d", Integer.valueOf(value.replace(".", "")));
        } catch (final Exception e) {
            return value;
        }
    }

    public String customFormatDate(final Date date, final String pattern) {
        if (date != null) {
            final DateFormat format = new SimpleDateFormat(pattern);
            return format.format(date);
        }
        return "";
    }

    public String generateColour(final int id) {

        final int R = ((id * 6) % 256);
        final int G = ((id * 300) % 256);
        final int B = ((id * 150) % 256);

        return String.format("#%02x%02x%02x50", R, G, B);
    }

    public String generateColour(final String color, final Integer tone) {
        if ((color == null) || color.isEmpty()) {
            return "#FFFFFF";
        }

        final Integer transparency = 25 + ((tone % 10) * 23);
        return String.valueOf(color + Integer.toHexString(transparency));
    }

    public String generateTextColor(final String backgroundColor) {
        final int r = Integer.parseInt(backgroundColor.substring(1, 3), 16);
        final int g = Integer.parseInt(backgroundColor.substring(3, 5), 16);
        final int b = Integer.parseInt(backgroundColor.substring(5, 7), 16);

        return String.format("#%02X%02X%02X", 255 - r, 255 - g, 255 - b);
    }

    public String getRowStyleClassImp(final Integer id) {
        return "<style type=\"text/css\">.highlightStyle" + id + "{background-color:" + this.generateColour(id) + " !important;background-image:none;}</style>";
    }

    public String getRowStyleClassImp(final Integer id, final String color, final Integer tone) {
        final String backgroundColor = this.generateColour(color, tone);
        final String textColor = this.generateTextColor(backgroundColor);
        return "<style type=\"text/css\">.highlightStyle"
               + (id == null ? 0 : id)
               + "_"
               + tone
               + " {background-color:"
               + backgroundColor
               + " !important;background-image:none; "
               + "color:"
               + textColor
               + " !important;}</style>";
    }

    public String getRowStyleClassImp(final Integer id, final Integer tone) {
        final String backgroundColor = this.generateColour(tone);
        final String textColor = this.generateTextColor(backgroundColor);
        return "<style type=\"text/css\">.highlightStyle"
               + (id == null ? 0 : id)
               + "_"
               + tone
               + " {background-color:"
               + backgroundColor
               + " !important;background-image:none; "
               + "color:"
               + textColor
               + " !important;}</style>";
    }

    public String getRowStyleClass(final Integer id) {
        return "highlightStyle" + id;
    }

    public String getRowStyleClass(final Integer id, final Integer tone) {
        return "highlightStyle" + id + "_" + tone;
    }

    public static String getFileExtension(final String fileName) {
        String extension = "";

        final int i = fileName.lastIndexOf('.');
        if (i >= 0) {
            extension = fileName.substring(i + 1);
        }
        return extension;
    }

    public static <T> List<T> setToList(final Set<T> set) {
        return new ArrayList<>(set);
    }

    public static boolean checkFormat(final String userInput) {
        if (userInput == null) {
            return false;
        }
        boolean expressionStarted = false;
        boolean letterInserted = false;

        for (int index = 0; index < userInput.length(); index++) {
            final char chr = userInput.charAt(index);
            if (Character.isLetter(chr)) {
                if (!expressionStarted) {
                    return false;
                }
                letterInserted = true;
            } else if (chr == '{') {
                if (expressionStarted) {
                    return false;
                }
                expressionStarted = true;
            } else if (chr == '}') {
                if (!expressionStarted || !letterInserted) {
                    return false;
                }
                expressionStarted = false;
                letterInserted = false;
            } else if (expressionStarted) {
                return false;
            }
        }

        return (!expressionStarted && !letterInserted);
    }

}
