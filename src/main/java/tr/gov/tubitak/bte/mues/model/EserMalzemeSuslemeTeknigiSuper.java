package tr.gov.tubitak.bte.mues.model;

import java.util.LinkedHashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.OrderBy;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

/**
 *
 * ali.kelle
 */
@Audited
@MappedSuperclass

public class EserMalzemeSuslemeTeknigiSuper extends AbstractEntity implements DeleteValidatable
{

    private static final long serialVersionUID = 2967247329571605844L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "MALZEME_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Malzeme           malzeme;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "SUSLEME_TEKNIGI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private SuslemeTeknigi    suslemeTeknigi;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
               name = "ESER_MALZEME_SUSLEME_TEKNIGI_RENK",
               joinColumns = @JoinColumn(name = "ESER_MALZEME_SUSLEME_TEKNIGI"),
               inverseJoinColumns = @JoinColumn(name = "RENK")
       )
    @OrderBy("ID")
    private Set<Renk>         renks;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public EserMalzemeSuslemeTeknigiSuper() {
        // intentially left blank
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Malzeme getMalzeme() {
        return this.malzeme;
    }

    public void setMalzeme(final Malzeme malzeme) {
        this.malzeme = malzeme;
    }

    public Set<Renk> getRenks() {
        if (this.renks == null) {
            this.renks = new LinkedHashSet<>();
        }
        return this.renks;
    }

    public void setRenks(final Set<Renk> renks) {
        this.renks = renks;
    }

    public SuslemeTeknigi getSuslemeTeknigi() {
        return this.suslemeTeknigi;
    }

    public void setSuslemeTeknigi(final SuslemeTeknigi suslemeTeknigi) {
        this.suslemeTeknigi = suslemeTeknigi;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Joiner.on("-").skipNulls().join(this.malzeme, this.suslemeTeknigi);
    }

}
