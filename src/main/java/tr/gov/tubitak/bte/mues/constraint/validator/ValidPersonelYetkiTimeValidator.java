/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2017© */
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.ResourceBundle;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import tr.gov.tubitak.bte.mues.constraint.ValidPersonelYetkiTime;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;

/**
*
 *
 */
public class ValidPersonelYetkiTimeValidator implements ConstraintValidator<ValidPersonelYetkiTime, KullaniciBirimRol> {

    @Inject
    private ResourceBundle bundle;

    public ValidPersonelYetkiTimeValidator() {
        // default constructor
    }

    /* (non-Javadoc)
     * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
     */
    @Override
    public void initialize(final ValidPersonelYetkiTime constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final KullaniciBirimRol gorev, final ConstraintValidatorContext context) {

        boolean returnValue = true;
        if (gorev == null) {
            return true;
        }
        // because baslangicTarihi is a required field, it is assumed not null
        if ((gorev.getAssignmentEndTime() != null) && (gorev.getAssignmentStartTime().compareTo(gorev.getAssignmentEndTime()) > 0)) {
            UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().put("validationClientId", "editorDialog:formEditor:assignmentStartTime");

            // TODO: zaman kalınca daha generik bir şekilde halledilmesi sağlanacaktır. Aşağıda devam
            // UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).findComponent("editorDialog:formEditor:assignmentStartTime");

            this.raiseFlag(this.bundle.getString("valid.time.tips"), context);
            returnValue = false;
        }
        return returnValue;
    }

    private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

}
