package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.io.IOException;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import javax.faces.application.FacesMessage;

import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.beans.DocumentObjectBinder;
import org.apache.solr.client.solrj.response.PivotField;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.response.RangeFacet;
import org.apache.solr.client.solrj.response.json.NestableJsonFacet;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrException;
import org.apache.solr.common.util.NamedList;
import org.primefaces.PrimeFaces;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Identifiable;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.search.CriterionEnum;
import tr.gov.tubitak.bte.mues.search.SearchConstants;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.SearchUtil;

public class LazyGroupLoadSolrDataModel<T extends Identifiable<?>> extends LazyDataModel<T> {

    private static final long    serialVersionUID = -4739489425974372483L;

    private static final Logger  logger           = LoggerFactory.getLogger(LazyGroupLoadSolrDataModel.class);

    private final SolrSearcher   solrSearcher;

    private boolean              firstTime;

    private final Class<T>       clazz;

    private List<T>              data;

    private final List<Metadata> pivotFieldList;

    private SolrQuery            solrQuery        = null;

    public LazyGroupLoadSolrDataModel(final SolrSearcher solrSearcher, final List<Metadata> pivotFieldList, final Class<T> clazz) {

        this(solrSearcher, pivotFieldList, clazz, solrSearcher.getQuery());
    }

    public LazyGroupLoadSolrDataModel(final SolrSearcher solrSearcher, final List<Metadata> pivotFieldList, final Class<T> clazz, final SolrQuery solrQuery) {
        this.solrSearcher = solrSearcher;
        this.solrQuery = solrQuery;
        this.pivotFieldList = pivotFieldList;
        this.clazz = clazz;
        this.firstTime = true;

    }

    @Override
    public List<T> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        if (this.solrSearcher != null) {

            if (this.firstTime) {
                PrimeFaces.current().executeScript("PF('searchTableWidget').paginator.setPage(0)");
            }

            return this.solrLoad(first, pageSize, sortBy, filterBy);
        } else {
            return Collections.emptyList();
        }

    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
        return 100; // see the javadoc of this method
    }

    private List<T> solrLoad(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        try {

            if ((this.data == null) || this.data.isEmpty()) {

                logger.debug("{}", this.solrQuery);

                final QueryResponse solrResponse = this.solrSearcher.makeSearch(this.solrQuery);

                final SolrDocumentList solrDocList = new SolrDocumentList();

                final NamedList<List<PivotField>> facetPivot = solrResponse.getFacetPivot();

                @SuppressWarnings({ "rawtypes" })
                final List<RangeFacet> facets = solrResponse.getFacetRanges();

                final List<String> pivotFieldStringList = new ArrayList<>();
                for (final Metadata metadata : this.pivotFieldList) {
                    pivotFieldStringList.add(metadata.getName());
                }

                pivotFieldStringList.add("sayisi");

                final List<String> parsePivotResult;
                // Converting List to Array of strings
                final String[] fields = pivotFieldStringList.stream().toArray(String[]::new);
                if (((facetPivot != null) && (facetPivot.size() > 0))) {

                    parsePivotResult = SearchUtil.parsePivotResult(facetPivot);
                } else if (((facets != null) && (!facets.isEmpty()))) {
                    parsePivotResult = SearchUtil.parseFacetResult(facets, new StringBuilder());

                } else {
                    final NestableJsonFacet jsonFacetingResponse = solrResponse.getJsonFacetingResponse();
                    parsePivotResult = new ArrayList<>();
                    SearchUtil.parseJsonFacetResult(jsonFacetingResponse, null, parsePivotResult);

                }
                final List<T> listEser = this.parsePivotResultToORMModel(solrDocList, parsePivotResult, fields, this.clazz);

                this.recalculateFirst(first, pageSize, listEser.size());
                this.setRowCount(listEser.size());

                if (this.firstTime) {
                    this.solrSearcher.setTotalCount(listEser.size());
                    this.solrSearcher.setDuration(solrResponse.getElapsedTime());
                    this.firstTime = false;
                }
                this.data = listEser;
            }

            if ((sortBy != null) && !sortBy.isEmpty()) {
                this.sortEntities(this.data, sortBy);
            }

            return this.data;

        } catch (final SolrServerException | SolrException | IOException e) {

            MuesUtil.showMessage("Solr Sunucusuna Ulaşmada Hata", FacesMessage.SEVERITY_ERROR);
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Solr Sunucusuna Ulaşmada Hata'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine Başvurunuz. '"
                                     + ", 'severity':'error'})");
            logger.error("[solrLoad] : Hata : {}", e.getMessage(), e);
            return Collections.emptyList();

        }
    }

    public void sortEntities(final List<T> listResult, final Map<String, SortMeta> sortBy) {

        listResult.sort(new Comparator<T>() {

            @Override
            public int compare(final T entity1, final T entity2) {

                int comparison = 1;

                for (final SortMeta sortMeta : sortBy.values()) {
                    final String field = sortMeta.getField(); // Get the field name from SortMeta

                    final SortOrder order = sortMeta.getOrder(); // Get the sort order (ascending or descending)

                    // Use reflection or appropriate methods to get the field values
                    // You might need to adjust this part based on your ArtifactEntity class structure
                    final Comparable<Object> value1 = this.getValue(entity1, field);
                    final Comparable<Object> value2 = this.getValue(entity2, field);

                    if (order == SortOrder.DESCENDING) {
                        comparison = -comparison; // Invert the comparison for descending order
                    }

                    if ((value1 != null) && (value2 != null)) {
                        comparison = comparison * value1.compareTo(value2);
                    } else if ((value1 == null) && (value2 != null)) {
                        comparison = -1 * comparison;
                    } else if ((value1 != null)) {
                        comparison = 1 * comparison;
                    } else {
                        comparison = 0;
                    }

                }
                return comparison; // Return 0 if entities are considered equal according to the sort criteria
            }

            private Comparable<Object> getValue(final T entity1, final String fieldName) {

                try {
                    final Field field = LazyGroupLoadSolrDataModel.this.clazz.getDeclaredField(fieldName);
                    // Ensure the field is accessible (e.g., if it's private)
                    field.setAccessible(true);
                    // Get the field value from the entity
                    final Object fieldValue = field.get(entity1);

                    if (fieldValue instanceof Comparable) {
                        return (Comparable<Object>) fieldValue;
                    } else {
                        // Handle cases where the field is not a Comparable type
                        throw new IllegalArgumentException("Field is not a Comparable type.");
                    }

                } catch (NoSuchFieldException | IllegalAccessException | SecurityException e) {

                    e.printStackTrace();
                }

                return null;
            }
        });
    }

    public List<T> parsePivotResultToORMModel(final SolrDocumentList solrDocList, final List<String> parsePivotResult, final String[] fields, final Class<T> clazz) {
        final String datePattern = "yyyy-MM-dd";
        final SimpleDateFormat simpleDateFormat = new SimpleDateFormat(datePattern);
        for (final String string : parsePivotResult) {

            final SolrDocument sd = new SolrDocument();
            final String[] split = string.split(SearchConstants.SEPERATION_CHARS);

            for (int j = 0; j < split.length; j++) {
                // sayi metadata mapde olmadigi icin null gelebilir.
                final Metadata metadata = this.solrSearcher.getMetaDataMap().get(fields[j]);
                if ((metadata != null) && metadata.getDataTypeId().equals(CriterionEnum.DATE.getCode())) {
                    try {
                        sd.addField(fields[j], simpleDateFormat.parse(split[j]));
                    } catch (final ParseException e) {
                        sd.addField(fields[j], split[j]);

                    }
                } else if ((metadata != null) && metadata.getDataTypeId().equals(CriterionEnum.BOOLEAN.getCode())) {
                    sd.addField(fields[j], Boolean.parseBoolean(split[j]));
                } else {
                    sd.addField(fields[j], split[j]);

                }
            }

            solrDocList.add(sd);
        }
        final DocumentObjectBinder documentObjectBinder = new DocumentObjectBinder();
        return documentObjectBinder.getBeans(clazz, solrDocList);
    }

    @Override
    public T getRowData(final String rowKey) {
        for (final T artifact : this.data) {
            if (artifact.getId().equals(Integer.valueOf(rowKey))) {
                return artifact;
            }
        }

        return null;
    }

    @Override
    public String getRowKey(final T row) {
        return (String) row.getId();
    }
}
