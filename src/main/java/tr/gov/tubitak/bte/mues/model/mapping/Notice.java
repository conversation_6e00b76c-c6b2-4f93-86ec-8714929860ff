package tr.gov.tubitak.bte.mues.model.mapping;

import java.io.Serializable;

public class Notice implements Serializable {

    private static final long serialVersionUID = 1297083797496775679L;

    private final Integer     countOfPendingMyStarted;

    private final Integer     countOfWaitingMyEvaluation;

    private final Integer     countOfWaitingTransfer;

    private final Integer     countOfMyDraft;

    private final Integer     countOfMyRejected;

    private final Integer     countOfMineApproved;

    private final Integer     countOfAllApproved;

    private final Integer     countOfResearcherSuggestion;

    private final Integer     countOfAll;
    
    private final Integer 	  countOfTransferPhase;

    public Notice(final Integer countOfPendingMyStarted,
                  final Integer countOfWaitingMyEvaluation,
                  final Integer countOfWaitingTransfer,
                  final Integer countOfMyDraft,
                  final Integer countOfMyRejected,
                  final Integer countOfMineApproved,
                  final Integer countOfResearcherSuggestion,
                  final Integer countOfAll) {
        this.countOfPendingMyStarted = countOfPendingMyStarted;
        this.countOfWaitingMyEvaluation = countOfWaitingMyEvaluation;
        this.countOfWaitingTransfer = countOfWaitingTransfer;
        this.countOfMyDraft = countOfMyDraft;
        this.countOfMyRejected = countOfMyRejected;
        this.countOfMineApproved = countOfMineApproved;
        this.countOfResearcherSuggestion = countOfResearcherSuggestion;
        this.countOfAll = countOfAll;
        this.countOfAllApproved = null;
        this.countOfTransferPhase = null;
    }

    // this constructor method for the omk module
    public Notice(final Integer countOfPendingMyStarted,
                  final Integer countOfWaitingMyEvaluation,
                  final Integer countOfMyDraft,
                  final Integer countOfMyRejected,
                  final Integer countOfAllApproved,
                  final Integer countOfAll,
                  final Integer countOfTransferPhase) {
        this.countOfPendingMyStarted = countOfPendingMyStarted;
        this.countOfWaitingMyEvaluation = countOfWaitingMyEvaluation;
        this.countOfMyDraft = countOfMyDraft;
        this.countOfMyRejected = countOfMyRejected;
        this.countOfAll = countOfAll;
        this.countOfAllApproved = countOfAllApproved;
        this.countOfTransferPhase = countOfTransferPhase;
        this.countOfWaitingTransfer = null;
        this.countOfResearcherSuggestion = null;
        this.countOfMineApproved = null;
    }

    // getters ................................................................

    public Integer getCountOfPendingMyStarted() {
        return this.countOfPendingMyStarted;
    }

    public Integer getCountOfWaitingMyEvaluation() {
        return this.countOfWaitingMyEvaluation;
    }

    public Integer getCountOfWaitingTransfer() {
        return this.countOfWaitingTransfer;
    }

    public Integer getCountOfMyDraft() {
        return this.countOfMyDraft;
    }

    public Integer getCountOfMyRejected() {
        return this.countOfMyRejected;
    }

    public Integer getCountOfMineApproved() {
        return this.countOfMineApproved;
    }

    public Integer getCountOfAllApproved() {
        return this.countOfAllApproved;
    }

    public Integer getCountOfResearcherSuggestion() {
        return this.countOfResearcherSuggestion;
    }

    public Integer getCountOfAll() {
        return this.countOfAll;
    }
    
    public Integer getCountOfTransferPhase() {
		return this.countOfTransferPhase;
	}

    // total excludes draft and all
    public Integer getTotal() {
        if (this.countOfWaitingTransfer == null) {
            return this.countOfWaitingMyEvaluation + this.countOfMyRejected;
        }
        return this.countOfWaitingMyEvaluation + this.countOfMyRejected + this.countOfWaitingTransfer;
    }

}
