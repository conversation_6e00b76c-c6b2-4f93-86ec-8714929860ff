package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.UretimBolgesi;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class UretimBolgesiFacade extends AbstractFacade<UretimBolgesi> {

    public UretimBolgesiFacade() {
        super(UretimBolgesi.class);
    }

    public List<UretimBolgesi> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("UretimBolgesi.findByNameAndAciklama", UretimBolgesi.class)
                      .setParameter("str", "%" + query + "%")
                      .getResultList();
    }

}
