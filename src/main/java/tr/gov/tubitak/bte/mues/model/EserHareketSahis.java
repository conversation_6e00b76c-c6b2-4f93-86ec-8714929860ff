package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "ESER_HAREKET_SAHIS")
public class EserHareketSahis extends EserHareketSahisSuper {

    private static final long serialVersionUID = -1439062404804167115L;

    public EserHareketSahis() {
        // default constructor.
    }

    // getters and setters ....................................................
    @Override
    public String toString() {
        if (this.getSahis() != null) {
            return Optional.ofNullable(this.getSahis().getTitle()).orElse("" + this.getId());
        }
        return "";
    }

    @Override
    public String getTitle() {
        return this.toString();
    }

}
