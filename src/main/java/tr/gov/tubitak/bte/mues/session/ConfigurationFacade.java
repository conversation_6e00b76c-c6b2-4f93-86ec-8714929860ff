package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Configuration;
import tr.gov.tubitak.bte.mues.model.MuesConfiguration;

@RequestScoped
public class ConfigurationFacade extends AbstractFacade<Configuration> {

    public ConfigurationFacade() {
        super(Configuration.class);
    }

    public List<MuesConfiguration> findAllMuesConf() {
        return this.em.createNamedQuery("MuesConfiguration.findAll", MuesConfiguration.class).getResultList();
    }

}
