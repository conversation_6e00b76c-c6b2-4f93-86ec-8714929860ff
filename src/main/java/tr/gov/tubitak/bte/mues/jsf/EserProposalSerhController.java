package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.EserProposalSerh;
import tr.gov.tubitak.bte.mues.session.EserProposalSerhFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class EserProposalSerhController extends AbstractController<EserProposalSerh> implements SingleFileUploadable {

    private static final long      serialVersionUID = -4465419352459118582L;

    @Inject
    private EserProposalSerhFacade facade;

    @Inject
    private FileUploadHelper       fileUploadHelper;

    public EserProposalSerhController() {
        super(EserProposalSerh.class);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setDocPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getDocPath() != null) {
            this.getModel().setDocPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDocPath(), FolderType.ACK));
        }
    }

    public void makeFileRelatedOperations(final EserProposalSerh eserSerh) {
        this.setModel(eserSerh);
        this.writeToPermanentFolder();
    }

    // getters and setters ....................................................

    @Override
    public EserProposalSerhFacade getFacade() {
        return this.facade;
    }

    public List<String> buildFilePathFromModel(final EserProposalSerh eserSerh) {
        return this.fileUploadHelper.constructMainCopyImagePath(eserSerh.getDocPath(), FolderType.ACK);
    }

}
