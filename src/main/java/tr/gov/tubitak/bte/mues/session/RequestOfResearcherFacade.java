package tr.gov.tubitak.bte.mues.session;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.RequestOfResearcher;
import tr.gov.tubitak.bte.mues.model.Researcher;


@RequestScoped
public class RequestOfResearcherFacade extends AbstractFacade<RequestOfResearcher> {
	
	@Inject 
	KullaniciFacade kullaniciFacade;

    public RequestOfResearcherFacade() {
        super(RequestOfResearcher.class);
    }
    
    public Ku<PERSON><PERSON> create(final Researcher researcher) {

        <PERSON><PERSON><PERSON> kullanici;

        final List<Kullanici> findKullaniciList = this.findKullaniciByResearcherId(researcher.getId());

        if (!findKullaniciList.isEmpty()) {
            kullanici = findKullaniciList.iterator().next();

        } else {
            // ayni tcNO'dan bir kullanici varsa onun researcher bilgisini update etmeli, bu query onun icin
            kullanici = this.kullaniciFacade.findKullaniciByTCKN(researcher.getTcIdentityNo());
            
            if(kullanici==null) {
            	 kullanici = new Kullanici();
            }
            
            kullanici.setResearcher(researcher);
           
            if (researcher.getTcIdentityNo() != null) {
                kullanici.setKullaniciAdi(researcher.getTcIdentityNo());
            } else {
                kullanici.setKullaniciAdi(researcher.getPassportNo());
            }
            kullanici.setAd(researcher.getAd());
            kullanici.setSoyad(researcher.getSurname());
        }
        kullanici.setAktif(true);
        kullanici.setSilinmis(false);

        if (kullanici.getId() == null) { // sadece yeni kullanici ise sifre set edilmeli
            try {
                // :info back-door added for testing purposes. After test completed, this will be removed
                kullanici.setSifreHash(Base64.getEncoder().encodeToString(MessageDigest.getInstance("SHA-256").digest("1234".getBytes())));
            } catch (final NoSuchAlgorithmException e) {
                this.logger.error("[create] : Hata : {}", e.getMessage(), e);
            }
        }

        return kullanici;
    }
    
    public List<Kullanici> findKullaniciByResearcherId(final Integer researcherId) {
        return this.getEM().createNamedQuery("Kullanici.findByResearcher", Kullanici.class).setParameter("researcherId", researcherId).getResultList();
	}

    @SuppressWarnings("unchecked")
    public List<Object[]> findResearcherRequestCount(final int mudurlukId) {
        final String query = "SELECT YEAR(r.requestDate) as year, re.nativeForeigner, COUNT(*) as number "
                             + "FROM RequestofResearcher r, Researcher re WHERE re.ID=r.researcherId AND r.muzeMudurluguId=(?1) "
                             + "GROUP BY YEAR(r.requestDate), re.nativeForeigner ORDER BY YEAR(r.requestDate) DESC";

        return this.getEM().createNativeQuery(query).setParameter(1, mudurlukId).getResultList();
    }

    public List<RequestOfResearcher> findByResearcher(final Researcher researcher) {
        return this.getEM().createNamedQuery("RequestOfResearcher.findByResearcher", RequestOfResearcher.class).setParameter("researcher", researcher).setParameter("date", new Date()).getResultList();
    }

    public List<RequestOfResearcher> findByMudurluk(final List<Mudurluk> mudurlukList) {
        return this.getEM().createNamedQuery("RequestOfResearcher.findByMudurluk", RequestOfResearcher.class).setParameter("mudurlukList", mudurlukList).getResultList();
    }
    
    public List<KullaniciBirimRol> findByUserAndMudurlukAndRol(final Integer mudurlukId, final String rolCode, final Integer userId) {
        return this.em.createNamedQuery("KullaniciBirimRol.findByUserAndMudurlukAndRol", KullaniciBirimRol.class)
                      .setParameter("muzeId", mudurlukId)
                      .setParameter("kod", rolCode)
                      .setParameter("userId", userId)
                      .getResultList();
    }
    
    
    

}
