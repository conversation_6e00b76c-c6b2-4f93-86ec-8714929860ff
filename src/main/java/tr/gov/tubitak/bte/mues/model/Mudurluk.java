package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "MUZE_MUDURLUGU", uniqueConstraints = { @UniqueConstraint(columnNames = { "kod", "ad" }) })
@NamedQuery(name = "Mudurluk.findEagerById", query = "SELECT m FROM Mudurluk m LEFT JOIN FETCH m.il LEFT JOIN FETCH m.ilce WHERE m.id = :id")
@NamedNativeQuery(name = "Mudurluk.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM BAGLI_BIRIM WHERE SILINMIS = 0 AND MUZE_MUDURLUGU_ID = :id) "
                                                                  + "+ (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL WHERE SILINMIS = 0 AND mudurlukId = :id) "
                                                                  + "+ (SELECT case when count(1) > 0 then 1 else 0 end FROM WorkflowView WHERE eserSilinmis = 0 AND mudurlukId = :id) "
                                                                  + "+ (SELECT case when count(1) > 0 then 1 else 0 end FROM KULLANICI_BIRIM_ROL WHERE SILINMIS = 0 AND MUZE_MUDURLUGU_ID = :id)")

public class Mudurluk extends MudurlukSuper {

    private static final long serialVersionUID = -1121870182876513222L;

    @Size(max = 20)
    @Column(name = "expenseUnitCode", length = 20)
    private String            expenseUnitCode;

    public Mudurluk() {
        // default contructor.
    }

    public Mudurluk(final Integer integer) {
        super(integer);
    }

    // getters and setters ....................................................

    public String getExpenseUnitCode() {
        return this.expenseUnitCode;
    }

    public void setExpenseUnitCode(final String expenseUnitCode) {
        this.expenseUnitCode = expenseUnitCode;
    }

}
