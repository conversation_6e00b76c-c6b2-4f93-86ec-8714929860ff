/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2017© */
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.Objects;
import java.util.stream.IntStream;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import tr.gov.tubitak.bte.mues.constraint.TCKN;
import tr.gov.tubitak.bte.mues.model.EserFotograf;

/**
*
 *
 */
public class TCKNValidator implements ConstraintValidator<TCKN, String> {

    public TCKNValidator() {
    }

    /* (non-Javadoc)
     * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
     */
    @Override
    public void initialize(final TCKN constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final String tckn, final ConstraintValidatorContext context) {
        if (tckn == null) {
            return true;
        }
        if (tckn.length() == 11) {
            final int totalOdd = IntStream.range(0, 9).filter(x -> (x % 2) == 0).map(x -> Character.getNumericValue(tckn.charAt(x))).sum();
            final int totalEven = IntStream.range(0, 9).filter(x -> (x % 2) != 0).map(x -> Character.getNumericValue(tckn.charAt(x))).sum();

            final int total = totalOdd + totalEven + Integer.valueOf(tckn.substring(9, 10));
            final int lastDigit = total % 10;

            if (tckn.substring(10).equals(String.valueOf(lastDigit))) {
                final int check = ((totalOdd * 7) - totalEven) % 10;
                if (tckn.substring(9, 10).equals(String.valueOf(check))) {
                    return true;
                }
            }
        }
        return false;
    }

    public static void main(final String[] args) {
        System.out.println(Objects.equals(null, null));
        final EserFotograf eserFotograf = new EserFotograf();
        final EserFotograf eserFotograf2 = new EserFotograf();

        System.out.println(eserFotograf.equals(eserFotograf2));
        System.out.println(eserFotograf2.hashCode());


        
    }

}
