package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalKeyword;
import tr.gov.tubitak.bte.mues.model.Keyword;
import tr.gov.tubitak.bte.mues.session.EserProposalKeywordFacade;
import tr.gov.tubitak.bte.mues.session.KeywordFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class EserProposalKeywordController extends AbstractController<EserProposalKeyword> {

    private static final long serialVersionUID = 3127508102532126852L;

    @Inject
    private KeywordFacade     facade;

    public EserProposalKeywordController() {
        super(EserProposalKeyword.class);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalKeywordFacade getFacade() {
        return null;
    }

    public List<Keyword> filterByNameAndKeyword(final String query, final List<Integer> ids) {
        return this.facade.filterByNameAndKeyword(query, MuesUtil.toIds(ids));
    }

}
