package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.transaction.Transactional;

import tr.gov.tubitak.bte.mues.model.MailEnum;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@RequestScoped
@Transactional
public class SecurityImplementationUtilFacade {

    @Inject
    protected EntityManager em;

    public SecurityImplementationUtilFacade() {
        // Blank constructor
    }

    /***
     * Returns artifact ids that the also in the SecurityImplementation table
     * 
     * @param eserIds artifact ids
     * @return eser ids
     */
    @SuppressWarnings("unchecked")
    public List<Integer> findSecurityImplementationByEserIds(final List<Integer> eserIds) {
        return this.getEM()
                   .createNativeQuery("select distinct permanentId from SecurityImplementation S LEFT JOIN WorkflowView W on S.eserId = W.eserId where S.eserId in (:eserIds)")
                   .setParameter("eserIds", eserIds)
                   .getResultList();
    }

    /***
     * Returns email addresses by permission and mudurluk
     * 
     * @param mudurlukId
     * @param permission
     * @return
     */
    @SuppressWarnings("unchecked")
    public List<String> findEmailByMudurlukAndPermission(final Integer mudurlukId, final String permission) {
        return this.getEM()
                   .createNativeQuery("SELECT distinct P.EPOSTA_KURUMSAL FROM KULLANICI_BIRIM_ROL KBR "
                                      + " LEFT JOIN KULLANICI K on K.ID = KBR.KULLANICI_ID LEFT JOIN PERSONEL P on K.PERSONEL_ID = P.ID "
                                      + " LEFT JOIN ROL_IZIN RI on KBR.ROL_ID = RI.ROL_ID LEFT JOIN IZIN I on RI.IZIN_ID = I.ID "
                                      + " LEFT JOIN MUZE_MUDURLUGU M on M.ID = KBR.MUZE_MUDURLUGU_ID "
                                      + " WHERE I.KOD = :permission AND KBR.AKTIF = 1 AND KBR.SILINMIS = 0 AND (M.ID is null OR M.ID = :mudurlukId)")
                   .setParameter("permission", permission)
                   .setParameter("mudurlukId", mudurlukId)
                   .getResultList();
    }

    /***
     * Inserts securityImplementationNotifications as bulk considering SecurityImplementation last records
     * 
     * @param eserIds eserIds
     * @param mudurlukId mudurlukId
     * @param personelId personelId
     * @param aciklama aciklama
     * @return dBOperationResult
     */
    public DBOperationResult insertAsBulk(final List<Integer> eserIds, final Integer mudurlukId, final Integer personelId, final MailEnum mailEnum) {
        this.getEM()
            .createNativeQuery("INSERT into SecurityImplementationNotification (securityImplementationId, mudurlukId, dateCreated, createdBy, type, AKTIF, SILINMIS, ACIKLAMA) "
                               + " SELECT distinct S.ID, :mudurlukId, GETDATE(), :personelId, :type, 1, 0, :aciklama from SecurityImplementation S "
                               + "   RIGHT JOIN (select max(ID) as ID from SecurityImplementation group by eserId, malzemeId) SS on SS.ID = S.ID where S.eserId in (:eserIds)")
            .setParameter("eserIds", MuesUtil.toIds(eserIds))
            .setParameter("mudurlukId", mudurlukId)
            .setParameter("personelId", personelId)
            .setParameter("type", mailEnum.equals(MailEnum.SECURITY_IMPLEMENTATION_LAB) ? 0 : 1)
            .setParameter("aciklama", mailEnum.getTitle())
            .executeUpdate();
        this.getEM().flush();
        return DBOperationResult.success();
    }

    public EntityManager getEM() {
        return this.em;
    }

}
