package tr.gov.tubitak.bte.mues.session;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.ResourceBundle;

import javax.ejb.TransactionAttribute;
import javax.ejb.TransactionAttributeType;
import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.NoResultException;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.OptimisticLockException;
import javax.persistence.PersistenceException;
import javax.persistence.Query;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import javax.validation.ConstraintViolationException;

import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.DeleteValidatable;
import tr.gov.tubitak.bte.mues.model.IEntity;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * Abstract class for database related services. Classes implementing this class should use @Transactional annotation (and implement Serializable and
 * use @Named if intended to be used in facelets)
 */
@Transactional
public abstract class AbstractFacade<T extends IEntity<?>> {

    private static final String UPDATEDMESSAGE    = "Updated : {}";

    private static final String PERSISTED_MESSAGE = "Persisted : {}";

    @Inject
    protected EntityManager     em;

    @Inject
    private ResourceBundle      bundle;

    protected final Logger      logger;

    private final Class<T>      entityClass;

    // end of global fields ...................................................

    protected AbstractFacade(final Class<T> entityClass) {
        this.entityClass = entityClass;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Transactional
    public void refresh(final T x) {
        this.getEM().find(this.entityClass, x.getId());

    }

    @Transactional
    public DBOperationResult create(final T entity) {
        try {
            this.em.persist(entity);
            this.logger.info(PERSISTED_MESSAGE, entity);

        } catch (final OptimisticLockException e) {
            return DBOperationResult.failure("Değiştirmeye Çalıştığınız Kaynak Başkası Tarafından Değiştirilmiş. Kaynağı Tekrardan Güncelleyiniz.");

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getMessage());

        }

        catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult create(final List<AbstractEntity> entities) {
        try {
            for (final AbstractEntity abstractEntity : entities) {

                this.em.persist(abstractEntity);
                this.logger.info(PERSISTED_MESSAGE, abstractEntity);
            }

        } catch (final OptimisticLockException e) {
            return DBOperationResult.failure("Değiştirmeye Çalıştığınız Kaynak Başkası Tarafından Değiştirilmiş. Kaynağı Tekrardan Güncelleyiniz.");

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult create(final AbstractEntity... entities) {
        try {
            for (final AbstractEntity abstractEntity : entities) {

                this.em.persist(abstractEntity);
                this.logger.info(PERSISTED_MESSAGE, abstractEntity);
            }

        } catch (final OptimisticLockException e) {
            return DBOperationResult.failure("Değiştirmeye Çalıştığınız Kaynak Başkası Tarafından Değiştirilmiş. Kaynağı Tekrardan Güncelleyiniz.");

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult update(final List<AbstractEntity> entities) {
        try {
            for (final AbstractEntity each : entities) {
                if (each.getId() == null) {
                    this.em.persist(each);
                    this.logger.info(PERSISTED_MESSAGE, each);

                } else {
                    this.em.merge(each);
                    this.logger.info(UPDATEDMESSAGE, each);
                }
            }
            this.em.flush();

        } catch (final OptimisticLockException e) {
            return DBOperationResult.failure("Değiştirmeye Çalıştığınız Kaynak Başkası Tarafından Değiştirilmiş. Kaynağı Tekrardan Güncelleyiniz.");

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult update(final AbstractEntity... entities) {
        try {
            for (final AbstractEntity each : entities) {
                if (each.getId() == null) {
                    this.em.persist(each);
                    this.logger.info(PERSISTED_MESSAGE, each);

                } else {
                    this.em.merge(each);
                    this.logger.info(UPDATEDMESSAGE, each);
                }
            }
            this.em.flush();

        } catch (final OptimisticLockException e) {
            return DBOperationResult.failure("Değiştirmeye Çalıştığınız Kaynak Başkası Tarafından Değiştirilmiş. Kaynağı Tekrardan Güncelleyiniz.");

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult update(final T entity) {
        try {
            this.em.merge(entity);
            this.em.flush();
            this.logger.info(UPDATEDMESSAGE, entity);

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final Exception e) {
            return this.handleException(e);

        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult deleteById(final Object id) {
        final T entity = this.em.getReference(this.getEntityClass(), id);

        return this.delete(entity);
    }

    /**
     * delete entity list that couldn't be deletede using cascade strategy
     * 
     * @param entities2
     * 
     * @param entities
     * @return
     */
    @Transactional
    public DBOperationResult deletePermanently(final List<AbstractEntity> entities) {
        try {

            for (final AbstractEntity entity : entities) {
                if (entity instanceof DeleteValidatable) {
                    final Query query = this.em.createNamedQuery(entity.getClass().getSimpleName() + ".validateBeforeDelete");
                    final int numberOfDependantObjects = (Integer) query.setParameter("id", entity.getId()).getSingleResult();

                    if (numberOfDependantObjects > 0) {
                        return DBOperationResult.failure("Bu ögeye bağlı " + numberOfDependantObjects + " nesne olduğu için bu öge silinemez!");
                    }
                }
            }

            for (final AbstractEntity entity : entities) {
                final AbstractEntity entity1 = this.em.merge(entity);
                this.em.remove(entity1);
              
                this.logger.info("Removed : {}", entity);
            }
            this.em.flush();

        } catch (final Exception e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();

    }

    @Transactional
    public DBOperationResult delete(final T entity) {
        try {
            this.em.remove(this.findEagerById((Integer) entity.getId()));
            this.logger.info("Removed : {}", entity);

        } catch (final Exception e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    @Transactional
    public DBOperationResult delete(final T entity, final String deleteDescription) {
        MuesUtil.setSessionMapParameter("deleteDescription", deleteDescription);
        return this.delete(entity);
    }

    @Transactional
    public DBOperationResult virtualDeleteOperation(final boolean deletionStatus, final T entity, final String deleteDescription) {
        try {
            if (deletionStatus && (entity instanceof DeleteValidatable)) {
                final Query query = this.em.createNamedQuery(this.entityClass.getSimpleName() + ".validateBeforeDelete");
                final int numberOfDependantObjects = (Integer) query.setParameter("id", entity.getId()).getSingleResult();

                if (numberOfDependantObjects > 0) {
                    return DBOperationResult.failure("Bu ögeye bağlı " + numberOfDependantObjects + " nesne olduğu için bu öge silinemez!");
                }
            }
            entity.setSilinmis(deletionStatus);

            this.em.merge(entity);

            MuesUtil.setSessionMapParameter("deleteDescription", deleteDescription);

            if (deletionStatus) {
                this.logger.info("Marked as deleted : {}", entity);

            } else {
                this.logger.info("Marked as undeleted : {}", entity);
            }

        } catch (final Exception e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

    // finders ................................................................

    public T findById(final Integer id) {
        return this.em.find(this.getEntityClass(), id);
    }

    public T findReferansById(final Integer id) {
        return this.em.getReference(this.getEntityClass(), id);
    }

    /**
     * For lazily loaded fields, this method calls for named query for each entity. Reason behind this implementation is to speed up the query
     * process.
     *
     * @param id object id of the related entity
     * @return entity as all the joined fields fetched
     */
    @TransactionAttribute(TransactionAttributeType.REQUIRES_NEW)
    public T findEagerById(final Integer id) {
        try {
            return this.em.createNamedQuery(this.entityClass.getSimpleName() + ".findEagerById", this.getEntityClass()).setParameter("id", id).getSingleResult();

        } catch (final NoResultException nre) {
            MuesUtil.showMessage("Nesne bulunamadı", "Sorgulanan nesne: " + id, FacesMessage.SEVERITY_WARN);
            this.logger.debug("Sorgulanan nesne bulunamadı {}", nre.getMessage());
            return null;
        }
    }

    /**
     * For lazily loaded fields, this method scans all the annotations of the declared fields and calls getters of the lazily loaded fields to prevent
     * related exceptions {@link org.hibernate.LazyInitializationException}: could not initialize proxy - no Session BEWARE: toString() method is
     * called intentionally because if a method of entity is not called, the entity is not loaded
     *
     * @param id object id of the related entity
     * @return entity as all the joined fields fetched
     */
    @TransactionAttribute
    public T findEagerByIdViaReflection(final Object id) {
        final T entity = this.em.find(this.getEntityClass(), id);

        return this.traverseLazyFields(entity);
    }

    public T traverseLazyFields(final T entity) {

        for (final Field field : this.getEntityClass().getDeclaredFields()) {

            final OneToOne oneToOne = field.getAnnotation(OneToOne.class);
            final OneToMany oneToMany = field.getAnnotation(OneToMany.class);
            final ManyToOne manyToOne = field.getAnnotation(ManyToOne.class);

            if (((oneToOne != null) && oneToOne.fetch().equals(FetchType.LAZY))
                || ((oneToMany != null) && oneToMany.fetch().equals(FetchType.LAZY))
                || ((manyToOne != null) && manyToOne.fetch().equals(FetchType.LAZY))) {
                try {
                    final Method m = new PropertyDescriptor(field.getName(), this.getEntityClass()).getReadMethod();
                    final Object invoke = m.invoke(entity);
                    if (invoke != null) {
                        invoke.toString();
                    }

                } catch (final Exception e) {
                    this.logger.error("[findEagerByIdViaReflection] : Hata : {}", e.getMessage(), e);
                }
            }

        }
        return entity;
    }

    public List<T> findAll() {
        return this.em.createNamedQuery(this.getEntityClass().getSimpleName() + ".findAll", this.getEntityClass()).getResultList();
    }

    public List<T> findActive() {
        return this.em.createNamedQuery(this.getEntityClass().getSimpleName() + ".findActive", this.getEntityClass()).getResultList();
    }

    public List<T> filterByName(final String paramName, final String paramValue) {
        final CriteriaBuilder cb = this.em.getCriteriaBuilder();
        final CriteriaQuery<T> cq = cb.createQuery(this.getEntityClass());
        final Root<T> root = cq.from(this.getEntityClass());
        // Predicate predicate = cb.like(cb.function("unaccent", String.class, cb.lower(root.<String> get("ad"))), cb.parameter(String.class, "ad"));
        cq.select(root);

        final Predicate like = cb.like(root.<String> get(paramName), cb.parameter(String.class, paramName));
        final Predicate p1 = cb.equal(root.<Boolean> get("aktif"), true);
        final Predicate p2 = cb.equal(root.<Boolean> get("silinmis"), false);

        final Predicate predicate = cb.and(like, p1, p2);
        cq.where(predicate);

        cq.orderBy(cb.asc(root.get(paramName)));

        final TypedQuery<T> q = this.em.createQuery(cq);
        if (paramValue != null) {
            q.setParameter(paramName, "%" + paramValue + "%");
        }
        return q.setMaxResults(1000).getResultList();
    }

    // utilities ..............................................................

    public DBOperationResult handleException(final Exception e) {
        try {
            if ((e.getCause() != null) && e.getCause().toString().startsWith("org.hibernate.exception.ConstraintViolationException")) {
                final String msg = this.bundle.getString("base.record.already")
                                   + e.getCause()
                                      .getCause()
                                      .getMessage()
                                      .replaceFirst("^.*dbo\\.", "'")
                                      .replaceFirst("yinelenen anahtar", "mükerrer değer")
                                      .replaceFirst("Yinelenen anahtar değeri", "Mükerrer değer");
                this.logger.error("[handleException] : Hata : {}", e.getMessage(), e);
                return DBOperationResult.failure(msg);
            }

        } catch (final NullPointerException npe) {
            this.logger.error("[handleException] : Hata : {}", e.getMessage(), e);
            this.logger.error("[handleException] : Hata : {}", npe.getMessage(), npe);
            return DBOperationResult.failure(e.getMessage());
        }

        this.logger.error("[handleException] : Hata : {}", e.getMessage(), e);
        return DBOperationResult.failure(e.getMessage());
    }

    // Envers history of entities. Should be used by all
    public List<Object[]> history(final Integer id) {

        final AuditReader reader = AuditReaderFactory.get(this.em);

        @SuppressWarnings("unchecked")
        final List<Object[]> hasil = reader.createQuery()
                                           .forRevisionsOfEntity(this.getEntityClass(), false, true)
                                           .add(AuditEntity.id().eq(id))
                                           .addOrder(AuditEntity.revisionProperty("timestamp").desc())
                                           .getResultList();
        return hasil;
    }

    public Object latestHistory(final Integer id) {

        final AuditReader reader = AuditReaderFactory.get(this.em);

        return reader.createQuery().forRevisionsOfEntity(this.getEntityClass(), true, true).add(AuditEntity.id().eq(id)).addOrder(AuditEntity.revisionProperty("timestamp").desc()).getSingleResult();

    }

    // getters and setters ....................................................

    public EntityManager getEM() {
        return this.em;
    }

    public Class<T> getEntityClass() {
        return this.entityClass;
    }

}