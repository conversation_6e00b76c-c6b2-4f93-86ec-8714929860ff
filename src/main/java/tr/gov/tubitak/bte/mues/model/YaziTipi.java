package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "YAZI_TIPI")
@NamedQuery(name = "YaziTipi.findEagerById", query = "SELECT y FROM YaziTipi y WHERE y.id = :id")
@NamedQuery(name = "YaziTipi.findAll", query = "SELECT DISTINCT(y) FROM YaziTipi y ORDER BY y.silinmis, y.aktif DESC, y.ad")
@NamedQuery(name = "YaziTipi.findActive", query = "SELECT y FROM YaziTipi y WHERE y.aktif = true AND y.silinmis = false ORDER BY y.ad")
@NamedQuery(name = "YaziTipi.findByNameAndDil", query = "SELECT y FROM YaziTipi y JOIN FETCH y.yaziTipiDils yd WHERE yd.dil = :dil AND y.ad LIKE :ad AND yd.aktif = true AND yd.silinmis = false ORDER BY y.ad")
@NamedQuery(name = "YaziTipi.findByNameAndAciklama", query = "SELECT y FROM YaziTipi y WHERE y.aktif = true AND y.silinmis = false AND (y.ad LIKE :str OR y.aciklama LIKE :str) ORDER BY y.ad, y.aciklama")
@NamedNativeQuery(name = "YaziTipi.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_FOTOGRAF WHERE SILINMIS = 0 AND YAZI_TIPI_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM YAZI_TIPI_DIL WHERE SILINMIS = 0 AND YAZI_TIPI_ID = :id)")

public class YaziTipi extends AbstractEntity implements DeleteValidatable {

    private static final long         serialVersionUID = -987415252789739723L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                    ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                    aciklama;

    @OneToMany(mappedBy = "yaziTipi")
    private Collection<Transcription> transcriptions;

    @OneToMany(mappedBy = "yaziTipi", cascade = CascadeType.ALL, orphanRemoval = true)
    private Collection<YaziTipiDil>   yaziTipiDils;

    public YaziTipi() {
        // default constrcutor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<Transcription> getTranscriptions() {
        return this.transcriptions;
    }

    public void setTranscriptions(final Collection<Transcription> transcriptions) {
        this.transcriptions = transcriptions;
    }

    public Collection<YaziTipiDil> getYaziTipiDils() {
        return this.yaziTipiDils;
    }

    public void setYaziTipiDils(final Collection<YaziTipiDil> yaziTipiDils) {
        this.yaziTipiDils = yaziTipiDils;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
