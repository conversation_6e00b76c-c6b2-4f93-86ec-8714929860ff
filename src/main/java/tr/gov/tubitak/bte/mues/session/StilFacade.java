package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Stil;

/**
 *
 */
@RequestScoped
public class StilFacade extends AbstractFacade<Stil> {

    public StilFacade() {
        super(Stil.class);
    }

    public List<Stil> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Stil.findByNameAndAciklama", Stil.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
