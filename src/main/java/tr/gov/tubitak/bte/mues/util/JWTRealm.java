package tr.gov.tubitak.bte.mues.util;

import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.realm.AuthenticatingRealm;

public class JWTRealm extends AuthenticatingRealm {

    @Override
    public boolean supports(final AuthenticationToken token) {
        return token instanceof JWTAuthenticationToken;
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(final AuthenticationToken token) {
        final JWTAuthenticationToken jwtAuthToken = (JWTAuthenticationToken) token;
        return new SimpleAuthenticationInfo(jwtAuthToken.getUserId(), jwtAuthToken.getToken(), this.getName());
    }

}