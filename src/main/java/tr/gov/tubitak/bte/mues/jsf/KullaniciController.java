package tr.gov.tubitak.bte.mues.jsf;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Announcement;
import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.MailEnum;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.session.AuditFacade;
import tr.gov.tubitak.bte.mues.session.KullaniciFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class KullaniciController extends AbstractController<Kullanici> {

    private static final String   KULLANICI_LISTELE = "kullanici:listele";

    private static final long     serialVersionUID  = -1107647595392683351L;

    @Inject
    private KullaniciFacade       facade;

    @Inject
    private MailSender            mailSender;

    @Inject
    private SessionBean           sessionBean;

    @Inject
    private AbstractParameters    params;

    @Inject
    private transient AuditFacade auditFacade;

    private Integer               numberOfUsers;

    private List<Kullanici>       activeUsers;

    private boolean               read;

    public KullaniciController() {
        super(Kullanici.class);
    }

    public void updateCurrentUserReadList() {
        if (!this.sessionBean.getAnnouncements().isEmpty()) {
            final Announcement announcement = this.sessionBean.getAnnouncements().remove(0);
            final Kullanici kullanici = this.facade.findEagerById(this.sessionBean.getCurrentUser().getId());
            kullanici.setDuyuruNumarasi(announcement);
            this.facade.update(kullanici);
        }
    }

    public void updateCurrentUserKVKK() {
        this.sessionBean.getCurrentUser().setGdpApproved(Boolean.TRUE);
        this.setModel(this.sessionBean.getCurrentUser());
        this.update();

        this.sessionBean.setGeneralDataProtectionRegulationApproved(null);
        this.auditFacade.log(AuditEvent.KVKKOnaylama, "Kullanıcı Açık Rıza Beyan Formu Onaylama");
    }

    public void resetPassword(final Kullanici model) {
        super.setModel(model);

        this.getModel().setSifreHash(null);
        this.getModel().setSifirlamaIstegi(false);

        if ((this.getModel().getId() == null) || (this.getModel().getSifreHash() == null)) {

            final String newPassword = this.generatePassword();

            final String passwordHash = MuesUtil.hashPassword(this.getModel().getKullaniciAdi(), newPassword);

            this.getModel().setSifreHash(passwordHash);

            final MailEnum mail = MailEnum.PASSWORD_RESET;
            final String body = MessageFormat.format(mail.getBody(), this.getModel().getPersonelView().getTitle(), this.params.get("mail.subject.prefix"), newPassword);

            this.mailSender.send(this.getModel().getPersonelView().getEpostaKurumsal(), "", "", mail.getTitle(), body);

            super.update();
        }
    }

    private String generatePassword() {

        final Random random = new Random(); // NOSONAR code is not used in prod
        final StringBuilder newPassword = new StringBuilder();
        newPassword.append(MuesUtil.NUMBERS[random.nextInt(MuesUtil.NUMBERS.length)]);
        newPassword.append(MuesUtil.LOWER_LETTERS[random.nextInt(MuesUtil.LOWER_LETTERS.length)]);
        newPassword.append(MuesUtil.UPPER_LETTERS[random.nextInt(MuesUtil.UPPER_LETTERS.length)]);
        newPassword.append(MuesUtil.SYMBOLS[random.nextInt(MuesUtil.SYMBOLS.length)]);

        // password length can be manipulated below (4 comes from above, the rest comes from below)
        for (int i = 0; i < 4; ++i) {
            newPassword.append(MuesUtil.ALL_CHARACTERS[random.nextInt(MuesUtil.ALL_CHARACTERS.length)]);
        }

        return mixString(newPassword.toString());
    }

    private static String mixString(final String originalString) {
        final StringBuilder sb = new StringBuilder(originalString);

        char temp;
        int swapWith;
        for (int i = 0; i < sb.length(); i++) {
            temp = sb.charAt(i);
            swapWith = (int) Math.floor(Math.random() * sb.length());// NOSONAR code is not used in prod
            sb.setCharAt(i, sb.charAt(swapWith));
            sb.setCharAt(swapWith, temp);
        }

        return sb.toString();
    }

    public List<Kullanici> filterByNameAndPermission(final String query) {
        return this.getFacade().filterByNameAndMudurluk(query, this.sessionBean.fetchMudurlukListByPermission(KULLANICI_LISTELE));
    }

    public List<Kullanici> filterByMudurlukAndRol(final List<Mudurluk> muzeler, final List<Rol> roller) {
        return this.getFacade().filterByMudurlukAndRol(muzeler, roller);
    }

    public List<Kullanici> filterByExternalUserName(final String query) {
        return this.getFacade().filterByExternalUserName(query);
    }

    public List<Kullanici> bringActiveUsers() {
        if (this.activeUsers == null) {
            this.activeUsers = this.getFacade().findByUsernames(this.params.getLoginMap().keySet());
        }
        return this.activeUsers;
    }

    // getter and setters .....................................................

    @Override
    public KullaniciFacade getFacade() {
        return this.facade;
    }

    @Override
    public List<Kullanici> getItems() {
        if (this.items == null) {
            final List<Mudurluk> mudurluguListByPermission = this.sessionBean.fetchMudurlukListByPermission(KULLANICI_LISTELE);

            if (mudurluguListByPermission != null && !mudurluguListByPermission.isEmpty()) {
                this.items = this.getFacade().findByMudurluk(mudurluguListByPermission);
            }
        }
        return this.items;
    }

    public Integer getNumberOfUsers() {
        if (this.numberOfUsers == null) {
            final List<Mudurluk> mudurluguListByPermission = this.sessionBean.fetchMudurlukListByPermission(KULLANICI_LISTELE);

            if (mudurluguListByPermission != null) {
                this.numberOfUsers = this.getFacade().findByMudurlukActive(mudurluguListByPermission);
            }
        }
        return this.numberOfUsers;
    }

    public List<Kullanici> getSifirlamaListesi() {
        return this.facade.findUsersRequestingPasswordReset();
    }

    public boolean getRead() {
        return this.read;
    }

    public void setRead(final boolean read) {
        this.read = read;
    }

    public List<Kullanici> getActiveUsers() {
        if (this.activeUsers == null) {
            this.activeUsers = new ArrayList<>();
        }
        return this.activeUsers;
    }

    public void setActiveUsers(final List<Kullanici> activeUsers) {
        this.activeUsers = activeUsers;
    }

}
