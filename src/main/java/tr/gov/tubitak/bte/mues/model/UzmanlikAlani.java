package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Entity
@Table(name = "UZMANLIK_ALANI")
@NamedQuery(name = "UzmanlikAlani.findEagerById", query = "SELECT u FROM UzmanlikAlani u WHERE u.id = :id")
@NamedQuery(name = "UzmanlikAlani.findAll", query = "SELECT u FROM UzmanlikAlani u ORDER BY u.silinmis, u.aktif DESC, u.ad")
@NamedQuery(name = "UzmanlikAlani.findActive", query = "SELECT u FROM UzmanlikAlani u WHERE u.aktif = true AND u.silinmis = false ORDER BY u.ad")
@NamedQuery(name = "UzmanlikAlani.filterByFullNameAndAciklamaPreventDuplicate", query = "SELECT u FROM UzmanlikAlani u WHERE u.id NOT IN :ids AND u.aktif = true AND u.silinmis = false AND (u.ad LIKE :str OR u.aciklama LIKE :str) ORDER BY u.ad")
@NamedNativeQuery(name = "UzmanlikAlani.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL WHERE SILINMIS = 0 AND UZMANLIK_ALANI_ID = :id)")
public class UzmanlikAlani extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 7445048621223861577L;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public UzmanlikAlani() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
