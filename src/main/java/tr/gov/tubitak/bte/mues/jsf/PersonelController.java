package tr.gov.tubitak.bte.mues.jsf;

import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.NoResultException;

import org.apache.shiro.SecurityUtils;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.ToggleEvent;
import org.primefaces.model.Visibility;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.EditPermissible;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.PersonelUzmanlikAlani;
import tr.gov.tubitak.bte.mues.model.PersonelYabanciDil;
import tr.gov.tubitak.bte.mues.session.AuditFacade;
import tr.gov.tubitak.bte.mues.session.KullaniciFacade;
import tr.gov.tubitak.bte.mues.session.PersonelFacade;
import tr.gov.tubitak.bte.mues.util.CurrentUser;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesException;

@Named
@ViewScoped
public class PersonelController extends AbstractController<Personel> implements SingleFileUploadable {

    private static final long               serialVersionUID = 6767457728064281536L;

    @Inject
    private PersonelFacade                  facade;

    @Inject
    private KullaniciFacade                 kullaniciFacade;

    @Inject
    private AuditFacade                     auditFacade;

    @Inject
    private PersonelGorevController         personelGorevController;

    @Inject
    private PersonelUzmanlikAlaniController personelUzmanlikAlaniController;

    @Inject
    private PersonelYabanciDilController    personelYabanciDilController;

    @Inject
    private FileUploadHelper                fileUploadHelper;

    @Inject
    private SessionBean                     sessionBean;

    @Inject
    private MuesParameters                  muesParameters;

    @Inject
    @CurrentUser
    private Kullanici                       me;

    private Set<Integer>                    permittedPersonelID;

    private transient List<Object[]>        museumDirectorateHistoryList;

    private transient List<Object[]>        unvanHistoryList;

    private transient List<Object[]>        kadroDurumuHistoryList;

    private transient List<Object[]>        meslekHistoryList;

    private boolean                         toggleActive     = Boolean.FALSE;

    private transient Personel              tempPersonnel;

    @PostConstruct
    public void init() {
        this.permittedPersonelID = this.kullaniciFacade.preparePermittedUserNames(this.me.getId());
        this.personelGorevController.fetchCommissionPersonels();
    }

    public PersonelController() {
        super(Personel.class);
    }

    @Override
    public DBOperationResult create() {
        this.getModel().setDateCreated(new Date());
        this.getModel().setDateUpdated(new Date());
        if (this.getModel().getMudurluk() != null) {
            this.getModel().setMudurlukAd(this.getModel().getMudurluk().getAd());
        }
        return super.create();
    }

    @Override
    public void newRecord() {
        this.setTempPersonnel(null);
        this.setMuseumDirectorateHistoryList(null);
        super.newRecord();
        this.personelUzmanlikAlaniController.setModel(new PersonelUzmanlikAlani());
        this.getModel().setCalismaDurumu(true);
    }

    @Override
    public DBOperationResult update() {
        this.getModel().setDateUpdated(new Date());
        this.getModel().setMudurlukAd(this.getModel().getMudurluk().getAd());
        try {
            final Kullanici findById = this.kullaniciFacade.findKullanici(this.getModel().getTcKimlikNo());
            findById.setAd(this.getModel().getAd());
            findById.setSoyad(this.getModel().getSoyad());
            this.kullaniciFacade.update(findById);

        } catch (final NoResultException e) {
            this.logger.info("Kullanıcı olmayan personel {}", e.getMessage());
        }
        return super.update();
    }

    @Override
    public void showDetail(final Integer id) {
        try {
            this.setModel(this.getFacade().findEagerById(id));
            this.setNewMode(false);
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + id + " numaralı personeli görüntülemiştir.";
            this.auditFacade.log(AuditEvent.PersonelGoruntuleme, msg);
            this.listHistoryOfMuseumDirectorate(this.getModel().getId());
        } catch (final MuesException e) {
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + id + " numaralı personeli görüntüleyememiştir.";
            this.auditFacade.log(AuditEvent.PersonelGoruntuleme, msg, e.getMessage());
        }
    }

    public void uploadFotografToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadTaahhutToTempFolder(final FileUploadEvent event) {
        this.getModel().setTaahhutPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {

        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.muesParameters.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
        if (this.getModel().getTaahhutPath() != null) {
            this.getModel().setTaahhutPath(this.muesParameters.writeMainCopyToFile(this.getModel().getTaahhutPath(), FolderType.OTHER));
        }
    }

    public boolean isDisabled(final AbstractEntity personel) {
        if (SecurityUtils.getSubject().hasRole("SUPERUSER")
            || SecurityUtils.getSubject().hasRole("sistem_merkez_sorumlusu")
            || SecurityUtils.getSubject().hasRole("KVMGM_merkez_yoneticisi")
            || SecurityUtils.getSubject().hasRole("muzeler_daire_baskani")
            || SecurityUtils.getSubject().hasRole("ktb_merkez_yoneticisi")) {
            return false;
        }
        if (personel instanceof EditPermissible) {
            return !this.permittedPersonelID.contains(((EditPermissible) personel).getUserIdentifier());
        }
        return false;
    }

    public void initInspectors() {
        this.setItems(this.facade.findAllInspectors());
    }

    public DBOperationResult assignPersonnelAsInspector() {
        this.setModel(this.tempPersonnel);
        this.getModel().setInspector(Boolean.TRUE);
        final DBOperationResult result = super.update();
        this.setItems(this.facade.findAllInspectors());
        return result;
    }

    public DBOperationResult dissociatePersonnelAsInspector(final Integer personelId) {
        this.showDetail(personelId);
        this.getModel().setInspector(Boolean.FALSE);
        final DBOperationResult result = super.update();
        this.setItems(null);
        this.setItems(this.facade.findAllInspectors());
        return result;
    }

    public void assignCurrentPersonel() {
        this.setModel(this.facade.findEagerById(this.me.getPersonelView().getId()));
        this.setNewMode(false);
    }

    public void addPersonelYabanciDil() {
        if (this.getModel().getPersonelYabanciDil() == null) {
            this.getModel().setPersonelYabanciDil(new LinkedHashSet<>());
        }
        this.personelYabanciDilController.getModel().setPersonel(this.getModel());
        this.getModel().getPersonelYabanciDil().add(this.personelYabanciDilController.getModel());
    }

    public void removePersonelYabanciDil(final PersonelYabanciDil personelYabanciDil) {
        this.getModel().getPersonelYabanciDil().remove(personelYabanciDil);
    }

    public void addPersonelUzmanlikAlani() {
        if (this.getModel().getPersonelUzmanlikAlani() == null) {
            this.getModel().setPersonelUzmanlikAlani(new LinkedHashSet<>());
        }
        this.personelUzmanlikAlaniController.getModel().setPersonel(this.getModel());
        this.getModel().getPersonelUzmanlikAlani().add(this.personelUzmanlikAlaniController.getModel());
    }

    public void removePersonelUzmanlikAlani(final PersonelUzmanlikAlani personelUzmanlikAlani) {
        this.getModel().getPersonelUzmanlikAlani().remove(personelUzmanlikAlani);
    }

    public List<Personel> filterByFullNameAndAciklama(final String query) {
        return this.facade.filterByFullNameAndAciklama(query);
    }

    public List<Personel> filterByNonKullaniciAndName(final String query) {
        return this.facade.filterByNonKullaniciAndName(query);
    }

    public List<Personel> findAllUndeletedPersoenelByNameAndAciklama(final String query) {
        return this.facade.findAllUndeletedPersoenelByNameAndAciklama(query);
    }

    public List<Personel> findAllPersonnelPreventDuplicate(final String query, final List<Integer> excludedPersonnel) {
        return this.facade.findAllPersonnelPreventDuplicate(query, excludedPersonnel);
    }

    public List<Personel> findAllPersonnelPreventDuplicateWithMuseum(final String query, final List<Integer> excludedPersonnel, final Integer museumId) {
        return this.facade.findAllPersonnelPreventDuplicateWithMuseum(query, excludedPersonnel, museumId);
    }

    public List<Personel> findAllFromDirectoratesExcludeIds(final String query, final List<Integer> personelIds, final List<Integer> directoratesList) {
        this.setItems(this.facade.findAllFromDirectoratesExcludeIds(query, personelIds, directoratesList));
        return this.getItems();
    }

    public List<Personel> filterByFullNameAndAciklamaAndMuseumDirectorate(final String query) {
        final FacesContext context = FacesContext.getCurrentInstance();
        final Mudurluk mudurluk = (Mudurluk) UIComponent.getCurrentComponent(context).getAttributes().get("mudurluk");

        return this.facade.filterByFullNameAndAciklamaAndMuseumDirectorates(query, List.of(mudurluk));
    }

    public List<Personel> filterByFullNameAndAciklamaAndMuseumDirectorates(final String query) {
        return this.facade.filterByFullNameAndAciklamaAndMuseumDirectorates(query, this.sessionBean.getPermissionBasedDirectorates());
    }

    public List<Personel> findByNameAndMuseumDirectorate(final Integer muzeMudurlukId) {
        return this.facade.findByNameAndMuseumDirectorate(muzeMudurlukId);
    }

    public List<Personel> filterByFullNameAndAciklamaAndMuseumDirectorates(final String query, final List<Mudurluk> directoratesList) {
        this.setItems(this.facade.filterByFullNameAndAciklamaAndMuseumDirectorates(query, directoratesList));
        return this.getItems();
    }

    public void listHistoryOfMuseumDirectorate(final Integer id) {
        final List<Object[]> auditList = this.getFacade().historyOfMuseumDirectorateByNativeQuery(id);
        this.setMuseumDirectorateHistoryList(auditList);
    }

    public void listHistoryOfUnvan(final Integer id) {
        final List<Object[]> auditList = this.getFacade().historyOfUnvanByNativeQuery(id);
        this.setUnvanHistoryList(auditList);
    }

    public void listHistoryOfKadroDurumu(final Integer id) {
        final List<Object[]> auditList = this.getFacade().historyOfKadroDurumuByNativeQuery(id);
        this.setKadroDurumuHistoryList(auditList);
    }

    public void listHistoryOfMeslek(final Integer id) {
        final List<Object[]> auditList = this.getFacade().historyOfMeslekByNativeQuery(id);
        this.setMeslekHistoryList(auditList);
    }

    public void onRowToggle(final ToggleEvent event) {
        this.toggleActive = event.getVisibility() == Visibility.HIDDEN;
    }

    // getters and setters .................................................................

    @Override
    public PersonelFacade getFacade() {
        return this.facade;
    }

    public List<Object[]> getMuseumDirectorateHistoryList() {
        return this.museumDirectorateHistoryList;
    }

    public void setMuseumDirectorateHistoryList(final List<Object[]> museumDirectorateHistoryList) {
        this.museumDirectorateHistoryList = museumDirectorateHistoryList;
    }

    public List<Object[]> getUnvanHistoryList() {
        return this.unvanHistoryList;
    }

    public void setUnvanHistoryList(final List<Object[]> unvanHistoryList) {
        this.unvanHistoryList = unvanHistoryList;
    }

    public List<Object[]> getKadroDurumuHistoryList() {
        return this.kadroDurumuHistoryList;
    }

    public void setKadroDurumuHistoryList(final List<Object[]> kadroDurumuHistoryList) {
        this.kadroDurumuHistoryList = kadroDurumuHistoryList;
    }

    public List<Object[]> getMeslekHistoryList() {
        return this.meslekHistoryList;
    }

    public void setMeslekHistoryList(final List<Object[]> meslekHistoryList) {
        this.meslekHistoryList = meslekHistoryList;
    }

    public boolean isToggleActive() {
        return this.toggleActive;
    }

    public void setToggleActive(final boolean toggleActive) {
        this.toggleActive = toggleActive;
    }

    public Personel getTempPersonnel() {
        return this.tempPersonnel;
    }

    public void setTempPersonnel(final Personel tempPersonnel) {
        this.tempPersonnel = tempPersonnel;
    }

}
