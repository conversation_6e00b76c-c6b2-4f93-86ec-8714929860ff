package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Root;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.JpaLazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.util.SerializableSupplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.SahisController;
import tr.gov.tubitak.bte.mues.model.Sahis;

public class LazySahisDataModel extends JpaLazyDataModel<Sahis> {

    private static final long     serialVersionUID = 6151610502385044678L;

    private static final Logger   logger           = LoggerFactory.getLogger(LazySahisDataModel.class);

    private List<Sahis>           data;

    private final SahisController sahisController;

    public LazySahisDataModel(final Class<Sahis> entityClass, final SerializableSupplier<EntityManager> entityManager, final SahisController sahisController) {
        super(entityClass, entityManager, "id");
        this.sahisController = sahisController;
    }

    @Override
    public List<Sahis> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        final EntityManager em = this.entityManager.get();

        final CriteriaBuilder cb = em.getCriteriaBuilder();
        CriteriaQuery<Sahis> cq = cb.createQuery(this.entityClass);
        final Root<Sahis> root = cq.from(this.entityClass);
        root.fetch("il", JoinType.LEFT);
        root.fetch("ilce", JoinType.LEFT);
        // Order By 'AD' initally
        cq.orderBy(cb.asc(root.get("ad")));

        cq = cq.select(root);

        this.applyFilters(cb, cq, root, filterBy);
        this.applySort(cb, cq, root, sortBy);
        final TypedQuery<Sahis> query = em.createQuery(cq);
        query.setFirstResult(first);
        query.setMaxResults(pageSize);

        this.data = query.getResultList();

        logger.debug("filters: {} _ multiSortMeta Length, {}", sortBy, filterBy);

        return this.data;
    }

    @Override
    public Sahis getRowData(final String rowKey) {

        for (final Sahis row : this.sahisController.getSelectionList()) {
            if (row.getId().toString().equals(rowKey)) {
                return row;
            }
        }
        for (final Sahis each : this.data) {
            if (each.getId().equals(Integer.valueOf(rowKey))) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final Sahis sahis) {
        return sahis.getId().toString();
    }

}
