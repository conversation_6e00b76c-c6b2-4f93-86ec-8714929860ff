package tr.gov.tubitak.bte.mues.util.audits;

import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Transient;

import org.apache.poi.ss.formula.eval.NotImplementedException;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RevisionNumber;
import org.hibernate.envers.RevisionTimestamp;

import tr.gov.tubitak.bte.mues.model.IEntity;

@Audited
@MappedSuperclass
 @NamedQuery(name = "countEntryByUserUsername", query = "SELECT count(a.userName) FROM Audit a WHERE a.userName = :username") 
public class AuditSuper implements IEntity<Long> {

    @Id
    @RevisionNumber
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long              id;

    private static final long serialVersionUID = -5883184394871978049L;

    /**
     * data da {@link Revision}
     */
    @RevisionTimestamp
    private long              timestamp;

    private String            userName;

    @Column(name = "entity")
    private String            entity;

    @Column(name = "userIp")
    private String            userIp;

    @Column(name = "revType")
    private Integer           revType;

    @Column(name = "aciklama")
    private String            aciklama;

    /*-------------------------------------------------------------------
     *                          CONSTRUCTORS
     *-------------------------------------------------------------------*/
    /**
     * 
     * @param id
     */
    public AuditSuper() {

        this.entity = null;
    }

    /**
     * 
     *
     * @param entity
     */
    public AuditSuper(final String entity) {

        this.entity = entity;
    }

    /*-------------------------------------------------------------------
     *                      GETTERS AND SETTERS
     *-------------------------------------------------------------------*/
    /**
     *
     * @return the id
     */
    @Override
    public Long getId() {
        return this.id;
    }

    /**
     *
     * @param id the id to set
     */
    public void setId(final long id) {
        this.id = id;
    }

    /**
     *
     * @return the timestamp
     */
    public Long getTimestamp() {
        return this.timestamp;
    }

    /**
     *
     * @param timestamp the timestamp to set
     */
    public void setTimestamp(final Long timestamp) {
        this.timestamp = timestamp;
    }

    /**
     *
     * @return the userId
     */
    public String getUserName() {
        return this.userName;
    }

    /**
     *
     * @param userId the userId to set
     */
    public void setUserName(final String userId) {
        this.userName = userId;
    }

    // ---Metadata
    /**
     *
     * @return the entity
     */
    public String getEntity() {
        return this.entity;
    }

    /**
     *
     * @param entity the entity to set
     */
    public void setEntity(final String entity) {
        this.entity = entity;
    }

    public String getUserIp() {
        return this.userIp;
    }

    public void setUserIp(final String userIp) {
        this.userIp = userIp;
    }

    public Integer getRevType() {
        return this.revType;
    }

    public void setRevType(final Integer objectId) {
        this.revType = objectId;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Transient
    public Date getRevisionDate() {
        return new Date(this.timestamp);
    }

    /*-------------------------------------------------------------------
     *                           BEHAVIORS
     *-------------------------------------------------------------------*/

    /*
     * (non-Javadoc)
     * @see java.lang.Object#hashCode()
     */
    @Override
    public int hashCode() {

        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((this.entity == null) ? 0 : this.entity.hashCode());
        result = (prime * result) + (int) (this.id ^ (this.id >>> 32));
        result = (prime * result) + (int) (this.timestamp ^ (this.timestamp >>> 32));
        result = (prime * result) + ((this.userName == null) ? 0 : this.userName.hashCode());
        return result;
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final AuditSuper other = (AuditSuper) obj;
        if (this.entity == null) {
            if (other.entity != null) {
                return false;
            }
        } else if (!this.entity.equals(other.entity)) {
            return false;
        }
        if (!Objects.equals(this.id, other.id)) {
            return false;
        }
        if (this.timestamp != other.timestamp) {
            return false;
        }
        if (this.userName == null) {
            if (other.userName != null) {
                return false;
            }
        } else if (!this.userName.equals(other.userName)) {
            return false;
        }
        return true;
    }

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public void setSilinmis(final Boolean b) {
        // TODO Auto-generated method stub

    }

    @Override
    public void setAktif(final Boolean b) {
        // TODO Auto-generated method stub

    }

    @Override
    public Boolean getAktif() {
        throw new NotImplementedException("getAktif");
    }

}
