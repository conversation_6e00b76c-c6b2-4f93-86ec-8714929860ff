package tr.gov.tubitak.bte.mues.model.mapping;

public class EserTipiBazli {

    private Integer mudurlukId;

    private String  mudurluk;

    private String  fotografPath;

    private Integer eserTipiId;

    private String  eserTipi;

    private Integer artifactsPendingApprovalCount;

    private Integer artifactsDraftCount;

    private Integer artifactsRejectedCount;

    private Integer eserSayisi;

    public EserTipiBazli(final Integer mudurlukId,
                         final String mudurluk,
                         final String fotografPath,
                         final Integer eserTipiId,
                         final String eserTipi,
                         final Integer eserSayisi,
                         final Integer artifactsPendingApprovalCount,
                         final Integer artifactsDraftCount,
                         final Integer artifactsRejectedCount) {
        this.mudurlukId = mudurlukId;
        this.mudurluk = mudurluk;
        this.fotografPath = fotografPath;
        this.eserTipiId = eserTipiId;
        this.eserTipi = eserTipi;
        this.eserSayisi = eserSayisi;
        this.artifactsPendingApprovalCount = artifactsPendingApprovalCount;
        this.artifactsDraftCount = artifactsDraftCount;
        this.artifactsRejectedCount = artifactsRejectedCount;
    }

    // getters and setters ....................................................

    public Integer getMudurlukId() {
        return this.mudurlukId;
    }

    public void setMudurlukId(final Integer mudurlukId) {
        this.mudurlukId = mudurlukId;
    }

    public void setMudurluk(final String mudurluk) {
        this.mudurluk = mudurluk;
    }

    public String getMudurluk() {
        return this.mudurluk;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setEserTipi(final String eserTipi) {
        this.eserTipi = eserTipi;
    }

    public String getEserTipi() {
        return this.eserTipi;
    }

    public void setEserSayisi(final Integer eserSayisi) {
        this.eserSayisi = eserSayisi;
    }

    public Integer getEserSayisi() {
        return this.eserSayisi;
    }

    public Integer getEserTipiId() {
        return this.eserTipiId;
    }

    public void setEserTipiId(final Integer eserTipiId) {
        this.eserTipiId = eserTipiId;
    }

    public Integer getArtifactsPendingApprovalCount() {
        return this.artifactsPendingApprovalCount;
    }

    public void setArtifactsPendingApprovalCount(final Integer artifactsPendingApprovalCount) {
        this.artifactsPendingApprovalCount = artifactsPendingApprovalCount;
    }

    public Integer getArtifactsDraftCount() {
        return this.artifactsDraftCount;
    }

    public void setArtifactsDraftCount(final Integer artifactsDraftCount) {
        this.artifactsDraftCount = artifactsDraftCount;
    }

    public Integer getArtifactsRejectedCount() {
        return this.artifactsRejectedCount;
    }

    public void setArtifactsRejectedCount(final Integer artifactsRejectedCount) {
        this.artifactsRejectedCount = artifactsRejectedCount;
    }

}
