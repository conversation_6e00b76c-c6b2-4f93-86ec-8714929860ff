/*TUBITAK-BILGEM BTE,Gebze-Kocaeli,2017©*/
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.ResourceBundle;

import javax.inject.Inject;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import org.primefaces.PrimeFaces;

import tr.gov.tubitak.bte.mues.constraint.ValidArtifactJoinable;
import tr.gov.tubitak.bte.mues.model.ArtifactJoinable;

/***
 * 
*
 *
 */
public class ValidArtifactJoinableValidator implements ConstraintValidator<ValidArtifactJoinable, ArtifactJoinable> {

    @Inject
    private ResourceBundle bundle;

    public ValidArtifactJoinableValidator() {
        // default consructor
    }

    /* (non-Javadoc)
    * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
    */
    @Override
    public void initialize(final ValidArtifactJoinable constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final ArtifactJoinable artifactJoinable, final ConstraintValidatorContext context) {
        boolean result = true;
        if (artifactJoinable == null) {
            return true;
        }
        if ((artifactJoinable.getCombinedArtifacts() == null) || (artifactJoinable.getCombinedArtifacts().size() < 2)) {
            this.raiseFlag(this.bundle.getString("valid.eser.combine.cardinal"), context);
            this.addCssErrorClassToComponent("formBirlestirmeEdit:joinedArtifacts");
            this.addCssErrorClassToComponent("formEditor:groupedArtifacts");
            result = false;
        }
        return result;
    }

    private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }
    
    private void addCssErrorClassToComponent(final String componentId) {
        PrimeFaces.current().executeScript("$(PrimeFaces.escapeClientId(\"" + componentId + "\")).addClass('required-input-field ui-state-error')");
    }

}
