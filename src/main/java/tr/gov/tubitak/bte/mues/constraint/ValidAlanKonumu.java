package tr.gov.tubitak.bte.mues.constraint;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;
import javax.validation.ReportAsSingleViolation;

import tr.gov.tubitak.bte.mues.constraint.validator.AlanKonumuValidator;

@ReportAsSingleViolation
@Constraint(validatedBy = { AlanKonumuValidator.class })
@Documented
@Target({ ElementType.TYPE, ElementType.METHOD, ElementType.FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidAlanKonumu {
	String message() default "{tr.gov.tubitak.bte.mues.constraint.ValidAlanKonumu}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
