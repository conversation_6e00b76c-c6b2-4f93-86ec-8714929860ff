
package tr.gov.tubitak.bte.mues.search;

import java.util.List;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Metadata;

/**
 * <AUTHOR>
 * 
 *         <PERSON><PERSON> kriterlerinin tutulduğu, SEARCH_CRITERIA tablosuyla eşleştirilmiş model sınıfı .
 *
 */
@Audited
@MappedSuperclass
@DiscriminatorColumn(discriminatorType = DiscriminatorType.INTEGER, name = "criterionType")
public abstract class AbstractCriterionSuper extends AbstractEntity implements ICriterion {

    private static final long         serialVersionUID   = 5050448009701860228L;

    @Column(name = "rowId")
    private Integer                   rowId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "metadataId", referencedColumnName = "ID")
    private Metadata                  metadata;

    @ManyToOne
    @JoinColumn(name = "searchId", referencedColumnName = "ID")
    private SearchCriterionDefinition searchSearchCriterionDefinition;

    @Column(name = "comparisonOperator")
    private ComparisonOperatorEnum    comparisonOperator = ComparisonOperatorEnum.NOP;

    @Column(name = "criterionType", updatable = false, insertable = false)
    private Integer                   criterionType;

    @Transient
    private ICriterion                parentCriterion;

    @Transient
    private List<ICriterion>          children;

    /**
     * Yapıcı metot.
     */
    public AbstractCriterionSuper() {
        // default constructor
    }

    // getters and setters ....................................................

    public int getComparisonOperator() {
        return this.getComparisonOperatorEnum().getCode();
    }

    /**
     * Kıyaslama operatörünü döner.
     *
     * @return kıyaslama operatörü
     */

    public ComparisonOperatorEnum getComparisonOperatorEnum() {
        if (this.comparisonOperator != null) {
            return this.comparisonOperator;
        }
        return ComparisonOperatorEnum.NOP;
    }

    public void setComparisonOperator(final int comparisonOperator) {
        this.comparisonOperator = ComparisonOperatorEnum.parse(comparisonOperator);
    }

    public void setComparisonOperator(final ComparisonOperatorEnum comparisonOperator) {
        this.comparisonOperator = comparisonOperator;
    }

    public List<ICriterion> getChildren() {
        return this.children;
    }

    @Override
    public void setChildren(final List<ICriterion> children) {
        this.children = children;
    }

    /**
     * Kriter sıra numarasını döner.
     *
     * @return sıra numarası
     */

    public Integer getRowId() {
        return this.rowId;
    }

    /**
     * Kriter sıra numarasını atar.
     *
     * @param rowId sıra numarası
     */
    @Override
    public void setRowId(final Integer rowId) {
        this.rowId = rowId;
    }

    @Override
    public Metadata getMetadata() {
        return this.metadata;
    }

    public void setMetadata(final Metadata metadata) {
        this.metadata = metadata;
    }

    /**
     * Gets the search searchSearchCriterionDefinition.
     *
     * @return the search searchSearchCriterionDefinition
     */
    public SearchCriterionDefinition getSearchCriterionDefinition() {
        return this.searchSearchCriterionDefinition;
    }

    @Override
    public void setSearchCriterionDefinition(final SearchCriterionDefinition searchCriterionDefinition) {
        this.searchSearchCriterionDefinition = searchCriterionDefinition;
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj == null) || (this.getClass() != obj.getClass())) {
            return false;
        }
        final AbstractCriterionSuper other = (AbstractCriterionSuper) obj;

        if (!Objects.equals(this.comparisonOperator, other.comparisonOperator) || !Objects.equals(this.criterionType, other.criterionType)) {
            return false;
        }
        if (!Objects.equals(this.metadata, other.metadata)) {
            return false;
        }

        if (!Objects.equals(this.parentCriterion, other.parentCriterion)) {
            return false;
        }
        if (!this.getText().equals(other.getText())) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.comparisonOperator, this.criterionType, this.metadata, this.parentCriterion);
    }

    public ICriterion getParentCriterion() {
        return this.parentCriterion;
    }

    public void setParentCriterion(final ICriterion parentCriterion) {
        this.parentCriterion = parentCriterion;
    }

}
