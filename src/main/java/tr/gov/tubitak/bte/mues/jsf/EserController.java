package tr.gov.tubitak.bte.mues.jsf;

import java.io.UncheckedIOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.ComponentSystemEvent;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.EntityNotFoundException;

import org.hibernate.LazyInitializationException;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.omnifaces.cdi.ViewScoped;
import org.omnifaces.util.Faces;
import org.primefaces.PrimeFaces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.LazyDataModel;
import org.primefaces.shaded.json.JSONObject;

import tr.gov.tubitak.bte.mues.jsf.util.EserMediaController;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.AlanKonumu;
import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.EserAtolye;
import tr.gov.tubitak.bte.mues.model.EserCizim;
import tr.gov.tubitak.bte.mues.model.EserDepo;
import tr.gov.tubitak.bte.mues.model.EserFotograf;
import tr.gov.tubitak.bte.mues.model.EserHareket;
import tr.gov.tubitak.bte.mues.model.EserKaynakLiteratur;
import tr.gov.tubitak.bte.mues.model.EserKeyword;
import tr.gov.tubitak.bte.mues.model.EserMalzemeSuslemeTeknigi;
import tr.gov.tubitak.bte.mues.model.EserMalzemeYapimTeknigi;
import tr.gov.tubitak.bte.mues.model.EserMeasure;
import tr.gov.tubitak.bte.mues.model.EserMedia;
import tr.gov.tubitak.bte.mues.model.EserSerh;
import tr.gov.tubitak.bte.mues.model.EserStil;
import tr.gov.tubitak.bte.mues.model.EserVersion;
import tr.gov.tubitak.bte.mues.model.EserYayinLiteratur;
import tr.gov.tubitak.bte.mues.model.EserZimmet;
import tr.gov.tubitak.bte.mues.model.Keyword;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.MuesPick;
import tr.gov.tubitak.bte.mues.model.SeparationEser;
import tr.gov.tubitak.bte.mues.model.Transcription;
import tr.gov.tubitak.bte.mues.model.UygarlikDonem;
import tr.gov.tubitak.bte.mues.model.Workflow;
import tr.gov.tubitak.bte.mues.model.mapping.TimeSpectrum;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyTimeSpectrumDataModel;
import tr.gov.tubitak.bte.mues.search.SearchQualifier;
import tr.gov.tubitak.bte.mues.search.controller.SearchController;
import tr.gov.tubitak.bte.mues.session.AuditFacade;
import tr.gov.tubitak.bte.mues.session.EserFacade;
import tr.gov.tubitak.bte.mues.session.SeparationEserFacade;
import tr.gov.tubitak.bte.mues.session.TimeSpectrumLazyLoadFacade;
import tr.gov.tubitak.bte.mues.session.WorkflowFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class EserController extends AbstractController<Eser> {

    private static final String                  ARTIFACT_DELETE_SUCCESS  = "artifact.delete.success";

    private static final String                  ARTIFACT_DELETE_FAIL     = "artifact.delete.fail";

    private static final long                    serialVersionUID         = 4331249019200112541L;

    @Inject
    private transient AuditFacade                auditFacade;

    @Inject
    private transient EserFacade                 facade;

    @Inject
    private transient SeparationEserFacade       separationEserFacade;

    @Inject
    private transient TimeSpectrumLazyLoadFacade timeSpectrumFacade;

    @Inject
    private transient ResourceBundle             bundle;

    @Inject
    private UygarlikDonemController              uygarlikDonemController;

    @Inject
    private WorkflowController                   workflowController;

    @Inject
    private KeywordController                    keywordController;

    @Inject
    private EserDepoController                   eserDepoController;

    @Inject
    private EserZimmetController                 eserZimmetController;

    @Inject
    private EserYayinLiteraturController         eserYayinLiteraturController;

    @Inject
    private EserKaynakLiteraturController        eserKaynakLiteraturController;

    @Inject
    private EserMeasureController                eserMeasureController;

    @Inject
    private EserKeywordController                eserKeywordController;

    @Inject
    private EserHareketController                eserHareketController;

    @Inject
    private EserMalzemeYapimTeknigiController    eserMalzemeYapimTeknigiController;

    @Inject
    private EserMalzemeSuslemeTeknigiController  eserMalzemeSuslemeTeknigiController;

    @Inject
    private EserSerhController                   eserSerhController;

    @Inject
    private FileUploadHelper                     fileUploadHelper;

    @Inject
    private EserFotografController               eserFotografController;

    @Inject
    private EserMediaController                  eserMediaController;

    @Inject
    private EserCizimController                  eserCizimController;

    @Inject
    private TranscriptionController              transcriptionController;

    @Inject
    private EserStilController                   eserStilController;

    @Inject
    private EserAtolyeController                 eserAtolyeController;

    @Inject
    private Parameters                           parameters;

    @Inject
    private MuesParameters                       muesParameters;

    @Inject
    private SessionBean                          sessionBean;

    @Inject
    @SearchQualifier
    private SearchController                     searchController;

    @Inject
    private BirlestirmeController                birlestirmeController;

    @Inject
    private WorkflowFacade                       workflowFacade;

    private Integer                              permanentId;

    private transient DataModel<TimeSpectrum>    lazyTimeSpectrumDataModel;

    private Integer                              mode;

    private Workflow                             workflow;

    private transient List<Object[]>             lastApprovedEser;

    private final List<Integer>                  approvedEserFotoIdList   = new ArrayList<>();

    private final List<Integer>                  approvedEserZimmetIdList = new ArrayList<>();

    private final List<Integer>                  approvedTransIdList      = new ArrayList<>();

    private final List<Integer>                  approvedKeywordIdList    = new ArrayList<>();

    private final List<Integer>                  approvedSerhIdList       = new ArrayList<>();

    private String                               separatedEserTitle;

    // end of global fields ..............................................

    public EserController() {
        super(Eser.class);
    }

    public void newRecordIfNew() {
        if (this.getModel() == null) {
            this.newRecord();
        }
        this.setItems(new ArrayList<>());
    }

    @Override
    public void newRecord() {
        super.newRecord();
        final EserDepo eserDepo = new EserDepo();
        final Eser lastEser = this.facade.getLastEserOfUserId(this.sessionBean.getCurrentUser().getId());

        this.retrieveMudurlukWithEserPermission(lastEser).ifPresentOrElse(mudurluk ->
            {
                eserDepo.setAlanKonumu(lastEser.getEserDepo().getAlanKonumu());
                this.eserDepoController.restoreFields(eserDepo);// set by user last record
            }, () -> this.eserDepoController.setModel(eserDepo));

        this.uygarlikDonemController.setModel(new UygarlikDonem());
        this.keywordController.setModel(new Keyword());
        this.transcriptionController.newRecord();
    }

    private Optional<Mudurluk> retrieveMudurlukWithEserPermission(final Eser lastEser) {

        return Optional.ofNullable(lastEser)
                       .map(Eser::getEserDepo)
                       .map(EserDepo::getAlanKonumu)
                       .map(AlanKonumu::getAlan)
                       .map(Alan::getBina)
                       .map(Bina::getBagliBirim)
                       .map(BagliBirim::getMudurluk)
                       .filter(mudurluk -> this.sessionBean.isPermittedMuseum("eser:ekle", mudurluk.getId()));
    }

    public List<Eser> findByNameAndMuseumDirectorate(final int mudurlukId) {
        return this.getFacade().findByNameAndMuseumDirectorate(mudurlukId);
    }

    public void addMedia() {
        if (this.getModel().getVideos() == null) {
            this.getModel().setVideos(new LinkedHashSet<>());
        }
        this.eserMediaController.getModel().setEser(this.getModel());
        this.getModel().getVideos().add(this.eserMediaController.getModel());
    }

    public void removeMedia(final EserMedia eserMedia) {
        this.getModel().getVideos().remove(eserMedia);
    }

    public void saveAsDraft() {

        this.logger.debug("Save as Draft {} ", this.getMode());

        this.handleEserCreationDetails();

        Workflow tempWorkflow = null;
        String messageDetail = null;

        if (this.getModel().getId() == null) {

            tempWorkflow = this.createEser(EserVersion.DRAFT);
            messageDetail = this.bundle.getString("artifact.create.success.draft");

        } else {
            tempWorkflow = this.updateEser();
            messageDetail = this.bundle.getString("artifact.update.success.draft");
        }

        if (tempWorkflow.getReviewEnum().isModifiable()) {
            tempWorkflow.setReviewEnum(tempWorkflow.getReviewEnum().getSelf());
            this.persistEser(tempWorkflow, this.bundle.getString("base.create.success"), messageDetail);
        } else {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_WARN, "Kayıt başarısız", "Eseri normal ya da onaylı güncelleyiniz!");
        }
    }

    public Integer eserCopy(final Integer eserId) {
        final Eser esrCopy = this.getFacade().findEagerById(eserId);
        this.setModel(new Eser());
        this.getModel().setAyrilmisEser(esrCopy.getAyrilmisEser());
        this.getModel().setBirlestirilmisEser(esrCopy.getBirlestirilmisEser());
        this.getModel().setDarpYeri(esrCopy.getDarpYeri());
        this.getModel().setEserOzelAdi(esrCopy.getEserOzelAdi());
        this.getModel().setGenelAciklama(esrCopy.getGenelAciklama());
        this.getModel().setKiymet(esrCopy.getKiymet());
        this.getModel().setKronolojiAciklama(esrCopy.getKronolojiAciklama());
        this.getModel().setSignumEnd(esrCopy.getSignumEnd());
        this.getModel().setSignumStart(esrCopy.getSignumStart());
        this.getModel().setSignumUretim(esrCopy.getSignumUretim());
        this.getModel().setSignumUretimEnd(esrCopy.getSignumUretimEnd());
        this.getModel().setSikke(esrCopy.isSikke());
        this.getModel().setTorenselDurumu(esrCopy.getTorenselDurumu());
        this.getModel().setUniklikDurumu(esrCopy.getUniklikDurumu());
        this.getModel().setUretimYili(esrCopy.getUretimYili());
        this.getModel().setUretimYiliEnd(esrCopy.getUretimYiliEnd());

        this.getModel().setEserAltTur(esrCopy.getEserAltTur());
        this.getModel().setKondisyonDurumu(esrCopy.getKondisyonDurumu());
        this.getModel().setKullanacakVip(esrCopy.getKullanacakVip());
        this.getModel().setKullananVip(esrCopy.getKullananVip());
        this.getModel().setTasinirMalYonKod(esrCopy.getTasinirMalYonKod());
        this.getModel().setUretimBolgesi(esrCopy.getUretimBolgesi());
        this.getModel().setUretimYeri(esrCopy.getUretimYeri());
        this.getModel().setYapanVip(esrCopy.getYapanVip());
        this.getModel().setYaptiranVip(esrCopy.getYaptiranVip());
        this.getModel().setYazmaBasmaSecimi(esrCopy.getYazmaBasmaSecimi());
        this.getModel().setElisiDokumaSecimi(esrCopy.getElisiDokumaSecimi());
        this.getModel().setIslamiGayriSecimi(esrCopy.getIslamiGayriSecimi());
        this.getModel().setSikkeDarpYonu(esrCopy.getSikkeDarpYonu());
        this.getModel().setBagislayanVip(esrCopy.getBagislayanVip());
        this.getModel().setIliskilendirme(esrCopy.getIliskilendirme());

        this.uygarlikDonemController.setModel(new UygarlikDonem());
        this.uygarlikDonemController.setCag(esrCopy.getCag());
        this.uygarlikDonemController.getModel().setDonem(esrCopy.getDonem());
        this.uygarlikDonemController.setHukumdar(esrCopy.getHukumdar());
        this.uygarlikDonemController.getModel().setUygarlik(esrCopy.getUygarlik());
        this.uygarlikDonemController.getModel().setTermStart(esrCopy.getTermStart());
        this.uygarlikDonemController.getModel().setTermEnd(esrCopy.getTermEnd());

        this.retrieveMudurlukWithEserPermission(esrCopy).ifPresent(mudurluk ->
            {
                esrCopy.getEserDepo().setId(null);
                this.eserDepoController.setModel(esrCopy.getEserDepo());

            });

        this.getModel().setEserAtolyes(new HashSet<>());
        esrCopy.getEserAtolyes().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserAtolyes().add(x);
            });

        this.getModel().setEserCizims(new HashSet<>());
        esrCopy.getEserCizims().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserCizims().add(x);
            });

        this.getModel().setEserKaynakLiteraturs(new HashSet<>());
        esrCopy.getEserKaynakLiteraturs().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserKaynakLiteraturs().add(x);
            });

        this.getModel().setEserKeywords(new HashSet<>());
        esrCopy.getEserKeywords().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserKeywords().add(x);
            });

        this.getModel().setEserMalzemeSuslemeTeknigis(new HashSet<>());

        esrCopy.getEserMalzemeSuslemeTeknigis().forEach(oldSusleme ->
            {
                final EserMalzemeSuslemeTeknigi newSusleme = new EserMalzemeSuslemeTeknigi();

                newSusleme.setMalzeme(oldSusleme.getMalzeme());
                newSusleme.setSuslemeTeknigi(oldSusleme.getSuslemeTeknigi());
                newSusleme.setAciklama(oldSusleme.getAciklama());
                newSusleme.setRenks(new HashSet<>(oldSusleme.getRenks()));
                newSusleme.setId(null);
                newSusleme.setEser(this.getModel());
                this.getModel().getEserMalzemeSuslemeTeknigis().add(newSusleme);
            });

        this.getModel().setEserMalzemeYapimTeknigis(new HashSet<>());

        esrCopy.getEserMalzemeYapimTeknigis().forEach(oldYapim ->
            {
                final EserMalzemeYapimTeknigi newYapim = new EserMalzemeYapimTeknigi();

                newYapim.setMalzeme(oldYapim.getMalzeme());
                newYapim.setYapimTeknigi(oldYapim.getYapimTeknigi());
                newYapim.setAciklama(oldYapim.getAciklama());

                newYapim.setRenks(new HashSet<>(oldYapim.getRenks()));

                newYapim.setId(null);

                newYapim.setEser(this.getModel());

                this.getModel().getEserMalzemeYapimTeknigis().add(newYapim);
            });

        this.getModel().setEserStils(new HashSet<>());
        esrCopy.getEserStils().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserStils().add(x);
            });

        this.getModel().setEserYayinLiteraturs(new HashSet<>());
        esrCopy.getEserYayinLiteraturs().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserYayinLiteraturs().add(x);
            });

        this.getModel().setEserZimmets(new HashSet<>());
        esrCopy.getEserZimmets().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getEserZimmets().add(x);
            });

        this.getModel().setTranscriptions(new HashSet<>());
        esrCopy.getTranscriptions().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                this.getModel().getTranscriptions().add(x);
            });

        this.getModel().setEserHarekets(new HashSet<>());
        esrCopy.getEserHarekets().forEach(x ->
            {
                x.setId(null);
                x.setEser(this.getModel());
                x.setTeslimEdenSahis(new HashSet<>());
                x.getTeslimEdenSahis().forEach(y ->
                    {
                        y.setId(null);
                        y.setEserHareket(x);
                        x.getTeslimEdenSahis().add(y);
                    });
                this.getModel().getEserHarekets().add(x);
            });

        this.handleEserCreationDetails();
        final Workflow tempWorkflow = this.createEser(EserVersion.DRAFT);
        final String messageDetail = "Eser Kopyası Oluşturuldu";

        if (tempWorkflow.getReviewEnum().isModifiable()) {
            tempWorkflow.setReviewEnum(tempWorkflow.getReviewEnum().getSelf());
            this.persistCopyEsers(tempWorkflow, "Eser Kopyası Oluşturuldu", messageDetail);
        } else {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_WARN, "Kopyalama Başarısız", " ");
        }
        this.auditFacade.log(AuditEvent.EserKopyalama, esrCopy.getId() + " Id'li eser kullanılarak " + tempWorkflow.getArtifact().getId() + " Id'li şablon eser oluşturuldu.");
        return tempWorkflow.getArtifact().getId();
    }

    public void persistCopyEsers(final Workflow workflow, final String messageSummary, final String messageDetail) {
        this.getModel().setUpdateInProgress(true);
        this.getModel().setVersiyon(workflow.getReview());
        workflow.setArtifact(this.getModel());
        final DBOperationResult dbOperationResult = this.persistEntities(this.getModel(), workflow);
        if (!dbOperationResult.isSuccess()) {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_WARN, "Kopyalama Başarısız", " ");
        }
    }

    public void submitAsFinal() {

        this.logger.debug("submitAsFinal {} ", this.getMode());

        this.handleEserCreationDetails();

        Workflow tempWorkflow = null;
        String messageDetail = null;
        String messageHeader = null;

        if (this.getModel().getId() == null) {
            tempWorkflow = this.createEser(EserVersion.DRAFT);
            messageDetail = this.bundle.getString("artifact.create.success.final");
            messageHeader = this.bundle.getString("base.create.success");

        } else {
            tempWorkflow = this.updateEser();
            messageDetail = this.bundle.getString("artifact.update.success.final");
            messageHeader = this.bundle.getString("base.update.success");
        }

        if (tempWorkflow.getReviewEnum().isModifiable()) {
            tempWorkflow.setReviewEnum(tempWorkflow.getReviewEnum().getNext());
            this.persistEser(tempWorkflow, messageHeader, messageDetail);
            PrimeFaces.current().executeScript("startDownload();");
        } else {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_WARN, "Kayıt başarısız", "Eseri normal ya da onaylı güncelleyiniz!");
        }
    }

    public Workflow createEser(final EserVersion version) {
        // artifact part
        this.getModel().setCreatedBy(this.sessionBean.getCurrentUser());
        this.getModel().setDateCreated(new Date());
        this.getModel().setUpdatedBy(this.sessionBean.getCurrentUser());
        this.getModel().setDateUpdated(new Date());
        this.getModel().setCreateSessionId(MuesUtil.fetchSessionId());

        // workflow part
        final Workflow tempWorkflow = this.workflowController.startWorkflow(this.getModel(), version);
        tempWorkflow.setModifier(this.sessionBean.getCurrentUser());
        tempWorkflow.setDateInitiated(new Date());
        tempWorkflow.setDateModified(new Date());
        tempWorkflow.setArtifact(this.getModel());

        return tempWorkflow;
    }

    private Workflow updateEser() {
        final Workflow tempWorkflow = this.facade.findByArtifact(this.getModel());
        if (this.mode != null) {
            tempWorkflow.setReview(this.mode);
        }
        this.getModel().setUpdatedBy(this.sessionBean.getCurrentUser());
        this.getModel().setDateUpdated(new Date());

        // workflow part
        tempWorkflow.setModifier(this.sessionBean.getCurrentUser());
        tempWorkflow.setDateModified(new Date());

        return tempWorkflow;
    }

    public void deleteEserWithSerh() {
        if (this.getModel().getEserSerhs() == null) {
            this.getModel().setEserSerhs(new LinkedHashSet<>());
        }

        this.eserSerhController.getModel().setEser(this.getModel());
        this.getModel().getEserSerhs().add(this.eserSerhController.getModel());
        this.getModel().setSilinmis(true);
        this.getModel().setUpdatedBy(this.sessionBean.getCurrentUser());
        this.getModel().setDateUpdated(new Date());

        final Workflow tempWorkflow = this.facade.findByArtifact(this.getModel());
        tempWorkflow.setModifier(this.sessionBean.getCurrentUser());
        tempWorkflow.setDateModified(new Date());
        tempWorkflow.setSilinmis(true);

        final List<AbstractEntity> entities = new ArrayList<>();
        this.eserSerhController.writeToPermanentFolder();
        entities.add(tempWorkflow);

        if (Boolean.TRUE.equals(this.getModel().getBirlestirilmisEser())) {
            // birlestirme yapilmis bir eser ise birlestirme islemini de siler
            this.birlestirmeController.fetchBirlestirmeByEser(this.getModel());
            this.birlestirmeController.getModel().setSilinmis(true);
            entities.add(this.birlestirmeController.getModel());
        }
        entities.add(this.getModel());

        MuesUtil.setSessionMapParameter("deleteDescriptionEser", "Silinen Eser Eski Envanter No: " + this.getModel().getEnvanterNo());

        if (this.facade.update(entities).isSuccess()) {
            this.logger.info("[deleteEserWithSerh] : {}", this.bundle.getString(ARTIFACT_DELETE_SUCCESS));
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_INFO, this.bundle.getString(ARTIFACT_DELETE_SUCCESS), this.getModel().getOneId());
        }

        MuesUtil.removeSessionMapParameter("deleteDescriptionEser");
    }

    public void deleteSelectedWorkflows() {

        final List<Integer> ids = this.workflowController.getSelectedWorkflows()
                                                         .stream()
                                                         .map(x -> x.getWorkflowId())
                                                         .collect(Collectors.toList());

        final StringBuilder successMessages = new StringBuilder();
        final StringBuilder failureMessages = new StringBuilder();

        this.workflowController.setSelectedWorkflows(null);

        // Process each workflow deletion
        for (final Integer id : ids) {
            final DBOperationResult result = this.deleteWorkflowById(id);
            // Using getModel().getOneId() assumes that setModel has updated the model for the current workflow.
            if (result.isSuccess()) {
                successMessages.append(this.getModel().getOneId()).append(" ");
                this.logger.info("[deleteSelectedWorkflows] {} deleted successfully", this.getModel().getOneId());
            } else {
                failureMessages.append(this.getModel().getOneId())
                               .append(" : ")
                               .append(result.getMessage())
                               .append("\n");
            }
        }

        if (successMessages.length() > 0) {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_INFO, this.bundle.getString(ARTIFACT_DELETE_SUCCESS), successMessages.toString());
        }
        if (failureMessages.length() > 0) {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_WARN, this.bundle.getString(ARTIFACT_DELETE_FAIL), failureMessages.toString());
        }

    }

    public void deleteDraftEserPermanently(final Integer id) {
        final DBOperationResult result = this.deleteWorkflowById(id);

        if (result.isSuccess()) {
            MuesUtil.showMessage(FacesMessage.SEVERITY_INFO,
                                 this.bundle.getString(ARTIFACT_DELETE_SUCCESS),
                                 this.getModel().getOneId());
            this.logger.info("[deleteDraftEserPermanently] {} deleted successfully", this.getModel().getOneId());
        } else {
            this.logger.info("[deleteDraftEserPermanently] Deletion failed for {}: {}",
                             this.getModel().getOneId(),
                             result.getMessage());
            MuesUtil.showMessage(FacesMessage.SEVERITY_WARN,
                                 this.bundle.getString(ARTIFACT_DELETE_FAIL),
                                 this.getModel().getOneId() + "\n" + result.getMessage());
        }
    }

    private DBOperationResult deleteWorkflowById(final Integer id) {

        // Retrieve the workflow using its id
        final Workflow workflowtemp = this.workflowFacade.findEagerById(id);

        this.setModel(this.facade.findEagerById(workflowtemp.getArtifact().getId()));

        final List<AbstractEntity> entities = new ArrayList<>();
        entities.add(workflowtemp);
        entities.add(this.getModel());

        if (Boolean.TRUE.equals(this.getModel().getAyrilmisEser())) {
            final SeparationEser separationEser = this.separationEserFacade.findByEserId(this.getModel().getId());
            entities.add(separationEser);
        }

        final List<String> deletePaths = this.prepairDeletePath();

        final DBOperationResult result = this.deletePermanently(entities);

        if (result.isSuccess()) {
            this.fileUploadHelper.deleteFilesPermanently(deletePaths);
        }

        return result;
    }

    private DBOperationResult deletePermanently(final List<AbstractEntity> entities) {
        return this.facade.deletePermanently(entities);
    }

    private List<String> prepairDeletePath() {
        //
        final List<String> filePaths = new ArrayList<>();

        if (this.getModel().getEserSerhs() != null) {
            this.getModel().getEserSerhs().stream().filter(x -> x.getDocPath() != null).forEach(x -> filePaths.addAll(this.eserSerhController.buildFilePathFromModel(x)));
        }
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().forEach(x -> filePaths.addAll(this.eserFotografController.buildFilePathFromModel(x)));
        }
        if (this.getModel().getEserCizims() != null) {
            this.getModel().getEserCizims().stream().filter(x -> x.getFotografPath() != null).forEach(x -> filePaths.addAll(this.eserCizimController.buildFilePathFromModel(x)));
        }
        if (this.getModel().getTranscriptions() != null) {
            this.getModel().getTranscriptions().stream().filter(x -> x.getTranscriptionPath() != null).forEach(x -> filePaths.addAll(this.transcriptionController.buildFilePathFromModel(x)));
        }

        return filePaths;
    }

    public void persistEser(final Workflow workflow, final String messageSummary, final String messageDetail) {
        // set muze mudurlugu

        workflow.setMudurluk(this.eserDepoController.getMudurluk());
        this.getModel().setMudurluk(this.eserDepoController.getMudurluk());

        // set eser as update in progress
        this.getModel().setUpdateInProgress(true);
        this.getModel().setVersiyon(workflow.getReview());
        workflow.setArtifact(this.getModel());
        final DBOperationResult dbOperationResult = this.persistEntities(this.getModel(), workflow);
        this.logger.info("[finishUpPersistencyOfArtifact] : {}", messageDetail);
        if (dbOperationResult.isSuccess()) {

            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_INFO, messageSummary, messageDetail);
            this.redirectToEserEditPage(this.getModel().getId());
        } else {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_INFO, dbOperationResult.getMessage(), dbOperationResult.getMessage());
            this.redirectToEserEditPage(this.getModel().getId());

        }
    }

    private void continueEserCreationFromAddingSerh() {
        if ((this.mode != null) && EserVersion.getCertifiedList().contains(this.mode)) {
            this.makeFileRelatedOperations();

            final Workflow tempWorkflow = this.facade.findByArtifact(this.getModel());
            tempWorkflow.setReview(this.mode);

            this.persistEser(tempWorkflow, "İşlem Başlatıldı", "Onaylı güncelleme işlemi başlatıldı.");
        }
    }

    private void restoreEserViaPermanentId() {
        this.uygarlikDonemController.setModel(new UygarlikDonem());
        if (this.getModel().getCag() != null) {
            this.uygarlikDonemController.setKronoloji(this.getModel().getCag().getKronoloji());
        }
        this.uygarlikDonemController.setCag(this.getModel().getCag());
        this.uygarlikDonemController.getModel().setDonem(this.getModel().getDonem());
        this.uygarlikDonemController.getModel().setUygarlik(this.getModel().getUygarlik());
        this.uygarlikDonemController.setHukumdar(this.getModel().getHukumdar());
        Optional.ofNullable(this.getModel().getTermStart()).ifPresent(x -> this.uygarlikDonemController.fixStartTerm(x));
        Optional.ofNullable(this.getModel().getTermEnd()).ifPresent(x -> this.uygarlikDonemController.fixEndTerm(x));

        // set ana foto
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().filter(EserFotograf::getAnaFotograf).forEach(x -> this.eserFotografController.setAnaFoto(x));
        }

        // set location variables
        if (this.getModel().getEserDepo() != null) {
            this.eserDepoController.restoreFields(this.getModel().getEserDepo());
        }

        this.workflow = this.facade.findByArtifact(this.getModel());
    }

    // modify .................................................................

    public void loadDataTable() {
        this.resetTable();
        this.lazyTimeSpectrumDataModel = new LazyTimeSpectrumDataModel(this.timeSpectrumFacade);
    }

    public void resetTable() {
        ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("donemHukumdarSecimiForm:donemHukumdarTableId")).reset();
    }

    /** Set artifact name from UI */
    public void fixEserOzelAd() {
        this.getModel().setEserOzelAdi(this.getModel().getEserAltTur().getTitle());
    }

    public void fixSikke() {
        if ((this.getModel() != null) && (this.getModel().getTasinirMalYonKod() != null)) {
            this.getModel().setSikke(this.parameters.get("eser.sikke.kod").equals(this.getModel().getTasinirMalYonKod().getKod()));
        }
    }

    // disablers ..............................................................

    public boolean disabledButton() {
        // Eser ilk kayıt sayfasında
        if (this.workflow == null) {
            return false;
        } else {
            // Eser hiç onaylanmamış
            if (this.getModel().getPermanentId() == null) {
                // Eser Onaya gönderilmiş

                return !this.workflow.getReviewEnum().isModifiable();

                // Eser Daha önce Onaylanmış
            } else {
                // Eser sertifikalı güncelle adımındaysa disabled false olmali
                return !EserVersion.getCertifiedList().contains(this.workflow.getReview());
            }
        }
    }

    public boolean disabledEditableButton() {
        // Eser ilk kayıt sayfasında
        if (this.workflow == null) {
            return false;
        } else {
            // Eser hiç onaylanmamış
            if (this.getModel().getPermanentId() == null) {
                // Eser Onaya gönderilmiş
                return !this.workflow.getReviewEnum().isModifiable();
                // Eser Daha önce Onaylanmış
            } else {
                // Eser sertifikalı güncelle adımında
                if (EserVersion.getCertifiedList().contains(this.workflow.getReview())) {
                    return false;
                }
                return !this.workflow.getReviewEnum().isRegular();
            }
        }
    }

    public boolean disabledItem(final Integer itemId) {
        return (itemId != null) && this.disabledButton();
    }

    public boolean disableEnvanterNo() {
        if (((this.getModel().getId() != null) & (this.getModel().getPermanentId() == null)) && (this.facade.checkImplementation(this.getModel().getId()) > 0)) {
            return true;
        }
        return false;
    }

    public boolean disabledFotografItem(final Integer itemId) {
        // itemID null kontrolü ilk eser kaydı yapılırken patlamaması için
        if ((itemId != null) && (this.workflow != null) && !EserVersion.getCertifiedList().contains(this.workflow.getReview())) {

            if (this.workflow.getReviewEnum().isModifiable()) {

                if (this.approvedEserFotoIdList.isEmpty()) {

                    this.historyOfEser(this.getModel());

                    if ((this.lastApprovedEser != null) && !this.lastApprovedEser.isEmpty() && (((Eser) this.lastApprovedEser.get(0)[0]).getEserFotografs() != null)) {
                        final Eser tmpEser = (Eser) this.lastApprovedEser.get(0)[0];

                        if (tmpEser.getEserFotografs() != null) {
                            for (final EserFotograf each : tmpEser.getEserFotografs()) {
                                for (final EserFotograf any : this.getModel().getEserFotografs()) {
                                    final Field[] lastWorkedFields = each.getClass().getDeclaredFields();
                                    final Field[] currFields = any.getClass().getDeclaredFields();
                                    if (this.isObjectsEqual(each, any, lastWorkedFields, currFields)) {
                                        this.approvedEserFotoIdList.add(each.getId());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                return this.approvedEserFotoIdList.contains(itemId);
            } else {
                return true;
            }
        }
        return false;
    }

    public List<Keyword> filterByNameAndKeyword(final String query) {
        final List<Keyword> keywords = new ArrayList<>();
        if (this.getModel().getEserKeywords() != null) {
            this.getModel().getEserKeywords().stream().forEach(x -> keywords.add(x.getKeyword()));
        }
        return this.eserKeywordController.filterByNameAndKeyword(query, MuesUtil.toIds(keywords));
    }

    public boolean disabledZimmetItem(final Integer itemId) {

        if ((itemId != null) && (this.workflow != null) && (!EserVersion.getCertifiedList().contains(this.workflow.getReview()))) {

            if (this.workflow.getReviewEnum().isModifiable()) {
                if (this.approvedEserZimmetIdList.isEmpty()) {
                    this.historyOfEser(this.getModel());
                    if ((this.lastApprovedEser != null) && !this.lastApprovedEser.isEmpty()) {
                        final Eser tmpEser = (Eser) this.lastApprovedEser.get(0)[0];

                        if (tmpEser.getEserZimmets() != null) {
                            for (final EserZimmet each : tmpEser.getEserZimmets()) {
                                for (final EserZimmet any : this.getModel().getEserZimmets()) {
                                    final Field[] lastWorkedFields = each.getClass().getDeclaredFields();
                                    final Field[] currEserFields = any.getClass().getDeclaredFields();
                                    if (this.isObjectsEqual(each, any, lastWorkedFields, currEserFields)) {
                                        this.approvedEserZimmetIdList.add(each.getId());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                return this.approvedEserZimmetIdList.contains(itemId);
            } else {
                return true;
            }
        }
        return false;

    }

    public boolean disabledEserMedia(final Integer itemId) {
        if ((itemId != null) && (this.workflow != null) && !EserVersion.getCertifiedList().contains(this.workflow.getReview())) {

            if (this.workflow.getReviewEnum().isModifiable()) {
                if (this.approvedTransIdList.isEmpty()) {
                    final List<Object[]> lastApprovalEser = this.historyOfEser(this.getModel());
                    if ((lastApprovalEser != null) && !lastApprovalEser.isEmpty()) {
                        final Eser tmpEser = (Eser) lastApprovalEser.get(0)[0];

                        if (tmpEser.getVideos() != null) {
                            for (final EserMedia each : tmpEser.getVideos()) {
                                for (final EserMedia any : this.getModel().getVideos()) {
                                    final Field[] lastWorkedFields = each.getClass().getDeclaredFields();
                                    final Field[] currFields = any.getClass().getDeclaredFields();
                                    if (this.isObjectsEqual(each, any, lastWorkedFields, currFields)) {
                                        this.approvedTransIdList.add(each.getId());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                return this.approvedTransIdList.contains(itemId);
            } else {
                return true;
            }
        }
        return false;
    }

    public boolean disabledTranscriptionItem(final Integer itemId) {
        if ((itemId != null) && (this.workflow != null) && !EserVersion.getCertifiedList().contains(this.workflow.getReview())) {

            if (this.workflow.getReviewEnum().isModifiable()) {
                if (this.approvedTransIdList.isEmpty()) {
                    final List<Object[]> lastApprovalEser = this.historyOfEser(this.getModel());
                    if ((lastApprovalEser != null) && !lastApprovalEser.isEmpty()) {
                        final Eser tmpEser = (Eser) lastApprovalEser.get(0)[0];

                        if (tmpEser.getTranscriptions() != null) {
                            for (final Transcription each : tmpEser.getTranscriptions()) {
                                for (final Transcription any : this.getModel().getTranscriptions()) {
                                    final Field[] lastWorkedFields = each.getClass().getDeclaredFields();
                                    final Field[] currFields = any.getClass().getDeclaredFields();
                                    if (this.isObjectsEqual(each, any, lastWorkedFields, currFields)) {
                                        this.approvedTransIdList.add(each.getId());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                return this.approvedTransIdList.contains(itemId);
            } else {
                return true;
            }
        }
        return false;
    }

    public boolean disabledKeywordItem(final Integer itemId) {
        if ((itemId != null) && (this.workflow != null) && !EserVersion.getCertifiedList().contains(this.workflow.getReview())) {

            if (this.workflow.getReviewEnum().isModifiable()) {
                if (this.approvedKeywordIdList.isEmpty()) {

                    this.historyOfEser(this.getModel());

                    if ((this.lastApprovedEser != null) && !this.lastApprovedEser.isEmpty()) {
                        final Eser tmpEser = (Eser) this.lastApprovedEser.get(0)[0];

                        if (tmpEser.getEserKeywords() != null) {
                            for (final EserKeyword each : tmpEser.getEserKeywords()) {
                                for (final EserKeyword any : this.getModel().getEserKeywords()) {
                                    final Field[] lastWorkedFields = each.getClass().getDeclaredFields();
                                    final Field[] currFields = any.getClass().getDeclaredFields();
                                    if (this.isObjectsEqual(each, any, lastWorkedFields, currFields)) {
                                        this.approvedKeywordIdList.add(each.getId());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                return this.approvedKeywordIdList.contains(itemId);
            } else {
                return true;
            }
        }
        return false;
    }

    public boolean disabledSerhItem(final Integer itemId) {
        if ((itemId != null) && (this.workflow != null) && !EserVersion.getCertifiedList().contains(this.workflow.getReview())) {

            if (this.workflow.getReviewEnum().isModifiable()) {
                if (this.approvedSerhIdList.isEmpty()) {
                    this.historyOfEser(this.getModel());
                    if ((this.lastApprovedEser != null) && !this.lastApprovedEser.isEmpty()) {
                        final Eser tmpEser = (Eser) this.lastApprovedEser.get(0)[0];

                        if (tmpEser.getEserSerhs() != null) {
                            for (final EserSerh each : tmpEser.getEserSerhs()) {
                                for (final EserSerh any : this.getModel().getEserSerhs()) {
                                    final Field[] lastWorkedFields = each.getClass().getDeclaredFields();
                                    final Field[] currFields = any.getClass().getDeclaredFields();
                                    if (this.isObjectsEqual(each, any, lastWorkedFields, currFields)) {
                                        this.approvedSerhIdList.add(each.getId());
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                return this.approvedSerhIdList.contains(itemId);
            } else {
                return true;
            }
        }
        return false;
    }

    private boolean isObjectsEqual(final Object lastObj, final Object currObj, final Field[] lastWorkedField, final Field[] currField) {
        int equalFlag = 0;
        for (int i = 0; i < lastWorkedField.length; i++) {
            lastWorkedField[i].setAccessible(true);
            currField[i].setAccessible(true);
            try {
                if ((lastWorkedField[i].get(lastObj) != null) && (currField[i].get(currObj) != null) && lastWorkedField[i].get(lastObj).equals(currField[i].get(currObj))) {
                    equalFlag++;
                }
                if ((lastWorkedField[i].get(lastObj) == null) && (currField[i].get(currObj) == null)) {
                    equalFlag++;
                }
            } catch (final IllegalArgumentException | IllegalAccessException | EntityNotFoundException | LazyInitializationException e) {
                this.logger.debug("Hata {}", e.getMessage());
                return false;
            }
        }

        return equalFlag == lastWorkedField.length;
    }

    // Envers history Function
    @SuppressWarnings("unchecked")
    public List<Object[]> historyOfEser(final Eser eser) {
        final Date lastWorkTime = this.workflow.getDateCompleted();

        if ((this.lastApprovedEser == null) && (lastWorkTime != null)) {
            this.lastApprovedEser = AuditReaderFactory.get(this.getFacade().getEM())
                                                      .createQuery()
                                                      .forRevisionsOfEntity(Eser.class, false, true)
                                                      .add(AuditEntity.id().eq(eser.getId()))
                                                      .add(AuditEntity.revisionProperty("timestamp").gt(lastWorkTime.getTime()))
                                                      .add(AuditEntity.revisionProperty("revType").eq(AuditEvent.EserGuncelleme.getCode()))
                                                      .addOrder(AuditEntity.revisionProperty("timestamp").asc())
                                                      .setMaxResults(1)
                                                      .getResultList();
        }

        return this.lastApprovedEser;
    }

    // handlers ...............................................................

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setEnvanterDefteriPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    protected void handleEserCreationDetails() {
        this.fixTimeRelatedFields();
        this.makeFileRelatedOperations();
        this.fixSikkeBeforePersist();

        if (this.getModel().getEserDepos() == null) {
            this.getModel().setEserDepos(new LinkedHashSet<>());
        }
        this.getModel().getEserDepos().add(this.eserDepoController.getModel());

        if (!MuesUtil.isEmptyOrNull(this.getModel().getEserDepos())) {
            this.getModel().getEserDepos().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (!MuesUtil.isEmptyOrNull(this.getModel().getEserZimmets())) {
            this.getModel().getEserZimmets().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserMalzemeYapimTeknigis() != null) {
            this.getModel().getEserMalzemeYapimTeknigis().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserMalzemeSuslemeTeknigis() != null) {
            this.getModel().getEserMalzemeSuslemeTeknigis().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserSerhs() != null) {
            this.getModel().getEserSerhs().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserHarekets() != null) {
            this.getModel().getEserHarekets().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserStils() != null) {
            this.getModel().getEserStils().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserAtolyes() != null) {
            this.getModel().getEserAtolyes().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserYayinLiteraturs() != null) {
            this.getModel().getEserYayinLiteraturs().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserKaynakLiteraturs() != null) {
            this.getModel().getEserKaynakLiteraturs().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserMeasures() != null) {
            this.getModel().getEserMeasures().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserKeywords() != null) {
            this.getModel().getEserKeywords().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getTranscriptions() != null) {
            this.getModel().getTranscriptions().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserCizims() != null) {
            this.getModel().getEserCizims().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().forEach(x -> x.setEser(this.getModel()));
            this.fixRestOfFotografStuff();
        }
    }

    private void fixRestOfFotografStuff() {
        if (this.getModel().getEserFotografs().size() == 1) {
            this.getModel().getEserFotografs().iterator().next().setAnaFotograf(true);

        } else if (this.getModel().getEserFotografs().size() > 1) {
            if (this.eserFotografController.getAnaFoto() == null) {
                MuesUtil.showMessageWithClientId(":eserForm:eserFotografDataTable", this.bundle.getString("file.photo.main.mark"));

            } else {
                this.getModel().getEserFotografs().stream().forEach(x ->
                    {
                        x.setAnaFotograf(false);
                        if (x.equals(this.eserFotografController.getAnaFoto())) {
                            x.setAnaFotograf(true);
                        }
                    });
            }
        }
    }

    // adders .................................................................

    public void addSerh() {
        if (this.getModel().getEserSerhs() == null) {
            this.getModel().setEserSerhs(new LinkedHashSet<>());
        }

        this.eserSerhController.getModel().setEser(this.getModel());
        this.getModel().getEserSerhs().add(this.eserSerhController.getModel());

        this.continueEserCreationFromAddingSerh();
    }

    public Eser fetchEserById(final int id) {
        return this.facade.findEagerById(id);
    }

    public void addKeyword() {
        if (this.getModel().getEserKeywords() == null) {
            this.getModel().setEserKeywords(new LinkedHashSet<>());
        }
        this.getModel().getEserKeywords().add(this.eserKeywordController.getModel());
    }

    public void addMeasure() {
        if (this.getModel().getEserMeasures() == null) {
            this.getModel().setEserMeasures(new LinkedHashSet<>());
        }
        this.updateMeasureTag(this.eserMeasureController.getModel());
        this.getModel().getEserMeasures().add(this.eserMeasureController.getModel());
    }

    public void addMeasureList() {
        if (this.getModel().getEserMeasures() == null) {
            this.getModel().setEserMeasures(new LinkedHashSet<>());
        }
        this.getModel().getEserMeasures().addAll(this.eserMeasureController.getSelectedEserMeasures());
        this.eserMeasureController.resetInputFieldsLists();
    }

    public void addZimmetList() {
        if (this.getModel().getEserZimmets() == null) {
            this.getModel().setEserZimmets(new LinkedHashSet<>());
        }
        this.getModel().getEserZimmets().addAll(this.eserZimmetController.getSelectionList());
    }

    public void addStilList() {
        if (this.getModel().getEserStils() == null) {
            this.getModel().setEserStils(new LinkedHashSet<>());
        }
        this.getModel().getEserStils().addAll(this.eserStilController.getSelectedEserStils());
    }

    /**
     * Updates the measure tag for the annotation-dialog
     * 
     * @param measure
     */
    public void updateMeasureTag(final EserMeasure measure) {
        if ((measure.getAnnotation() != null) && !measure.getAnnotation().getJson().isEmpty()) {
            final JSONObject json = new JSONObject(measure.getAnnotation().getJson());
            final JSONObject jsonBody = json.getJSONArray("body").getJSONObject(0);
            jsonBody.put("value", measure.getMeasure().getType() + ": " + measure.getDeger() + " " + measure.getMeasure());
            measure.getAnnotation().setJson(json.toString());
        }
    }

    public void addKaynak() {
        if (this.getModel().getEserKaynakLiteraturs() == null) {
            this.getModel().setEserKaynakLiteraturs(new LinkedHashSet<>());
        }
        this.getModel().getEserKaynakLiteraturs().add(this.eserKaynakLiteraturController.getModel());
    }

    public void addYayin() {
        if (this.getModel().getEserYayinLiteraturs() == null) {
            this.getModel().setEserYayinLiteraturs(new LinkedHashSet<>());
        }
        this.getModel().getEserYayinLiteraturs().add(this.eserYayinLiteraturController.getModel());
    }

    public void addFotograf() {
        if (this.getModel().getEserFotografs() == null) {
            this.getModel().setEserFotografs(new LinkedHashSet<>());
        }
        if (this.getModel().getEserFotografs().isEmpty()) {
            this.eserFotografController.setAnaFoto(this.eserFotografController.getModel());
        }
        this.getModel().getEserFotografs().add(this.eserFotografController.getModel());
    }

    public void addMultiPhoto() {
        if (this.getModel().getEserFotografs() == null) {
            this.getModel().setEserFotografs(new LinkedHashSet<>());
        }

        for (final EserFotograf photo : this.eserFotografController.getItems()) {
            if (this.getModel().getEserFotografs().isEmpty()) {
                this.eserFotografController.setAnaFoto(photo);
            }
            photo.setEser(this.getModel());
            this.getModel().getEserFotografs().add(photo);
        }
        this.eserFotografController.getItems().clear();
    }

    public void addCizim() {
        if (this.getModel().getEserCizims() == null) {
            this.getModel().setEserCizims(new LinkedHashSet<>());
        }
        this.getModel().getEserCizims().add(this.eserCizimController.getModel());
    }

    public void addMultiCizim() {
        if (this.getModel().getEserCizims() == null) {
            this.getModel().setEserCizims(new LinkedHashSet<>());
        }

        for (final EserCizim photo : this.eserCizimController.getItems()) {
            photo.setEser(this.getModel());
            this.getModel().getEserCizims().add(photo);
        }
        this.eserCizimController.getItems().clear();
    }

    public void addTranscription() {
        if (this.getModel().getTranscriptions() == null) {
            this.getModel().setTranscriptions(new LinkedHashSet<>());
        }
        this.getModel().getTranscriptions().add(this.transcriptionController.getModel());
    }

    public void addMalzemeYapimTeknigi() {
        if (this.getModel().getEserMalzemeYapimTeknigis() == null) {
            this.getModel().setEserMalzemeYapimTeknigis(new LinkedHashSet<>());
        }
        this.getModel().getEserMalzemeYapimTeknigis().add(this.eserMalzemeYapimTeknigiController.getModel());
    }

    public void addYapimTeknigiList() {
        if (this.getModel().getEserMalzemeYapimTeknigis() == null) {
            this.getModel().setEserMalzemeYapimTeknigis(new LinkedHashSet<>());
        }
        for (final EserMalzemeYapimTeknigi eserMalzemeYapimTeknigi : this.eserMalzemeYapimTeknigiController.getSelectedEserMalzemeYapimTeknigis()) {
            eserMalzemeYapimTeknigi.setEser(this.getModel());
            this.getModel().getEserMalzemeYapimTeknigis().add(eserMalzemeYapimTeknigi);
        }
        this.eserMalzemeYapimTeknigiController.resetInputFieldsLists();
    }

    public void addMalzemeSuslemeTeknigi() {
        if (this.getModel().getEserMalzemeSuslemeTeknigis() == null) {
            this.getModel().setEserMalzemeSuslemeTeknigis(new LinkedHashSet<>());
        }
        this.getModel().getEserMalzemeSuslemeTeknigis().add(this.eserMalzemeSuslemeTeknigiController.getModel());
    }

    public void addSuslemeTeknigiList() {
        if (this.getModel().getEserMalzemeSuslemeTeknigis() == null) {
            this.getModel().setEserMalzemeSuslemeTeknigis(new LinkedHashSet<>());
        }
        for (final EserMalzemeSuslemeTeknigi eserMalzemeSuslemeTeknigi : this.eserMalzemeSuslemeTeknigiController.getSelectedEserMalzemeSuslemeTeknigis()) {
            eserMalzemeSuslemeTeknigi.setEser(this.getModel());
            this.getModel().getEserMalzemeSuslemeTeknigis().add(eserMalzemeSuslemeTeknigi);
        }
        this.eserMalzemeSuslemeTeknigiController.resetInputFieldsLists();
    }

    public void addHareket() {
        if (this.getModel().getEserHarekets() == null) {
            this.getModel().setEserHarekets(new LinkedHashSet<>());
        }

        // yeni yapiyla eserHareketSahis kaydi icin
        this.eserHareketController.addEserHareketSahis();

        this.getModel().getEserHarekets().add(this.eserHareketController.getModel());
    }

    public void addStil() {
        if (this.getModel().getEserStils() == null) {
            this.getModel().setEserStils(new LinkedHashSet<>());
        }
        this.getModel().getEserStils().add(this.eserStilController.getModel());
    }

    public void addAtolye() {
        if (this.getModel().getEserAtolyes() == null) {
            this.getModel().setEserAtolyes(new LinkedHashSet<>());
        }
        this.getModel().getEserAtolyes().add(this.eserAtolyeController.getModel());
    }

    public void addZimmet() {
        if (this.getModel().getEserZimmets() == null) {
            this.getModel().setEserZimmets(new LinkedHashSet<>());
        }
        this.getModel().getEserZimmets().add(this.eserZimmetController.getModel());
    }

    // removers ...............................................................

    public void removeKeyword(final EserKeyword keyword) {
        this.getModel().getEserKeywords().remove(keyword);
    }

    public void removeMeasure(final EserMeasure measure) {
        this.getModel().getEserMeasures().remove(measure);
    }

    public void removeKaynak(final EserKaynakLiteratur literatur) {
        this.getModel().getEserKaynakLiteraturs().remove(literatur);
    }

    public void removeYayin(final EserYayinLiteratur yayin) {
        this.getModel().getEserYayinLiteraturs().remove(yayin);
    }

    public void removeSerh(final EserSerh serh) {
        this.getModel().getEserSerhs().remove(serh);
    }

    public void removeFotograf(final EserFotograf eserFotograf) {

        Optional.ofNullable(this.getModel().getEserMeasures())
                .ifPresent(measures ->
                    {
                        final boolean anyMatch = measures.stream()
                                                         .anyMatch(x -> (x.getAnnotation() != null)
                                                                        && x.getAnnotation().getEserFotograf().getFotografBasligi().equals(eserFotograf.getFotografBasligi()));

                        if (anyMatch) {
                            MuesUtil.showMessage("Fotografa ile ilgili ölçü çizimleri mevcuttur, önce onların silinmesi gerekir.", FacesMessage.SEVERITY_WARN);
                            return;

                        }
                        // Further processing based on the result...
                    });

        this.getModel().getEserFotografs().remove(eserFotograf);
        if (eserFotograf.equals(this.eserFotografController.getAnaFoto())) {

            if (this.getModel().getEserFotografs().isEmpty()) {
                this.eserFotografController.setAnaFoto(null);
            } else {
                final EserFotograf next = this.getModel().getEserFotografs().iterator().next();
                next.setAnaFotograf(true);
                this.eserFotografController.setAnaFoto(next);
            }
        }
    }

    public void removeCizim(final EserCizim eserCizim) {
        this.getModel().getEserCizims().remove(eserCizim);
    }

    public void removeTranscription(final Transcription eserTranscription) {
        this.getModel().getTranscriptions().remove(eserTranscription);
    }

    public void removeMalzemeYapimTeknigi(final EserMalzemeYapimTeknigi eserMalzemeYapimTeknigi) {
        this.getModel().getEserMalzemeYapimTeknigis().remove(eserMalzemeYapimTeknigi);
    }

    public void removeMalzemeSuslemeTeknigi(final EserMalzemeSuslemeTeknigi eserMalzemeSuslemeTeknigi) {
        this.getModel().getEserMalzemeSuslemeTeknigis().remove(eserMalzemeSuslemeTeknigi);
    }

    public void removeHareket(final EserHareket eserHareket) {
        this.getModel().getEserHarekets().remove(eserHareket);
    }

    public void removeStil(final EserStil eserStil) {
        this.getModel().getEserStils().remove(eserStil);
    }

    public void removeAtolye(final EserAtolye eserAtolye) {
        this.getModel().getEserAtolyes().remove(eserAtolye);
    }

    public void removeZimmetPerson(final EserZimmet eserZimmet) {
        this.getModel().getEserZimmets().remove(eserZimmet);
    }

    // utilities ...............................................................

    public void checkPermissibleArtifact(final ComponentSystemEvent event) {
        if ((this.getModel() != null)
            && !Objects.equals(this.getModel().getVersiyon(), EserVersion.APPROVED.getCode())
            && !Objects.equals(this.getModel().getVersiyon(), EserVersion.MUSEUM_TRANSFER_RECEIVER_APPROVED.getCode())
            && (this.getModel().getPermanentId() != null)) {
            this.searchController.checkPermissibleArtifact(event);
        }
    }

    public void validateTerms() {
        if (this.getModel().getSignificantUretim() == null) {
            return;
        }
        if (this.getModel().getSignumUretim() == 3) {
            if (-this.getModel().getSignificantUretim() >= Parameters.getPrehistoryThreshold()) {
                this.getModel().setSignumUretim(2);
            }
        } else if ((this.getModel().getSignumUretim() == 2) && (-this.getModel().getSignificantUretim() < Parameters.getPrehistoryThreshold())) {

            this.getModel().setSignumUretim(3);

        }

        final Integer prodYear = this.toTimeline(this.getModel().getSignumUretim(), this.getModel().getSignificantUretim());
        if (prodYear > MuesUtil.getThisYear()) {
            this.getModel().setUretimYili(MuesUtil.getThisYear());
            MuesUtil.showMessage(":eserForm:uretimYili", this.bundle.getString("base.warning"), "Darp Başlangıç Yılı Gelecekte Olamaz.", FacesMessage.SEVERITY_WARN);
        }
    }

    public void validateEnvanterNoTerms() {

        if ((this.getModel().getEnvanterNo() != null) && !this.getModel().getEnvanterNo().isEmpty()) {
            final List<Workflow> list = this.workflowFacade.findByEnvanterNoAndMudurluk(this.getModel(), this.eserDepoController.getMudurluk());
            if (!list.isEmpty()) {
                MuesUtil.showMessage(this.bundle.getString("base.warning"), this.bundle.getString("valid.eser.envanterNo"), FacesMessage.SEVERITY_WARN);
                this.getModel().setEnvanterNo("");
            }
        }
    }

    public boolean disableDarpYiliEndInputs() {
        // darp yılı başlangıcı seçilmeden, bitiş üzerinde seçimler yapılamaz
        return ((this.getModel().getSignumUretim() == null) || (this.getModel().getSignificantUretim() == null));
    }

    public void validateDarpYiliTerms() {

        if ((this.getModel().getSignumUretimEnd() != null) && (this.getModel().getSignificantUretimEnd() != null) && (this.getModel().getSignumUretimEnd() == 0)) {
            // darp yılı bitiş tarihini önce seçmiş sonra vazgeçmiş olabilir.
            this.getModel().setSignumUretimEnd(0);
            this.getModel().setSignificantUretimEnd(null);
        }

        if ((this.getModel().getSignumUretim() != null)
            && (this.getModel().getSignumUretimEnd() != null)
            && (this.getModel().getSignificantUretim() != null)
            && (this.getModel().getSignificantUretimEnd() != null)) {

            final Integer startYear = this.toTimeline(this.getModel().getSignumUretim(), this.getModel().getSignificantUretim());
            final Integer endYear = this.toTimeline(this.getModel().getSignumUretimEnd(), this.getModel().getSignificantUretimEnd());

            if ((startYear > endYear) || (startYear > MuesUtil.getThisYear())) {
                this.fireMessage("Başlangıç Tarihi Bitiş Tarininden Büyük Olamaz.");
                this.getModel().setSignumUretimEnd(0);
                this.getModel().setSignificantUretimEnd(null);
            }

        }
    }

    private void fireMessage(final String message) {
        MuesUtil.showMessage(":eserForm:uretimYiliEnd", this.bundle.getString("base.warning"), message, FacesMessage.SEVERITY_WARN);
    }

    private int toTimeline(final Integer signum, final int date) {
        if ((signum == 2) || (signum == 3)) {
            return -date;
        }
        return date;
    }

    private void makeFileRelatedOperations() {
        // copy temp file to the exact location and update model with that location
        if (this.getModel().getEnvanterDefteriPath() != null) {
            this.getModel().setEnvanterDefteriPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getEnvanterDefteriPath(), FolderType.INVENTORY));
        }
        if (this.getModel().getEserSerhs() != null) {
            this.getModel().getEserSerhs().stream().forEach(x -> this.eserSerhController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().forEach(x -> this.eserFotografController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getEserCizims() != null) {
            this.getModel().getEserCizims().stream().forEach(x -> this.eserCizimController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getTranscriptions() != null) {
            this.getModel().getTranscriptions().stream().forEach(x -> this.transcriptionController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getVideos() != null) {
            this.getModel().getVideos().stream().forEach(x -> this.eserMediaController.makeFileRelatedOperations(x));
        }
    }

    private void fixTimeRelatedFields() {
        /* *** below order is important *** */
        this.getModel().setCag(this.uygarlikDonemController.getCag());
        this.getModel().setDonem(this.uygarlikDonemController.getModel().getDonem());
        this.getModel().setUygarlik(this.uygarlikDonemController.getModel().getUygarlik());
        this.getModel().setHukumdar(this.uygarlikDonemController.getHukumdar());
        this.uygarlikDonemController.fixTimeRelatedFieldsInModel();
        this.getModel().setTermStart(this.uygarlikDonemController.getModel().getTermStart());
        this.getModel().setTermEnd(this.uygarlikDonemController.getModel().getTermEnd());
    }

    private void fixSikkeBeforePersist() {
        if (this.getModel().isSikke()) {
            this.getModel().setElisiDokumaSecimi(null);
            this.getModel().setYazmaBasmaSecimi(null);

        } else {
            this.getModel().setSikke(false);
            this.getModel().setSikkeDarpYonu(null);
            this.getModel().setDarpYeri(null);
            this.getModel().setUretimYili(null);
            this.getModel().setUretimYiliEnd(null);
        }
    }

    public DBOperationResult persistEntities(final Eser artifact, final Workflow workflow) {
        workflow.setModifier(this.sessionBean.getCurrentUser());

        final List<AbstractEntity> entities = new ArrayList<>();

        // final Eser birlestirme ise, birlestirmeye giren child final eserler updateInProgress true yapiliyor
        if ((workflow.getReviewEnum() == EserVersion.BIRLESTIRME_PENDING_REVIEW) || (workflow.getReviewEnum() == EserVersion.BIRLESTIRME_REJECTED_PENDING_REVIEW)) {

            this.birlestirmeController.getModel().getJoinedArtifacts().stream().forEach(x ->
                {
                    x.getEser().setUpdateInProgress(true);
                    entities.add(x);
                });
        }
        entities.add(artifact);
        entities.add(workflow);
        return this.facade.update(entities);
    }

    protected void redirectToEserEditPage(final Integer eserId) {
        final String message = "Eser, ID alırken null değer döndü!";
        if (eserId == null) {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_ERROR, this.bundle.getString("base.process.failure"), message);

        } else {
            try {
                Faces.redirect(FacesContext.getCurrentInstance().getViewRoot().getViewId().substring(1) + "?faces-redirect=true&e=" + eserId);

            } catch (final UncheckedIOException e) {
                this.logger.info("[EserController.redirectToEserEditPageByEserId] : {} {}", message, e.getMessage());
            }
        }
    }

    public List<MuesPick> eserGelisSekli() {
        final List<Integer> a = new ArrayList<>();
        // exclude GELIS_SEKLI_SATIN_ALMA_HIBE from screen
        a.add(MuesParameters.GELIS_SEKLI_SATIN_ALMA_HIBE);
        return this.muesParameters.filterPickByEserGelisSekli("", a);
    }

    // TODO: ayrilmis eser boolean degeri eklendikten sonra bu kisim kaldirilabilinir.
    public void assignTitleOfSeparatedArtifact(final Integer id) {

        final SeparationEser separationEser = this.separationEserFacade.findByEserId(id);
        if (separationEser != null) {
            this.separatedEserTitle = "Bu eser " + separationEser.getSeparation().getSeparatedArtifact().getTitle() + " başlıklı eserden ayrılmıştır.";
        } else {
            this.separatedEserTitle = null;
        }
    }

    // getters and setters ....................................................

    @Override
    public EserFacade getFacade() {
        return this.facade;
    }

    public Integer getPermanentId() {
        return this.permanentId;
    }

    public void setPermanentId(final Integer permanentId) {
        if (permanentId == null) {
            return;
        }
        this.setNewMode(false);
        if ((this.permanentId == null) || !Objects.equals(this.permanentId, permanentId)) {
            final Eser esr = this.getFacade().findEagerById(permanentId);
            if (esr == null) {
                return;
            }
            if ((esr.getAyrilmisEser() != null) && esr.getAyrilmisEser().booleanValue()) {
                this.assignTitleOfSeparatedArtifact(permanentId);
            }
            this.setModel(esr);
            this.restoreEserViaPermanentId();
            this.permanentId = permanentId;

            this.auditFacade.log(AuditEvent.EserGoruntuleme, this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + this.getModel().getEserId() + " numaralı eseri görüntülemiştir.");
        }
    }

    public DataModel<TimeSpectrum> getLazyTimeSpectrumDataModel() {
        return this.lazyTimeSpectrumDataModel;
    }

    public void setLazyTimeSpectrumDataModel(final LazyDataModel<TimeSpectrum> lazyTimeSpectrumDataModel) {
        this.lazyTimeSpectrumDataModel = lazyTimeSpectrumDataModel;
    }

    public Integer getMode() {
        return this.mode;
    }

    public void setMode(final Integer mode) {
        this.mode = mode;
    }

    public String getSeparatedEserTitle() {
        return this.separatedEserTitle;
    }

    public void setSeparatedEserTitle(final String separatedEserTitle) {
        this.separatedEserTitle = separatedEserTitle;
    }

}
