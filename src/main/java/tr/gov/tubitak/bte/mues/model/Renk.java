package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityResult;
import javax.persistence.FieldResult;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "Renk")
@NamedQuery(name = "Renk.findEagerById", query = "SELECT r FROM Renk r WHERE r.id = :id")
@NamedQuery(name = "Renk.findAll", query = "SELECT r FROM Renk r ORDER BY r.silinmis, r.aktif DESC, r.mueskod")
@NamedQuery(name = "Renk.findActive", query = "SELECT r FROM Renk r WHERE r.aktif = true AND r.silinmis = false ORDER BY r.ad")
@NamedNativeQuery(name = "Renk.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_SUSLEME_TEKNIGI WHERE SILINMIS = 0 AND RENK_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_YAPIM_TEKNIGI WHERE SILINMIS = 0 AND RENK_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_AnalysisPhotograph WHERE SILINMIS = 0 AND renkId = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_DeterminationColour WHERE SILINMIS = 0 AND RENK_ID = :id) + "
                                                              + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_SampleAnalysisPhotograph WHERE SILINMIS = 0 AND renkId = :id)")

@SqlResultSetMapping(name = "Renk", entities = {
                                                 @EntityResult(entityClass = tr.gov.tubitak.bte.mues.model.Renk.class, fields = {
                                                                                                                                  @FieldResult(name = "id", column = "ID"),
                                                                                                                                  @FieldResult(name = "ad", column = "ad"),
                                                                                                                                  @FieldResult(name = "mueskod", column = "mueskod"),
                                                                                                                                  @FieldResult(name = "ncs", column = "NCS"),
                                                                                                                                  @FieldResult(name = "cmyk", column = "cmyk"),
                                                                                                                                  @FieldResult(name = "rgb", column = "RGB"),
                                                                                                                                  @FieldResult(name = "aciklama", column = "aciklama"),
                                                                                                                                  @FieldResult(name = "aktif", column = "AKTIF"),
                                                                                                                                  @FieldResult(name = "silinmis", column = "SILINMIS"), }) })

public class Renk extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 6198321446843214190L;

    @Size(max = 50)
    @Column(name = "ad", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "mueskod", length = 50)
    private String            mueskod;

    @Size(max = 12)
    @Column(name = "NCS", length = 12)
    private String            ncs;

    @Size(max = 12)
    @Column(name = "RGB", length = 12)
    private String            rgb;

    @Size(max = 15)
    @Column(name = "cmyk", length = 12)
    private String            cmyk;

    @Size(max = 150)
    @Column(name = "aciklama", length = 150)
    private String            aciklama;

    public Renk() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    public String getNcs() {
        return this.ncs;
    }

    public void setNcs(final String ncs) {
        this.ncs = ncs;
    }

    public String getRgb() {
        return this.rgb;
    }

    public void setRgb(final String rgb) {
        this.rgb = rgb;
    }

    public String getCmyk() {
        return this.cmyk;
    }

    public void setCmyk(final String cmyk) {
        this.cmyk = cmyk;
    }

    public String getMueskod() {
        return this.mueskod;
    }

    public void setMueskod(final String mueskod) {
        this.mueskod = mueskod;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable("(" + this.mueskod + ") " + this.ad).orElse("" + this.getId());
    }

}
