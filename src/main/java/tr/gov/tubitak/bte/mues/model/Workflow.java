package tr.gov.tubitak.bte.mues.model;

import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Version;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Entity
@Table(name = "Workflow")
@Audited
public class Workflow extends WorkflowSuper {

    private static final long serialVersionUID = -6938754745427428184L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinTable(name = "Workflow_Comment", joinColumns = @JoinColumn(name = "workflow"), inverseJoinColumns = @JoinColumn(name = "comment"))
    private Set<Comment>      comments;

    @Version
    private int               version;

    public Workflow() {
        // blank constructor
    }

    // getters and setters ....................................................

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return this.getArtifact().toString() + " - " + this.getReviewEnum().getLabel();
    }

    public Set<Comment> getComments() {
        return this.comments;
    }

    public void setComments(final Set<Comment> comments) {
        this.comments = comments;
    }

    public int getVersion() {
        return this.version;
    }

    public void setVersion(final int version) {
        this.version = version;
    }

}
