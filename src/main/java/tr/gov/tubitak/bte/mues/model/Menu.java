package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 *
*
 */
@Entity
@Table(name = "MENU", uniqueConstraints = { @UniqueConstraint(columnNames = { "parentMenuId", "MENU_SIRASI" }) })
public class Menu extends MenuSuper {

    private static final long serialVersionUID = -1094719437907436172L;

    public Menu() {
    }

}
