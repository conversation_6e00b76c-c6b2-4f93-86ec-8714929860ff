package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.faces.context.FacesContext;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.datatable.DataTable;

import tr.gov.tubitak.bte.mues.model.Renk;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyColorDataModel;
import tr.gov.tubitak.bte.mues.session.RenkFacade;
import tr.gov.tubitak.bte.mues.session.RenkLazyLoadFacade;

@Named
@ViewScoped
public class RenkController extends AbstractController<Renk> {

    private static final long  serialVersionUID = 5671846083606732010L;

    @Inject
    private RenkFacade         facade;

    @Inject
    private RenkLazyLoadFacade renkLazyLoadFacade;

    private List<Renk>         selectionList;

    private DataModel<Renk>    lazyColorDataModel;

    public RenkController() {
        super(Renk.class);
    }

    public List<Renk> filterByNameAndDescription(final String query) {
        return this.facade.filterByNameAndDescription(query);

    }

    public void loadDataTable(final String suffix) {
        this.resetTable(suffix);
        this.setLazyColorDataModel(new LazyColorDataModel(this.renkLazyLoadFacade, this));
        this.getSelectionList().clear();
    }

    public void resetTable(final String suffix) {

        final DataTable dataTable = ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(":eserMalzeme" + suffix + "Teknigi:colorSecimiForm:colorTableId"));
        if (dataTable != null) {
            dataTable.reset();
        }
    }

    // getters and setters ....................................................

    @Override
    public RenkFacade getFacade() {
        return this.facade;
    }

    public List<Renk> getSelectionList() {
        if (this.selectionList == null) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;
    }

    public void setSelectionList(final List<Renk> selectionList) {
        this.selectionList = selectionList;
    }

    public DataModel<Renk> getLazyColorDataModel() {
        return this.lazyColorDataModel;
    }

    public void setLazyColorDataModel(final DataModel<Renk> lazyColorDataModel) {
        this.lazyColorDataModel = lazyColorDataModel;
    }

}
