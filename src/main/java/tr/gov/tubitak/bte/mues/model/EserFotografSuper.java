package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "EserFotograf.findAll", query = "SELECT e FROM EserFotograf e JOIN FETCH e.ilgiliYuz ORDER BY e.silinmis, e.aktif DESC")
@NamedQuery(name = "EserFotograf.findActive", query = "SELECT e FROM EserFotograf e WHERE e.aktif = true AND e.silinmis = false")
public class EserFotografSuper extends AbstractEntity {

    private static final long serialVersionUID = 5725467593267544741L;

    @Size(max = 50)
    @Column(name = "FOTOGRAF_BASLIGI", length = 50)
    private String            fotografBasligi;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "FOTOGRAF_PATH", length = 250)
    private String            fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Column(name = "ANA_FOTOGRAF")
    private Boolean           anaFotograf;

    public EserFotografSuper() {
        this.anaFotograf = false;
    }

    // getters and setters ....................................................

    public String getFotografBasligi() {
        return this.fotografBasligi;
    }

    public void setFotografBasligi(final String fotografBasligi) {
        this.fotografBasligi = fotografBasligi;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Boolean getAnaFotograf() {
        return this.anaFotograf;
    }

    public void setAnaFotograf(final Boolean anaFotograf) {
        this.anaFotograf = anaFotograf;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.fotografBasligi).orElse("" + this.getId());
    }

}
