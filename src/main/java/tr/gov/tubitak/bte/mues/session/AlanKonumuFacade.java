package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.AlanKonumu;
import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.Mudurluk;

/**
 *
*
 */
@RequestScoped
public class AlanKonumuFacade extends AbstractFacade<AlanKonumu> {

    public AlanKonumuFacade() {
        super(AlanKonumu.class);
    }

    public List<BagliBirim> findByNameAndAciklamaAndMudurluk(final String query, final Mudurluk mudurluk) {
        return this.em.createNamedQuery("BagliBirim.findByNameAndAciklamaAndMudurluk", BagliBirim.class).setParameter("str", "%" + query + "%").setParameter("muze", mudurluk).getResultList();
    }

    public List<Bina> findByNameAndAciklamaAndBagliBirim(final String query, final BagliBirim birim) {
        return this.em.createNamedQuery("Bina.findByNameAndAciklamaAndBirim", Bina.class).setParameter("str", "%" + query + "%").setParameter("birim", birim).getResultList();
    }

    public List<Bina> findByNameAndAciklamaAndBirimPreventDuplicate(final String query, final BagliBirim birim, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("Bina.findByNameAndAciklamaAndBirimPreventDuplicate", Bina.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("birim", birim)
                      .setParameter("ids", excludedIds)
                      .getResultList();
    }

    public List<Alan> findByNameAndAciklamaAndBina(final String query, final Bina bina) {
        return this.em.createNamedQuery("Alan.findByNameAndAciklamaAndBina", Alan.class).setParameter("str", "%" + query + "%").setParameter("bina", bina).getResultList();
    }

    public List<Alan> filterByNameAndBinaPreventDuplicate(final String query, final Bina bina, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("Alan.filterByNameAndBinaPreventDuplicate", Alan.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("bina", bina)
                      .setParameter("ids", excludedIds)
                      .getResultList();
    }

    public List<AlanKonumu> findByNameAndAciklamaAndAlan(final String query, final Alan alan) {
        return this.em.createNamedQuery("AlanKonumu.findByNameAndAciklamaAndAlan", AlanKonumu.class).setParameter("str", "%" + query + "%").setParameter("alan", alan).getResultList();
    }

    public List<AlanKonumu> findByNameAndMuseumDirectorate(final Integer id) {
        return this.em.createNamedQuery("AlanKonumu.findByMuseumDirectorate", AlanKonumu.class).setParameter("id", id).getResultList();
    }

    public List<AlanKonumu> findTeshirSalonuByNameAndMuseumDirectorate(final Integer id) {
        return this.em.createNamedQuery("AlanKonumu.findTeshirSalonuByMuseumDirectorate", AlanKonumu.class).setParameter("id", id).getResultList();
    }

    public List<AlanKonumu> findByMudurluk(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("AlanKonumu.findByMudurluk", AlanKonumu.class).setParameter("muzeler", muzelist).getResultList();
    }

}
