package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.util.Date;
import java.util.Properties;

import javax.annotation.PostConstruct;
import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.mail.Message;
import javax.mail.MessagingException;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import javax.mail.Transport;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.session.AuditFacade;

@RequestScoped
public class MailSender implements Serializable {

    private static final long     serialVersionUID = 7133461527542136186L;

    private static final Logger   logger           = LoggerFactory.getLogger(MailSender.class);

    @Inject
    private transient AuditFacade auditFacade;

    @Inject
    private AbstractParameters    params;

    private Properties            props;

    public MailSender() {
    }

    @PostConstruct
    private void init() {
        this.props = this.params.getStartsWith("mail.");
    }

    /**
     * Use this method to send email to single or multiple recipients. Separate email addresses with semicolon, or comma. Send empty String "" if any
     * of the parameter is not going to be used.
     * 
     * @param to String containing email address(es) as the email addressed to
     * @param cc String containing email address(es) as carbon copy address(es)
     * @param bcc String containing email address(es) as blind carbon copy address(es)
     * @param subject the title of the email
     * @param body the content of the email
     * @return true if the email is sent; false otherwise
     */
    public boolean send(final String to, final String cc, final String bcc, final String subject, final String body) {
        return this.doSend(to, cc, bcc, subject, body);
    }

    /*
     * Asagidaki method herbir kullaniciya sanki sadece ona gonderilmis gibi email gonderir
    public boolean sendToMany(final List<Kullanici> recipients, final String mailExtension, final String subject, final String body) {
        final Session session = this.prepareSession();
        Transport tport = null;
        boolean success = true;
    
        try {
            tport = session.getTransport();
            tport.connect();
    
            for (final Kullanici each : recipients) {
                if (!this.doSend(each.getPersonel().getEpostaKurumsal() + mailExtension, "", "", subject, body, session)) {
                    success = false;
                }
            }
            return success;
    
        } catch (final MessagingException e) {
            logger.error("Mail could not be send, error message is: {}", e.getMessage());
            return false;
    
        } finally {
            try {
                if (tport != null) {
                    tport.close();
                }
            } catch (final MessagingException e) {
                logger.error("Mail could not be send, error message is: {}", e.getMessage());
            }
        }
    }
    */

    private boolean doSend(final String to, final String cc, final String bcc, final String subject, final String body) {
        return this.doSend(to, cc, bcc, subject, body, this.prepareSession());
    }

    private boolean doSend(final String to, final String cc, final String bcc, final String subject, final String body, final Session session) {
        try {
            final MimeMessage message = new MimeMessage(session);
            message.setFrom(new InternetAddress(this.props.getProperty("mail.from"))); // from
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(to)); // to
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(cc)); // cc
            message.setRecipients(Message.RecipientType.BCC, InternetAddress.parse(bcc)); // bcc
            message.setSubject(this.props.getProperty("mail.subject.prefix") + " - " + subject, "utf-8"); // mail subject
            message.setText(body, "utf-8"); // mail content
            message.setSentDate(new Date());

            Transport.send(message);
            logger.info("Mail '{}' is sent to the recepients", message.getSubject());
            this.auditFacade.log(AuditEvent.EPostaGonder, "Kime: " + to, "; KK: " + cc, "; Gizli: " + bcc, "; Konu: " + subject);
            return true;

        } catch (final MessagingException e) {
            logger.error("Mail could not be send, error message is: {}", e.getMessage());
            this.auditFacade.log(AuditEvent.EPostaGonderHata, "Kime: " + to, "; KK: " + cc, "; Gizli: " + bcc, "; Konu: " + subject, e.getMessage());
        }
        return false;
    }

    private Session prepareSession() {
        final String username = this.props.getProperty("mail.smtp.auth.username");
        final String password = this.props.getProperty("mail.smtp.auth.password");

        this.props.put("mail.smtp.timeout", 1000);
        this.props.put("mail.smtp.connectiontimeout", 1000);

        return Session.getInstance(this.props, new javax.mail.Authenticator() {

            @Override
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(username, password);
            }
        });

    }

}
