package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Dil;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class DilFacade extends AbstractFacade<Dil> {

    public DilFacade() {
        super(Dil.class);
    }

    public List<Dil> filterByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Dil.findByNameAndAciklama", Dil.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
