package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Measure;
import tr.gov.tubitak.bte.mues.model.MeasureType;

/**
 *
*
 */
@RequestScoped
public class MeasureFacade extends AbstractFacade<Measure> {

    public MeasureFacade() {
        super(Measure.class);
    }

    public List<Measure> findByNameAndType(final String value, final MeasureType type) {
        return this.em.createNamedQuery("Measure.findByNameAndMeasureType", Measure.class).setParameter("ad", "%" + value + "%").setParameter("type", type).getResultList();
    }

}
