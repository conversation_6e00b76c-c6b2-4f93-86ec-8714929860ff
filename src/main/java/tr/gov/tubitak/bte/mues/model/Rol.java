package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "ROL")
@NamedNativeQuery(name = "Rol.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM KULLANICI_BIRIM_ROL WHERE SILINMIS = 0 AND ROL_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM ROL_IZIN WHERE ROL_ID = :id)")
public class Rol extends RolSuper {

    private static final long serialVersionUID = 8324413649114320370L;

    public Rol() {
        // blank constructor
    }

}
