package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Cag;
import tr.gov.tubitak.bte.mues.model.Kronoloji;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class CagFacade extends AbstractFacade<Cag> {

    public CagFacade() {
        super(Cag.class);
    }

    public List<Cag> filterByNameAndKronoloji(final String value, final Kronoloji kronoloji) {
        return this.em.createNamedQuery("Cag.findByNameAndKronoloji", Cag.class).setParameter("ad", "%" + value + "%").setParameter("krono", kronoloji).getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> findAllKronolojiAndTermDates() {
        final String qry = " SELECT MIN(c.termStart) as 'Min', MAX(c.termEnd) as 'Max', c.KRONOLOJI_ID FROM Cag c GROUP BY c.KRONOLOJI_ID; ";
        return this.getEM().createNativeQuery(qry).getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<Cag> findAllCagAndTermDates() {
        final String qry = " SELECT * FROM Cag; ";
        return this.getEM().createNativeQuery(qry, "Cag").getResultList();
    }

}
