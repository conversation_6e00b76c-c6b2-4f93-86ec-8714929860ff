package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Entity
@Table(name = "MESLEK")
@NamedQuery(name = "Meslek.findEagerById", query = "SELECT m FROM Meslek m WHERE m.id = :id")
@NamedQuery(name = "Meslek.findAll", query = "SELECT m FROM Meslek m ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "Meslek.findActive", query = "SELECT m FROM Meslek m WHERE m.aktif = true AND m.silinmis = false ORDER BY m.ad")
@NamedNativeQuery(name = "Meslek.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL WHERE SILINMIS = 0 AND MESLEK_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM SAHIS WHERE SILINMIS = 0 AND MESLEK_ID = :id)")
public class Meslek extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 2352699080882056191L;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Meslek() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
