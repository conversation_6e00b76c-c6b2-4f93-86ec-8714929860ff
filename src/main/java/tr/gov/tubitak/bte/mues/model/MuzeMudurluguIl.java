package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "MuzeMudurluguIl")
public class MuzeMudurluguIl extends MuzeMudurluguIlSuper {

    private static final long serialVersionUID = 1035253373119984286L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "MUZE_MUDURLUGU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk          mudurluk;

    public MuzeMudurluguIl() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    @Override
    public String toString() {
        return "{Müze Müdürlüğü: " + this.getMudurluk().getAd() + ", İl: " + this.getIl().getAd() + "}";
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.getMudurluk().getAd(), MuesUtil.surroundWithParanthesis(this.getIl().getAd()));
    }

}
