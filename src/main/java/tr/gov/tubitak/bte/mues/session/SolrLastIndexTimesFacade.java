package tr.gov.tubitak.bte.mues.session;

import java.util.Date;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceException;
import javax.transaction.Transactional;
import javax.validation.ConstraintViolationException;

import tr.gov.tubitak.bte.mues.model.SolrLastIndexTimes;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.DateUtil;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

@ApplicationScoped
public class SolrLastIndexTimesFacade extends AbstractFacade<SolrLastIndexTimes> {

    protected SolrLastIndexTimesFacade() {
        super(SolrLastIndexTimes.class);
    }

    public SolrLastIndexTimes findBySolrCore(final SolrEnum solrEnum) {
        try {
            return this.em.createNamedQuery("SolrLastIndexTimes.findBySolrCore", SolrLastIndexTimes.class).setParameter("solrCoreCode", solrEnum.getCode()).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }

    @Transactional
    public DBOperationResult updateLastIndexTime(final SolrEnum solrEnum) {
        try {

            final Date currentTime = DateUtil.getCurrentTime();

            this.getEM()
                .createNativeQuery("UPDATE SolrLastIndexTimes SET lastIndexTime = :currentTime WHERE solrCoreCode = :solrCore")
                .setParameter("solrCore", solrEnum.getCode())
                .setParameter("currentTime", currentTime)
                .executeUpdate();

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();
    }

}
