package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.YapimTeknigi;

/**
 *
*
 */
@RequestScoped
public class YapimTeknigiFacade extends AbstractFacade<YapimTeknigi> {

    public YapimTeknigiFacade() {
        super(YapimTeknigi.class);
    }

    public List<YapimTeknigi> filterByNameAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("YapimTeknigi.findByNameAndMalzeme", YapimTeknigi.class).setParameter("ad", "%" + query + "%").setParameter("malzeme", malzeme).getResultList();
    }

    public List<YapimTeknigi> filterByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("YapimTeknigi.findByNameAndAciklama", YapimTeknigi.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<YapimTeknigi> filterByNameAndAciklamaAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("YapimTeknigi.findByNameAndAciklamaAndMalzeme", YapimTeknigi.class).setParameter("str", "%" + query + "%").setParameter("malzeme", malzeme).getResultList();
    }

}
