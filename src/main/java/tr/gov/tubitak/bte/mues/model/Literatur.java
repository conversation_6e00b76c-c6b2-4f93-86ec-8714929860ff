package tr.gov.tubitak.bte.mues.model;

import java.util.Date;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityResult;
import javax.persistence.FetchType;
import javax.persistence.FieldResult;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
 */
@Audited
@Entity
@Table(name = "Literatur")
@NamedQuery(name = "Literatur.findEagerById", query = "SELECT k FROM Literatur k LEFT JOIN FETCH k.addedBy WHERE k.id = :id")
@NamedQuery(name = "Literatur.findAll", query = "SELECT k FROM Literatur k LEFT JOIN FETCH k.addedBy ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "Literatur.findActive", query = "SELECT k FROM Literatur k WHERE k.aktif = true AND k.silinmis = false ORDER BY k.ad")
@NamedNativeQuery(name = "Literatur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM Eser_KaynakLiteratur WHERE SILINMIS = 0 AND literaturId = :id)")
// lazy load tableların generic çalışması için eklendi.
@SqlResultSetMapping(name = "Literatur", entities = {
                                                      @EntityResult(entityClass = tr.gov.tubitak.bte.mues.model.Literatur.class, fields = {
                                                                                                                                            @FieldResult(name = "id", column = "ID"),
                                                                                                                                            @FieldResult(name = "ad", column = "ad"),
                                                                                                                                            @FieldResult(name = "yazar", column = "yazar"),
                                                                                                                                            @FieldResult(name = "isbn", column = "isbn"),
                                                                                                                                            @FieldResult(name = "issn", column = "issn"),
                                                                                                                                            @FieldResult(name = "doi", column = "doi"),
                                                                                                                                            @FieldResult(name = "addedBy", column = "addedBy"),
                                                                                                                                            @FieldResult(name = "literaturDate", column = "literaturDate"),
                                                                                                                                            @FieldResult(name = "literatureFilePath", column = "literatureFilePath"),
                                                                                                                                            @FieldResult(name = "aciklama", column = "aciklama"),
                                                                                                                                            @FieldResult(name = "aktif", column = "AKTIF"),
                                                                                                                                            @FieldResult(name = "silinmis", column = "SILINMIS"), }) })
@NamedQuery(name = "Literatur.findByIsbnOrIssn", query = "SELECT l FROM Literatur l WHERE l.isbn = :isbn OR l.issn = :issn")
public class Literatur extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -5863239450954373285L;

    @Size(max = 250)
    @Column(name = "ad", length = 250)
    private String            ad;

    @Size(max = 200)
    @Column(name = "yazar", length = 200)
    private String            yazar;

    @Size(max = 100)
    @Column(name = "isbn", length = 100)
    private String            isbn;

    @Size(max = 100)
    @Column(name = "issn", length = 100)
    private String            issn;

    @Size(max = 100)
    @Column(name = "doi", length = 100)
    private String            doi;

    @JoinColumn(name = "addedBy", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView      addedBy;

    @FilePathCheck
    @Size(max = 100)
    @Column(name = "literatureFilePath", length = 100)
    private String            literatureFilePath;

    @Column(name = "literaturDate")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              literaturDate;

    @Size(max = 200)
    @Column(name = "aciklama", length = 200)
    private String            aciklama;

    public Literatur() {
        // default constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getYazar() {
        return this.yazar;
    }

    public void setYazar(final String yazar) {
        this.yazar = yazar;
    }

    public String getIsbn() {
        return this.isbn;
    }

    public void setIsbn(final String isbn) {
        this.isbn = isbn;
    }

    public String getIssn() {
        return this.issn;
    }

    public void setIssn(final String issn) {
        this.issn = issn;
    }

    public String getDoi() {
        return this.doi;
    }

    public void setDoi(final String doi) {
        this.doi = doi;
    }

    public PersonelView getAddedBy() {
        return this.addedBy;
    }

    public void setAddedBy(final PersonelView addedBy) {
        this.addedBy = addedBy;
    }

    public String getLiteratureFilePath() {
        return this.literatureFilePath;
    }

    public void setLiteratureFilePath(final String literatureFilePath) {
        this.literatureFilePath = literatureFilePath;
    }

    public Date getLiteraturDate() {
        return this.literaturDate;
    }

    public void setLiteraturDate(final Date literaturDate) {
        this.literaturDate = literaturDate;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
