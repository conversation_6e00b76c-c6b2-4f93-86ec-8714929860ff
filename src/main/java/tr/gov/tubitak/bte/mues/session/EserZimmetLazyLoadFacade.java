package tr.gov.tubitak.bte.mues.session;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;

import tr.gov.tubitak.bte.mues.jsf.EserDepoController;
import tr.gov.tubitak.bte.mues.jsf.EserZimmetGroupController;
import tr.gov.tubitak.bte.mues.jsf.SessionBean;
import tr.gov.tubitak.bte.mues.model.EserZimmet;
import tr.gov.tubitak.bte.mues.util.DateUtil;

/**
 *
 * <AUTHOR>
 */

@RequestScoped
@Named
public class EserZimmetLazyLoadFacade extends AbstractLazyLoadFacade<EserZimmet> {

    private static final long              serialVersionUID      = 5873906341457214187L;

    private static final String            ZIMMET_PERSONEL_TITLE = "zimmetPersonel.title";

    private static final String            ALAN_KONUMU           = "alanKonumu";

    private static final String            ZIMMET_PERSONEL       = "zimmetPersonel";

    @Inject
    private SessionBean                    sessionBean;

    @Inject
    private EserZimmetGroupController      eserZimmetGroupController;
    @Inject
    private EserDepoController             eserDepoController;

    int                                    userId;

    private transient Join<Object, Object> bagliBirim;

    private transient Join<Object, Object> alanKonumu;

    private transient Join<Object, Object> alan;

    private transient Join<Object, Object> bina;

    private transient Join<Object, Object> eserDepo;

    private transient Join<Object, Object> eser;

    private transient Join<Object, Object> eserZimmets;

    private transient Join<Object, Object> eserZimmetsPersonel;

    public EserZimmetLazyLoadFacade() {
        super(EserZimmet.class);

    }

    @SuppressWarnings("unchecked")
    @Override
    public void addFetches(final Root<EserZimmet> root) {
        root.fetch(ZIMMET_PERSONEL, JoinType.LEFT);
        this.eser = (Join<Object, Object>) root.fetch("eser", JoinType.LEFT);

        this.eserZimmets = (Join<Object, Object>) this.eser.fetch("eserZimmets", JoinType.LEFT);

        this.eserZimmetsPersonel = (Join<Object, Object>) this.eserZimmets.fetch("zimmetPersonel", JoinType.LEFT);

        final Join<Object, Object> eserAltTur = (Join<Object, Object>) this.eser.fetch("eserAltTur", JoinType.LEFT);

        eserAltTur.fetch("eserTur", JoinType.LEFT);

        this.eserDepo = (Join<Object, Object>) this.eser.fetch("eserDepos", JoinType.LEFT);
        this.alanKonumu = (Join<Object, Object>) this.eserDepo.fetch(ALAN_KONUMU, JoinType.LEFT);
        this.alan = (Join<Object, Object>) this.alanKonumu.fetch("alan", JoinType.LEFT);
        this.bina = (Join<Object, Object>) this.alan.fetch("bina", JoinType.LEFT);
        this.bagliBirim = (Join<Object, Object>) this.bina.fetch("bagliBirim", JoinType.LEFT);
        this.bagliBirim.fetch("mudurluk", JoinType.LEFT);

    }

    @Override
    public void addJoinsForCount(final Root<EserZimmet> root) {
        root.join(ZIMMET_PERSONEL, JoinType.LEFT);
        final Join<Object, Object> eser1 = root.join("eser", JoinType.LEFT);

        this.eserDepo = eser1.join("eserDepos", JoinType.LEFT);
        this.alanKonumu = this.eserDepo.join(ALAN_KONUMU, JoinType.LEFT);
        this.alan = this.alanKonumu.join("alan", JoinType.LEFT);
        this.bina = this.alan.join("bina", JoinType.LEFT);
        this.bagliBirim = this.bina.join("bagliBirim", JoinType.LEFT);
    }

    private String getLikeExp(final Object v) {
        return "%" + v.toString() + "%";
    }

    @Override
    public List<Order> createOrders(final CriteriaBuilder cb, final Root<EserZimmet> root, final Map<String, SortMeta> multiSortMeta) {
        final List<Order> orderList = new ArrayList<>();

        if ((multiSortMeta != null) && !multiSortMeta.isEmpty()) {
            for (final Iterator<String> it = multiSortMeta.keySet().iterator(); it.hasNext();) {
                final String sortProperty = it.next();
                final SortMeta sortMeta = multiSortMeta.get(sortProperty);

                if (sortMeta.getField().contains(ZIMMET_PERSONEL_TITLE)) {
                    // Special handling for zimmetPersonel.title sorting
                    this.addNameAndSurnameOrders(cb, root, sortMeta, orderList);
                } else {
                    this.addRegularOrder(cb, root, sortMeta, orderList);
                }

            }
        } else {
            orderList.add(cb.desc(this.eser.get("id"))); // default order
        }
        return orderList;
    }

    private void addNameAndSurnameOrders(final CriteriaBuilder cb, final Root<EserZimmet> root, final SortMeta sortMeta, final List<Order> orderList) {
        final String[] nameFields = { "zimmetPersonel.ad", "zimmetPersonel.soyad" };
        for (final String field : nameFields) {
            final SortMeta subsortMeta = SortMeta.builder().field(field).order(sortMeta.getOrder()).build();
            this.addRegularOrder(cb, root, subsortMeta, orderList);
        }
    }

    private void addRegularOrder(final CriteriaBuilder cb, final Root<EserZimmet> root, final SortMeta sortMeta, final List<Order> orderList) {
        final Path<Object> path = this.getPath(sortMeta, root);
        orderList.add(sortMeta.getOrder().equals(SortOrder.ASCENDING) ? cb.asc(path) : cb.desc(path));
    }

    @Override
    public List<Predicate> createPredicates(final CriteriaBuilder cb, final Root<EserZimmet> root, final Map<String, FilterMeta> filters) {

        final List<Predicate> predicates = new ArrayList<>();

        if (this.eserZimmetGroupController.isPermitted() && this.eserZimmetGroupController.getOperationType() && (this.eserZimmetGroupController.getPersonel() != null)) {
            this.userId = this.eserZimmetGroupController.getPersonel().getId();
            predicates.add(cb.equal(root.get(ZIMMET_PERSONEL), this.userId));
        } else if (this.eserZimmetGroupController.getOperationType()) {
            this.userId = this.sessionBean.getCurrentUser().getPersonelView().getId();
            predicates.add(cb.equal(root.get(ZIMMET_PERSONEL), this.userId));

        } else if ((this.eserDepoController.getModel() != null) && (this.eserDepoController.getModel().getAlanKonumu() != null)) {
            predicates.add(cb.equal(this.eserDepo.get(ALAN_KONUMU), this.eserDepoController.getModel().getAlanKonumu().getId()));
        } else if ((this.eserDepoController.getModel() != null) && (this.eserDepoController.getAlan() != null)) {
            predicates.add(cb.equal(this.alanKonumu.get("alan"), this.eserDepoController.getAlan().getId()));
        }

        predicates.add(cb.equal(root.get("aktif"), true));
        predicates.add(cb.equal(root.get("silinmis"), false));

        if (this.eserZimmets != null) {
            predicates.add(cb.equal(this.eserZimmets.get("aktif"), true));

        }
        predicates.add(cb.equal(root.get("silinmis"), false));
        predicates.add(cb.equal(root.get("eser").get("aktif"), true));
        predicates.add(cb.equal(root.get("eser").get("silinmis"), false));
        // predicates.add(cb.equal(root.get("eser").get("versiyon"), EserVersion.APPROVED.getCode()));

        if ((this.eserZimmetGroupController.getRemoveIds() != null) && !this.eserZimmetGroupController.getRemoveIds().isEmpty()) {
            predicates.add(cb.not(root.get("eser").get("id").in(this.eserZimmetGroupController.getRemoveIds())));
            predicates.add(cb.equal(this.bagliBirim.get("mudurluk"), this.eserZimmetGroupController.getMudurlukId()));
        }

        if ((filters != null) && !filters.isEmpty()) {

            filters.forEach((k, v) ->
                {

                    final String[] array = k.split("\\.");
                    if (v.getFilterValue() == null) {
                        // do nothing
                    } else if (k.equals("eser.permanentId")) {
                        final String value = v.getFilterValue().toString().replace("TR.M.", "").replace(".", "").replaceFirst("^0+", "");
                        predicates.add(cb.like(root.get(array[0]).get(array[1]).as(String.class), this.getLikeExp(value)));
                    } else if (k.equals("eser.id")) {
                        final String value = v.getFilterValue().toString().replace("G.", "").replace(".", "").replaceFirst("^0+", "");
                        predicates.add(cb.like(root.get(array[0]).get(array[1]).as(String.class), this.getLikeExp(value)));
                    } else if (k.equals(ZIMMET_PERSONEL_TITLE)) {
                        final Expression<String> exp = cb.function("CONCAT",
                                                                   String.class,
                                                                   cb.lower(root.join(array[0]).get("ad")),
                                                                   cb.literal(" "),
                                                                   cb.lower(root.join(array[0]).get("soyad")),
                                                                   cb.literal(" ("),
                                                                   root.join(array[0]).get("sicilNo"),
                                                                   cb.literal(")"));

                        predicates.add(cb.like(exp, this.getLikeExp(v.getFilterValue().toString())));
                    } else if (k.equals("eser.zimmetPersonelTitles")) {
                        if (this.eserZimmetsPersonel != null) {

                            final Expression<String> exp = cb.function("CONCAT",
                                                                       String.class,
                                                                       cb.lower(this.eserZimmetsPersonel.get("ad")),
                                                                       cb.literal(" "),
                                                                       cb.lower(this.eserZimmetsPersonel.get("soyad")),
                                                                       cb.literal(" ("),
                                                                       this.eserZimmetsPersonel.get("sicilNo"),
                                                                       cb.literal(")"));

                            predicates.add(cb.like(exp, this.getLikeExp(v.getFilterValue().toString())));
                        }
                    } else if (k.equals("zimmetTarihi")) {

                        final String filterValue = v.getFilterValue().toString();

                        final String fromPart = filterValue.substring(0, filterValue.indexOf("~"));
                        final String toPart = filterValue.substring(filterValue.indexOf("~") + 1);

                        if (!fromPart.isEmpty()) {
                            predicates.add(cb.greaterThanOrEqualTo(root.get(k), cb.literal(DateUtil.getDateDD_MM_YYYYString(fromPart))));
                        }

                        if (!fromPart.isEmpty() && !toPart.isEmpty()) {
                            predicates.add(cb.and());
                        }

                        if (!toPart.isEmpty()) {
                            predicates.add(cb.lessThanOrEqualTo(root.get(k), DateUtil.getDateDD_MM_YYYYString(toPart)));
                        }

                    } else if (array.length >= 2) {

                        Path<?> currentPath = root;

                        for (int i = 0; i < (array.length - 1); i++) {
                            currentPath = ((From<?, ?>) currentPath).join(array[i]); // Use From for proper casting
                        }

                        predicates.add(cb.like(cb.lower(currentPath.get(array[array.length - 1]).as(String.class)), this.getLikeExp(v.getFilterValue().toString())));

                    } else {
                        predicates.add(cb.like(cb.lower(root.get(array[0]).as(String.class)),
                                this.getLikeExp(v.getFilterValue().toString())));
                    }
                });

        }
        return predicates;
    }

    public int getUserId() {
        return this.userId;
    }

    public void setUserId(final int userId) {
        this.userId = userId;
    }

}
