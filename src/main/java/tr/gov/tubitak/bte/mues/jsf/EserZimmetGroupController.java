package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.tabview.TabView;
import org.primefaces.event.TabChangeEvent;
import org.primefaces.event.UnselectEvent;
import org.primefaces.model.LazyDataModel;

import tr.gov.tubitak.bte.mues.model.*;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyEserZimmetDataModel;
import tr.gov.tubitak.bte.mues.session.*;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class EserZimmetGroupController extends AbstractController<EserZimmet> {

    private static final long serialVersionUID = -8430948565262040698L;

    @Inject
    private SessionBean sessionBean;

    @Inject
    private WorkflowFacade workflowFacade;

    @Inject
    private EserZimmetLazyLoadFacade eserZimmetLazyLoadFacade;

    @Inject
    private KullaniciFacade kullaniciFacade;

    @Inject
    private EserZimmetFacade facade;

    @Inject
    PersonelController personelController;

    @Inject
    WorkflowController workflowController;

    @Inject
    PersonelFacade personelFacade;

    @Inject
    AuditFacade auditFacade;

    @Inject
    EserDepoController eserDepoController;

    private List<Personel> selectedPersonel;

    // Ekranda yapilan secilimlerin gorunmesi icin yapilmistir.
    private List<EserZimmet> selectedEserZimmet;

    private List<Workflow> selectedWorkFlow;

    // Taslak eser devrinde devreden KULLANICI user olarak tutulmaktadır.
    private Kullanici user;

    // Eser devri'nde devreden, Eser zimmet düşme işleminde zimmet sahibi personel
    // olarak tutulmaktadır.
    private Personel personel;

    // devralan
    private Kullanici assignee;

    private transient LazyDataModel<EserZimmet> lazyEserZimmetDataModel;

    private Date liabilityDate;

    private String description;

    private int activeIndex;

    // if the user is permitted to change liability or not
    private boolean permitted;

    private boolean allSelected;

    private boolean transferArtifactOtherMuzeum = Boolean.FALSE;

    private boolean operationType = true;

    private List<Integer> removeIds;

    private Integer mudurlukId = null;

    public static final String DEFAULT_ERROR = "İşlem sırasında bir hata oluştu";

    private static final String LIABILITY_CHANGE_SUCCESS_MESSAGE = "Eserler zimmet devri için onaya gönderilmiştir";

    private static final String LIABILITY_SHARE_SUCCESS_MESSAGE = "Eserler zimmet paylaşımı için onaya gönderilmiştir";

    /**
     * Eser devir sayfasına girebilmek için eser:cokluzimmet iznine sahip olmak gerekiyor. Bu sayfada 4 farklı işlem mevcut; eser zimmet devri,eser
     * zimmet paylasimi, taslak eser devri ve zimmet düşme. Bu işlemlerde eğer kullanıcının zimmet:devreden yetkisi varsa kendisi dışında başka
     * personelleri de devreden kısmında seçebiliyor. Eğer kullanıcının bu yetkisi yoksa sadece kendisine ait olan eserler üzerinden işlem
     * yapabiliyor.
     * <p>
     * Giriş yapmış kullanıcının çalıştığı müzeleri belirlemek için de tüm kısımlarda eser:cokluzimmet yetkisi kullanılıyor. Bu yetkiye sahip olduğu
     * müzelerde çalışan kullanıcı ve personeller listeleniyor.
     * <p>
     * Eser devri ve eser zimmet düşme işlemleri personel üzerinden ilerliyor. Taslak eser devri ise kullanıcı üzerinden ilerliyor.
     */

    public EserZimmetGroupController() {
        super(EserZimmet.class);
    }

    @PostConstruct
    private void init() {
        loadAutomatically();
    }

    private void loadAutomatically() {
        if (!checkPermission()) {
            this.fetchTaslakEser();
            this.fetchEserZimmet();
        }
    }

    private boolean checkPermission() {
        final List<Mudurluk> mudurluguListByPermission1 = this.sessionBean.fetchMudurlukListByPermission("zimmet:devreden");
        final List<Mudurluk> mudurluguListByPermission2 = this.sessionBean.fetchMudurlukListByPermission("eserZimmet:baskaMuzeyeDevret");

        if (((mudurluguListByPermission1 != null) && !mudurluguListByPermission1.isEmpty()) || ((mudurluguListByPermission2 != null) && !mudurluguListByPermission2.isEmpty())) {
            this.permitted = true;
            return true;
        } else {
            this.permitted = false;
            return false;
        }
//        this.operationType = true;
    }

    // getters and setters ....................................................

    @Override
    public EserZimmetFacade getFacade() {
        return this.facade;
    }

    /***
     * Filter users without selected one
     *
     * @param text
     * @return the user list without selected one
     */
    public List<Kullanici> autoCompleteKullaniciByNameAndMudurlukExcludeSelectedKullanici(final String query) {
        final List<Integer> ids = new ArrayList<>();
        final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");

        final List<Integer> directorates;
        if (!this.transferArtifactOtherMuzeum && (this.user != null)) {

            directorates = Arrays.asList(this.user.getPersonelView().getMudurluk());
        } else {

            directorates = MuesUtil.toIds(this.sessionBean.fetchByPermission(permission));
        }

        if (this.user != null) {
            ids.add(this.user.getId());
        }
        return this.kullaniciFacade.filterByNameAndMudurlukExcludeIds(query, directorates, MuesUtil.toIds(ids));
    }

    /***
     * Filter user List query parameter
     *
     * @param text
     * @return the user list
     */
    public List<Kullanici> autoCompleteKullaniciByNameAndMudurluk(final String query) {
        final List<Integer> ids = new ArrayList<>();
        final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");

        final List<Integer> directorates = MuesUtil.toIds(this.sessionBean.fetchByPermission(permission));

        this.user = null;
        return this.kullaniciFacade.filterByNameAndMudurlukExcludeIds(query, directorates, MuesUtil.toIds(ids));
    }

    /***
     * Filter the personel without selected one
     *
     * @param text
     * @return the personel list without selected one
     */
    public List<Personel> filterPersonelWithoutSelectedOne(final String query) {
        final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");

        // Yetki sahibi olunan müzeler
        final List<Mudurluk> directorates = this.sessionBean.fetchByPermission(permission);

        // Yeni zimmet sahipleri
        final List<Integer> personnelIds = new ArrayList<>();

        if (this.getSelectedPersonel() != null) {
            this.getSelectedPersonel().stream().forEach(x -> personnelIds.add(x.getId()));
        }

        this.setSelectedPersonel(null);

        return this.personelFacade.findAllFromDirectoratesExcludeIds(query, MuesUtil.toIds(personnelIds), MuesUtil.toIds(directorates));
    }

    /***
     * Filter the personel without selected one
     *
     * @param text
     * @return the personel list without selected one
     */
    public List<Personel> filterPersonelWithoutSelectedList(final String query) {
        final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");

        // Yetki sahibi olunan müzeler
        List<Mudurluk> directorates = this.sessionBean.fetchByPermission(permission);
        final List<Integer> personnelIds = new ArrayList<>();

        if (this.personel != null) {
            // eski zimmet sahibi
            if (!this.transferArtifactOtherMuzeum) {
                directorates = Arrays.asList(this.personel.getMudurluk());
            }
            personnelIds.add(this.personel.getId());

        } else {

            if (!this.transferArtifactOtherMuzeum) {
                directorates = Arrays.asList(this.eserDepoController.getMudurluk());
            }
        }

        // Yeni zimmet sahipleri
        if (this.getSelectedPersonel() != null) {
            this.getSelectedPersonel().stream().forEach(x -> personnelIds.add(x.getId()));
        }

        return this.personelFacade.findAllFromDirectoratesExcludeIds(query, MuesUtil.toIds(personnelIds), MuesUtil.toIds(directorates));
    }

    /***
     * COKLU ZIMMET DEGISIKLIGI yapar. Ekrandan seçime göre bulk ya da batch olarak islem yapar.
     *
     */

    private int determinePersonelId() {
        if (!this.operationType) {
            return 0;
        } else if (this.permitted) {
            return this.personel.getId();
        } else {
            return this.sessionBean.getCurrentUser().getPersonelView().getId();
        }
    }

    @Transactional
    public void onChangeLiability(final boolean isLiabilityWillBeRemoved) {

        final int personelId = this.determinePersonelId();
        final LiabilityOperationEnum liabilityType = isLiabilityWillBeRemoved ? LiabilityOperationEnum.LIABILITY_CHANGE : LiabilityOperationEnum.LIABILITY_SHARE;

        final AuditEvent eventType = isLiabilityWillBeRemoved ? AuditEvent.LIABILITY_CHANGE : AuditEvent.LIABILITY_SHARE;

        DBOperationResult result;

        if (this.allSelected) {
            result = this.handleLiabilityTransferForAllOptionSelected(personelId, liabilityType);
        } else {
            result = this.handleLiabilityTransferForSelecet(liabilityType);
        }

        if (result.isSuccess()) {

            final String message = liabilityType.getTitle() + result.getMessage();

            final String message1 = isLiabilityWillBeRemoved ? EserZimmetGroupController.LIABILITY_CHANGE_SUCCESS_MESSAGE : EserZimmetGroupController.LIABILITY_SHARE_SUCCESS_MESSAGE;

            MuesUtil.showMessage(message1, message, FacesMessage.SEVERITY_INFO);
            this.auditFacade.log(eventType, message);

        } else {
            MuesUtil.showMessage(DEFAULT_ERROR, FacesMessage.SEVERITY_ERROR);
        }

        this.resetFields();
    }

    private DBOperationResult handleLiabilityTransferForAllOptionSelected(final int personelId, final LiabilityOperationEnum liabilityType) {

        final List<Integer> emptyPersonelIds = null;

        return this.facade.bulkUpdateForLiability(personelId, EserVersion.LIABILITY_PENDING_REVIEW.getCode(), MuesUtil.toIds(this.selectedPersonel), this.getLiabilityDate(), this.getDescription(), liabilityType.getCode(), MuesUtil.toIds(emptyPersonelIds));

    }

    private DBOperationResult handleLiabilityTransferForSelecet(final LiabilityOperationEnum liabilityType) {

        final List<EserZimmet> selectedEserZimmetList = new ArrayList<>(this.getSelectedEserZimmet());

        return this.facade.batchUpdateForLiability(EserVersion.LIABILITY_PENDING_REVIEW.getCode(), selectedEserZimmetList, this.description, MuesUtil.toIds(this.selectedPersonel), this.getLiabilityDate(), liabilityType.getCode());

    }

    private void resetFields() {
        this.selectedPersonel.clear();
        this.liabilityDate = null;
        this.selectedEserZimmet = null;
        this.description = "";
        this.fetchEserZimmet();
    }

    /***
     * DRAFT ESER ICIN DEVRI. Gecici eserlerin devri icin ekrandan seçime göre bulk ya da batch olarak islem yapar.
     *
     */
    public void changeDraftLiability() {
        final int userId;
        if (this.permitted) {
            userId = this.user.getId();
        } else {
            userId = this.sessionBean.getCurrentUser().getId();
        }
        if (this.allSelected) {

            final List<Integer> states = new ArrayList<>();
            states.add(EserVersion.DRAFT.getCode());
            states.add(EserVersion.DRAFT_REJECTED.getCode());

            if ((this.facade.bulkUpdateForDrafts(this.assignee.getId(), userId, states)).isSuccess()) {
                MuesUtil.showMessage("Taslak eserlerin devri yapılmıştır", FacesMessage.SEVERITY_INFO);
            } else {
                MuesUtil.showMessage(DEFAULT_ERROR, FacesMessage.SEVERITY_ERROR);
            }
        } else {
            final List<Integer> eserIDs = new ArrayList<>();

            (this.selectedWorkFlow).stream().forEach(wf -> eserIDs.add(wf.getArtifact().getId()));

            if ((this.facade.batchUpdateForDrafts(this.assignee.getId(), eserIDs)).isSuccess()) {

                MuesUtil.showMessage("Seçilen taslak eserlerin devri yapılmıştır", FacesMessage.SEVERITY_INFO);
            } else {
                MuesUtil.showMessage(DEFAULT_ERROR, FacesMessage.SEVERITY_ERROR);
            }
        }

        this.setAssignee(null);
        this.setSelectedWorkFlow(null);
        this.fetchTaslakEser();
    }

    /***
     * ZIMMETTEN DUSME islemi yapar. Ekrandan secime göre bulk yada batch olarak islem yapar.
     *
     */
    @Transactional
    public void removeLiability() {
        final DBOperationResult result;
        StringBuilder message = new StringBuilder();

        int personelId;

        if (this.permitted) {
            personelId = this.personel.getId();
        } else {
            personelId = this.sessionBean.getCurrentUser().getPersonelView().getId();
        }

        if (this.allSelected) {

            final List<String> artifactFromArtifactLiabilityPersonelWithFormatted = this.facade.fetchArtifactFromArtifactLiabilityPersonelWithFormatted(personelId);
            final List<Integer> artifactFromArtifactLiabilityPersonel = this.facade.fetchArtifactFromArtifactLiabilityPersonel(personelId);

            if (!artifactFromArtifactLiabilityPersonelWithFormatted.isEmpty()) {
                message.append(artifactFromArtifactLiabilityPersonelWithFormatted);
                message.append(" ID'li eserler sadece bir kişi üzerine zimmetli olduğundan, bu eserler dışındaki eserlerin zimmet düşmesi onaya gönderildi");
            } else {
                message.append("Eserler zimmet düşümü için onaya gönderildi");
            }

            result = this.facade.bulkRemoveForLiabiliy(personelId, this.getDescription(), MuesUtil.toIds(artifactFromArtifactLiabilityPersonel));

        } else {

            final List<Integer> eserIDs = new ArrayList<>();
            List<EserZimmet> collect;

            this.getSelectedEserZimmet().stream().forEach(ez -> eserIDs.add(ez.getEser().getId()));

            final List<String> artifactFromArtifactLiabilityPersonel = this.facade.fetchArtifactFromArtifactLiabilityPersonelWithFormatted(this.personel.getId(), eserIDs);

            if (!artifactFromArtifactLiabilityPersonel.isEmpty()) {

                message.append(artifactFromArtifactLiabilityPersonel);

                message.append(" ID'li eserler sadece bir kişi üzerine zimmetli olduğundan, bu eserler dışındaki eserlerin zimmet düşmesi onaya gönderildi");
                eserIDs.removeAll(artifactFromArtifactLiabilityPersonel);

                collect = this.getSelectedEserZimmet().stream().filter(x -> !artifactFromArtifactLiabilityPersonel.contains(x.getEser().getId())).collect((Collectors.toList()));
            } else {
                message.append("Seçili eserler zimmet düşümü için onaya gönderildi");
                collect = this.getSelectedEserZimmet().stream().collect((Collectors.toList()));
            }

            result = this.facade.batchRemoveForLiability(personelId, this.getDescription(), collect);

        }

        if (result.isSuccess()) {
            MuesUtil.showMessage(message.toString(), FacesMessage.SEVERITY_INFO);
        } else {
            MuesUtil.showMessage(DEFAULT_ERROR, FacesMessage.SEVERITY_ERROR);
        }

        this.selectedEserZimmet = null;
        this.fetchEserZimmet();
        this.description = "";
        this.allSelected = false;
    }

    public List<EserZimmet> findByEserId(final Integer eserId) {
        return this.facade.findbyEserId(eserId);
    }

    public void activeTabChanged(final TabChangeEvent<TabView> event) {
        this.setActiveIndex(((TabView) event.getComponent()).getActiveIndex());
        this.operationType = true;
        this.resetTable();
        loadAutomatically();
    }

    public void resetTable() {
        this.setDescription(null);
        this.workflowController.setItems(null);
        this.allSelected = false;
        this.setUser(null);
        this.setPersonel(null);
        this.setAssignee(null);
        this.lazyEserZimmetDataModel = null;
        this.selectedEserZimmet = null;
        this.getSelectedPersonel().clear();
        this.transferArtifactOtherMuzeum = Boolean.FALSE;
        this.eserDepoController.setBirim(null);
        this.eserDepoController.setBina(null);
        this.eserDepoController.setAlan(null);
        this.eserDepoController.getModel().setAlanKonumu(null);
        this.selectedWorkFlow = null;

    }

    public boolean personelUnselectListener(final UnselectEvent<?> event) {
        return this.selectedPersonel.remove(event.getObject());
    }

    // -------------------------------- setter/getter -------------------------------
    public List<Personel> getSelectedPersonel() {
        if (this.selectedPersonel == null) {
            this.selectedPersonel = new ArrayList<>();
        }
        return this.selectedPersonel;
    }

    public void setSelectedPersonel(final List<Personel> selectedPersonel) {
        this.selectedPersonel = selectedPersonel;
    }


    public void fetchEserZimmet() {
        this.checkPermission();
        this.lazyEserZimmetDataModel = new LazyEserZimmetDataModel(this.eserZimmetLazyLoadFacade, this);
    }

    public void fetchTaslakEser() {
        this.checkPermission();
        final List<Integer> draftTransferablestates = new ArrayList<>();
        draftTransferablestates.add(EserVersion.DRAFT.getCode());
        draftTransferablestates.add(EserVersion.DRAFT_REJECTED.getCode());

        if ((this.user == null) && !this.permitted) {
            this.workflowController.setItems(this.workflowFacade.findByVersionAndUser(this.sessionBean.getCurrentUser(), draftTransferablestates));
        } else {
            this.workflowController.setItems(this.workflowFacade.findByVersionAndUser(this.user, draftTransferablestates));
        }

    }

    public LazyDataModel<EserZimmet> getLazyEserZimmetDataModel() {

        return this.lazyEserZimmetDataModel;
    }

    public void setLazyEserZimmetDataModel(final LazyDataModel<EserZimmet> lazyEserZimmetDataModel) {
        this.lazyEserZimmetDataModel = lazyEserZimmetDataModel;
    }

    public int getActiveIndex() {
        return this.activeIndex;
    }

    public void setActiveIndex(final int activeIndex) {
        this.activeIndex = activeIndex;
    }

    public Date getLiabilityDate() {
        return this.liabilityDate;
    }

    public void setLiabilityDate(final Date liabilityDate) {
        this.liabilityDate = liabilityDate;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(final String description) {
        this.description = description;
    }

    public Kullanici getAssignee() {
        return this.assignee;
    }

    public void setAssignee(final Kullanici assignee) {
        this.assignee = assignee;
    }

    public Kullanici getUser() {
        return this.user;
    }

    public void setUser(final Kullanici user) {
        this.user = user;
    }

    public Personel getPersonel() {
        return this.personel;
    }

    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public boolean isPermitted() {
        return this.permitted;
    }

    public void setPermitted(final boolean permitted) {
        this.permitted = permitted;
    }

    public boolean isAllSelected() {
        return this.allSelected;
    }

    public void setAllSelected(final boolean allSelected) {
        this.allSelected = allSelected;
    }

    public List<EserZimmet> getSelectedEserZimmet() {
        if (this.selectedEserZimmet == null) {
            this.selectedEserZimmet = new ArrayList<>();
        }
        return this.selectedEserZimmet;
    }

    public void setSelectedEserZimmet(final List<EserZimmet> selectionList) {
        this.selectedEserZimmet = selectionList;

    }

    public List<Workflow> getSelectedWorkFlow() {
        return this.selectedWorkFlow;
    }

    public void setSelectedWorkFlow(final List<Workflow> selectedWorkFlow) {
        this.selectedWorkFlow = selectedWorkFlow;
    }

    public boolean getOperationType() {
        return this.operationType;
    }

    public void setOperationType(final boolean operationType) {
        this.operationType = operationType;
    }

    public List<Integer> getRemoveIds() {
        return this.removeIds;
    }

    public void setRemoveIds(final List<Integer> removeIds) {
        this.removeIds = removeIds;
    }

    public Integer getMudurlukId() {
        return this.mudurlukId;
    }

    public void setMudurlukId(final Integer mudurlukId) {
        this.mudurlukId = mudurlukId;
    }

    public boolean isTransferArtifactOtherMuzeum() {
        return this.transferArtifactOtherMuzeum;
    }

    public void setTransferArtifactOtherMuzeum(final boolean transferArtifactOtherMuzeum) {
        this.transferArtifactOtherMuzeum = transferArtifactOtherMuzeum;
    }

}
