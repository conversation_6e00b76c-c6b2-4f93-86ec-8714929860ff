/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2017© */
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.ResourceBundle;

import javax.inject.Inject;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import tr.gov.tubitak.bte.mues.constraint.ValidEserHareket;
import tr.gov.tubitak.bte.mues.model.EserHareketSuper;

/**
*
 *
 */
public class ValidEserHareketValidator implements ConstraintValidator<ValidEserHareket, EserHareketSuper> {

    @Inject
    private transient ResourceBundle bundle;

    public ValidEserHareketValidator() {
        // default constructor
    }

    /* (non-Javadoc)
     * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
     */
    @Override
    public void initialize(final ValidEserHareket constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final EserHareketSuper hareket, final ConstraintValidatorContext context) {
        boolean returnValue = true;
        if (hareket == null) {
            return true;
        }
        if ((hareket.getMuzeyeGelisTarihi() != null) && (hareket.getOnayTarihi() != null) && hareket.getMuzeyeGelisTarihi().after(hareket.getOnayTarihi())) {
            this.raiseFlag("Müzeye geliş tarihi veya emanete alınma tarihi komisyon tarihinden sonra olamaz", context);
            returnValue = false;
        }
        if ((hareket.getMuzeyeGelisTarihi() != null) && (hareket.getEnvantereAlinmaTarihi() != null) && hareket.getMuzeyeGelisTarihi().after(hareket.getEnvantereAlinmaTarihi())) {
            this.raiseFlag("Müzeye geliş tarihi veya emanete alınma tarihi envantere alınma tarihinden sonra olamaz", context);
            returnValue = false;
        }
        if ((hareket.getEleGecirmeTarihi() != null)
            && (hareket.getMuzeyeGelisTarihi() != null)
            && (hareket.getEnvantereAlinmaTarihi() != null)
            && hareket.getEleGecirmeTarihi().after(hareket.getMuzeyeGelisTarihi())
            && hareket.getEleGecirmeTarihi().after(hareket.getEnvantereAlinmaTarihi())) {
            this.raiseFlag("Buluntu/bağış/elde etme tarihi, müzeye geliş/emanete alınma tarihi ve envantere alınma tarihinden sonra olamaz", context);
            returnValue = false;
        }
        if ((hareket.getOnayTarihi() != null) && (hareket.getEnvantereAlinmaTarihi() != null) && hareket.getOnayTarihi().after(hareket.getEnvantereAlinmaTarihi())) {
            this.raiseFlag("Komisyon tarihi envantere alınma tarihinden sonra olamaz", context);
            returnValue = false;
        }
        if ((hareket.getTeslimAlanPersonel() != null) && (hareket.getTeslimEdenPersonel() != null) && hareket.getTeslimAlanPersonel().equals(hareket.getTeslimEdenPersonel())) {
            this.raiseFlag(this.bundle.getString("valid.tips.personel"), context);
            returnValue = false;
        }
        return returnValue;
    }

    private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

}
