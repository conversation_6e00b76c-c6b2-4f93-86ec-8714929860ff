package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.session.DilFacade;

@Named
@ViewScoped
public class DilController extends AbstractController<Dil> {

    private static final long  serialVersionUID = 8233077479204840250L;

    @Inject
    private DilFacade          facade;

    public DilController() {
        super(Dil.class);
    }

    public List<Dil> filterByNameAndAciklama(final String query) {
        return this.facade.filterByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public DilFacade getFacade() {
        return this.facade;
    }

}
