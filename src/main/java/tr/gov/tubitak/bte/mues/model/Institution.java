package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "Institution")
@NamedQuery(name = "Institution.findEagerById", query = "SELECT i FROM Institution i where i.id = :id")
@NamedQuery(name = "Institution.findAll", query = "SELECT i FROM Institution i ORDER BY i.silinmis, i.aktif DESC, i.id DESC")
@NamedQuery(name = "Institution.findActive", query = "SELECT i FROM Institution i where i.aktif = true and i.silinmis = false ORDER BY i.silinmis, i.aktif DESC, i.id DESC")
@NamedQuery(name = "Institution.findByNameAndAciklama", query = "SELECT i FROM Institution i where i.aktif = true and i.silinmis = false AND (i.ad LIKE :str OR i.aciklama LIKE :str)  ORDER BY i.ad ")
@NamedNativeQuery(name = "Institution.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ExternalUser WHERE SILINMIS = 0 AND institutionId = :id) + "
                                                                     + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Kam_CulturalPropertySmuggling WHERE institutionId = :id)")
public class Institution extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -4150954668803950143L;
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 25)
    @Column(name = "phone", length = 25)
    private String            phone;

    @Size(max = 50)
    @Column(name = "email", length = 50)
    private String            email;

    @Size(max = 150)
    @Column(name = "address", length = 150)
    private String            address;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Institution() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(final String email) {
        this.email = email;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(final String address) {
        this.address = address;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.getAd()).orElse("" + this.getId());
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(final String phone) {
        this.phone = phone;
    }

}
