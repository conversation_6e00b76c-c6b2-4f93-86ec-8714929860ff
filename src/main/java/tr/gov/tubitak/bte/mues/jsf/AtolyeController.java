package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Atolye;
import tr.gov.tubitak.bte.mues.session.AtolyeFacade;

@Named
@ViewScoped
public class AtolyeController extends AbstractController<Atolye> {

    private static final long serialVersionUID = -2120774579173030839L;

    @Inject
    private AtolyeFacade      facade;

    public AtolyeController() {
        super(Atolye.class);
    }

    public List<Atolye> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public AtolyeFacade getFacade() {
        return this.facade;
    }

}
