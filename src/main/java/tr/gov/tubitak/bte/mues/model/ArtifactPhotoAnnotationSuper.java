package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
 * <AUTHOR>
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "ArtifactPhotoAnnotation.findAnnatotionByPhotoId", query = "SELECT a FROM ArtifactPhotoAnnotation a LEFT JOIN FETCH a.eserFotograf af WHERE af.id = :photoId")
@NamedQuery(name = "ArtifactPhotoAnnotation.findEagerById", query = "SELECT a FROM ArtifactPhotoAnnotation a LEFT JOIN FETCH a.eserFotograf af WHERE a.id = :id")

public class ArtifactPhotoAnnotationSuper extends AbstractEntity {

    private static final long serialVersionUID = -3865829377154252544L;

    @Column(name = "json")
    private String            json;

    @Column(name = "color")
    private String            color;

    @Column(name = "fillColor")
    private String            fillColor;

    @Column(name = "opacity")
    private String            opacity;

    @Column(name = "strokeWidth")
    private Integer           strokeWidth;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public ArtifactPhotoAnnotationSuper() {
        // default constructor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return this.getId().toString();
    }

    public String getJson() {
        return this.json;
    }

    public void setJson(final String json) {
        this.json = json;
    }

    public String getColor() {
        return this.color;
    }

    public void setColor(final String color) {
        this.color = color;
    }

    public String getFillColor() {
        return this.fillColor;
    }

    public void setFillColor(final String fillColor) {
        this.fillColor = fillColor;
    }

    public String getOpacity() {
        return this.opacity;
    }

    public void setOpacity(final String opacity) {
        this.opacity = opacity;
    }

    public Integer getStrokeWidth() {
        return this.strokeWidth;
    }

    public void setStrokeWidth(final Integer strokeWidth) {
        this.strokeWidth = strokeWidth;
    }

}
