package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "Style")
@NamedQuery(name = "Stil.findEagerById", query = "SELECT s FROM Stil s WHERE s.id = :id")
@NamedQuery(name = "Stil.findAll", query = "SELECT s FROM Stil s ORDER BY s.silinmis, s.aktif DESC")
@NamedQuery(name = "Stil.findActive", query = "SELECT s FROM Stil s WHERE s.aktif = true AND s.silinmis = false")
@NamedQuery(name = "Stil.findByNameAndAciklama", query = "SELECT s FROM Stil s WHERE s.aktif = true AND s.silinmis = false AND (s.ad LIKE :str OR s.aciklama LIKE :str) ORDER BY s.ad, s.aciklama")
public class Stil extends AbstractEntity {

    private static final long serialVersionUID = 7097486972591276865L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "note", length = 150)
    private String            aciklama;

    public Stil() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
