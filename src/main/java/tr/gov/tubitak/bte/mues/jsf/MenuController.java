package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.enterprise.context.SessionScoped;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.shiro.SecurityUtils;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.common.SolrInputDocument;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.menu.BaseMenuModel;
import org.primefaces.model.menu.DefaultMenuItem;
import org.primefaces.model.menu.DefaultSubMenu;
import org.primefaces.model.menu.DefaultSubMenu.Builder;
import org.primefaces.model.menu.MenuElement;
import org.primefaces.model.menu.MenuModel;

import com.lowagie.text.pdf.PdfReader;
import com.lowagie.text.pdf.parser.PdfTextExtractor;

import tr.gov.tubitak.bte.mues.model.Menu;
import tr.gov.tubitak.bte.mues.model.MenuSuper;
import tr.gov.tubitak.bte.mues.search.AbstractSolrSearcherIndexingUtil;
import tr.gov.tubitak.bte.mues.session.MenuFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.SearchUtil;

@Named
@SessionScoped
public class MenuController extends AbstractController<Menu> implements SingleFileUploadable {

    private static final long                serialVersionUID = 6701383686098968284L;

    @Inject
    private transient MenuFacade             facade;

    @Inject
    private FileUploadHelper                 fileUploadHelper;

    @Inject
    private AbstractSolrSearcherIndexingUtil searcherIndexingUtil;

    @Inject
    private SearchUtil                       searchUtil;

    @Inject
    private AbstractParameters               params;

    private transient MenuModel              menuModel;

    private List<Menu>                       permittedMenus;

    /** Since help is in the topBar and for every page refresh this boolean variable is here to prevent multiple DB visits */
    private boolean                          showHelp;

    public MenuController() {
        super(Menu.class);
    }

    @PostConstruct
    public void init() {
        this.menuModel = new BaseMenuModel();
        this.permittedMenus = new ArrayList<>();

        final String anasayfaURL = FacesContext.getCurrentInstance().getExternalContext().getRequestContextPath() + "/anasayfa";

        final DefaultMenuItem main = DefaultMenuItem.builder().title("Anasayfa").value("Anasayfa").url(anasayfaURL).icon("icon-home-outline").build();

        this.menuModel.getElements().add(main);

        final List<Menu> rootMenuItems = this.facade.fetchMenuByHierarchy();
        for (final Menu each : rootMenuItems) {

            final Builder builder = DefaultSubMenu.builder().label(each.getAd()).icon(each.getIcon());

            each.getChildren().stream().filter(x -> this.isPermitted(x.getRequiredPermission())).forEach(x -> {
                builder.addElement(this.createMenuItem(x));
                this.permittedMenus.add(x);
            });

            final DefaultSubMenu subMenu = builder.build();

            if (subMenu.getElementsCount() > 0) {

                this.menuModel.getElements().add(subMenu);
            }
        }
    }

    private boolean isPermitted(final String permission) {
        return MuesUtil.isEmptyOrNull(permission) || SecurityUtils.getSubject().isPermitted(permission);
    }

    private MenuElement createMenuItem(final Menu menuItem) {
        return DefaultMenuItem.builder().title(menuItem.getAciklama()).value(menuItem.getAd()).outcome(menuItem.getPageAddress() + ".xhtml").icon(menuItem.getIcon()).build();
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setHelpContentPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getHelpContentPath() != null) {
            this.getModel().setHelpContentPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getHelpContentPath(), FolderType.HELP));

            if (this.getModel().getPageAddress() != null) {
                this.persistToSolr(this.getModel().getHelpContentPath(), this.getModel());
            }
        }
    }

    public List<Menu> filterRootsByName(final String query) {
        return this.getFacade().filterRootsByName(query);
    }

    // help related ...........................................................

    // TODO
    private void persistToSolr(final String docPath, final MenuSuper menu) {
        try {
            final SolrInputDocument doc = new SolrInputDocument();
            doc.setField("id", menu.getPageAddress());
            doc.setField("menuItemHelpPath", menu.getHelpContentPath());
            doc.setField("menuItemContent", this.extractText(Files.readAllBytes(this.params.getAbsolutePath(docPath, FolderType.HELP))));

            this.searcherIndexingUtil.setServer(this.searchUtil.getSolrHelpCore());
            this.searcherIndexingUtil.addRecordToSolr(doc);

        } catch (final IOException | SolrServerException e) {
            this.logger.error("[persistToSolr] : Hata : {}", e.getMessage(), e);
        }
    }

    private String extractText(final byte[] pdfFile) throws IOException {
        final PdfReader reader = new PdfReader(pdfFile);

        final PdfTextExtractor extractor = new PdfTextExtractor(reader);
        final int numberOfPage = reader.getNumberOfPages();

        final StringBuilder content = new StringBuilder();
        for (int i = 1; i <= numberOfPage; i++) {
            content.append(extractor.getTextFromPage(i));
        }
        reader.close();

        return content.toString();
    }

    // getters and setters ....................................................

    @Override
    public MenuFacade getFacade() {
        return this.facade;
    }

    public MenuModel getMenuModel() {
        return this.menuModel;
    }

    public List<Menu> getPermittedMenus() {
        return this.permittedMenus;
    }

    public boolean getShowHelp() {
        return this.showHelp;
    }

    public void setShowHelp(final boolean showHelp) {
        this.showHelp = showHelp;
    }

}
