package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

/**
 *
 * ali.kelle
 */
@Entity
@Table(name = "EP_Eser_KaynakLiteratur")
@NamedQuery(name = "EserProposalKaynakLiteratur.findEagerById", query = "SELECT x FROM EserProposalKaynakLiteratur x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.literatur WHERE x.id = :id")
@NamedQuery(name = "EserProposalKaynakLiteratur.findAll", query = "SELECT x FROM EserProposalKaynakLiteratur x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.literatur ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "EserProposalKaynakLiteratur.findActive", query = "SELECT x FROM EserProposalKaynakLiteratur x WHERE x.aktif = true AND x.silinmis = false")
public class EserProposalKaynakLiteratur extends EserKaynakLiteraturSuper implements DeleteValidatable {

    private static final long serialVersionUID = -4922230238332485943L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalKaynakLiteratur() {
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
