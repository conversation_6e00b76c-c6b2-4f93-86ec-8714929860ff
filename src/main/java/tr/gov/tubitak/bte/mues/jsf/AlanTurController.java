package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.AlanTur;
import tr.gov.tubitak.bte.mues.session.AlanTurFacade;

@Named
@ViewScoped
public class AlanTurController extends AbstractController<AlanTur> {

    private static final long serialVersionUID = 818035325233734331L;

    @Inject
    private AlanTurFacade     alanTurFacade;

    public AlanTurController() {
        super(AlanTur.class);
    }

    @Override
    public AlanTurFacade getFacade() {
        return this.alanTurFacade;
    }

}
