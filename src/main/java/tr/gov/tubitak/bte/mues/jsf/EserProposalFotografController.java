package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.EserProposalFotograf;
import tr.gov.tubitak.bte.mues.session.EserProposalFotografFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class EserProposalFotografController extends AbstractController<EserProposalFotograf> implements SingleFileUploadable {

    private static final long          serialVersionUID = -128005660308802713L;

    @Inject
    private EserProposalFotografFacade facade;

    @Inject
    private FileUploadHelper           fileUploadHelper;

    private EserProposalFotograf       anaFoto;

    public EserProposalFotografController() {
        super(EserProposalFotograf.class);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografBasligi(MuesUtil.extractFileName(event.getFile().getFileName()));
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath((this.fileUploadHelper.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK)));
        }
    }

    public void makeFileRelatedOperations(final EserProposalFotograf eserProposalFotograf) {
        this.setModel(eserProposalFotograf);
        this.writeToPermanentFolder();
    }

    public List<String> buildFilePathFromModel(final EserProposalFotograf model) {
        return this.fileUploadHelper.constructMainCopyImagePath(model.getFotografPath(), FolderType.IMAGE_AK);

    }

    public void uploadMultiPhoto(final FileUploadEvent event) {
        final EserProposalFotograf photo = new EserProposalFotograf();
        photo.setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
        photo.setFotografBasligi(MuesUtil.extractFileName(event.getFile().getFileName()));
        this.getItems().add(photo);
    }

    public void clearItems() {
        this.getItems().clear();
    }
    // getters and setters ....................................................

    @Override
    public List<EserProposalFotograf> getItems() {
        if (this.items == null) {
            this.items = new ArrayList<>();
        }
        return this.items;
    }

    @Override
    public EserProposalFotografFacade getFacade() {
        return this.facade;
    }

    public EserProposalFotograf getAnaFoto() {
        return this.anaFoto;
    }

    public void setAnaFoto(final EserProposalFotograf anaFoto) {
        this.anaFoto = anaFoto;
    }

}
