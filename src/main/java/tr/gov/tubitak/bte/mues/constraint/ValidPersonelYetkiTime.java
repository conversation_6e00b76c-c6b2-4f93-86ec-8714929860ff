package tr.gov.tubitak.bte.mues.constraint;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

import tr.gov.tubitak.bte.mues.constraint.validator.ValidPersonelYetkiTimeValidator;

@Target({ ElementType.TYPE, ElementType.ANNOTATION_TYPE })
@Constraint(validatedBy = { ValidPersonelYetkiTimeValidator.class })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidPersonelYetkiTime {

    String message() default "{tr.gov.tubitak.bte.mues.constraint.ValidPersonelYetkiTime}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
