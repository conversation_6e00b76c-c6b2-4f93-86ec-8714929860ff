package tr.gov.tubitak.bte.mues.session;

import javax.enterprise.context.RequestScoped;
import javax.persistence.Query;

import tr.gov.tubitak.bte.mues.model.EserDepo;

/**
 *
 * 
 */
@RequestScoped
public class EserDepoFacade extends AbstractFacade<EserDepo> {

    public EserDepoFacade() {
        super(EserDepo.class);
    }

    // Bu method eser envanter fişindeki alankonumu farklılığını bulmak için yazıldı.
    public Integer findLastApprovedAlanKonumId(final Integer eserId, final Long lastApprovedRevisionNo) {
        final String qry = " SELECT ed.ALAN_KONUMU_ID FROM audit_ESER_DEPO ed WHERE ed.ESER_ID = :eserId AND ed.REV <= :lastApprovedRevisionNo ORDER BY ed.REV DESC";
        final Query q = this.getEM().createNativeQuery(qry).setParameter("eserId", eserId).setParameter("lastApprovedRevisionNo", lastApprovedRevisionNo);
        return (Integer) q.getResultList().stream().findFirst().orElse(Integer.valueOf(0));
    }

}
