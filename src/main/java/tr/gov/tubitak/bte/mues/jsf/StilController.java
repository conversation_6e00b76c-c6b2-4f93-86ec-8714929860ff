package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Stil;
import tr.gov.tubitak.bte.mues.session.StilFacade;

@Named
@ViewScoped
public class StilController extends AbstractController<Stil> {

    private static final long serialVersionUID = -2120774579173030839L;

    @Inject
    private StilFacade        facade;

    public StilController() {
        super(Stil.class);
    }

    public List<Stil> findActive() {
        this.setItems(this.facade.findActive());
        return this.getItems();
    }

    public List<Stil> filterByNameAndAciklama(final String query) {
        return this.facade.findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public StilFacade getFacade() {
        return this.facade;
    }

}
