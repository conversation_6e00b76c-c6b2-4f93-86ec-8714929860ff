package tr.gov.tubitak.bte.mues.model;

import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "KullaniciBirimRol.findEagerById", query = "SELECT x FROM KullaniciBirimRol x JOIN FETCH x.kullanici k LEFT JOIN FETCH k.personel  LEFT JOIN FETCH x.createdBy c LEFT JOIN FETCH x.mudurluk LEFT JOIN FETCH x.rol  LEFT JOIN FETCH x.assignmentType WHERE x.id = :id")
@NamedQuery(name = "KullaniciBirimRol.fetchKbrByUser", query = "SELECT x FROM KullaniciBirimRol x JOIN x.kullanici k LEFT JOIN FETCH x.mudurluk LEFT JOIN FETCH x.rol WHERE x.silinmis = false AND x.kullanici = :kullanici")
@NamedQuery(name = "KullaniciBirimRol.fetchKbrByUserAndMudurluk", query = "SELECT x FROM KullaniciBirimRol x JOIN x.kullanici k LEFT JOIN FETCH x.mudurluk WHERE x.silinmis = false AND x.kullanici = :kullanici AND x.mudurluk = :mudurluk")
@NamedQuery(name = "KullaniciBirimRol.findAll", query = "SELECT x FROM KullaniciBirimRol x LEFT JOIN FETCH x.kullanici k LEFT JOIN FETCH k.personel JOIN FETCH x.createdBy c LEFT JOIN FETCH x.mudurluk LEFT JOIN FETCH x.rol ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "KullaniciBirimRol.findKullaniciRolsCodeByKullanici", query = "SELECT z.kod FROM Rol z WHERE z IN (SELECT  x.rol FROM KullaniciBirimRol x LEFT JOIN x.kullanici k LEFT JOIN x.rol r WHERE k.id = :id AND x.aktif = true AND x.silinmis = false )")
@NamedQuery(name = "KullaniciBirimRol.findByMudurluk", query = "SELECT x FROM KullaniciBirimRol x JOIN FETCH x.kullanici k LEFT JOIN fetch k.personel JOIN fetch x.rol LEFT JOIN fetch x.mudurluk LEFT JOIN FETCH x.assignmentType LEFT JOIN FETCH x.createdBy WHERE  x.mudurluk IN :muzeler OR (x.mudurluk IS NULL AND true=:showRolesWithoutMuseums) ORDER BY x.silinmis ASC, x.aktif DESC, k.ad ASC, k.soyad ASC")
@NamedQuery(name = "KullaniciBirimRol.findByMudurlukAndRol", query = "SELECT x FROM KullaniciBirimRol x JOIN FETCH x.kullanici k JOIN FETCH k.personel JOIN fetch x.rol JOIN fetch x.mudurluk WHERE x.mudurluk.id = :muzeId AND x.rol.kod = :kod AND x.aktif = true AND x.silinmis = false")
@NamedQuery(name = "KullaniciBirimRol.findByUserAndMudurlukAndRol", query = "SELECT x FROM KullaniciBirimRol x JOIN FETCH x.kullanici k JOIN fetch x.rol JOIN fetch x.mudurluk WHERE x.kullanici.id = :userId AND x.mudurluk.id = :muzeId AND x.rol.kod = :kod AND x.aktif = true AND x.silinmis = false")
@NamedQuery(name = "KullaniciBirimRol.findActive", query = "SELECT x FROM KullaniciBirimRol x JOIN FETCH x.kullanici WHERE x.aktif = true AND x.silinmis = false")
public class KullaniciBirimRolSuper extends AbstractEntity implements EditPermissible {

    private static final long serialVersionUID = 181231918226418523L;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "KULLANICI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Kullanici         kullanici;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "MUZE_MUDURLUGU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk          mudurluk;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ROL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Rol               rol;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "assignmentType", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick          assignmentType;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "createdBy", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView      createdBy;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "aprovePermitDocPath", length = 150)
    private String            aprovePermitDocPath;

    @Column(name = "assignmentStartTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              assignmentStartTime;

    @Column(name = "assignmentEndTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              assignmentEndTime;

    @Column(name = "dateCreated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              dateCreated;

    @Column(name = "isDeletedByUser")
    private Boolean           isDeletedByUser  = Boolean.FALSE;

    public KullaniciBirimRolSuper() {
        // default constructor
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Kullanici getKullanici() {
        return this.kullanici;
    }

    public void setKullanici(final Kullanici kullanici) {
        this.kullanici = kullanici;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Rol getRol() {
        return this.rol;
    }

    public void setRol(final Rol rol) {
        this.rol = rol;
    }

    public MuesPick getAssignmentType() {
        return this.assignmentType;
    }

    public void setAssignmentType(final MuesPick assignmentType) {
        this.assignmentType = assignmentType;
    }

    public String getAprovePermitDocPath() {
        return this.aprovePermitDocPath;
    }

    public void setAprovePermitDocPath(final String aprovePermitDocPath) {
        this.aprovePermitDocPath = aprovePermitDocPath;
    }

    public Date getAssignmentStartTime() {
        return this.assignmentStartTime;
    }

    public void setAssignmentStartTime(final Date assignmentStartTime) {
        this.assignmentStartTime = assignmentStartTime;
    }

    public Date getAssignmentEndTime() {
        return this.assignmentEndTime;
    }

    public void setAssignmentEndTime(final Date assignmentEndTime) {
        this.assignmentEndTime = assignmentEndTime;
    }

    public Date getDateCreated() {
        return this.dateCreated;
    }

    public void setDateCreated(final Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Boolean getIsDeletedByUser() {
        return this.isDeletedByUser;
    }

    public void setIsDeletedByUser(final Boolean deletedByUser) {
        this.isDeletedByUser = deletedByUser;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Stream.of(this.kullanici.getTitle(), this.rol.getTitle()).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }

    @Override
    public Integer getUserIdentifier() {
        return this.kullanici.getPersonelView().getId();
    }

    public PersonelView getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(final PersonelView createdBy) {
        this.createdBy = createdBy;
    }

}
