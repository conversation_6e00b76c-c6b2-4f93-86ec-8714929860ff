package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

@Audited
@Entity
@Table(name = "EserSerh")
public class EserSerh extends EserSerhSuper {

    private static final long serialVersionUID = 5262644200337308054L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Eser              eser;

    public EserSerh() {
        // default constructor
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }

    public void setEser(final Eser eser) {
        this.eser = eser;
    }

}
