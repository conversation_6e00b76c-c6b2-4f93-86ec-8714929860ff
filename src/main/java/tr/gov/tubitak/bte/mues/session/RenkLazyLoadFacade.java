package tr.gov.tubitak.bte.mues.session;

import java.util.Map;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.SortMeta;

import tr.gov.tubitak.bte.mues.model.Renk;
import tr.gov.tubitak.bte.mues.search.SearchConstants;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class RenkLazyLoadFacade extends LazyLoadFromDBFacade<Renk> {

    private static final long serialVersionUID = -1326850945831985446L;

    @Inject
    protected EntityManager   em;

    public RenkLazyLoadFacade() {
        super(Renk.class);
        this.setCustomQuery(SearchConstants.EMPTY);
    }

    @Override
    protected String appendLike(final String param, final FilterMeta filterMeta) {
        if ("globalFilter".equals(param)) {
            return "(LOWER(o.ad) LIKE LOWER(:globalFilter) OR LOWER(o.mueskod) LIKE LOWER(:globalFilter) OR LOWER(o.ncs) LIKE LOWER(:globalFilter) OR LOWER(o.rgb) LIKE LOWER(:globalFilter) OR LOWER(o.cmyk) LIKE LOWER(:globalFilter) OR LOWER(o.aciklama) LIKE LOWER(:globalFilter) )";
        }
        return super.appendLike(param, filterMeta);
    }

    @Override
    protected String sortClause(final Map<String, SortMeta> multiSortMeta) {

        final StringBuilder str = new StringBuilder();

        if ((multiSortMeta != null) && !multiSortMeta.isEmpty()) {

            return super.sortClause(multiSortMeta);

        }
        str.append("mueskod");
        str.insert(0, " ORDER BY ");
        return str.toString();
    }

    @Override
    protected EntityManager getEM() {
        return this.em;
    }

}
