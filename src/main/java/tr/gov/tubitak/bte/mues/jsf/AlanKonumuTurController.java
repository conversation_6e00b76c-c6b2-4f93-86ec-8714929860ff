package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.AlanKonumuTur;
import tr.gov.tubitak.bte.mues.session.AlanKonumuTurFacade;

@Named
@ViewScoped
public class AlanKonumuTurController extends AbstractController<AlanKonumuTur> {

    private static final long   serialVersionUID = -1060335753746299588L;

    @Inject
    private AlanKonumuTurFacade alanKonumuTurFacade;

    public AlanKonumuTurController() {
        super(AlanKonumuTur.class);
    }

    @Override
    public AlanKonumuTurFacade getFacade() {
        return this.alanKonumuTurFacade;
    }

}
