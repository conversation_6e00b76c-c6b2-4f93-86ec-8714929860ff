package tr.gov.tubitak.bte.mues.jsf;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.BagliBirimAltTur;
import tr.gov.tubitak.bte.mues.model.BagliBirimBagliBirimAltTur;
import tr.gov.tubitak.bte.mues.model.BagliBirimTur;
import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.Ilce;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.session.BagliBirimAltTurFacade;
import tr.gov.tubitak.bte.mues.session.BagliBirimFacade;
import tr.gov.tubitak.bte.mues.session.IlceFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class BagliBirimController extends AbstractController<BagliBirim> implements SingleFileUploadable {

    private static final long                    serialVersionUID = 3620247200540319302L;

    @Inject
    private BagliBirimFacade                     facade;

    @Inject
    private IlceFacade                           ilceFacade;

    @Inject
    private BagliBirimAltTurFacade               bagliBirimAltTurFacade;

    @Inject
    private FileUploadHelper                     fileUploadHelper;

    @Inject
    private SessionBean                          sessionBean;

    @Inject
    private BagliBirimBagliBirimAltTurController bagliBirimBagliBirimAltTurController;
    
    @Inject
    private BagliBirimFacade 					 bagliBirimFacade;

    private BagliBirimTur                        bagliBirimTur;

    private Integer                              numberOfBagliBirims;

    private String                               dwgFileToDelete;

    @PostConstruct
    public void init() {
        this.dwgFileToDelete = "";
    }

    public BagliBirimController() {
        super(BagliBirim.class);
    }

    @Override
    public DBOperationResult update() {
        final DBOperationResult result = super.update();
        if ((this.getDwgFileToDelete() != null) && !this.getDwgFileToDelete().isBlank() && result.isSuccess()) {
            this.fileUploadHelper.deleteDwgFilePermanently(this.dwgFileToDelete);
        }
        return result;
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.getModel().setMudurluk(this.sessionBean.filterByPermission("baglibirim:guncelle"));
        prepareKodFieldForCreation();
    }
    
    public void prepareKodFieldForCreation() {

    	final List<BagliBirim> result = this.bagliBirimFacade.findByMudurluk(this.getModel().getMudurluk().getId());
    	
    	if(result != null && !result.isEmpty()) {
    		
	        String initialKod = result.get(0).getKod();

	        if(initialKod != null && !initialKod.isBlank()) {
	        	//if previous kod ends with numeric character
	        	if(Character.isDigit(initialKod.charAt(initialKod.length()-1))) {
			        String[] tokens = initialKod.split("[.-]");
			        if(tokens.length < 1) {
			        	return;
			        }
			        Integer intKod = Integer.parseInt(tokens[tokens.length - 1]);
			        tokens[tokens.length - 1] = tokens[tokens.length - 1].replace(Integer.toString(intKod), Integer.toString(intKod+1));
			       	this.getModel().setKod(String.join(".", tokens));
	        	}
	        	//if previous kod does not end with numeric character
	        	else {
		        	this.getModel().setKod(initialKod);
		        }
	        }
        }
    }

    @Override
    public void toggleActive(final BagliBirim bagliBirim) {
        this.facade.toggleActiveSelfAndDescendants(bagliBirim);
        this.setModel(bagliBirim);
        this.getModel().setAktif(!this.getModel().getAktif());
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDocumentToTempFolder(final FileUploadEvent event) {
        this.getModel().setPdfPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDwgDocumentToTempFolder(final FileUploadEvent event) {
        if ((this.getModel().getDwgPath() != null) && !this.getModel().getDwgPath().isBlank()) {
            this.setDwgFileToDelete(this.getModel().getDwgPath());
        }
        this.getModel().setDwgPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void deleteDwgFileFromModel() {
        this.setDwgFileToDelete(this.getModel().getDwgPath());
        this.getModel().setDwgPath(null);
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
        if (this.getModel().getPdfPath() != null) {
            this.getModel().setPdfPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getPdfPath(), FolderType.OTHER));
        }
        if (this.getModel().getDwgPath() != null) {
            this.getModel().setDwgPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDwgPath(), FolderType.DWG));
        }
    }

    public void addBagliBirimBagliBirimAltTur() {
        if (this.getModel().getBagliBirimBagliBirimAltTurs() == null) {
            this.getModel().setBagliBirimBagliBirimAltTurs(new LinkedHashSet<>());
        }
        this.bagliBirimBagliBirimAltTurController.getModel().getBagliBirimAltTur().setBagliBirimTur(this.bagliBirimTur);
        this.getModel().getBagliBirimBagliBirimAltTurs().add(this.bagliBirimBagliBirimAltTurController.getModel());
    }

    public void removeBagliBirimBagliBirimAltTur(final BagliBirimBagliBirimAltTur bagliBirimAltTur) {
        this.getModel().getBagliBirimBagliBirimAltTurs().remove(bagliBirimAltTur);
    }

    public List<BagliBirimAltTur> filterByNameAndTur(final String value) {
        if ((this.getModel() != null) && (this.getModel().getBagliBirimBagliBirimAltTurs() != null) && !this.getModel().getBagliBirimBagliBirimAltTurs().isEmpty()) {
            final List<BagliBirimAltTur> altTurs = this.getModel().getBagliBirimBagliBirimAltTurs().stream().map(BagliBirimBagliBirimAltTur::getBagliBirimAltTur).collect(Collectors.toList());
            return this.bagliBirimAltTurFacade.filterByNameAndTurPreventDuplication(value, this.bagliBirimTur, altTurs);
        }
        return this.bagliBirimAltTurFacade.filterByNameAndTur(value, this.bagliBirimTur);
    }

    public void handleIlChange(final SelectEvent<Il> event) {
        this.logger.debug("İl Changed{}", event.getSource());
        this.getModel().setIlce(null);
    }
    
    public void handleDirectorateChange(final SelectEvent event) {
    	this.prepareKodFieldForCreation();
    }

    public List<Ilce> filterByNameAndIl(final String value) {
        return this.ilceFacade.filterByNameAndIl(value, this.getModel().getIl());
    }
    
    // getters and setters ....................................................

    @Override
    public BagliBirimFacade getFacade() {
        return this.facade;
    }

    @Override
    public List<BagliBirim> getItems() {
        if (this.items == null) {
            final List<Mudurluk> mudurlukListByPermission = this.sessionBean.fetchMudurlukListByPermission("baglibirim:listele");

            if (mudurlukListByPermission != null) {
                this.items = this.getFacade().findByMudurluk(mudurlukListByPermission);
            }
        }
        return this.items;
    }

    public BagliBirimTur getBagliBirimTur() {
        return this.bagliBirimTur;
    }

    public void setBagliBirimTur(final BagliBirimTur bagliBirimTur) {
        this.bagliBirimTur = bagliBirimTur;
    }

    public Integer getNumberOfBagliBirims() {
        if (this.numberOfBagliBirims == null) {
            this.numberOfBagliBirims = this.getFacade().findNumberOfBagliBirims();
        }
        return this.numberOfBagliBirims;
    }

    public String getDwgFileToDelete() {
        return this.dwgFileToDelete;
    }

    public void setDwgFileToDelete(final String dwgFile) {
        this.dwgFileToDelete = dwgFile;
    }

}
