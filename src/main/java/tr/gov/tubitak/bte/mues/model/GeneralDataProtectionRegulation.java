package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "GeneralDataProtectionRegulation")
@NamedQuery(name = "GeneralDataProtectionRegulation.findEagerById", query = "SELECT a FROM GeneralDataProtectionRegulation a   WHERE a.id = :id")
@NamedQuery(name = "GeneralDataProtectionRegulation.findAll", query = "SELECT a FROM GeneralDataProtectionRegulation a ORDER BY a.silinmis, a.aktif DESC, id DESC")
public class GeneralDataProtectionRegulation extends AbstractEntity {

    private static final long serialVersionUID = -1190777132669476282L;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "rol", referencedColumnName = "ID")

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "documentPath", length = 150)
    private String            documentPath;

    public GeneralDataProtectionRegulation() {
        // blank constructor
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getDocumentPath() {
        return this.documentPath;
    }

    public void setDocumentPath(final String documentPath) {
        this.documentPath = documentPath;
    }

    @Override
    public String getTitle() {
        return this.aciklama;
    }

}
