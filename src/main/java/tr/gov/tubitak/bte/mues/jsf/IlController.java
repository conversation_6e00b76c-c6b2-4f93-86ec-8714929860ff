package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.session.IlFacade;

@Named
@ViewScoped
public class IlController extends AbstractController<Il> {

    private static final long serialVersionUID = 5315937074953322347L;

    @Inject
    private IlFacade          facade;

    public IlController() {
        super(Il.class);
    }

    @Override
    public IlFacade getFacade() {
        return this.facade;
    }

}
