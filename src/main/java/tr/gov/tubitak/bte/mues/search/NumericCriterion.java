package tr.gov.tubitak.bte.mues.search;

import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

/**
 * <PERSON><PERSON> tabanlı kriterleri tutan sınıf.
 */
@Entity
@DiscriminatorValue("2")
public class NumericCriterion extends AbstractSimpleCriterion {

    private static final long serialVersionUID = -5624453640412972404L;

    private String            firstValue;

    private String            secondValue;

    /**
     * Yapıcı metot.
     */
    public NumericCriterion() {
    }

    @Override
    @Transient
    public String getFirstValue() {
        return this.firstValue;
    }

    @Override
    @Transient
    public String getSecondValue() {
        return this.secondValue;
    }

    @Override
    public ICriterion setModel(final CriterionModel model) {
        super.setSuperModel(model);
        this.firstValue = model.getTextValue1();
        this.secondValue = model.getTextValue2();
        return this;
    }

    @Override
    @Transient
    public String getFirstValueText() {
        return this.firstValue;
    }

    @Override
    @Transient
    public String getSecondValueText() {
        return this.secondValue;
    }

    @Column(name = "TEXT_VALUE1")
    public String getTextValue1() {
        return this.firstValue;
    }

    public void setTextValue1(final String textValue1) {
        this.firstValue = textValue1;
    }

    @Column(name = "TEXT_VALUE2")
    public String getTextValue2() {
        return this.secondValue;
    }

    public void setTextValue2(final String textValue2) {
        this.secondValue = textValue2;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.firstValue, this.secondValue);
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final NumericCriterion other = (NumericCriterion) obj;
        return Objects.equals(this.firstValue, other.firstValue) && Objects.equals(this.secondValue, other.secondValue);
    }

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }

}
