package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;

/**
 *
*
 */
@Entity
@Table(name = "UYGARLIK")
@NamedQuery(name = "Uygarlik.findEagerById", query = "SELECT u FROM Uygarlik u WHERE u.id = :id")
@NamedQuery(name = "Uygarlik.findAll", query = "SELECT u FROM Uygarlik u ORDER BY u.silinmis, u.aktif DESC, u.ad")
@NamedQuery(name = "Uygarlik.findActive", query = "SELECT u FROM Uygarlik u WHERE u.aktif = true AND u.silinmis = false ORDER BY u.ad")
@NamedNativeQuery(name = "Uygarlik.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER WHERE SILINMIS = 0 AND UYGARLIK_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM HUKUMDAR WHERE SILINMIS = 0 AND UYGARLIK_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM UretimYeri WHERE SILINMIS = 0 AND uygarlik = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM UYGARLIK_DONEM WHERE SILINMIS = 0 AND UYGARLIK_ID = :id)")

public class Uygarlik extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 1116132551265428243L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String            fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Transient
    private Integer           termStart;

    @Transient
    private Integer           termEnd;

    @Transient
    private Integer           signumStart;

    @Transient
    private Integer           signumEnd;

    public Uygarlik() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Integer getTermStart() {
        return this.termStart;
    }

    public void setTermStart(final Integer termStart) {
        this.termStart = termStart;
    }

    public Integer getTermEnd() {
        return this.termEnd;
    }

    public void setTermEnd(final Integer termEnd) {
        this.termEnd = termEnd;
    }

    public Integer getSignumStart() {
        if ((this.signumStart == null) && (this.termStart != null)) {
            if (this.termStart > 0) {
                this.signumStart = 1;
            } else {
                if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumStart = 2;
                } else {
                    this.signumStart = 3;
                }
            }
        }
        return this.signumStart;
    }

    public void setSignumStart(final Integer signumStart) {
        this.signumStart = signumStart;
    }

    public Integer getSignumEnd() {
        if ((this.signumEnd == null) && (this.termEnd != null)) {
            if (this.termEnd > 0) {
                this.signumEnd = 1;
            } else {
                if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumEnd = 2;
                } else {
                    this.signumEnd = 3;
                }
            }
        }
        return this.signumEnd;
    }

    public void setSignumEnd(final Integer signumEnd) {
        this.signumEnd = signumEnd;
    }

    public Integer getSignificantStart() {
        return this.termStart == null ? null : this.termStart < 0 ? -this.termStart : this.termStart;
    }

    public void setSignificantStart(final Integer significantStart) {
        if (this.getSignumStart() != null) {
            // it is already positive after Christ
            if (this.getSignumStart() == 1) {
                this.termStart = significantStart;
            } else {
                // set negative if before Christ
                this.termStart = -significantStart;
            }
        }
    }

    public Integer getSignificantEnd() {
        return this.termEnd == null ? null : this.termEnd < 0 ? -this.termEnd : this.termEnd;
    }

    public void setSignificantEnd(final Integer significantEnd) {
        if (this.getSignumEnd() != null) {
            // it is already positive after Christ
            if (this.getSignumEnd() == 1) {
                this.termEnd = significantEnd;
            } else {
                // set negative if before Christ
                this.termEnd = -significantEnd;
            }
        }
    }

    public String getTermStartTitle() {
        if (this.termStart == null) {
            return null;
        }
        if (this.termStart < 0) {
            if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                return "M.Ö. " + -this.termStart;
            }
            return "G.Ö. " + -this.termStart;
        }
        return "M.S. " + this.termStart;
    }

    public String getTermEndTitle() {
        if (this.termEnd == null) {
            return null;
        }
        if (this.termEnd < 0) {
            if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                return "M.Ö. " + -this.termEnd;
            }
            return "G.Ö. " + -this.termEnd;
        }
        return "M.S. " + this.termEnd;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
