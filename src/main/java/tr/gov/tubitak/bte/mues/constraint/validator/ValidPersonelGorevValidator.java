/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2017© */
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.ResourceBundle;

import javax.inject.Inject;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import tr.gov.tubitak.bte.mues.constraint.ValidPersonelGorev;
import tr.gov.tubitak.bte.mues.model.PersonelGorev;

/**
*
 *
 */
public class ValidPersonelGorevValidator implements ConstraintValidator<ValidPersonelGorev, PersonelGorev> {

    @Inject
    private transient ResourceBundle bundle;

    public ValidPersonelGorevValidator() {
    }

    /* (non-Javadoc)
     * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
     */
    @Override
    public void initialize(final ValidPersonelGorev constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final PersonelGorev gorev, final ConstraintValidatorContext context) {
        if (gorev == null) {
            return true;
        }
        // because baslangicTarihi is a required field, it is assumed not null
        if ((gorev.getGorevBitisTarihi() != null) && (gorev.getGorevBaslamaTarihi().compareTo(gorev.getGorevBitisTarihi()) > 0)) {
            this.raiseFlag(this.bundle.getString("valid.time.tips"), context);
            return false;
        }
        return true;
    }

    private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

}
