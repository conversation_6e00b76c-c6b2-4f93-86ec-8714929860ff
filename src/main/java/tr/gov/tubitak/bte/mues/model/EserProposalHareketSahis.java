package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "EP_ESER_HAREKET_SAHIS")
public class EserProposalHareketSahis extends AbstractEntity implements DeleteValidatable {

    private static final long   serialVersionUID = 8582999553642405009L;

    @JoinColumn(name = "SAHIS", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Sahis               sahis;

    @JoinColumn(name = "ESER_HAREKET", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposalHareket eserHareket;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String              aciklama;

    public EserProposalHareketSahis() {
        // intentially blank constructor
    }

    // getters and setters ....................................................

    public Sahis getSahis() {
        return this.sahis;
    }

    public void setSahis(final Sahis sahis) {
        this.sahis = sahis;
    }

    public EserProposalHareket getEserHareket() {
        return this.eserHareket;
    }

    public void setEserHareket(final EserProposalHareket eserHareket) {
        this.eserHareket = eserHareket;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        if (this.getSahis() != null) {
            return Optional.ofNullable(this.getSahis().getTitle()).orElse("" + this.getId());
        }
        return "";
    }

    @Override
    public String getTitle() {
        return this.toString();
    }

}
