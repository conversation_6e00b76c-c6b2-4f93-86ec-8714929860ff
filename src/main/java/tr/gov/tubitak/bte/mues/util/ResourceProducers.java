package tr.gov.tubitak.bte.mues.util;

import java.util.Locale;
import java.util.ResourceBundle;

import javax.enterprise.context.ApplicationScoped;
import javax.enterprise.context.Dependent;
import javax.enterprise.context.RequestScoped;
import javax.enterprise.inject.Produces;
import javax.enterprise.inject.spi.InjectionPoint;
import javax.faces.context.FacesContext;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApplicationScoped
public class ResourceProducers {

    // use @SuppressWarnings to tell IDE to ignore warnings about field not being referenced directly
    /**
     * If omnifaces doesn't include CDI injectable PersistenceContext producer, then this could be used instead.
     * <p>
     * It injects <code>PersistenceContext</code>
     * <p>
     * and can be injected by typing the following:
     * 
     * <pre>
     * {@literal @}Inject
     * EnityManager em;
     * </pre>
     * 
     * CDI will automatically inject it. Persistence unit name also can be added to specify which database to inject.
     * <p>
     * Will it work? Try and see.
     * <p>
     * Some concerns exist in stackoverflow saying that method producer should be used instead field producer.
     */
    @Produces
    @PersistenceContext
    private EntityManager em;

    /**
     * Produces Logger instances. Can be injected into classes which will use logger.
     * <p>
     * To use it inside a class, just type the following
     * </p>
     * 
     * <pre>
     * {@literal @}Inject
     * Logger logger;
     * </pre>
     * 
     * CDI will automatically inject it.
     */
    @Produces
    public Logger produceLogger(final InjectionPoint injectionPoint) {
        return LoggerFactory.getLogger(injectionPoint.getMember().getDeclaringClass().getName());
    }

    /**
     * If omnifaces doesn't include CDI injectable FacesProducer, then this could be used instead.
     * <p>
     * It calls <code>FacesContext.getCurrentInstance()</code>
     * <p>
     * method and can be injected by typing the following:
     * 
     * <pre>
     * {@literal @}Inject
     * FacesContext facesContext;
     * </pre>
     * 
     * CDI will automatically inject it.
     */
    @Produces
    @RequestScoped
    public FacesContext produceFacesContext() {
        return FacesContext.getCurrentInstance();
    }

    @Produces
    @Dependent
    public ResourceBundle getResourceBundle() {
        return ResourceBundle.getBundle("/labels", FacesContext.getCurrentInstance() == null ? Locale.getDefault() : FacesContext.getCurrentInstance().getViewRoot().getLocale());
    }

}