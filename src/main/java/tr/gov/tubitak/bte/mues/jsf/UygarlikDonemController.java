package tr.gov.tubitak.bte.mues.jsf;

import java.time.Year;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.NoResultException;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Cag;
import tr.gov.tubitak.bte.mues.model.Donem;
import tr.gov.tubitak.bte.mues.model.Hukumdar;
import tr.gov.tubitak.bte.mues.model.Kronoloji;
import tr.gov.tubitak.bte.mues.model.Uygarlik;
import tr.gov.tubitak.bte.mues.model.UygarlikDonem;
import tr.gov.tubitak.bte.mues.model.mapping.TimeSpectrum;
import tr.gov.tubitak.bte.mues.session.CagFacade;
import tr.gov.tubitak.bte.mues.session.DonemFacade;
import tr.gov.tubitak.bte.mues.session.HukumdarFacade;
import tr.gov.tubitak.bte.mues.session.UygarlikDonemFacade;
import tr.gov.tubitak.bte.mues.session.UygarlikFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class UygarlikDonemController extends AbstractController<UygarlikDonem> {

    private static final long             serialVersionUID = 8441883835549916679L;

    @Inject
    private transient UygarlikDonemFacade facade;

    @Inject
    private transient ResourceBundle      bundle;

    @Inject
    private transient CagFacade           cagFacade;

    @Inject
    private transient DonemFacade         donemFacade;

    @Inject
    private transient UygarlikFacade      uygarlikFacade;

    @Inject
    private transient HukumdarFacade      hukumdarFacade;

    private Kronoloji                     kronoloji;

    private Cag                           cag;

    private Hukumdar                      hukumdar;

    private Integer                       signumStart_peg;

    private Integer                       signumEnd_peg;

    private Integer                       significantStart_peg;

    private Integer                       significantEnd_peg;

    private Integer                       signumStart;

    private Integer                       signumEnd;

    private Integer                       significantStart;

    private Integer                       significantEnd;

    public UygarlikDonemController() {
        super(UygarlikDonem.class);
    }
    
    @PostConstruct
    public void init() {
        if (this.getModel() == null) {
            this.setModel(new UygarlikDonem());  // Model nesnesi initialize ediliyor
        }
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.kronoloji = null;
        this.cag = null;
    }

    @Override
    public void showDetail(final UygarlikDonem item) {
        super.showDetail(item);
        this.kronoloji = this.getModel().getDonem().getCag().getKronoloji();
        this.cag = this.getModel().getDonem().getCag();
    }

    // filters ................................................................

    public List<Cag> filterByNameAndKronoloji(final String value) {
        return this.cagFacade.filterByNameAndKronoloji(value, this.kronoloji);
    }

    public List<Donem> filterByNameAndCag(final String value) {
        return this.donemFacade.filterByNameAndCag(value, this.cag);
    }

    public List<Uygarlik> filterByNameAndDonem(final String value) {
        return this.facade.filterByNameAndDonem(value, this.getModel().getDonem());
    }

    public List<Hukumdar> filterByNameAndUygarlik(final String value) {
        return this.hukumdarFacade.filterByNameAndUygarlik(value, this.getModel().getUygarlik());
    }

    // handlers ...............................................................

    public void handleTimeSpectrumSelect(final TimeSpectrum timeSpectrum) {
        if (timeSpectrum.getKronolojiId() != null) {
            this.kronoloji = new Kronoloji();
            this.kronoloji.setId(timeSpectrum.getKronolojiId());
            this.kronoloji.setAd(timeSpectrum.getKronolojiAdi());
            this.changeKronoloji(this.kronoloji);
        }
        if (timeSpectrum.getCagId() != null) {
            this.cag = this.cagFacade.findById(timeSpectrum.getCagId());
            this.changeCag(this.cag);
        }
        if (timeSpectrum.getDonemId() != null) {
            this.getModel().setDonem(this.donemFacade.findById(timeSpectrum.getDonemId()));
            this.changeDonem(this.getModel().getDonem());
        }
        if (timeSpectrum.getUygarlikId() != null) {
            this.getModel().setUygarlik(this.uygarlikFacade.findById(timeSpectrum.getUygarlikId()));
            this.changeUygarlik(this.getModel().getUygarlik());
        }
        if (timeSpectrum.getHukumdarId() != null) {
            this.hukumdar = this.hukumdarFacade.findById(timeSpectrum.getHukumdarId());
            this.changeHukumdar(this.hukumdar);
        }
    }

    public void handleKronolojiChange() {
        this.changeKronoloji(this.kronoloji);
    }

    public void handleCagChange() {
        this.changeCag(this.cag);
    }

    public void handleDonemChange() {
        this.changeDonem(this.getModel().getDonem());
    }

    public void handleUygarlikChange() {
        this.changeUygarlik(this.getModel().getUygarlik());
    }

    public void handleHukumdarChange() {
        this.changeHukumdar(this.hukumdar);
    }

    // changers ...............................................................

    public void changeKronoloji(final Kronoloji kronoloji) {
        this.setKronoloji(kronoloji);
        this.changeCag(null);
        this.fixFrontValues(null, null);
    }

    public void changeCag(final Cag cag) {
        this.setCag(cag);
        this.changeDonem(null);

        if (cag != null) {
            this.fixFrontValues(cag.getTermStart(), cag.getTermEnd());
        }
    }

    public void changeDonem(final Donem donem) {
    	this.getModel().setDonem(donem);
        this.changeUygarlik(null);

        try {
            if (this.getModel() != null && this.getModel().getDonem() != null) {
                Optional.ofNullable(this.getFacade().findByDonem(this.getModel().getDonem())).ifPresent(x -> this.fixDate(x));
                this.fixFrontValues(this.getModel().getTermStart(), this.getModel().getTermEnd());
            }
        } catch (final NoResultException e) {
            MuesUtil.showMessage("Uyarı", "Donem ile ilişkilendirilmiş tarih bulunamadı.", FacesMessage.SEVERITY_INFO);
        }
    }

    public void changeUygarlik(final Uygarlik uygarlik) {
    	this.getModel().setUygarlik(uygarlik);
        this.changeHukumdar(null);

        if (this.getModel()!= null && this.getModel().getUygarlik() != null) {
            final List<UygarlikDonem> itemsByDonemUygarlik = this.itemsByDonemUygarlik(this.getModel());
            if (!itemsByDonemUygarlik.isEmpty()) {
                this.fixDate(itemsByDonemUygarlik.get(0));

            }

            this.fixFrontValues(this.getModel().getTermStart(), this.getModel().getTermEnd());
        }
    }

    public void changeHukumdar(final Hukumdar hukumdar) {
        this.setHukumdar(hukumdar);

        if (this.getHukumdar() != null) {
            this.getModel().setTermStart(this.getHukumdar().getTermStart());
            this.getModel().setTermEnd(this.getHukumdar().getTermEnd());

            if ((hukumdar.getTermStart() != null) && (hukumdar.getTermEnd() != null)) {
                this.fixFrontValues(hukumdar.getTermStart(), hukumdar.getTermEnd());
            }
        }
    }

    public void fixTimeRelatedFieldsInModel() {
        if ((this.signumStart != null) && (this.significantStart != null)) {
            this.getModel().setTermStart(MuesUtil.toTimeline(this.signumStart, this.significantStart));
        }
        if ((this.signumEnd != null) && (this.significantEnd != null)) {
            this.getModel().setTermEnd(MuesUtil.toTimeline(this.signumEnd, this.significantEnd));
        }
    }

    public void fixStartTerm(final Integer start) {
        this.signumStart_peg = this.signumStart = start < 0 ? (start < AbstractParameters.getPrehistoryThreshold() ? 3 : 2) : 1;
        this.significantStart_peg = this.significantStart = start < 0 ? -start : start;
    }

    public void fixEndTerm(final Integer end) {
        this.signumEnd_peg = this.signumEnd = end < 0 ? (end < AbstractParameters.getPrehistoryThreshold() ? 3 : 2) : 1;
        this.significantEnd_peg = this.significantEnd = end < 0 ? -end : end;
    }

    // validators .............................................................

    public void validateEserTerms() {
        if (this.signumStart == null) {
            this.signumStart = 0;
        }
        if (this.significantStart == null) {
            this.significantStart = 0;
        }
        if (this.signumEnd == null) {
            this.signumEnd = 0;
        }
        if (this.significantEnd == null) {
            this.significantEnd = 0;
        }

        if (this.signumStart == 3) {
            if (-this.significantStart >= AbstractParameters.getPrehistoryThreshold()) {
                this.signumStart = 2;
            }
        } else if (this.signumStart == 2) {
            if (-this.significantStart < AbstractParameters.getPrehistoryThreshold()) {
                this.signumStart = 3;
            }
        }
        if (this.signumEnd == 3) {
            if (-this.significantEnd >= AbstractParameters.getPrehistoryThreshold()) {
                this.signumEnd = 2;
            }
        } else if (this.signumEnd == 2) {
            if (-this.significantEnd < AbstractParameters.getPrehistoryThreshold()) {
                this.signumEnd = 3;
            }
        }

        final Integer startYear = MuesUtil.toTimeline(this.signumStart, this.significantStart);
        final Integer endYear = MuesUtil.toTimeline(this.signumEnd, this.significantEnd);

        if ((startYear > endYear) || (startYear > MuesUtil.getThisYear()) || (endYear > MuesUtil.getThisYear())) {
            this.fixTimeFields();
            this.fireMessage("Başlangıç tarihi bitiş tarininden büyük olamaz.");
            return;
        }

        if ((this.signumStart_peg == null) || (this.significantStart_peg == null) || (this.signumEnd_peg == null) || (this.significantEnd_peg == null)) {
            return;
        }
        final Integer startYearPeg = MuesUtil.toTimeline(this.signumStart_peg, this.significantStart_peg);
        final Integer endYearPeg = MuesUtil.toTimeline(this.signumEnd_peg, this.significantEnd_peg);
        if ((startYear < startYearPeg) || (endYear > endYearPeg)) {
            this.fixTimeFields();
            this.fireMessage("Başlangıç ve bitiş tarihleri izin verilen aralıklar harici olamaz.");
        }
    }

    public void validateUygarlikDonemTerms() {
        if ((this.getModel().getSignumStart() == null)
            || (this.getModel().getSignumEnd() == null)
            || (this.getModel().getSignificantStart() == null)
            || (this.getModel().getSignificantEnd() == null)) {
            return;
        }

        this.getModel().setSignumEnd(this.toSignum(this.getModel().getSignumEnd(), this.getModel().getSignificantEnd()));
        this.getModel().setSignumStart(this.toSignum(this.getModel().getSignumStart(), this.getModel().getSignificantStart()));

        final Integer startYear = MuesUtil.toTimeline(this.getModel().getSignumStart(), this.getModel().getSignificantStart());
        final Integer endYear = MuesUtil.toTimeline(this.getModel().getSignumEnd(), this.getModel().getSignificantEnd());

        if (startYear > endYear) {
            MuesUtil.showMessage("Validasyon hatası", "'Süre sonu' 'Süre başlangıcı'ndan küçük olamaz.", FacesMessage.SEVERITY_WARN);
            return;
        }
        if ((startYear > Year.now().getValue()) || (endYear > Year.now().getValue())) {
            MuesUtil.showMessage("Validasyon hatası", "'Süre başlangıcı' veya 'Süre sonu' günümüzden sonra olamaz.", FacesMessage.SEVERITY_WARN);
            return;
        }
    }

    // utilities ..............................................................

    private List<UygarlikDonem> itemsByDonemUygarlik(final UygarlikDonem donemUygarlik) {
        return this.getFacade().findByDonemAndUygarlik(donemUygarlik.getDonem(), donemUygarlik.getUygarlik());
    }

    private void fireMessage(final String message) {
        MuesUtil.showMessage("eserForm:endTerm", this.bundle.getString("base.warning"), message, FacesMessage.SEVERITY_WARN);
    }

    public void fixFrontValues(final Integer start, final Integer end) {
        this.fixStart_pegs(start);
        this.fixEnd_pegs(end);
        this.fixTimeFields();
    }

    private void fixTimeFields() {
        this.significantStart = this.significantStart_peg;
        this.significantEnd = this.significantEnd_peg;
        this.signumStart = this.signumStart_peg;
        this.signumEnd = this.signumEnd_peg;
    }

    private void fixDate(final UygarlikDonem donemUygarlik) {
        this.getModel().setSignumStart(donemUygarlik.getSignumStart());
        this.getModel().setSignumEnd(donemUygarlik.getSignumEnd());
        this.getModel().setTermStart(donemUygarlik.getTermStart());
        this.getModel().setTermEnd(donemUygarlik.getTermEnd());
    }

    private Integer toSignum(final Integer sign, final Integer date) {
        if ((sign == 3) && (-date > AbstractParameters.getPrehistoryThreshold())) {
            return 2;
        }
        if ((sign == 2) && (-date < AbstractParameters.getPrehistoryThreshold())) {
            return 3;
        }
        return sign;
    }

    // fix pegs ...............................................................

    private void fixStart_pegs(final Integer start) {
        if (start == null) {
            this.significantStart_peg = null;
            this.signumStart_peg = null;

        } else {
            if (start >= 0) {
                this.significantStart_peg = start;
                this.signumStart_peg = 1;

            } else {
                if (start >= AbstractParameters.getPrehistoryThreshold()) {
                    this.significantStart_peg = -start;
                    this.signumStart_peg = 2;

                } else {
                    this.significantStart_peg = -start;
                    this.signumStart_peg = 3;
                }
            }
        }
    }

    private void fixEnd_pegs(final Integer end) {
        if (end == null) {
            this.significantEnd_peg = null;
            this.signumEnd_peg = null;

        } else {
            if (end >= 0) {
                this.significantEnd_peg = end;
                this.signumEnd_peg = 1;

            } else {
                if (end >= AbstractParameters.getPrehistoryThreshold()) {
                    this.significantEnd_peg = -end;
                    this.signumEnd_peg = 2;

                } else {
                    this.significantEnd_peg = -end;
                    this.signumEnd_peg = 3;
                }
            }
        }
    }

    // getters and setters ....................................................

    @Override
    public UygarlikDonemFacade getFacade() {
        return this.facade;
    }

    public Cag getCag() {
        return this.cag;
    }

    public void setCag(final Cag cag) {
        this.cag = cag;
    }

    public Kronoloji getKronoloji() {
        return this.kronoloji;
    }

    public void setKronoloji(final Kronoloji kronoloji) {
        this.kronoloji = kronoloji;
    }

    public Hukumdar getHukumdar() {
        return this.hukumdar;
    }

    public void setHukumdar(final Hukumdar hukumdar) {
        this.hukumdar = hukumdar;
    }

    public Integer getSignumStart() {
        return this.signumStart;
    }

    public void setSignumStart(final Integer signumStart) {
        this.signumStart = signumStart;
    }

    public Integer getSignumEnd() {
        return this.signumEnd;
    }

    public void setSignumEnd(final Integer signumEnd) {
        this.signumEnd = signumEnd;
    }

    public Integer getSignificantStart() {
        return this.significantStart;
    }

    public void setSignificantEnd(final Integer significantEnd) {
        this.significantEnd = significantEnd;
    }

    public Integer getSignificantEnd() {
        return this.significantEnd;
    }

    public void setSignificantStart(final Integer significantStart) {
        this.significantStart = significantStart;
    }

}
