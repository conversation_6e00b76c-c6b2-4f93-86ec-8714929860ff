package tr.gov.tubitak.bte.mues.search.controller;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.shaded.json.JSONArray;
import org.primefaces.shaded.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.jsf.FileUploadHelper;
import tr.gov.tubitak.bte.mues.jsf.MuesParameters;
import tr.gov.tubitak.bte.mues.model.EserFotograf;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.MultipartUtility;

@Named
@ViewScoped
public class ImageSearchUtil implements Serializable {

    private static final long            serialVersionUID               = 1L;

    private static final String          SIMILARITY_PREDICTION_REST_URL = "similarity.prediction.rest.url";

    private static final Logger          logger                         = LoggerFactory.getLogger(ImageSearchUtil.class);

    @Inject
    private FileUploadHelper             fileUploadHelper;

    @Inject
    private transient AbstractParameters parameters;

    @Inject
    private transient MuesParameters     muesParameters;

    private EserFotograf                 selectedPhoto;

    private String                       predictionURL;

    public ImageSearchUtil() {
        // Blank constructor
    }

    @PostConstruct
    public void init() {
        // set default URL for Eser
        this.setPredictionURL(this.parameters.get(SIMILARITY_PREDICTION_REST_URL));
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografBasligi(MuesUtil.extractFileName(event.getFile().getFileName()));
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public EserFotograf getModel() {
        if (this.selectedPhoto == null) {
            this.selectedPhoto = new EserFotograf();
        }
        return this.selectedPhoto;
    }

    @PreDestroy
    public void removePhoto() {
        if ((this.selectedPhoto != null) && (this.selectedPhoto.getFotografPath() != null)) {
            try {
                if (this.selectedPhoto.getFotografPath().startsWith(this.parameters.getTempDir())) {
                    Files.deleteIfExists(Paths.get(this.selectedPhoto.getFotografPath()));
                }
            } catch (final IOException e) {
                logger.error("[preDestroy] : Hata : {}", e.getMessage(), e);
            }
        }
    }

    /***
     * Search for content of image using rest service without ids
     * 
     * @return Eser ids
     */
    public List<String> searchEserIdsByImageSimilarity() {
        return this.searchEserIdsByImageSimilarity(null, null);
    }

    /***
     * Search for content of image using rest service
     * 
     * @return Eser ids
     */
    public List<String> searchEserIdsByImageSimilarity(final String solrIds, final Integer artifact_type) {
        try {
            if (this.getModel().getFotografPath() != null) {
                final File uploadFile = new File(this.getModel().getFotografPath());
                final String requestURL = this.getPredictionURL();

                final MultipartUtility multipart = new MultipartUtility(requestURL, "UTF-8");
                multipart.addFilePart("image", uploadFile);

                if (solrIds != null) {
                    multipart.addFormField("ids", solrIds.trim());
                }
                multipart.addFormField("artifact_type", artifact_type.toString());

                final List<String> response = multipart.finish();
                final StringBuilder sb = new StringBuilder();
                for (final String line : response) {
                    sb.append(line);
                }
                logger.info("Image Similarity response: {}", sb.toString());
                final List<String> ids = new ArrayList<>();
                final JSONArray jsonArray = new JSONArray(sb.toString());
                for (final Object object : jsonArray) {
                    ids.add(((JSONObject) object).get("id").toString());
                }
                return ids;
            }
        } catch (final NullPointerException | IOException e) {
            logger.error("[searchEserIdsByImageSimilarity] : Hata : {}", e.getMessage(), e);
        }
        return Collections.emptyList();
    }

    /***
     * Boost top 100 and rest of the ids for solr query (uses boost factor squaring the index)
     * 
     * @param ids
     * @return boosted top 100 and rest of the ids as string
     */
    public String addBoostForSolr(final List<String> uids) {
        int boostFactor = 100; // boosting top 100 records
        final StringBuilder boosted = new StringBuilder();
        for (final String uid : uids) {
            if (boostFactor > 1) {
                boosted.append(uid + "^" + Math.pow(boostFactor, 3) + " ");
                boostFactor--;
            } else {
                boosted.append(uid + " ");
            }
        }
        return boosted.toString();
    }

    /***
     * 
     * Copy file to show on GUI using context menu selection
     * 
     * @param filePath
     */
    public void setFile(final String filePath) {
        this.setFile(null, filePath);
    }

    public void setFile(final String artifactType, final String filePath) {
        final String newFileName = UUID.randomUUID().toString();
        final Path tempPath = Paths.get(this.parameters.getTempDir(), newFileName);
        try {
            if ((artifactType != null) && artifactType.equals("INVENTORY_ARTIFACT")) {
                Files.copy(this.muesParameters.getAbsolutePath(filePath, FolderType.IMAGE_AK), tempPath);
            } else {
                Files.copy(this.parameters.getAbsolutePath(filePath, FolderType.IMAGE_AK), tempPath);
            }
            this.getModel().setFotografPath(tempPath.toString());
        } catch (final IOException e) {
            logger.error("[setFile] : Hata : {}", e.getMessage(), e);
        }
    }

    public String getPredictionURL() {
        return this.predictionURL;
    }

    public void setPredictionURL(final String predictionURL) {
        this.predictionURL = predictionURL;
    }

}
