package tr.gov.tubitak.bte.mues.session;

import java.util.Calendar;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.enterprise.context.RequestScoped;

import org.apache.shiro.SecurityUtils;

import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.audits.Audit;

@RequestScoped
public class AuditFacade extends AbstractFacade<Audit> {

    /**
     * 
     */
    private static final long serialVersionUID = -1735152252239190961L;

    public AuditFacade() {
        super(Audit.class);
    }

    public void log(final Audit audit) {

        audit.setUserIp(MuesUtil.fetchUserIp());
        audit.setTimestamp(Calendar.getInstance().getTime().getTime());

        this.handleCreation(audit);
    }

    public void log(final AuditEvent type, final String... summary) {
        final Audit audit = new Audit();

        final Object user = SecurityUtils.getSubject().getPrincipal();
        Optional.ofNullable(user).ifPresent(x -> audit.setUserName(user.toString()));

        audit.setRevType(type.getCode());
        audit.setUserIp(MuesUtil.fetchUserIp());

        audit.setAciklama(MuesUtil.extractFileName(Stream.of(summary).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(", ")), 254));
        audit.setTimestamp(Calendar.getInstance().getTime().getTime());

        this.handleCreation(audit);
    }

    private void handleCreation(final Audit audit) {
        this.create(audit);
        this.logger.info("Persisted Audit Event: {}", audit);
    }

}
