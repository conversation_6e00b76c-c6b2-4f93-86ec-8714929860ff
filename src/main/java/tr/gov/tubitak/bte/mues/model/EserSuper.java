package tr.gov.tubitak.bte.mues.model;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

import org.apache.solr.client.solrj.beans.Field;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.util.Encryptor;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)

@MappedSuperclass

@NamedQuery(name = "Eser.findByNameAndMudurlukExcludeIds", query = "SELECT DISTINCT e FROM Eser e  LEFT JOIN FETCH e.anaFotograf ef "
                                                                   + "JOIN e.eserDepos d JOIN d.alanKonumu k JOIN k.alan a JOIN a.bina b JOIN b.bagliBirim bb JOIN bb.mudurluk m "
                                                                   + "WHERE e.aktif = true AND e.silinmis = false AND e.versiyon = 1 AND e.id NOT IN :ids AND bb.mudurluk in :muzeler "
                                                                   + "AND (e.eserOzelAdi LIKE :ad OR e.permanentId LIKE :ad) ORDER BY e.id DESC")
@NamedQuery(name = "Eser.getLastEserOfUserId", query = "SELECT e FROM Eser e "
                                                       + "LEFT JOIN FETCH e.eserDepos d  LEFT JOIN FETCH d.alanKonumu dk LEFT JOIN FETCH dk.alan da LEFT JOIN FETCH da.bina db LEFT JOIN FETCH db.bagliBirim dbb LEFT JOIN FETCH dbb.mudurluk "
                                                       + "LEFT JOIN FETCH e.updatedBy ed WHERE ed.id = :userId ORDER BY e.dateUpdated DESC")

public class EserSuper extends AbstractEntity {

    private static final long          serialVersionUID = 5834980331509052387L;

    private static final String        TR_M             = "TR.M.%s";

    private static final String        M_O              = "M.Ö. ";

    private static final String        G_O              = "G.Ö. ";

    private static final String        M_S              = "M.S. ";

    private static final DecimalFormat df               = new DecimalFormat("000,000,000");

    @Field("eserEserId")
    @Transient
    private String                     eserId;

    @Column(name = "permanentId")
    private Integer                    permanentId;

    @Column(name = "VERSIYON")
    private Integer                    versiyon;

    @Field("eserGenelAciklama")
    @Size(max = 4000)
    @Column(name = "GENEL_ACIKLAMA", length = 4000)
    private String                     genelAciklama;

    @Field("eserEskiEnvanterNo")
    @Size(max = 50)
    @Column(name = "ENVANTER_NO", length = 50)
    private String                     envanterNo;

    @Field("eserTasinirIslemFisiNo")
    @Size(max = 50)
    @Column(name = "TASINIR_ISLEM_FISI_NO", length = 50)
    private String                     tasinirIslemFisiNo;

    @Field("webSite")
    @Size(max = 150)
    @Column(name = "webSite", length = 150)
    private String                     webSite;

    @Size(max = 150)
    @Column(name = "ESER_OZEL_ADI", length = 150)
    private String                     eserOzelAdi;

    @Column(name = "SIKKE_DARP_YILI")
    private Integer                    uretimYili;

    @Column(name = "SIKKE_DARP_YILI_BITIS")
    private Integer                    uretimYiliEnd;

    @Column(name = "SIKKE")
    private boolean                    sikke;

    @Column(name = "UNIKLIK_DURUMU")
    private boolean                    uniklikDurumu;

    @Column(name = "TORENSEL_DURUMU")
    private boolean                    torenselDurumu;

    @Column(name = "updateInProgress")
    private boolean                    updateInProgress;

    @Column(name = "DONEM_BASLANGIC_YIL")
    private Integer                    termStart;

    @Column(name = "DONEM_BITIS_YIL")
    private Integer                    termEnd;

    @Field("eserKiymet")
    @Min(value = 0)
    @Max(value = 999999999999999999L)
    @Column(name = "KIYMET")
    private BigDecimal                 kiymet;

    @Size(max = 40)
    @Column(name = "createSessionId")
    private String                     createSessionId;

    @Column(name = "YARATMA_ZAMANI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       dateCreated;

    @Column(name = "DUZENLEME_ZAMANI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       dateUpdated;

    @Column(name = "SILME_ZAMANI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       dateDeleted;

    @Size(max = 150)
    @Column(name = "SILME_ACIKLAMASI", length = 150)
    private String                     silmeAciklamasi;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "ENVANTER_DEFTERI_PATH", length = 250)
    private String                     envanterDefteriPath;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "ENVANTER_FISH_PATH", length = 250)
    private String                     envanterFishiPath;

    @Size(max = 24)
    @Column(name = "KAREKOD", length = 24)
    private String                     qrCode;

    @Column(name = "birlestirilmisEser")
    private Boolean                    birlestirilmisEser;

    @Column(name = "ayrilmisEser")
    private Boolean                    ayrilmisEser;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserZimmet>            eserZimmets;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESER_ALT_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserAltTur                 eserAltTur;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "iliskilendirmeId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Iliskilendirme             iliskilendirme;

    @Size(max = 150)
    @Column(name = "kronolojiAciklama", length = 150)
    private String                     kronolojiAciklama;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "cagId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Cag                        cag;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "CAG_DONEM_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Donem                      donem;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "UYGARLIK_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Uygarlik                   uygarlik;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "HUKUMDAR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Hukumdar                   hukumdar;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "TASINIR_MAL_YON_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private TasinirMalYonetmeligiKod   tasinirMalYonKod;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESERI_BAGISLAYAN_ONEMLI_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Vip                        bagislayanVip;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESERI_YAPTIRAN_ONEMLI_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Vip                        yaptiranVip;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESERI_YAPAN_ONEMLI_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Vip                        yapanVip;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESERI_KULLANACAK_ONEMLI_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Vip                        kullanacakVip;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ESERI_KULLANAN_ONEMLI_KISI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Vip                        kullananVip;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "uretimYeriId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private UretimYeri                 uretimYeri;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "uretimBolgesiId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private UretimBolgesi              uretimBolgesi;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "darpYeriId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private DarpYeri                   darpYeri;

    @Transient
    private Integer                    signumStart;

    @Transient
    private Integer                    signumEnd;

    @Transient
    private Integer                    signumUretim;

    @Transient
    private Integer                    signumUretimEnd;


    /**
     * Instantiates a new eser.
     */
    public EserSuper() {
        // empty constructor for generation of model
    }
    // getters and setters ....................................................

    public String getCreateSessionId() {
        return this.createSessionId;
    }

    public void setCreateSessionId(final String createSessionId) {
        this.createSessionId = createSessionId;
    }

    public String getGenelAciklama() {
        return this.genelAciklama;
    }

    public void setGenelAciklama(final String genelAciklama) {
        this.genelAciklama = genelAciklama;
    }

    public String getEnvanterNo() {
        return this.envanterNo;
    }

    public void setEnvanterNo(final String envanterNo) {
        this.envanterNo = envanterNo;
    }

    public String getTasinirIslemFisiNo() {
        return this.tasinirIslemFisiNo;
    }

    public void setTasinirIslemFisiNo(final String tasinirIslemFisiNo) {
        this.tasinirIslemFisiNo = tasinirIslemFisiNo;
    }

    public String getEserOzelAdi() {
        return this.eserOzelAdi;
    }

    public void setEserOzelAdi(final String eserOzelAdi) {
        this.eserOzelAdi = eserOzelAdi;
    }

    public boolean getUniklikDurumu() {
        return this.uniklikDurumu;
    }

    public void setUniklikDurumu(final boolean uniklikDurumu) {
        this.uniklikDurumu = uniklikDurumu;
    }

    public boolean getTorenselDurumu() {
        return this.torenselDurumu;
    }

    public void setTorenselDurumu(final boolean torenselDurumu) {
        this.torenselDurumu = torenselDurumu;
    }

    public boolean isUpdateInProgress() {
        return this.updateInProgress;
    }

    public void setUpdateInProgress(final boolean updateInProgress) {
        this.updateInProgress = updateInProgress;
    }

    public Integer getTermStart() {
        return this.termStart;
    }

    public void setTermStart(final Integer termStart) {
        this.termStart = termStart;
    }

    public Integer getTermEnd() {
        return this.termEnd;
    }

    public void setTermEnd(final Integer termEnd) {
        this.termEnd = termEnd;
    }

    public BigDecimal getKiymet() {
        return this.kiymet;
    }

    public void setKiymet(final BigDecimal kiymet) {
        this.kiymet = kiymet;
    }

    public Date getDateCreated() {
        return this.dateCreated;
    }

    public void setDateCreated(final Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Date getDateUpdated() {
        return this.dateUpdated;
    }

    public void setDateUpdated(final Date dateUpdated) {
        this.dateUpdated = dateUpdated;
    }

    public Date getDateDeleted() {
        return this.dateDeleted;
    }

    public void setDateDeleted(final Date dateDeleted) {
        this.dateDeleted = dateDeleted;
    }

    public String getSilmeAciklamasi() {
        return this.silmeAciklamasi;
    }

    public void setSilmeAciklamasi(final String silmeAciklamasi) {
        this.silmeAciklamasi = silmeAciklamasi;
    }

    public String getEnvanterDefteriPath() {
        return this.envanterDefteriPath;
    }

    public void setEnvanterDefteriPath(final String envanterDefteriPath) {
        this.envanterDefteriPath = envanterDefteriPath;
    }

    public Integer getVersiyon() {
        return this.versiyon;
    }

    public void setVersiyon(final Integer versiyon) {
        this.versiyon = versiyon;
    }

    public Integer getPermanentId() {
        return this.permanentId;
    }

    public void setPermanentId(final Integer permanentId) {
        this.permanentId = permanentId;
        if (permanentId != null) {
            final Optional<String> tmp = Encryptor.encrypt(String.valueOf(permanentId));
            if (tmp.isPresent()) {
                this.qrCode = tmp.get();
            }
        }
    }

    public EserAltTur getEserAltTur() {
        return this.eserAltTur;
    }

    public void setEserAltTur(final EserAltTur eserAltTur) {
        this.eserAltTur = eserAltTur;
    }

    public Iliskilendirme getIliskilendirme() {
        return this.iliskilendirme;
    }

    public void setIliskilendirme(final Iliskilendirme iliskilendirme) {
        this.iliskilendirme = iliskilendirme;
    }

    public Cag getCag() {
        return this.cag;
    }

    public void setCag(final Cag cag) {
        this.cag = cag;
    }

    public Donem getDonem() {
        return this.donem;
    }

    public void setDonem(final Donem donem) {
        this.donem = donem;
    }

    public Hukumdar getHukumdar() {
        return this.hukumdar;
    }

    public void setHukumdar(final Hukumdar hukumdar) {
        this.hukumdar = hukumdar;
    }

    public Vip getYaptiranVip() {
        return this.yaptiranVip;
    }

    public void setYaptiranVip(final Vip yaptiranVip) {
        this.yaptiranVip = yaptiranVip;
    }

    public TasinirMalYonetmeligiKod getTasinirMalYonKod() {
        return this.tasinirMalYonKod;
    }

    public void setTasinirMalYonKod(final TasinirMalYonetmeligiKod tasinirMalYonKod) {
        this.tasinirMalYonKod = tasinirMalYonKod;
    }

    public Uygarlik getUygarlik() {
        return this.uygarlik;
    }

    public void setUygarlik(final Uygarlik uygarlik) {
        this.uygarlik = uygarlik;
    }

    public Vip getBagislayanVip() {
        return this.bagislayanVip;
    }

    public void setBagislayanVip(final Vip bagislayanVip) {
        this.bagislayanVip = bagislayanVip;
    }

    public Vip getYapanVip() {
        return this.yapanVip;
    }

    public void setYapanVip(final Vip yapanVip) {
        this.yapanVip = yapanVip;
    }

    public Vip getKullanacakVip() {
        return this.kullanacakVip;
    }

    public void setKullanacakVip(final Vip kullanacakVip) {
        this.kullanacakVip = kullanacakVip;
    }

    public Vip getKullananVip() {
        return this.kullananVip;
    }

    public void setKullananVip(final Vip kullananVip) {
        this.kullananVip = kullananVip;
    }

    public UretimYeri getUretimYeri() {
        return this.uretimYeri;
    }

    public void setUretimYeri(final UretimYeri uretimYeri) {
        this.uretimYeri = uretimYeri;
    }

    public UretimBolgesi getUretimBolgesi() {
        return this.uretimBolgesi;
    }

    public void setUretimBolgesi(final UretimBolgesi uretimBolgesi) {
        this.uretimBolgesi = uretimBolgesi;
    }

    public DarpYeri getDarpYeri() {
        return this.darpYeri;
    }

    public void setDarpYeri(final DarpYeri darpYeri) {
        this.darpYeri = darpYeri;
    }

    public String getEserId() {
        if (this.getId() == null) {
            return "---";
        }
        if (this.permanentId == null) {
            return "G." + this.getId().toString();
        }
        return String.format(this.getPrefix(), df.format(this.permanentId).replace(",", "."));
    }

    public void setEserId(final String eserId) {
        this.eserId = eserId;
    }

    public String getAssetId() {

        if (this.permanentId == null) {
            return null;
        }
        return String.format(this.getPrefix(), df.format(this.permanentId).replace(",", "."));
    }

    public String getTempId() {
        if (this.permanentId == null) {
            return "G." + this.getId();
        }
        return null;
    }

    public String getOneId() {
        if (this.permanentId == null) {
            return "G." + this.getId();
        }
        return String.format(this.getPrefix(), df.format(this.permanentId).replace(",", "."));
    }

    public Integer getUretimYili() {
        return this.uretimYili;
    }

    public void setUretimYili(final Integer uretimYili) {
        if (this.getSignumUretim() != null) {
            // it is already positive after Christ
            if (this.getSignumUretim() == 1) {
                this.uretimYili = uretimYili;

            } else {
                // set negative if before Christ
                this.uretimYili = -uretimYili;
            }
        }
    }

    public Integer getUretimYiliEnd() {
        return this.uretimYiliEnd;
    }

    public void setUretimYiliEnd(final Integer uretimYiliEnd) {
        if (this.getSignumUretimEnd() != null) {
            // it is already positive after Christ
            if (this.getSignumUretimEnd() == 1) {
                this.uretimYiliEnd = uretimYiliEnd;

            } else {
                // set negative if before Christ
                this.uretimYiliEnd = -uretimYiliEnd;
            }
        }
    }

    public Integer getSignumUretimEnd() {
        if ((this.signumUretimEnd == null) && (this.uretimYiliEnd != null)) {
            if (this.uretimYiliEnd > 0) {
                this.signumUretimEnd = 1;
            } else {
                this.signumUretimEnd = 2;
            }
        }
        return this.signumUretimEnd;
    }

    public Integer getSignificantStart() {
        if (this.termStart != null) {
            return this.termStart < 0 ? -this.termStart : this.termStart;
        }
        return null;
    }

    public void setSignificantStart(final Integer significantStart) {
        if (this.getSignumStart() != null) {
            // it is already positive after Christ
            if (this.getSignumStart() == 1) {
                this.termStart = significantStart;
            } else {
                // set negative if before Christ
                this.termStart = -significantStart;
            }
        }
    }

    public Integer getSignificantEnd() {
        if (this.termEnd != null) {
            return this.termEnd < 0 ? -this.termEnd : this.termEnd;
        }

        return null;
    }

    public void setSignificantEnd(final Integer significantEnd) {
        if (this.getSignumEnd() != null) {
            // it is already positive after Christ
            if (this.getSignumEnd() == 1) {
                this.termEnd = significantEnd;
            } else {
                // set negative if before Christ
                this.termEnd = -significantEnd;
            }
        }
    }

    public Integer getSignificantUretimEnd() {
        if (this.uretimYiliEnd != null) {
            return this.uretimYiliEnd < 0 ? -this.uretimYiliEnd : this.uretimYiliEnd;
        }
        return null;
    }

    public void setSignificantUretimEnd(final Integer uretimYiliEnd) {
        if (this.getSignumUretimEnd() != null) {
            // it is already positive after Christ
            if (this.getSignumUretimEnd() == 1) {
                this.uretimYiliEnd = uretimYiliEnd;
            } else {
                // set negative if before Christ
                // darp yılı seçildikten sonra yıl girilmediği zaman patlıyordu. if kontrolü eklendi 27.01.2020
                if (uretimYiliEnd != null) {
                    this.uretimYiliEnd = -uretimYiliEnd;
                } else {
                    this.uretimYiliEnd = uretimYiliEnd;
                }
            }
        }
    }

    public Integer getSignificantUretim() {
        if (this.uretimYili != null) {
            return this.uretimYili < 0 ? -this.uretimYili : this.uretimYili;
        }
        return null;
    }

    public void setSignificantUretim(final Integer uretimYili) {
        if (this.getSignumUretim() != null) {
            // it is already positive after Christ
            if (this.getSignumUretim() == 1) {
                this.uretimYili = uretimYili;
            } else {
                // set negative if before Christ
                // darp yılı seçildikten sonra yıl girilmediği zaman patlıyordu. if kontrolü eklendi 27.01.2020
                if (uretimYili != null) {
                    this.uretimYili = -uretimYili;
                } else {
                    this.uretimYili = uretimYili;
                }
            }
        }
    }

    public Integer getSignumStart() {
        if ((this.signumStart == null) && (this.termStart != null)) {
            if (this.termStart > 0) {
                this.signumStart = 1;
            } else {
                if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumStart = 2;
                } else {
                    this.signumStart = 3;
                }
            }
        }
        return this.signumStart;
    }

    public void setSignumStart(final Integer signumStart) {
        this.signumStart = signumStart;
    }

    public Integer getSignumEnd() {
        if ((this.signumEnd == null) && (this.termEnd != null)) {
            if (this.termEnd > 0) {
                this.signumEnd = 1;
            } else {
                if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumEnd = 2;
                } else {
                    this.signumEnd = 3;
                }
            }
        }
        return this.signumEnd;
    }

    public void setSignumEnd(final Integer signumEnd) {
        this.signumEnd = signumEnd;
    }

    public String getTermStartTitle() {
        if (this.termStart == null) {
            return null;
        }
        if (this.termStart < 0) {
            if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                return M_O + -this.termStart;
            }
            return G_O + -this.termStart;
        }
        return M_S + this.termStart;
    }

    public String getTermEndTitle() {
        if (this.termEnd == null) {
            return null;
        }
        if (this.termEnd < 0) {
            if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                return M_O + -this.termEnd;
            }
            return G_O + -this.termEnd;
        }
        return M_S + this.termEnd;
    }

    public String getUretimYiliTitle() {
        if (this.uretimYili == null) {
            return null;
        }
        if (this.uretimYili < 0) {
            return M_O + -this.uretimYili;
        }
        return M_S + this.uretimYili;
    }

    public String getUretimYiliEndTitle() {
        if (this.uretimYiliEnd == null) {
            return null;
        }
        if (this.uretimYiliEnd < 0) {
            return M_O + -this.uretimYiliEnd;
        }
        return M_S + this.uretimYiliEnd;
    }

    public Integer getSignumUretim() {
        if ((this.signumUretim == null) && (this.uretimYili != null)) {
            if (this.uretimYili > 0) {
                this.signumUretim = 1;
            } else {
                this.signumUretim = 2;
            }
        }
        return this.signumUretim;
    }

    public List<EserMalzemeSuslemeTeknigi> getListFromEserMalzemeSuslemeTeknigis(final Set<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigis) {
        return new ArrayList<>(eserMalzemeSuslemeTeknigis);
    }

    public void setSignumUretim(final Integer signumUretim) {
        this.signumUretim = signumUretim;
    }

    public void setSignumUretimEnd(final Integer signumUretimEnd) {
        this.signumUretimEnd = signumUretimEnd;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.getEserId()).orElse(this.eserOzelAdi);
    }

    @Override
    public String getTitle() {
        return Stream.of(this.getEserId(), MuesUtil.surroundWithParanthesis(this.eserOzelAdi)).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }

    public String getName() {
        return Optional.ofNullable(this.getEserOzelAdi()).orElse("" + this.getId());
    }

    public boolean isSikke() {
        return this.sikke;
    }

    public void setSikke(final boolean sikke) {
        this.sikke = sikke;
    }

    public String getQrCode() {
        return this.qrCode;
    }

    public void setQrCode(final String qrCode) {
        this.qrCode = qrCode;
    }

    public Set<EserZimmet> getEserZimmets() {
        return this.eserZimmets;
    }

    public void setEserZimmets(final Set<EserZimmet> eserZimmets) {
        this.eserZimmets = eserZimmets;
    }

    public String getEnvanterFishiPath() {
        return this.envanterFishiPath;
    }

    public void setEnvanterFishiPath(final String envanterFishiPath) {
        this.envanterFishiPath = envanterFishiPath;
    }

    public String getKronolojiAciklama() {
        return this.kronolojiAciklama;
    }

    public void setKronolojiAciklama(final String kronolojiAciklama) {
        this.kronolojiAciklama = kronolojiAciklama;
    }

    public Boolean getBirlestirilmisEser() {
        return this.birlestirilmisEser;
    }

    public void setBirlestirilmisEser(final Boolean birlestirilmisEser) {
        this.birlestirilmisEser = birlestirilmisEser;
    }

    public Boolean getAyrilmisEser() {
        return this.ayrilmisEser;
    }

    public void setAyrilmisEser(final Boolean ayrilmisEser) {
        this.ayrilmisEser = ayrilmisEser;
    }

    public String getWebSite() {
        return this.webSite;
    }

    public void setWebSite(final String webSite) {
        this.webSite = webSite;
    }

    public String getPrefix() {
        return TR_M;
    }

}
