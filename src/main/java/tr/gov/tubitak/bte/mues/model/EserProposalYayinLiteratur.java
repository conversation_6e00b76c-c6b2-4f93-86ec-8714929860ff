package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

/**
 *
 * ali.kelle
 */
@Entity
@Table(name = "EP_Eser_YayinLiteratur")
@NamedQuery(name = "EserProposalYayinLiteratur.findEagerById", query = "SELECT x FROM EserProposalYayinLiteratur x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.literatur WHERE x.id = :id")
@NamedQuery(name = "EserProposalYayinLiteratur.findAll", query = "SELECT x FROM EserProposalYayinLiteratur x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.literatur ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "EserProposalYayinLiteratur.findActive", query = "SELECT x FROM EserProposalYayinLiteratur x WHERE x.aktif = true AND x.silinmis = false")
public class EserProposalYayinLiteratur extends EserYayinLiteraturSuper {

    private static final long serialVersionUID = -1974633620324204562L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalYayinLiteratur() {
        // default constructor
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
