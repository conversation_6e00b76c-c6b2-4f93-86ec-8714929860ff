package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Keyword;

/**
 *
 */
@RequestScoped
public class KeywordFacade extends AbstractFacade<Keyword> {

    public KeywordFacade() {
        super(Keyword.class);
    }

    public List<Keyword> filterByNameAndKeyword(final String query, final List<Integer> keywordIds) {
        return this.em.createNamedQuery("Keyword.findByNameAndKeyword", Keyword.class).setParameter("ad", "%" + query + "%").setParameter("keywordIds", keywordIds).getResultList();
    }

}
