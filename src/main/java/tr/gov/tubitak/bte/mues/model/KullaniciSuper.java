package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Date;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "Kullanici.findEagerById", query = "SELECT k from Kullanici k LEFT JOIN FETCH k.personel p LEFT JOIN FETCH k.personelView pv LEFT JOIN FETCH p.mudurluk WHERE k.id = :id")
@NamedQuery(name = "Kullanici.findAll", query = "SELECT DISTINCT(k) from Kullanici k ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "Kullanici.findActive", query = "SELECT DISTINCT(k) from Kullanici k LEFT JOIN FETCH k.kullaniciBirimRols WHERE k.silinmis = false AND k.aktif = true ORDER BY k.ad")
@NamedQuery(name = "Kullanici.findByMudurluk", query = "SELECT DISTINCT(k) FROM Kullanici k LEFT JOIN FETCH k.kullaniciBirimRols kbr LEFT JOIN FETCH k.personel WHERE (kbr.mudurluk in :muzeler OR kbr.mudurluk IS NULL) ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "Kullanici.findByMudurlukActive", query = "SELECT DISTINCT(k) FROM Kullanici k JOIN FETCH k.kullaniciBirimRols kbr JOIN FETCH k.personel WHERE (kbr.mudurluk in :muzeler OR kbr.mudurluk IS NULL) AND k.silinmis = false AND k.aktif = true")
@NamedQuery(name = "Kullanici.findByMudurlukAndRol", query = "SELECT DISTINCT(k) FROM Kullanici k JOIN FETCH k.kullaniciBirimRols kbr JOIN FETCH k.personel WHERE  (kbr.mudurluk IN :muzeler) AND (kbr.rol IN :roller) ORDER BY k.ad")
@NamedQuery(name = "Kullanici.findByMuze", query = "SELECT DISTINCT(k) FROM Kullanici k JOIN FETCH k.kullaniciBirimRols kbr JOIN FETCH k.personel WHERE kbr.mudurluk IN :muzeler ORDER BY k.ad")
@NamedQuery(name = "Kullanici.findByUsername", query = "SELECT k FROM Kullanici k LEFT JOIN FETCH k.personel p LEFT JOIN FETCH p.mudurluk LEFT JOIN FETCH p.personelUzmanlikAlani pu LEFT JOIN FETCH pu.uzmanlikAlani LEFT JOIN FETCH p.personelYabanciDil py LEFT JOIN FETCH py.dil LEFT JOIN FETCH py.languageLevel WHERE k.kullaniciAdi = :tckn")
@NamedQuery(name = "Kullanici.findUsersRequestingPasswordReset", query = "SELECT k FROM Kullanici k LEFT JOIN FETCH k.personel WHERE k.aktif = true AND k.silinmis = false AND k.sifirlamaIstegi = true")
@NamedQuery(name = "Kullanici.findByNameAndMudurluks", query = "SELECT DISTINCT k FROM Kullanici k LEFT JOIN FETCH k.personel p LEFT JOIN FETCH k.kullaniciBirimRols kbr LEFT JOIN FETCH kbr.mudurluk "
                                                               + "LEFT JOIN FETCH kbr.rol WHERE k.silinmis = false AND k.aktif = true AND kbr.silinmis = false "
                                                               + "AND (p.calismaDurumu is null OR p.calismaDurumu = true) AND REPLACE(concat(k.ad, k.soyad), ' ', '') LIKE :str AND (kbr.mudurluk IN :muzeler OR kbr.mudurluk IS NULL)")
@NamedQuery(name = "Kullanici.filterByNameAndMudurlukExcludeIds", query = "SELECT DISTINCT k FROM Kullanici k JOIN FETCH k.personelView p "
                                                                          + "WHERE k.silinmis = false AND k.aktif = true AND p.calismaDurumu = true AND REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str AND (p.mudurluk IN :directorates AND k.id NOT IN :ids) ORDER BY p.ad,p.soyad DESC ")

@NamedQuery(name = "Kullanici.findByUsernames", query = "SELECT DISTINCT(k) FROM Kullanici k LEFT JOIN FETCH k.personelView pv "
                                                        + "LEFT JOIN FETCH k.kullaniciBirimRols kbr "
                                                        + "LEFT JOIN FETCH kbr.mudurluk m "
                                                        + "LEFT JOIN FETCH kbr.rol r "
                                                        + "WHERE k.kullaniciAdi IN :usernames")

public abstract class KullaniciSuper extends AbstractEntity implements EditPermissible {

    private static final long             serialVersionUID = -5384594000926869394L;

    @Size(max = 150)
    @Column(name = "KULLANICI_ADI", unique = true, length = 150)
    private String                        kullaniciAdi;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                        ad;

    @Size(max = 50)
    @Column(name = "SOYAD", length = 50)
    private String                        soyad;

    @Size(max = 44)
    @Column(name = "SIFRE_HASH", length = 44)
    private String                        sifreHash;

    @Size(max = 150)
    @Column(name = "GUVENLIK_SORUSU", length = 150)
    private String                        guvenlikSorusu;

    @Size(max = 15)
    @Column(name = "GUVENLIK_SORUSU_CEVABI", length = 15)
    private String                        guvenlikSorusuCevabi;

    @Basic
    @Column(name = "SON_MESAJ_NO")
    private int                           sonMesajNo;

    @Column(name = "HATALI_DENEME_SAYISI")
    private Integer                       hataliDenemeSayisi;

    @Column(name = "SON_GIRIS_ZAMANI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                          sonGirisZamani;

    @Column(name = "ONLINE_DURUMU")
    private Boolean                       onlineDurumu;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                        aciklama;

    @Column(name = "SIFIRLAMA_ISTEGI")
    private Boolean                       sifirlamaIstegi;

    @Column(name = "isGDPRApproved")
    private Boolean                       gdpApproved      = Boolean.FALSE;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "DUYURU_NUMARASI", referencedColumnName = "ID")
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    private Announcement                  duyuruNumarasi;

    @Deprecated
    @JoinColumn(name = "PERSONEL_ID", referencedColumnName = "ID")
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    private Personel                      personel;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToOne(optional = true, fetch = FetchType.LAZY)
    @JoinColumn(name = "PERSONEL_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    private PersonelView                  personelView;

    @OneToMany(mappedBy = "kullanici", fetch = FetchType.LAZY)
    private Collection<KullaniciBirimRol> kullaniciBirimRols;

    protected KullaniciSuper() {
        // default constructor
    }

    // getters and setters .........................................................

    public String getKullaniciAdi() {
        return this.kullaniciAdi;
    }

    public void setKullaniciAdi(final String kullaniciAdi) {
        this.kullaniciAdi = kullaniciAdi;
    }

    public String getAd() {
        return this.ad.trim();
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getSoyad() {
        return this.soyad.trim();
    }

    public void setSoyad(final String soyad) {
        this.soyad = soyad;
    }

    public String getSifreHash() {
        return this.sifreHash;
    }

    public void setSifreHash(final String sifreHash) {
        this.sifreHash = sifreHash;
    }

    public String getGuvenlikSorusu() {
        return this.guvenlikSorusu;
    }

    public void setGuvenlikSorusu(final String guvenlikSorusu) {
        this.guvenlikSorusu = guvenlikSorusu;
    }

    public String getGuvenlikSorusuCevabi() {
        return this.guvenlikSorusuCevabi;
    }

    public void setGuvenlikSorusuCevabi(final String guvenlikSorusuCevabi) {
        this.guvenlikSorusuCevabi = guvenlikSorusuCevabi;
    }

    public int getSonMesajNo() {
        return this.sonMesajNo;
    }

    public void setSonMesajNo(final int sonMesajNo) {
        this.sonMesajNo = sonMesajNo;
    }

    public Integer getHataliDenemeSayisi() {
        return this.hataliDenemeSayisi;
    }

    public void setHataliDenemeSayisi(final Integer hataliDenemeSayisi) {
        this.hataliDenemeSayisi = hataliDenemeSayisi;
    }

    public Date getSonGirisZamani() {
        return this.sonGirisZamani;
    }

    public void setSonGirisZamani(final Date sonGirisZamani) {
        this.sonGirisZamani = sonGirisZamani;
    }

    public Boolean getOnlineDurumu() {
        return this.onlineDurumu;
    }

    public void setOnlineDurumu(final Boolean onlineDurumu) {
        this.onlineDurumu = onlineDurumu;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Boolean getSifirlamaIstegi() {
        return this.sifirlamaIstegi;
    }

    public void setSifirlamaIstegi(final Boolean sifirlamaIstegi) {
        this.sifirlamaIstegi = sifirlamaIstegi;
    }

    public Boolean getGdpApproved() {
        return this.gdpApproved;
    }

    public void setGdpApproved(final Boolean gdpApproved) {
        this.gdpApproved = gdpApproved;
    }

    /**
     * @deprecated replace it with personelview due to personel circulation in modules.
     *
     */
    @Deprecated
    public Personel getPersonel() {
        return this.personel;
    }

    /**
     * @deprecated replace it with personelview due to personel circulation in modules.
     *
     * @param personel
     */
    @Deprecated
    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public Collection<KullaniciBirimRol> getKullaniciBirimRols() {
        return this.kullaniciBirimRols;
    }

    public void setKullaniciBirimRols(final Collection<KullaniciBirimRol> kullaniciBirimRols) {
        this.kullaniciBirimRols = kullaniciBirimRols;
    }

    @Override
    public String toString() {
        return Stream.of(this.ad, this.soyad).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }

    @Override
    public String getTitle() {
        return this.toString();
    }

    public Announcement getDuyuruNumarasi() {
        return this.duyuruNumarasi;
    }

    public void setDuyuruNumarasi(final Announcement duyuruNumarasi) {
        this.duyuruNumarasi = duyuruNumarasi;
    }

    public PersonelView getPersonelView() {
        return this.personelView;
    }

    public void setPersonelView(final PersonelView personelView) {
        this.personelView = personelView;
    }

}
