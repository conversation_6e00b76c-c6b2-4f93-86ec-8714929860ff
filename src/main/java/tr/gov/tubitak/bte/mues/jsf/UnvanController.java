package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Unvan;
import tr.gov.tubitak.bte.mues.session.UnvanFacade;

@Named
@ViewScoped
public class UnvanController extends AbstractController<Unvan> {

    private static final long serialVersionUID = -2299579878958970484L;

    @Inject
    private UnvanFacade       facade;

    public UnvanController() {
        super(Unvan.class);
    }

    @Override
    public UnvanFacade getFacade() {
        return this.facade;
    }

}
