package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "DIL")
@NamedQuery(name = "Dil.findEagerById", query = "SELECT d FROM Dil d WHERE d.id = :id")
@NamedQuery(name = "Dil.findAll", query = "SELECT d FROM Dil d ORDER BY d.silinmis, d.aktif DESC, d.ad")
@NamedQuery(name = "Dil.findActive", query = "SELECT d FROM Dil d WHERE d.aktif = true AND d.silinmis = false ORDER BY d.ad")
@NamedQuery(name = "Dil.findByNameAndAciklama", query = "SELECT d FROM Dil d WHERE d.aktif = true AND d.silinmis = false AND (d.ad LIKE :str OR d.aciklama LIKE :str) ORDER BY d.ad, d.aciklama")
@NamedQuery(name = "Dil.filterByFullNameAndAciklamaPreventDuplicate", query = "SELECT d FROM Dil d WHERE d.id NOT IN :ids AND d.aktif = true AND d.silinmis = false AND (d.ad LIKE :str OR d.aciklama LIKE :str) ORDER BY d.ad")
@NamedNativeQuery(name = "Dil.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM YAZI_TIPI_DIL WHERE SILINMIS = 0 AND DIL_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM Virtual_Keyboard_Language WHERE SILINMIS = 0 AND dil_Id = :id)")
public class Dil extends AbstractEntity implements DeleteValidatable {

    private static final long       serialVersionUID = 4809295044267419850L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                  ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                  deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                  aciklama;
    

    public Dil() {
        // defeult constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

 

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }
  
}
