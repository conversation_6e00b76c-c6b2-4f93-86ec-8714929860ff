package tr.gov.tubitak.bte.mues.model.mapping;

import java.io.Serializable;

import javax.persistence.Table;

@Table(name = "Lab_RestorationView")
public class LabRestorationView implements Serializable {

    private static final long serialVersionUID = 5952075630494501287L;

    private Integer           eserId;

    private Boolean           aktif;

    private Boolean           silinmis;

    private String            laboratuvarMudurlugu;

    private String            aciklama;

    private String            talepId;

    private String            voucherNo;
    
    private String            docPath;

    private String            envanterNo;

    private String            uzman;

    private Integer           id;
    
    private Integer           artifactId;


    public LabRestorationView() {
    }

    public LabRestorationView(final Integer eserId,
                              final Boolean aktif,
                              final Boolean silinmis,
                              final String aciklama,
                              final String voucherNo,
                              final String docPath,
                              final String laboratuvarMudurlugu,
                              final String talepId,
                              final String envanterNo,
                              final String uzman,
                              final Integer id,
                              final Integer artifactId) {
        this.eserId = eserId;
        this.aktif = aktif;
        this.silinmis = silinmis;
        this.laboratuvarMudurlugu = laboratuvarMudurlugu;
        this.aciklama = aciklama;
        this.talepId = talepId;
        this.voucherNo = voucherNo;
        this.docPath = docPath;
        this.envanterNo = envanterNo;
        this.uzman = uzman;
        this.id = id;
        this.artifactId = artifactId;
    }

    public Integer getEserId() {
        return this.eserId;
    }

    public void setEserId(final Integer eserId) {
        this.eserId = eserId;
    }
    
    public Integer getArtifactId() {
        return artifactId;
    }

    public void setArtifactId(Integer artifactId) {
        this.artifactId = artifactId;
    }

    public String getDocPath() {
        return docPath;
    }

    public void setDocPath(String docPath) {
        this.docPath = docPath;
    }

    public Boolean getAktif() {
        return this.aktif;
    }

    public void setAktif(final Boolean aktif) {
        this.aktif = aktif;
    }

    public Boolean getSilinmis() {
        return this.silinmis;
    }

    public void setSilinmis(final Boolean silinmis) {
        this.silinmis = silinmis;
    }

    public String getLaboratuvarMudurlugu() {
        return this.laboratuvarMudurlugu;
    }

    public void setLaboratuvarMudurlugu(final String laboratuvarMudurlugu) {
        this.laboratuvarMudurlugu = laboratuvarMudurlugu;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getTalepId() {
        return this.talepId;
    }

    public void setTalepId(final String talepId) {
        this.talepId = talepId;
    }

    public String getVoucherNo() {
        return this.voucherNo;
    }

    public void setVoucherNo(final String voucherNo) {
        this.voucherNo = voucherNo;
    }

    public String getEnvanterNo() {
        return this.envanterNo;
    }

    public void setEnvanterNo(final String envanterNo) {
        this.envanterNo = envanterNo;
    }

    public String getUzman() {
        return this.uzman;
    }

    public void setUzman(final String uzman) {
        this.uzman = uzman;
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(final Integer id) {
        this.id = id;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

}