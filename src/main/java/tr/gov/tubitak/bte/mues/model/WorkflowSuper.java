package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.envers.Audited;

@MappedSuperclass

@NamedQuery(name = "Workflow.findEagerById", query = "SELECT f FROM Workflow f "
                                                     + "LEFT JOIN FETCH f.artifact a "
                                                     + "LEFT JOIN FETCH a.anaFotograf af "
                                                     + "LEFT JOIN FETCH f.comments c LEFT JOIN FETCH c.by "
                                                     + "LEFT JOIN FETCH f.initiator "
                                                     + "LEFT JOIN FETCH f.modifier "
                                                     + "LEFT JOIN FETCH f.reviewer "
                                                     + "LEFT JOIN FETCH f.mudurluk "
                                                     + "WHERE f.id = :id")
@NamedQuery(name = "Workflow.findActive", query = "SELECT f FROM Workflow f "
                                                  + "LEFT JOIN FETCH f.artifact a "
                                                  + "LEFT JOIN FETCH f.initiator "
                                                  + "LEFT JOIN FETCH f.modifier "
                                                  + "LEFT JOIN FETCH f.reviewer "
                                                  + "LEFT JOIN FETCH f.mudurluk "
                                                  + "WHERE f.aktif = true "
                                                  + "AND f.silinmis = false "
                                                  + "AND a.aktif = true "
                                                  + "AND a.silinmis = false ")


@NamedQuery(name = "Workflow.findByVersionAndUser", query = "SELECT f FROM Workflow f "
                                                            + "LEFT JOIN FETCH f.artifact a "
                                                            + "LEFT JOIN FETCH a.eserAltTur at LEFT JOIN FETCH at.eserTur  "
                                                            + "LEFT JOIN FETCH a.anaFotograf af "
                                                            + "LEFT JOIN FETCH f.initiator "
                                                            + "LEFT JOIN FETCH f.modifier "
                                                            + "LEFT JOIN FETCH f.reviewer "
                                                            + "LEFT JOIN FETCH f.mudurluk "
                                                            + "LEFT JOIN FETCH a.eserDepos d  LEFT JOIN FETCH d.alanKonumu dk LEFT JOIN FETCH dk.alan da "
                                                            + "WHERE f.aktif = true "
                                                            + "AND f.silinmis = false "
                                                            + "AND a.aktif = true "
                                                            + "AND a.silinmis = false "
                                                            + "AND f.modifier = :user "
                                                            + "AND f.review IN :types")

@NamedQuery(name = "Workflow.findByVersionUserAndMudurluk", query = "SELECT f FROM Workflow f "
                                                                    + "LEFT JOIN FETCH f.artifact a "
                                                                    + "LEFT JOIN FETCH a.anaFotograf af "
                                                                    + "LEFT JOIN FETCH f.initiator "
                                                                    + "LEFT JOIN FETCH f.modifier "
                                                                    + "LEFT JOIN FETCH f.reviewer "
                                                                    + "LEFT JOIN FETCH f.mudurluk "
                                                                    + "LEFT JOIN FETCH a.eserDepos d  LEFT JOIN FETCH d.alanKonumu dk LEFT JOIN FETCH dk.alan da "
                                                                    + "WHERE f.aktif = true "
                                                                    + "AND f.silinmis = false "
                                                                    + "AND a.aktif = true "
                                                                    + "AND a.silinmis = false "
                                                                    + "AND f.mudurluk = :mudurluk "
                                                                    + "AND f.modifier = :user "
                                                                    + "AND f.review IN :types")
@NamedQuery(name = "Workflow.findByArtifact", query = "SELECT f FROM Workflow f "
                                                      + "LEFT JOIN FETCH f.artifact a "
                                                      + "LEFT JOIN FETCH a.anaFotograf af "
                                                      + "LEFT JOIN FETCH f.comments c LEFT JOIN FETCH c.by "
                                                      + "LEFT JOIN FETCH f.initiator "
                                                      + "LEFT JOIN FETCH f.modifier "
                                                      + "LEFT JOIN FETCH f.reviewer "
                                                      + "LEFT JOIN FETCH f.mudurluk "
                                                      + "WHERE f.aktif = true "
                                                      + "AND f.silinmis = false "
                                                      + "AND f.artifact = :eser")
@NamedQuery(name = "Workflow.findByEnvanterNoAndMudurluk", query = "SELECT f FROM Workflow f "
                                                                   + "LEFT JOIN FETCH f.artifact e "
                                                                   + "WHERE  REPLACE(COALESCE(e.envanterNo , ''),' ','') =  REPLACE(COALESCE(:envanterNo , '') , ' ','') AND f.mudurluk = :mudurluk and (f.artifact.id <> :eserId OR COALESCE(:eserId, NULL) is NULL ) "
                                                                   + "AND e.aktif = true AND e.silinmis = false")
@Audited
public class WorkflowSuper extends AbstractEntity {

    private static final long serialVersionUID = -6938754745427428184L;

    @JoinColumn(name = "review")
    private Integer           review;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "initiator")
    private Kullanici         initiator;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "modifier")
    private Kullanici         modifier;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "reviewer")
    private Kullanici         reviewer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mudurlukId")
    private Mudurluk          mudurluk;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dateInitiated")
    private Date              dateInitiated;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dateCompleted")
    private Date              dateCompleted;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "inventoryPinUpdatDate")
    private Date              inventoryPinUpdatDate;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "dateModified")
    private Date              dateModified;

    /** The artifact that included in the workflow */
    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Eser              artifact;

    public WorkflowSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Kullanici getInitiator() {
        return this.initiator;
    }

    public void setInitiator(final Kullanici initiator) {
        this.initiator = initiator;
    }

    public Kullanici getModifier() {
        return this.modifier;
    }

    public void setModifier(final Kullanici modifier) {
        this.modifier = modifier;
    }

    public Kullanici getReviewer() {
        return this.reviewer;
    }

    public void setReviewer(final Kullanici reviewer) {
        this.reviewer = reviewer;
    }

    public Eser getArtifact() {
        return this.artifact;
    }

    public void setArtifact(final Eser artifact) {
        this.artifact = artifact;
    }

    public Date getDateInitiated() {
        return this.dateInitiated;
    }

    public void setDateInitiated(final Date dateInitiated) {
        this.dateInitiated = dateInitiated;
    }

    public Date getDateModified() {
        return this.dateModified;
    }

    public void setDateModified(final Date dateModified) {
        this.dateModified = dateModified;
    }

    public Date getDateCompleted() {
        return this.dateCompleted;
    }

    public void setDateCompleted(final Date dateCompleted) {
        this.dateCompleted = dateCompleted;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Integer getReview() {
        return this.review;
    }

    public void setReview(final Integer review) {
        this.review = review;
    }

    public EserVersion getReviewEnum() {
        return EserVersion.parse(this.review);
    }

    public void setReviewEnum(final EserVersion eserVersion) {
        this.review = eserVersion.getCode();
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return this.artifact.toString() + " - " + this.getReviewEnum().getLabel();
    }

    @Override
    public boolean equals(final Object object) {
        return super.equals(object);

    }

    public Date getInventoryPinUpdatDate() {
        return this.inventoryPinUpdatDate;
    }

    public void setInventoryPinUpdatDate(final Date inventoryPinUpdatDate) {
        this.inventoryPinUpdatDate = inventoryPinUpdatDate;
    }

}
