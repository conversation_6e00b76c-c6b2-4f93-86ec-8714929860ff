package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserYayinLiteratur;
import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

@Named
@ViewScoped
public class EserYayinLiteraturController extends AbstractController<EserYayinLiteratur> {

    private static final long serialVersionUID = -8049755954148177647L;

    public EserYayinLiteraturController() {
        super(EserYayinLiteratur.class);
    }

    public void handleLiteraturSelect(final Literatur literatur) {
        this.getModel().setLiteratur(literatur);
    }

    // getters and setters ....................................................

    @Override
    public AbstractFacade<EserYayinLiteratur> getFacade() {
        return null;
    }

}
