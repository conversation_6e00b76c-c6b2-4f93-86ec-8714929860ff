/***********************************************************
 * SearchConstants.java - mues Projesi
 *
 * Kullanılan JRE: 1.8.0_91
 *
 * halis.yilboga - 20.Tem.2016
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2012©
 ***********************************************************/
package tr.gov.tubitak.bte.mues.search;

import java.util.Date;
import java.util.List;

import tr.gov.tubitak.bte.mues.util.DateUtil;

/**
 * <PERSON><PERSON><PERSON> alakal<PERSON> sabit değerleri tutulduğu sınıf.
 */
public final class SearchConstants {

    /** virgül karakteri. */
    public static final String COMMA_LITERAL          = ", ";

    public static final String EMPTY                  = "";

    public static final String FIELD                  = "field";

    /** SÜSLÜ PARANTEZ karakteri. */
    public static final String LEFT_CURLY_BRACE       = "{";

    /** tek tırnak karakteri. */
    public static final String QUOTE                  = "\"";

    /** SÜSLÜ PARANTEZ karakteri. */
    public static final String RIGHT_CURLY_BRACE      = "}";

    public static final String RIGHT_PARANTHESIS      = ")";

    public static final String BLANKSTRING            = "";

    /** The Constant PARANTHESIS_LEFT used to */
    public static final String PARANTHESIS_LEFT       = " ( ";

    /** The Constant PARANTHESIS_LEFT used to */
    public static final String PARANTHESIS_RIGHT      = " ) ";

    /** VEYA operatörü ifadesi. */
    public static final String SQL_OR                 = " OR ";

    /** VE operatörü ifadesi. */
    public static final String AND                    = " AND ";

    public static final String WHERE                  = " WHERE ";

    public static final String SQL_AND_LITERAL        = "&";

    public static final String TO                     = " TO ";

    public static final String RIGHT_BRACKET          = "]";

    public static final String LEFT_BRACKET           = "[";

    /** boşluk karakteri. */
    public static final String SPACE_LITERAL          = " ";

    /** yüzde karakteri. */
    public static final String PERCENT                = "%";

    public static final String SOLREQUALSSIGN         = ":";

    /**  */
    public static final String EQUALS_LETERAL         = "=";

    public static final String PLUS                   = "+";

    /** minimum arama metni uzunluğu. */
    public static final int    MIN_SEARCH_TEXT_LENGTH = 3;

    public static final long   ALL_METADATAS          = -2L;

    public static final String LIKE                   = " LIKE ";

    public static final Object GREATER_THAN_OR_EQUAL  = ">=";

    public static final Object LESS_THAN_OR_EQUAL     = "<=";

    public static final String STYLE_TAG              = "<b>";

    public static final String CLOSE_STYLE_TAG        = "</b>";

    public static final String COLORED_STYLE_TAG      = "<b style='color: darkgray;'>";

    public static final String LEFT_PARANTHESIS       = "(";

    public static final String ALL_QUERY              = "*:*";

    public static final String SEPERATION_CHARS       = "##";

    /** milisaniyeden saniyeye çevirici. */
    public static final int    SECOND_DIVIDER         = 1000;

    private SearchConstants() {
    }

    /**
     * Verilen metnin loglama tablosuna raporlama amaçlı eklemek amacıyla ayraçlı halini döner.
     *
     * @param text metin
     * @return metnin ayraç eklenmiş hali
     */
    public static String getSearchTextWithDelimeter(final String text) {
        return text + "!#?#! ";
    }

    public static String getANDLiteralTxt(final int length) {
        if (length == 0) {
            return BLANKSTRING;
        }
        return AND;
    }

    public static String sanitize(final String s) {
        final StringBuilder sb = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            final char c = s.charAt(i);
            // These characters are part of the query syntax and must be escaped
            if ((c == '\\')
                || (c == '+')
                || (c == '-')
                || (c == '!')
                || (c == '(')
                || (c == ')')
                || (c == ':')
                || (c == '^')
                || (c == '[')
                || (c == ']')
                || (c == '\"')
                || (c == '{')
                || (c == '}')
                || (c == '~')
                || (c == '*')
                || (c == '?')
                || (c == '|')
                || (c == '&')
                || (c == ';')
                // || Character.isWhitespace(c)
                || (c == '/')) {
                // modified from org.apache.solr.client.solrj.util.ClientUtils
                sb.append('\\');
            }
            sb.append(c);
        }
        return sb.toString();
    }

    /**
     * Verilen tarih nesnesini, veritabanından tarih sorgulamak için gereken formata dönüştürür.
     *
     * @param date tarih nesnesi
     * @return Nesnenin veritabanından sorgulamaya uygun formata çevrilmiş hali
     */
    public static String getDateYYYYMMDD(final Date date) {
        if (date == null) {
            return null;
        }
        return DateUtil.getDateYYYYMMDD(date);
    }

    /**
     * Verilen tarih nesnesini audit için uygun formata dönüştürür.
     *
     * @param date tarih nesnesi
     * @return Nesnenin uygun formata çevrilmiş hali
     */
    public static String getDateDDMMYYYY(final Date date) {
        if (date == null) {
            return "";
        }
        return DateUtil.getDateDDMMYYYY(date);
    }

    public static String getDateISOFormatStart(final Date date) {
        if (date == null) {
            return null;
        }
        return String.format("%tFT00:00:00Z", date);
    }

    public static String getDateISOFormatEnd(final Date date) {
        if (date == null) {
            return null;
        }
        return String.format("%tFT23:59:59Z", date);
    }

    /**
     * Verilen liste nesnesinin String olarak temsil edilmiş halini oluşturur.
     *
     * @param list String olarak istenen liste
     * @param header Listenin String olarak çevrilmiş halinin başına eklenen ön ek
     * @param separator Listedeki elemanları ayıracak ayraç
     * @param footer Listenin String olarak çevrilmiş halinin sonuna eklenen son ek
     *
     * @return Listenin String olarak çevrilmiş hali
     */
    public static String listToString(final List<? extends Object> list) {
        String delim = SearchConstants.BLANKSTRING;
        final StringBuilder sb = new StringBuilder(SearchConstants.LEFT_PARANTHESIS);
        if (list != null) {
            for (final Object object : list) {
                sb.append(delim).append(object);
                delim = SearchConstants.COMMA_LITERAL;
            }
        }
        return sb.append(SearchConstants.LEFT_PARANTHESIS).toString();
    }

}
