package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.TuzelKisi;

/**
 *
 */
@RequestScoped
public class TuzelKisiFacade extends AbstractFacade<TuzelKisi> {

    public TuzelKisiFacade() {
        super(TuzelKisi.class);
    }

    public List<TuzelKisi> filterByFullNameAndAciklamaPreventDuplicate(final String query, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("TuzelKisi.filterByFullNameAndAciklamaPreventDuplicate", TuzelKisi.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("ids", excludedIds)
                      .getResultList();
    }

}
