
package tr.gov.tubitak.bte.mues.search;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * The persistent class for the SearchDefinition database table.
 */
@Entity
@Table(name = "SearchCriterionDefinition")
public class SearchCriterionDefinition extends SearchCriterionDefinitionSuper {

    private static final long serialVersionUID = -2731796850711780715L;

    public SearchCriterionDefinition() {
        // default constructor
    }

}
