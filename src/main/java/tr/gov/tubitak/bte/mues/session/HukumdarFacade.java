package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Hukumdar;
import tr.gov.tubitak.bte.mues.model.Uygarlik;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class HukumdarFacade extends AbstractFacade<Hukumdar> {

    public HukumdarFacade() {
        super(Hukumdar.class);
    }

    public List<Hukumdar> filterByNameAndUygarlik(final String value, final Uygarlik uygarlik) {
        return this.em.createNamedQuery("Hukumdar.findByNameAndUygarlik", Hukumdar.class).setParameter("ad", "%" + value + "%").setParameter("uygarlik", uygarlik).getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> findUygarlikTermDatesByDonemId(final Integer donemId) {
        final String qry = " SELECT MIN(h.DONEM_BASLANGIC_YIL) as 'Min', MAX(h.DONEM_BITIS_YIL) as 'Max', u.ID FROM HUKUMDAR h LEFT JOIN UYGARLIK u ON u.ID = h.UYGARLIK_ID WHERE u.ID IN ( SELECT ud.UYGARLIK_ID FROM UYGARLIK_DONEM ud WHERE ud.DONEM_ID = :donemId ) GROUP BY u.ID ;  ";
        return this.getEM().createNativeQuery(qry).setParameter("donemId", donemId).getResultList();
    }

}
