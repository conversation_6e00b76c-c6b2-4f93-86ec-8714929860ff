package tr.gov.tubitak.bte.mues.solr.util;

import java.security.Key;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Authentication {

    private static final Logger logger = LoggerFactory.getLogger(Authentication.class);

    public static String encryptData(final String password) {

        try {
            final String text = password;
            final String key = "-CAXliteCAXlite-"; // 128 bit key

            // Create key and cipher
            final Key aesKey = new SecretKeySpec(key.getBytes(), "AES");
            final Cipher cipher = Cipher.getInstance("AES");

            // encrypt the text
            cipher.init(Cipher.ENCRYPT_MODE, aesKey);
            final byte[] encrypted = cipher.doFinal(text.getBytes());
            logger.error("[encryptData] : {}", new String(encrypted));

            final StringBuilder outputEncrypted = new StringBuilder("");
            for (int i = 0; i < encrypted.length; i++) {
                outputEncrypted.append(Integer.toString(encrypted[i])).append("A");
            }

            return outputEncrypted.toString();
        }

        catch (final Exception e) {
            logger.error("[encryptData] : Hata : {}", e.getMessage(), e);
            return null;
        }
    }

}
