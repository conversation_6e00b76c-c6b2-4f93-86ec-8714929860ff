/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.component.UIInput;
import javax.faces.context.FacesContext;

import org.omnifaces.util.Messages;
import org.primefaces.PrimeFaces;
import org.primefaces.event.data.FilterEvent;
import org.primefaces.model.FilterMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.IEntity;
import tr.gov.tubitak.bte.mues.model.mapping.DataTableFilter;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesException;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
*
 *
 */
public abstract class AbstractController<T extends IEntity<?>> implements Serializable {

    private static final long                            serialVersionUID = -7367720360970132010L;

    protected final transient Logger                     logger           = LoggerFactory.getLogger(AbstractController.class);

    private final transient Map<String, DataTableFilter> filterState      = new HashMap<>();

    private final Class<T>                               clazz;

    private boolean                                      newMode;

    private T                                            model;

    protected transient List<T>                          items;

    protected transient List<T>                          filteredValues;

    private String                                       deleteDescription;

    private String                                       clientComponent;

    // end of global fields ...................................................

    protected AbstractController(final Class<T> clazz) {
        this.clazz = clazz;
    }

    public abstract AbstractFacade<T> getFacade();

    // public methods .........................................................

    public void newRecord() {
        try {
            this.model = this.clazz.getDeclaredConstructor().newInstance();
            this.setNewMode(true);

        } catch (final InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException |

                NoSuchMethodException | SecurityException e) {

            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Arama alanı için criter oluşturulamadı.'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine başvurunuz '"
                                     + ", 'severity':'Error'})");

            this.logger.error("[AbstractController] : Hata : {}", e.getMessage(), e);

            throw new MuesException("");

        }
    }

    public DBOperationResult create() {
        return this.create("Kayıt başarıyla oluşturuldu.");
    }

    public DBOperationResult create(final List<AbstractEntity> list) {
        this.makeFileOperations();
        return this.getFacade().create(list);
    }

    public DBOperationResult create(final AbstractEntity... entities) {
        this.makeFileOperations();
        return this.getFacade().create(entities);
    }

    public DBOperationResult create(final String message) {
        this.makeFileOperations();

        final DBOperationResult result = this.getFacade().create(this.model);
        if (result.isSuccess()) {
            if (this.items != null) {
                this.model = this.getFacade().findEagerById((Integer) this.model.getId());
                this.items.add(0, this.model);

                if (this.filteredValues != null) {
                    this.filteredValues.add(0, this.model);
                }
            }
            Optional.ofNullable(message).filter(Predicate.not(String::isEmpty)).ifPresent(x -> MuesUtil.showMessage(x, FacesMessage.SEVERITY_INFO));
            this.logger.info("[create] : {}", message);

        } else {
            final FacesContext context = FacesContext.getCurrentInstance();
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
            context.validationFailed();
        }
        return result;
    }

    public DBOperationResult update() {
        return this.update("Kayıt başarıyla güncellendi.");
    }

    public DBOperationResult update(final String message) {
        this.makeFileOperations();
        final DBOperationResult result = this.getFacade().update(this.model);
        return this.afterUpdate(result, message);
    }

    public DBOperationResult update(final List<AbstractEntity> list) {
        this.makeFileOperations();
        final DBOperationResult result = this.getFacade().update(list);
        return this.afterUpdate(result, "Kayıt başarıyla güncellendi.");
    }

    public DBOperationResult update(final String message, final List<AbstractEntity> list) {
        this.makeFileOperations();
        final DBOperationResult result = this.getFacade().update(list);
        return this.afterUpdate(result, message);
    }

    public DBOperationResult update(final AbstractEntity... entities) {
        this.makeFileOperations();
        final DBOperationResult result = this.getFacade().update(entities);
        return this.afterUpdate(result, "Kayıt başarıyla güncellendi.");
    }

    public DBOperationResult update(final String message, final AbstractEntity... entities) {
        this.makeFileOperations();
        final DBOperationResult result = this.getFacade().update(entities);
        return this.afterUpdate(result, message);
    }

    public DBOperationResult afterUpdate(final DBOperationResult result, final String... message) {

        if (result.isSuccess()) {
            if (this.clientComponent != null) {

                final String rowClientId = this.clientComponent.substring(0, this.clientComponent.lastIndexOf(':'));
                final Integer rowNumber = Integer.parseInt(rowClientId.substring(rowClientId.lastIndexOf(':') + 1, rowClientId.length()));
                this.model = this.getFacade().findEagerById((Integer) this.model.getId());

                if (this.filteredValues != null) {
                    this.filteredValues.set(rowNumber, this.model);
                    final int indexOf = this.items.indexOf(this.model);
                    this.items.set(indexOf, this.model);

                } else {
                    this.items.set(rowNumber, this.model);
                }

                PrimeFaces.current().ajax().update(this.clientComponent.substring(0, this.clientComponent.indexOf(':')));
            }
            this.setClientComponent(null);
            Optional.ofNullable(message).ifPresent(x -> MuesUtil.showMessage(FacesMessage.SEVERITY_INFO, x));
            this.logger.debug("[update] : {} ", Optional.ofNullable(message));

        } else {
            final String clientId = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("validationClientId");
            final FacesContext context = FacesContext.getCurrentInstance();

            if ((clientId != null) && (clientId.length() > 0)) {

                final UIInput input = (UIInput) context.getViewRoot().findComponent(clientId);
                if (input == null) {
                    throw new MuesException("'validationClientId' parametresi set edilmemiş ki uyarı verilsin. Hata atiliyor ki düzeltilsin.");
                }
                final Object value = input.getValue();
                input.setValid(false);
                Messages.addError(clientId, result.getMessage());
                input.setValue(value);
            }
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
            context.validationFailed();
        }
        return result;
    }

    public void deletePermanently() {

        final DBOperationResult result = this.getFacade().delete(this.model, this.deleteDescription);

        if (result.isSuccess()) {
            this.items.remove(this.model);
            MuesUtil.showMessage("Kayıt başarıyla silindi.", FacesMessage.SEVERITY_INFO);
            this.logger.info("[deletePermanently] : Kayıt başarıyla silindi.");

        } else {
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
            this.logger.info("[deletePermanently] : Kayıt silinirken hata oluştu.");
        }
        this.deleteDescription = null;
    }

    public void delete() {
        final DBOperationResult result = this.getFacade().virtualDeleteOperation(true, this.model, this.deleteDescription);

        if (result.isSuccess()) {
            MuesUtil.showMessage("Kayıt başarıyla silindi.", FacesMessage.SEVERITY_INFO);
            this.logger.info("[delete] : Kayıt başarıyla silindi.");
            this.model = null;

        } else {
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
            this.logger.info("[delete] : Kayıt silinirken hata oluştu.");
        }
        this.deleteDescription = null;
    }

    public void undelete() {
        final DBOperationResult result = this.getFacade().virtualDeleteOperation(false, this.model, this.deleteDescription);
        if (result.isSuccess()) {
            this.deleteDescription = null;
            MuesUtil.showMessage("Kayıt başarıyla geri alındı.", FacesMessage.SEVERITY_INFO);
            this.logger.info("[undelete] : Kayıt başarıyla geri alındı.");
            this.model = null;

        } else {
            MuesUtil.showMessage(result.getMessage(), FacesMessage.SEVERITY_ERROR);
        }
    }

    public void toggleActive(final T entity) {
        this.model = entity;
        this.model.setAktif(!entity.getAktif());
        final DBOperationResult result = this.getFacade().update(this.model);
        this.afterUpdate(result, "Kayıt başarıyla güncellendi.");
    }

    public void showDetail(final T item) {
        this.showDetail((Integer) item.getId());
    }

    public void showDetail(final Integer id) {
        this.model = this.getFacade().findEagerById(id);
        this.newMode = false;
    }

    public List<T> filterByName(final String query) {
        return this.getFacade().filterByName("ad", query);
    }

    private void makeFileOperations() {
        if (this instanceof SingleFileUploadable) {
            final SingleFileUploadable sfu = (SingleFileUploadable) this;
            sfu.writeToPermanentFolder();
        }
    }

    // getters and setters ........................................

    public boolean isNewMode() {
        return this.newMode;
    }

    public void setNewMode(final boolean newMode) {
        this.newMode = newMode;
    }

    public T getModel() {
        return this.model;
    }

    public void setModel(final T model) {
        this.model = model;
    }

    public List<T> getItems() {
        if (this.items == null) {
            this.items = this.getFacade().findAll();
        }
        return this.items;
    }

    public void setItems(final List<T> items) {
        this.items = items;
    }

    public String getDeleteDescription() {
        return this.deleteDescription;
    }

    public void setDeleteDescription(final String deleteDescription) {
        this.deleteDescription = deleteDescription;
    }

    public List<T> getFilteredValues() {
        return this.filteredValues;
    }

    public void setFilteredValues(final List<T> filteredValues) {
        this.filteredValues = filteredValues;
    }

    public void onFilterChange(final FilterEvent filterEvent) {
        this.getFilterState().clear();
        final Map<String, FilterMeta> filters = filterEvent.getFilterBy();

        for (final Map.Entry<String, FilterMeta> entry : filters.entrySet()) {
            final String key = entry.getKey();
            this.getFilterState().put(key, new DataTableFilter(entry.getValue().getFilterValue()));
        }
    }

    public Object filterState(final String column) {
        return this.getFilterState().get(column) != null ? this.getFilterState().get(column) : new DataTableFilter();
    }

    public Map<String, DataTableFilter> getFilterState() {
        return this.filterState;
    }

    public String getClientComponent() {
        return this.clientComponent;
    }

    public void setClientComponent(final String clientComponent) {
        this.clientComponent = clientComponent;
    }

}
