package tr.gov.tubitak.bte.mues.jsf;

import java.io.UncheckedIOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.ResourceBundle;

import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.model.DataModel;
import javax.inject.Inject;

import org.omnifaces.util.Faces;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.model.LazyDataModel;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.EserProposal;
import tr.gov.tubitak.bte.mues.model.EserProposalAtolye;
import tr.gov.tubitak.bte.mues.model.EserProposalCizim;
import tr.gov.tubitak.bte.mues.model.EserProposalFotograf;
import tr.gov.tubitak.bte.mues.model.EserProposalKaynakLiteratur;
import tr.gov.tubitak.bte.mues.model.EserProposalKeyword;
import tr.gov.tubitak.bte.mues.model.EserProposalMalzemeSuslemeTeknigi;
import tr.gov.tubitak.bte.mues.model.EserProposalMalzemeYapimTeknigi;
import tr.gov.tubitak.bte.mues.model.EserProposalMeasure;
import tr.gov.tubitak.bte.mues.model.EserProposalSerh;
import tr.gov.tubitak.bte.mues.model.EserProposalStil;
import tr.gov.tubitak.bte.mues.model.EserProposalTranscription;
import tr.gov.tubitak.bte.mues.model.EserProposalYayinLiteratur;
import tr.gov.tubitak.bte.mues.model.IEntity;
import tr.gov.tubitak.bte.mues.model.Keyword;
import tr.gov.tubitak.bte.mues.model.mapping.TimeSpectrum;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyTimeSpectrumDataModel;
import tr.gov.tubitak.bte.mues.session.AuditFacade;
import tr.gov.tubitak.bte.mues.session.EserProposalFacade;
import tr.gov.tubitak.bte.mues.session.TimeSpectrumLazyLoadFacade;
import tr.gov.tubitak.bte.mues.util.MuesException;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

public class EserProposalAbstractController<T extends IEntity<?>> extends AbstractController<EserProposal> {

    private static final String                         ESER_SIKKE_KOD   = "255.06.06";

    private static final long                           serialVersionUID = 4331249019200112541L;

    @Inject
    private transient AuditFacade                       auditFacade;

    @Inject
    protected transient EserProposalFacade              facade;

    @Inject
    private transient TimeSpectrumLazyLoadFacade        timeSpectrumFacade;

    @Inject
    private transient ResourceBundle                    bundle;

    @Inject
    private EserProposalKeywordController               eserProposalKeywordController;

    @Inject
    private EserProposalFotografController              eserProposalFotografController;

    @Inject
    protected UygarlikDonemController                   uygarlikDonemController;

    @Inject
    protected TranscriptionController                   transcriptionController;

    @Inject
    private EserProposalMeasureController               eserProposalMeasureController;

    @Inject
    private EserProposalCizimController                 eserProposalCizimController;

    @Inject
    private EserProposalTranscriptionController         eserProposalTranscriptionController;

    @Inject
    private EserProposalMalzemeYapimTeknigiController   eserProposalMalzemeYapimTeknigiController;

    @Inject
    private EserProposalMalzemeSuslemeTeknigiController eserProposalMalzemeSuslemeTeknigiController;

    @Inject
    private EserProposalStilController                  eserProposalStilController;

    @Inject
    private EserProposalAtolyeController                eserProposalAtolyeController;

    @Inject
    private EserProposalYayinLiteraturController        eserProposalYayinLiteraturController;

    @Inject
    private EserProposalKaynakLiteraturController       eserProposalKaynakLiteraturController;

    @Inject
    private EserProposalSerhController                  eserProposalSerhController;

    @Inject
    private FileUploadHelper                            fileUploadHelper;

    private Integer                                     oneriEserId;

    private transient DataModel<TimeSpectrum>           lazyTimeSpectrumDataModel;

    @Inject
    protected SessionBean                               sessionBean;

    @Inject
    private EserProposalHareketController               eserHareketController;

    private Boolean                                     isDisabled;

    // end of global fields ..............................................

    protected EserProposalAbstractController() {
        super(EserProposal.class);
    }

    @Override
    public List<EserProposal> getItems() {
        if (this.items == null) {
            this.items = this.facade.findAllByApplicationType(this.sessionBean.getApplicationType(), MuesUtil.toIds(this.sessionBean.fetchByPermission("eserOneri:listele")));
        }
        return this.items;
    }

    public List<EserProposal> findByNameAndMuseumDirectorate(final int muzeMudurluguId) {
        return this.getFacade().findByNameAndMuseumDirectorate(muzeMudurluguId);
    }

    public List<Keyword> filterByNameAndKeyword(final String query) {
        final List<Keyword> keywords = new ArrayList<>();
        if (this.getModel().getEserKeywords() != null) {
            this.getModel().getEserKeywords().stream().forEach(x -> keywords.add(x.getKeyword()));
        }
        return this.eserProposalKeywordController.filterByNameAndKeyword(query, MuesUtil.toIds(keywords));

    }

    public void deleteEserProposal(final EserProposal eserProposal) {

        this.setModel(this.facade.findEagerById(eserProposal.getId()));
        final List<String> deletePaths = this.prepairDeletePath();
        super.deletePermanently();
        this.fileUploadHelper.deleteFilesPermanently(deletePaths);
    }

    /**
     * for transcational delete opearation before database operation construct file paths.
     * 
     * @return
     * 
     */
    private List<String> prepairDeletePath() {
        //
        final List<String> filePaths = new ArrayList<>();

        if (this.getModel().getEserSerhs() != null) {
            this.getModel().getEserSerhs().stream().forEach(x -> filePaths.addAll(this.eserProposalSerhController.buildFilePathFromModel(x)));
        }
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().forEach(x -> filePaths.addAll(this.eserProposalFotografController.buildFilePathFromModel(x)));
        }
        if (this.getModel().getEserCizims() != null) {
            this.getModel().getEserCizims().stream().forEach(x -> filePaths.addAll(this.eserProposalCizimController.buildFilePathFromModel(x)));
        }
        if (this.getModel().getTranscriptions() != null) {
            this.getModel().getTranscriptions().stream().forEach(x -> filePaths.addAll(this.eserProposalTranscriptionController.buildFilePathFromModel(x)));
        }

        return filePaths;
    }

    /** Set artifact name from UI */
    public void fixEserOzelAd() {
        this.getModel().setEserOzelAdi(this.getModel().getEserAltTur().getTitle());
    }

    public void fixSikke() {
        this.getModel().setSikke(ESER_SIKKE_KOD.equals(this.getModel().getTasinirMalYonKod().getKod()));
    }

    public void saveEser() {

        this.logger.debug("Eser Proposal Saved");

        this.handleEserCreationDetails();

        if (this.sessionBean.getCurrentUser().getResearcher() != null) {
            this.getModel().setEserOneriResearcher(this.sessionBean.getCurrentUser().getResearcher());
            this.getModel().setEserOneriYapanKisiOzet("Arastirmaci: " + this.sessionBean.getCurrentUser().getResearcher().getTitle());
        } else {
            this.getModel().setEserOneriPersonel(this.sessionBean.getCurrentUser().getPersonelView());
            this.getModel().setEserOneriYapanKisiOzet("Personel: " + this.sessionBean.getCurrentUser().getPersonelView().getTitle());
        }

        this.getModel().setDateCreated(new Date());
        this.getModel().setDateUpdated(this.getModel().getDateCreated());
        this.getModel().setCreateSessionId(MuesUtil.fetchSessionId());
        this.getModel().setApplicationType(Integer.parseInt(this.sessionBean.getApplicationType().getCode()));

        final List<AbstractEntity> entities = new ArrayList<>(1);
        entities.add(this.getModel());

        if (this.facade.update(entities).isSuccess()) {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_INFO, this.bundle.getString("base.create.success"), "Öneri Eser Kaydedildi, Ö.E." + this.getModel().getId());
            this.redirectToEserEditPage(this.getModel().getId());
        } else {
            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_ERROR, this.bundle.getString("base.process.failure"), "Eser Kaydı Başarısız !!!");
        }

    }

    public void updateEser() {

        this.logger.debug("Eser Proposal Updated ");

        this.handleEserCreationDetails();

        this.getModel().setDateUpdated(new Date());

        this.getModel().setApplicationType(Integer.parseInt(this.sessionBean.getApplicationType().getCode()));

        if (!this.update().isSuccess()) {

            MuesUtil.showFlashMessage(FacesMessage.SEVERITY_ERROR, this.bundle.getString("base.process.failure"), "Eser Güncelleme Başarısız !!!");
        }

    }

    protected void handleEserCreationDetails() {
        this.fixTimeRelatedFields();
        this.makeFileRelatedOperations();
        this.fixSikkeBeforePersist();

        if (this.getModel().getEserMalzemeYapimTeknigis() != null) {
            this.getModel().getEserMalzemeYapimTeknigis().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserMalzemeSuslemeTeknigis() != null) {
            this.getModel().getEserMalzemeSuslemeTeknigis().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserSerhs() != null) {
            this.getModel().getEserSerhs().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserHarekets() != null) {
            this.getModel().getEserHarekets().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserStils() != null) {
            this.getModel().getEserStils().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserAtolyes() != null) {
            this.getModel().getEserAtolyes().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserYayinLiteraturs() != null) {
            this.getModel().getEserYayinLiteraturs().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserKaynakLiteraturs() != null) {
            this.getModel().getEserKaynakLiteraturs().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserMeasures() != null) {
            this.getModel().getEserMeasures().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserKeywords() != null) {
            this.getModel().getEserKeywords().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getTranscriptions() != null) {
            this.getModel().getTranscriptions().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserCizims() != null) {
            this.getModel().getEserCizims().stream().forEach(x -> x.setEser(this.getModel()));
        }
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().forEach(x -> x.setEser(this.getModel()));
            this.fixRestOfFotografStuff();
        }
    }

    private void fixRestOfFotografStuff() {

        if (this.getModel().getEserFotografs().size() == 1) {
            this.getModel().getEserFotografs().iterator().next().setAnaFotograf(true);

        } else if (this.getModel().getEserFotografs().size() > 1) {
            if (this.eserProposalFotografController.getAnaFoto() == null) {
                MuesUtil.showMessageWithClientId(":eserForm:eserFotografDataTable", this.bundle.getString("file.photo.main.mark"));

            } else {
                this.getModel().getEserFotografs().stream().forEach(x -> {
                    x.setAnaFotograf(false);
                    if (x.equals(this.eserProposalFotografController.getAnaFoto())) {
                        x.setAnaFotograf(true);
                    }
                });
            }
        }
    }

    private void fixTimeRelatedFields() {
        /* *** below order is important *** */
        this.getModel().setCag(this.uygarlikDonemController.getCag());
        this.getModel().setDonem(this.uygarlikDonemController.getModel().getDonem());
        this.getModel().setUygarlik(this.uygarlikDonemController.getModel().getUygarlik());
        this.getModel().setHukumdar(this.uygarlikDonemController.getHukumdar());
        this.uygarlikDonemController.fixTimeRelatedFieldsInModel();
        this.getModel().setTermStart(this.uygarlikDonemController.getModel().getTermStart());
        this.getModel().setTermEnd(this.uygarlikDonemController.getModel().getTermEnd());
    }

    private void makeFileRelatedOperations() {
        // copy temp file to the exact location and update model with that location
        if (this.getModel().getEserSerhs() != null) {
            this.getModel().getEserSerhs().stream().forEach(x -> this.eserProposalSerhController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().forEach(x -> this.eserProposalFotografController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getEserCizims() != null) {
            this.getModel().getEserCizims().stream().forEach(x -> this.eserProposalCizimController.makeFileRelatedOperations(x));
        }
        if (this.getModel().getTranscriptions() != null) {
            this.getModel().getTranscriptions().stream().forEach(x -> this.eserProposalTranscriptionController.makeFileRelatedOperations(x));
        }

    }

    private void fixSikkeBeforePersist() {
        if (this.getModel().isSikke()) {
            this.getModel().setElisiDokumaSecimi(null);
            this.getModel().setYazmaBasmaSecimi(null);

        } else {
            this.getModel().setSikke(false);
            this.getModel().setSikkeDarpYonu(null);
        }
    }

    public void redirectToEserEditPage(final Integer eserId) {
        try {
            Faces.redirect(FacesContext.getCurrentInstance().getViewRoot().getViewId().substring(1) + "?faces-redirect=true&e=" + eserId);
        } catch (final UncheckedIOException e) {
            this.logger.info("[EserProposalController.redirectToEserEditPageByEserId] : {} {}", "Eser Kayıt Sayfasına Yönlendirmede Hata !!!", e.getMessage());
        }
    }

    public void redirectToEserEditPageFromList(final Integer eserId) {
        try {
            Faces.redirect(FacesContext.getCurrentInstance().getViewRoot().getViewId().substring(1).replace(".xhtml", "") + "-kayit?faces-redirect=true&e=" + eserId);
        } catch (final UncheckedIOException e) {
            this.logger.info("[EserProposalController.redirectToEserEditPageByEserId] : {} {}", "Eser Kayıt Sayfasına Yönlendirmede Hata !!!", e.getMessage());
        }
    }

    // handlers ...............................................................

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setEnvanterDefteriPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    // adders .................................................................

    public EserProposal fetchEserById(final int id) {
        return this.facade.findEagerById(id);
    }

    public void addFotograf() {
        if (this.getModel().getEserFotografs() == null) {
            this.getModel().setEserFotografs(new LinkedHashSet<>());
        }
        if (this.getModel().getEserFotografs().isEmpty()) {
            this.eserProposalFotografController.setAnaFoto(this.eserProposalFotografController.getModel());
        }
        this.getModel().getEserFotografs().add(this.eserProposalFotografController.getModel());
    }

    public void addMultiPhoto() {
        if (this.getModel().getEserFotografs() == null) {
            this.getModel().setEserFotografs(new LinkedHashSet<>());
        }
        for (final EserProposalFotograf photo : this.eserProposalFotografController.getItems()) {
            if (this.getModel().getEserFotografs().isEmpty()) {
                this.eserProposalFotografController.setAnaFoto(photo);
            }
            photo.setEser(this.getModel());
            this.getModel().getEserFotografs().add(photo);
        }
        this.eserProposalFotografController.getItems().clear();
    }

    public void addHareket() {
        if (this.getModel().getEserHarekets() == null) {
            this.getModel().setEserHarekets(new LinkedHashSet<>());
        }

        // yeni yapiyla eserHareketSahis kaydi icin
        this.eserHareketController.addEserHareketSahis();

        this.getModel().getEserHarekets().add(this.eserHareketController.getModel());
    }

    public void addMeasure() {
        if (this.getModel().getEserMeasures() == null) {
            this.getModel().setEserMeasures(new LinkedHashSet<>());
        }
        this.getModel().getEserMeasures().add(this.eserProposalMeasureController.getModel());
    }

    public void addMeasureList() {
        if (this.getModel().getEserMeasures() == null) {
            this.getModel().setEserMeasures(new LinkedHashSet<>());
        }
        this.getModel().getEserMeasures().addAll(this.eserProposalMeasureController.getSelectedEserMeasures());
        this.eserProposalMeasureController.resetInputFieldsLists();
    }

    public void addCizim() {
        if (this.getModel().getEserCizims() == null) {
            this.getModel().setEserCizims(new LinkedHashSet<>());
        }
        this.getModel().getEserCizims().add(this.eserProposalCizimController.getModel());
    }

    public void addTranscription() {
        if (this.getModel().getTranscriptions() == null) {
            this.getModel().setTranscriptions(new LinkedHashSet<>());
        }
        this.getModel().getTranscriptions().add(this.eserProposalTranscriptionController.getModel());
    }

    public void addKeyword() {
        if (this.getModel().getEserKeywords() == null) {
            this.getModel().setEserKeywords(new LinkedHashSet<>());
        }
        this.getModel().getEserKeywords().add(this.eserProposalKeywordController.getModel());
    }

    public void addMalzemeYapimTeknigi() {
        if (this.getModel().getEserMalzemeYapimTeknigis() == null) {
            this.getModel().setEserMalzemeYapimTeknigis(new LinkedHashSet<>());
        }
        this.getModel().getEserMalzemeYapimTeknigis().add(this.eserProposalMalzemeYapimTeknigiController.getModel());
    }

    public void addYapimTeknigiList() {
        if (this.getModel().getEserMalzemeYapimTeknigis() == null) {
            this.getModel().setEserMalzemeYapimTeknigis(new LinkedHashSet<>());
        }
        for (final EserProposalMalzemeYapimTeknigi eserMalzemeYapimTeknigi : this.eserProposalMalzemeYapimTeknigiController.getSelectedEserMalzemeYapimTeknigis()) {
            eserMalzemeYapimTeknigi.setEser(this.getModel());
            this.getModel().getEserMalzemeYapimTeknigis().add(eserMalzemeYapimTeknigi);
        }
        this.eserProposalMalzemeYapimTeknigiController.resetInputFieldsLists();
    }

    public void addMalzemeSuslemeTeknigi() {
        if (this.getModel().getEserMalzemeSuslemeTeknigis() == null) {
            this.getModel().setEserMalzemeSuslemeTeknigis(new LinkedHashSet<>());
        }
        this.getModel().getEserMalzemeSuslemeTeknigis().add(this.eserProposalMalzemeSuslemeTeknigiController.getModel());
    }

    public void addSuslemeTeknigiList() {
        if (this.getModel().getEserMalzemeSuslemeTeknigis() == null) {
            this.getModel().setEserMalzemeSuslemeTeknigis(new LinkedHashSet<>());
        }
        for (final EserProposalMalzemeSuslemeTeknigi eserMalzemeSuslemeTeknigi : this.eserProposalMalzemeSuslemeTeknigiController.getSelectedEserMalzemeSuslemeTeknigis()) {
            eserMalzemeSuslemeTeknigi.setEser(this.getModel());
            this.getModel().getEserMalzemeSuslemeTeknigis().add(eserMalzemeSuslemeTeknigi);
        }
        this.eserProposalMalzemeSuslemeTeknigiController.resetInputFieldsLists();
    }

    public void addStil() {
        if (this.getModel().getEserStils() == null) {
            this.getModel().setEserStils(new LinkedHashSet<>());
        }
        this.getModel().getEserStils().add(this.eserProposalStilController.getModel());
    }

    public void addStilList() {
        if (this.getModel().getEserStils() == null) {
            this.getModel().setEserStils(new LinkedHashSet<>());
        }
        this.getModel().getEserStils().addAll(this.eserProposalStilController.getSelectedEserStils());
    }

    public void addAtolye() {
        if (this.getModel().getEserAtolyes() == null) {
            this.getModel().setEserAtolyes(new LinkedHashSet<>());
        }
        this.getModel().getEserAtolyes().add(this.eserProposalAtolyeController.getModel());
    }

    public void addKaynak() {
        if (this.getModel().getEserKaynakLiteraturs() == null) {
            this.getModel().setEserKaynakLiteraturs(new LinkedHashSet<>());
        }
        this.getModel().getEserKaynakLiteraturs().add(this.eserProposalKaynakLiteraturController.getModel());
    }

    public void addYayin() {
        if (this.getModel().getEserYayinLiteraturs() == null) {
            this.getModel().setEserYayinLiteraturs(new LinkedHashSet<>());
        }
        this.getModel().getEserYayinLiteraturs().add(this.eserProposalYayinLiteraturController.getModel());
    }

    public void addSerh() {
        if (this.getModel().getEserSerhs() == null) {
            this.getModel().setEserSerhs(new LinkedHashSet<>());
        }
        this.eserProposalSerhController.getModel().setEser(this.getModel());
        this.getModel().getEserSerhs().add(this.eserProposalSerhController.getModel());
    }

    // removers ...............................................................

    public void removeFotograf(final EserProposalFotograf eserProposalFotograf) {
        this.getModel().getEserFotografs().remove(eserProposalFotograf);
        if (eserProposalFotograf.equals(this.eserProposalFotografController.getAnaFoto())) {
            if (this.getModel().getEserFotografs().isEmpty()) {
                this.eserProposalFotografController.setAnaFoto(null);
            } else {
                final EserProposalFotograf next = this.getModel().getEserFotografs().iterator().next();
                next.setAnaFotograf(true);
                this.eserProposalFotografController.setAnaFoto(next);
            }
        }
    }

    public void removeMeasure(final EserProposalMeasure measure) {
        this.getModel().getEserMeasures().remove(measure);
    }

    public void removeCizim(final EserProposalCizim eserCizim) {
        this.getModel().getEserCizims().remove(eserCizim);
    }

    public void removeTranscription(final EserProposalTranscription eserTranscription) {
        this.getModel().getTranscriptions().remove(eserTranscription);
    }

    public void removeKeyword(final EserProposalKeyword keyword) {
        this.getModel().getEserKeywords().remove(keyword);
    }

    public void removeMalzemeYapimTeknigi(final EserProposalMalzemeYapimTeknigi eserMalzemeYapimTeknigi) {
        this.getModel().getEserMalzemeYapimTeknigis().remove(eserMalzemeYapimTeknigi);
    }

    public void removeMalzemeSuslemeTeknigi(final EserProposalMalzemeSuslemeTeknigi eserMalzemeSuslemeTeknigi) {
        this.getModel().getEserMalzemeSuslemeTeknigis().remove(eserMalzemeSuslemeTeknigi);
    }

    public void removeStil(final EserProposalStil eserStil) {
        this.getModel().getEserStils().remove(eserStil);
    }

    public void removeAtolye(final EserProposalAtolye eserAtolye) {
        this.getModel().getEserAtolyes().remove(eserAtolye);
    }

    public void removeKaynak(final EserProposalKaynakLiteratur literatur) {
        this.getModel().getEserKaynakLiteraturs().remove(literatur);
    }

    public void removeYayin(final EserProposalYayinLiteratur yayin) {
        this.getModel().getEserYayinLiteraturs().remove(yayin);
    }

    public void removeSerh(final EserProposalSerh serh) {
        this.getModel().getEserSerhs().remove(serh);
    }

    private void restoreEserViaPermanentId() {
        this.uygarlikDonemController.newRecord();

        if (this.getModel().getCag() != null) {
            this.uygarlikDonemController.setKronoloji(this.getModel().getCag().getKronoloji());
        }
        this.uygarlikDonemController.setCag(this.getModel().getCag());
        this.uygarlikDonemController.getModel().setDonem(this.getModel().getDonem());
        this.uygarlikDonemController.getModel().setUygarlik(this.getModel().getUygarlik());
        this.uygarlikDonemController.setHukumdar(this.getModel().getHukumdar());
        Optional.ofNullable(this.getModel().getTermStart()).ifPresent(x -> this.uygarlikDonemController.fixStartTerm(x));
        Optional.ofNullable(this.getModel().getTermEnd()).ifPresent(x -> this.uygarlikDonemController.fixEndTerm(x));

        // set ana foto
        if (this.getModel().getEserFotografs() != null) {
            this.getModel().getEserFotografs().stream().filter(EserProposalFotograf::getAnaFotograf).forEach(x -> this.eserProposalFotografController.setAnaFoto(x));
        }

    }

    public void validateTerms() {
        if (this.getModel().getSignificantUretim() == null) {
            return;
        }
        if (this.getModel().getSignumUretim() == 3) {
            if (-this.getModel().getSignificantUretim() >= AbstractParameters.getPrehistoryThreshold()) {
                this.getModel().setSignumUretim(2);
            }
        } else if ((this.getModel().getSignumUretim() == 2) && (-this.getModel().getSignificantUretim() < AbstractParameters.getPrehistoryThreshold())) {

            this.getModel().setSignumUretim(3);

        }

        final Integer prodYear = this.toTimeline(this.getModel().getSignumUretim(), this.getModel().getSignificantUretim());
        if (prodYear > MuesUtil.getThisYear()) {
            this.getModel().setUretimYili(MuesUtil.getThisYear());
            MuesUtil.showMessage(":eserForm:uretimYili", this.bundle.getString("base.warning"), "Darp Başlangıç Yılı Gelecekte Olamaz.", FacesMessage.SEVERITY_WARN);
        }
    }

    // utilities ...............................................................

    public boolean disableDarpYiliEndInputs() {
        // darp yılı başlangıcı seçilmeden, bitiş üzerinde seçimler yapılamaz
        return ((this.getModel().getSignumUretim() == null) || (this.getModel().getSignificantUretim() == null));
    }

    private int toTimeline(final Integer signum, final int date) {
        if ((signum == 2) || (signum == 3)) {
            return -date;
        }
        return date;
    }

    @Override
    public void showDetail(final Integer id) {
        try {
            this.setModel(this.getFacade().findEagerById(id));
            this.setNewMode(false);
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + this.getModel().getOneId() + " numaralı öneri eseri görüntülemiştir.";
            this.auditFacade.log(AuditEvent.EserProposalGoruntuleme, msg);

        } catch (final MuesException e) {
            final String msg = this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + this.getModel().getOneId() + " numaralı öneri eseri görüntüleyememiştir.";
            this.auditFacade.log(AuditEvent.EserProposalGoruntulemeHata, msg, e.getMessage());
        }
    }

    public void loadDataTable() {
        this.resetTable();
        this.lazyTimeSpectrumDataModel = new LazyTimeSpectrumDataModel(this.timeSpectrumFacade);
    }

    public void resetTable() {
        ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("donemHukumdarSecimiForm:donemHukumdarTableId")).reset();
    }

    // getters and setters ....................................................

    @Override
    public EserProposalFacade getFacade() {
        return this.facade;
    }

    public DataModel<TimeSpectrum> getLazyTimeSpectrumDataModel() {
        return this.lazyTimeSpectrumDataModel;
    }

    public void setLazyTimeSpectrumDataModel(final LazyDataModel<TimeSpectrum> lazyTimeSpectrumDataModel) {
        this.lazyTimeSpectrumDataModel = lazyTimeSpectrumDataModel;
    }

    public Integer getOneriEserId() {
        return this.oneriEserId;
    }

    public void setOneriEserId(final Integer oneriEserId) {
        if (oneriEserId == null) {
            return;
        }
        this.setNewMode(false);
        if ((this.oneriEserId == null) || !Objects.equals(this.oneriEserId, oneriEserId)) {
            final EserProposal esr = this.getFacade().findEagerById(oneriEserId);
            if (esr == null) {
                return;
            }

            this.setModel(esr);
            this.restoreEserViaPermanentId();
            this.oneriEserId = oneriEserId;

            this.auditFacade.log(AuditEvent.EserProposalGoruntuleme,
                                 this.sessionBean.getCurrentUser().getKullaniciAdi() + " kullanıcısı " + this.getModel().getOneId() + " numaralı eseri görüntülemiştir.");
        }
    }

    public Boolean getIsDisabled() {
        return this.isDisabled;
    }

    public void setIsDisabled(final Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

}
