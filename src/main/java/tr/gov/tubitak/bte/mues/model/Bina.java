package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "BINA")
@NamedNativeQuery(name = "Bina.validateBeforeDelete", query = "SELECT (SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END FROM ALAN WHERE SILINMIS = 0 AND BINA_ID = :id)")
public class Bina extends BinaSuper implements DeleteValidatable {

    private static final long serialVersionUID = 6333391550779912072L;

    public Bina() {
        // default constructor
    }

}
