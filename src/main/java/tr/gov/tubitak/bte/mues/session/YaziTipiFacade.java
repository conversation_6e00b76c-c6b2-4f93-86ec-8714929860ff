package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Dil;
import tr.gov.tubitak.bte.mues.model.YaziTipi;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class YaziTipiFacade extends AbstractFacade<YaziTipi> {

    public YaziTipiFacade() {
        super(YaziTipi.class);
    }

    public List<YaziTipi> filterByNameAndDil(final String value, final Dil dil) {
        return this.em.createNamedQuery("YaziTipi.findByNameAndDil", YaziTipi.class)
                      .setParameter("ad", "%" + value + "%")
                      .setParameter("dil", dil)
                      .getResultList();
    }

    public List<YaziTipi> filterByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("YaziTipi.findByNameAndAciklama", YaziTipi.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
