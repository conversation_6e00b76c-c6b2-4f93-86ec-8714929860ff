package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;

/**
 *
*
 */
@Entity
@Table(name = "UYGARLIK_DONEM")
@NamedQuery(name = "UygarlikDonem.findEagerById", query = "SELECT x FROM UygarlikDonem x LEFT JOIN FETCH x.uygarlik LEFT JOIN FETCH x.donem d JOIN FETCH d.cag c JOIN FETCH c.kronoloji WHERE x.id = :id")
@NamedQuery(name = "UygarlikDonem.findAll", query = "SELECT x FROM UygarlikDonem x LEFT JOIN FETCH x.uygarlik LEFT JOIN FETCH x.donem d LEFT JOIN FETCH d.cag c LEFT JOIN FETCH c.kronoloji ORDER BY x.silinmis, x.aktif DESC")
@NamedQuery(name = "UygarlikDonem.findActive", query = "SELECT x FROM UygarlikDonem x WHERE x.aktif = true AND x.silinmis = false")
@NamedQuery(name = "UygarlikDonem.findByDonemAndUygarlik", query = "SELECT x FROM UygarlikDonem x WHERE x.donem = :donem AND x.uygarlik = :uygarlik AND x.aktif = true AND x.silinmis = false ORDER BY x.uygarlik.ad")
@NamedQuery(name = "UygarlikDonem.findByNameAndDonem", query = "SELECT u FROM UygarlikDonem x LEFT JOIN x.uygarlik u LEFT JOIN x.donem d WHERE x.aktif = true AND x.silinmis = false AND u.aktif = true AND u.silinmis = false AND d.aktif = true AND d.silinmis = false AND d = :donem AND u.ad LIKE :ad ORDER BY u.ad")
@NamedQuery(name = "UygarlikDonem.findByDonem", query = "SELECT x FROM UygarlikDonem x WHERE x.donem = :donem AND x.uygarlik IS NULL AND x.aktif = true AND x.silinmis = false")
@NamedQuery(name = "UygarlikDonem.findByUygarlik", query = "SELECT x FROM UygarlikDonem x WHERE x.uygarlik = :uygarlik AND x.aktif = true AND x.silinmis = false")
@NamedQuery(name = "UygarlikDonem.findAllUygarlik", query = "SELECT x FROM UygarlikDonem x LEFT JOIN FETCH x.uygarlik WHERE x.aktif = true AND x.silinmis = false AND x.uygarlik IS NOT NULL")
public class UygarlikDonem extends AbstractEntity {

    private static final long serialVersionUID = -1949925091636407649L;

    @JoinColumn(name = "DONEM_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Donem             donem;

    @JoinColumn(name = "UYGARLIK_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Uygarlik          uygarlik;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Column(name = "DONEM_BASLANGIC_YIL")
    private Integer           termStart;

    @Column(name = "DONEM_BITIS_YIL")
    private Integer           termEnd;

    @Transient
    private Integer           signumStart;

    @Transient
    private Integer           signumEnd;

    public UygarlikDonem() {
    }

    // getters and setters ....................................................

    public Donem getDonem() {
        return this.donem;
    }

    public void setDonem(final Donem donem) {
        this.donem = donem;
    }

    public Uygarlik getUygarlik() {
        return this.uygarlik;
    }

    public void setUygarlik(final Uygarlik uygarlik) {
        this.uygarlik = uygarlik;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Integer getTermStart() {
        return this.termStart;
    }

    public void setTermStart(final Integer termStart) {
        this.termStart = termStart;
    }

    public Integer getTermEnd() {
        return this.termEnd;
    }

    public void setTermEnd(final Integer termEnd) {
        this.termEnd = termEnd;
    }

    public Integer getSignumStart() {
        if ((this.signumStart == null) && (this.termStart != null)) {
            if (this.termStart > 0) {
                this.signumStart = 1;
            } else {
                if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumStart = 2;
                } else {
                    this.signumStart = 3;
                }
            }
        }
        return this.signumStart;
    }

    public void setSignumStart(final Integer signumStart) {
        this.signumStart = signumStart;
    }

    public Integer getSignumEnd() {
        if ((this.signumEnd == null) && (this.termEnd != null)) {
            if (this.termEnd > 0) {
                this.signumEnd = 1;
            } else {
                if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                    this.signumEnd = 2;
                } else {
                    this.signumEnd = 3;
                }
            }
        }
        return this.signumEnd;
    }

    public void setSignumEnd(final Integer signumEnd) {
        this.signumEnd = signumEnd;
    }

    public Integer getSignificantStart() {
        return this.termStart == null ? null : this.termStart < 0 ? -this.termStart : this.termStart;
    }

    public void setSignificantStart(final Integer significantStart) {
        if (this.getSignumStart() != null) {
            // it is already positive after Christ
            if (this.getSignumStart() == 1) {
                this.termStart = significantStart;
            } else {
                // set negative if before Christ
                this.termStart = -significantStart;
            }
        }
    }

    public Integer getSignificantEnd() {
        return this.termEnd == null ? null : this.termEnd < 0 ? -this.termEnd : this.termEnd;
    }

    public void setSignificantEnd(final Integer significantEnd) {
        if (this.getSignumEnd() != null) {
            // it is already positive after Christ
            if (this.getSignumEnd() == 1) {
                this.termEnd = significantEnd;
            } else {
                // set negative if before Christ
                this.termEnd = -significantEnd;
            }
        }
    }

    public String getTermStartTitle() {
        if (this.termStart == null) {
            return null;
        }
        if (this.termStart < 0) {
            if (this.termStart > AbstractParameters.getPrehistoryThreshold()) {
                return "M.Ö. " + -this.termStart;
            }
            return "G.Ö. " + -this.termStart;
        }
        return "M.S. " + this.termStart;
    }

    public String getTermEndTitle() {
        if (this.termEnd == null) {
            return null;
        }
        if (this.termEnd < 0) {
            if (this.termEnd > AbstractParameters.getPrehistoryThreshold()) {
                return "M.Ö. " + -this.termEnd;
            }
            return "G.Ö. " + -this.termEnd;
        }
        return "M.S. " + this.termEnd;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.aciklama).orElse("" + this.getId());
    }

    @Override
    public String toString() {
        // return "{Uygarlık: " + this.uygarlik + "; Dönem: " + this.donem + "}";
        return "";
    }

}
