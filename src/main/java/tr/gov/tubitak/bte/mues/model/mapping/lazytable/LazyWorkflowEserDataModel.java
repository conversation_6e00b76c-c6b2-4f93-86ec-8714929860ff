package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.util.List;
import java.util.Map;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.mapping.WorkflowEserView;
import tr.gov.tubitak.bte.mues.session.WorkflowEserViewLazyLoadFacade;

public class LazyWorkflowEserDataModel extends LazyDataModel<WorkflowEserView> {

    private static final long                              serialVersionUID = 3329819774365568615L;

    private static final Logger                            logger           = LoggerFactory.getLogger(LazyWorkflowEserDataModel.class);

    private final transient WorkflowEserViewLazyLoadFacade facade;

    private List<WorkflowEserView>                         data;

    public LazyWorkflowEserDataModel(final WorkflowEserViewLazyLoadFacade facade) {
        this.facade = facade;
    }

    @Override
    public List<WorkflowEserView> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {
        // this.setRowCount(this.facade.count(filterBy));
        this.data = this.facade.fetchData(first, pageSize, sortBy, filterBy);
        logger.debug("filters: {} _ multiSortMeta Length,  {}", sortBy, filterBy);
        return this.data;
    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
            return this.facade.count(filterBy);
        }

    @Override
    public WorkflowEserView getRowData(final String rowKey) {
        for (final WorkflowEserView each : this.data) {
            if (each.getEserID().toString().equals(rowKey)) {
                return each;
            }
        }
        return null;
    }

    @Override
    public String getRowKey(final WorkflowEserView w) {
        return w.getEserID().toString();
    }

}
