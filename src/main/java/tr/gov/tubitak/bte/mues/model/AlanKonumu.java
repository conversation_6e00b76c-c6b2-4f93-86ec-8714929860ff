package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.ValidAlanKonumu;
import tr.gov.tubitak.bte.mues.constraint.validator.AlanKonumuGroup;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "ALAN_KONUMU")
@NamedNativeQuery(name = "AlanKonumu.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_DEPO WHERE SILINMIS = 0 AND ALAN_KONUMU_ID = :id)")

@ValidAlanKonumu(groups = AlanKonumuGroup.class)
public class AlanKonumu extends AlanKonumuSuper implements DeleteValidatable {

    private static final long serialVersionUID = 8002475743152810876L;

    public AlanKonumu() {
        // defualt constructor
    }

}
