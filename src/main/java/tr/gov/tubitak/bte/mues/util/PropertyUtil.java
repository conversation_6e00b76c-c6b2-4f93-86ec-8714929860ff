package tr.gov.tubitak.bte.mues.util;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ResourceBundle;

import javax.enterprise.context.ApplicationScoped;
import javax.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ApplicationScoped
public class PropertyUtil implements Serializable {

    private static final long   serialVersionUID = 1714522960245386164L;

    private static final Logger logger           = LoggerFactory.getLogger(PropertyUtil.class);

    private ResourceBundle      bundle;

    PropertyUtil() {
        // intentially left blank
    }

    @Inject
    PropertyUtil(final ResourceBundle resourceBundle) {
        this.bundle = resourceBundle;
    }

    public String getMessage(final String messageCode, final Object... arguments) {
        try {
            if (this.bundle == null) {
                this.bundle = ResourceBundle.getBundle("labels"); // Manual load
            }
            return MessageFormat.format(this.bundle.getString(messageCode), arguments);

        } catch (final Exception e) {
            logger.error("PropertyUtil.getMessage error {}-{}", e.getMessage(), e);
            return null;
        }
    }
}
