package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.EserProposalMeasure;
import tr.gov.tubitak.bte.mues.model.Measure;
import tr.gov.tubitak.bte.mues.model.MeasureType;
import tr.gov.tubitak.bte.mues.session.EserProposalMeasureFacade;
import tr.gov.tubitak.bte.mues.session.MeasureFacade;
import tr.gov.tubitak.bte.mues.session.MeasureTypeFacade;

@Named
@ViewScoped
public class EserProposalMeasureController extends AbstractController<EserProposalMeasure> {

    private static final long         serialVersionUID = -4465419352459118582L;

    @Inject
    private EserProposalMeasureFacade facade;

    @Inject
    private MeasureFacade             measureFacade;

    @Inject
    private MeasureTypeFacade         measureTypeFacade;

    private MeasureType               measureType;

    private List<MeasureType>         measureTypeList;

    private List<Measure>             measureList;

    private List<EserProposalMeasure> eserMeasureList;

    public EserProposalMeasureController() {
        super(EserProposalMeasure.class);
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.measureType = null;
    }

    public void handleMeasureTypeChange(final SelectEvent event) {
        this.measureType = (MeasureType) event.getObject();
    }

    public void handleMeasureChange(final SelectEvent event) {
        final Measure measure = (Measure) event.getObject();
        measure.setType(this.measureType);
        this.getModel().setMeasure(measure);
    }

    public List<Measure> filterByNameAndType(final String value) {
        return this.getFacade().findByNameAndType(value, this.measureType);
    }

    public List<Measure> filterByMeasureType(final Integer typeID) {
        if (typeID != null) {
            return this.getMeasureList().stream().filter(x -> x.getType().getId().intValue() == typeID.intValue()).collect(Collectors.toList());
        }
        return this.getMeasureList();
    }

    public List<EserProposalMeasure> getSelectedEserMeasures() {
        return this.getEserMeasureList()
                   .stream()
                   .filter(x -> ((x.getMeasure() != null) && (x.getDeger() != null) && !x.getDeger().equals(""))
                                && ((x.getMeasure().getType() != null) && !x.getMeasure().getType().toString().equals(""))
                                && (x.getMeasure().getAd() != null) && !x.getMeasure().getAd().equals(""))
                   .collect(Collectors.toList());
    }

    // getters and setters ....................................................

    @Override
    public void setModel(final EserProposalMeasure eserMeasure) {
        super.setModel(eserMeasure);
        this.measureType = this.getModel().getMeasure().getType();
    }

    @Override
    public EserProposalMeasureFacade getFacade() {
        return this.facade;
    }

    public MeasureType getMeasureType() {
        return this.measureType;
    }

    public void setMeasureType(final MeasureType measureType) {
        this.measureType = measureType;
    }

    public List<MeasureType> getMeasureTypeList() {
        if (this.measureTypeList == null) {
            this.measureTypeList = this.measureTypeFacade.findActive();
        }
        return this.measureTypeList;
    }

    public void setMeasureTypeList(final List<MeasureType> measureTypeList) {
        this.measureTypeList = measureTypeList;
    }

    public List<Measure> getMeasureList() {
        if (this.measureList == null) {
            this.measureList = this.measureFacade.findActive();
        }
        return this.measureList;
    }

    public void setMeasureList(final List<Measure> measureList) {
        this.measureList = measureList;
    }

    public List<EserProposalMeasure> getEserMeasureList() {
        if (this.eserMeasureList == null) {
            this.eserMeasureList = new ArrayList<>();
            for (final MeasureType type : this.getMeasureTypeList()) {
                final EserProposalMeasure eserProposalMeasure = new EserProposalMeasure();
                final Measure measure = new Measure();
                measure.setType(type);
                eserProposalMeasure.setMeasure(measure);
                this.eserMeasureList.add(eserProposalMeasure);
            }
        }
        return this.eserMeasureList;
    }

    public void setEserMeasureList(final List<EserProposalMeasure> eserMeasureList) {
        this.eserMeasureList = eserMeasureList;
    }

    public void resetInputFieldsLists() {
        this.eserMeasureList = null;
        this.measureTypeList = null;
        super.setModel(null);

    }
}
