package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Entity
@Table(name = "MeasureType")
@NamedQuery(name = "MeasureType.findEagerById", query = "SELECT t FROM MeasureType t WHERE t.id = :id")
@NamedQuery(name = "MeasureType.findAll", query = "SELECT t FROM MeasureType t ORDER BY t.silinmis, t.aktif DESC, t.ad")
@NamedQuery(name = "MeasureType.findActive", query = "SELECT t FROM MeasureType t WHERE t.aktif = true AND t.silinmis = false ORDER BY t.ad")
@NamedQuery(name = "MeasureType.findByNameAndAciklama", query = "SELECT t FROM MeasureType t WHERE t.aktif = true AND t.silinmis = false AND (t.ad LIKE :str OR t.aciklama LIKE :str) ORDER BY t.ad")
public class MeasureType extends AbstractEntity {

    private static final long serialVersionUID = -5386957763144680452L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public MeasureType() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getAd();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
