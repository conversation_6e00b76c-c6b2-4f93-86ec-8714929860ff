package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.Ilce;
import tr.gov.tubitak.bte.mues.session.IlceFacade;

@Named
@ViewScoped
public class IlceController extends AbstractController<Ilce> {

    private static final long serialVersionUID = 7811177775899897181L;

    @Inject
    private IlceFacade        facade;

    private Il                il;

    public IlceController() {
        super(Ilce.class);
    }

    @Override
    public IlceFacade getFacade() {
        return this.facade;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public List<Ilce> filterByNameAndIl(final String value) {
        return this.getFacade().filterByNameAndIl(value, this.il);
    }

}
