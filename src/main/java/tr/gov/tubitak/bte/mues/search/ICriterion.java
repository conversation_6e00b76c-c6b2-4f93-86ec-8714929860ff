package tr.gov.tubitak.bte.mues.search;

import java.io.Serializable;
import java.util.List;

import tr.gov.tubitak.bte.mues.model.Metadata;

/**
 * ICriterion arayüzü, arama kriterlerinin ortak davranışlarını tanımlamaktadır.
 */
public interface ICriterion extends Serializable {

    /**
     * Arama kriteri modelini atar.
     *
     * @param model kriter modeli
     * @return arama kriter
     */
    public ICriterion setModel(CriterionModel model);

    /**
     * Kullanıcıya görüntülenecek metni döner.
     *
     * @return metin
     */
    public String getText();

    /**
     * Sorguda kullanılacak metni döner.
     *
     * @return metin
     */
    public String getSql();

    /**
     * Sorguda kullanılacak metni döner.
     *
     * @return metin
     */
    public String getFacet();

    /**
     * Kriter geçerli mi kontrolünü yapar.
     *
     * @return true, geçerli ise
     */
    public boolean isValid();

    /**
     * Kriterin ilişkili olduğu arama nesnesini atar.
     *
     * @param searchCriterionDefinition arama nesnesi
     */
    public void setSearchCriterionDefinition(final SearchCriterionDefinition searchCriterionDefinition);

    /**
     * Kriterin alt kriterlerini atar.
     *
     * @param children alt kriterler
     */
    public void setChildren(final List<ICriterion> children);

    /**
     * Listede sıralama için kullanılan değeri atar.
     *
     * @param rowId sıra no
     */
    public void setRowId(final Integer rowId);

    /**
     * Kriterin ilişkili olduğu üstveriyi döner.
     *
     * @return üstveri
     */
    public Metadata getMetadata();

}
