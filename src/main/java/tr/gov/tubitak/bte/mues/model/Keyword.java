package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

@Audited
@Entity
@Table(name = "Keyword")
@NamedQuery(name = "Keyword.findEagerById", query = "SELECT k FROM Keyword k WHERE k.id = :id")
@NamedQuery(name = "Keyword.findAll", query = "SELECT k FROM Keyword k ORDER BY k.silinmis, k.aktif DESC, k.ad")
@NamedQuery(name = "Keyword.findByNameAndKeyword", query = "SELECT k FROM Keyword k WHERE k.aktif = true AND k.silinmis = false AND k.ad LIKE :ad AND k.id NOT IN :keywordIds ORDER BY k.ad")
@NamedQuery(name = "Keyword.findActive", query = "SELECT k FROM Keyword k WHERE k.aktif = true AND k.silinmis = false ORDER BY k.ad")
@NamedNativeQuery(name = "Keyword.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM Eser_Keyword WHERE SILINMIS = 0 AND keywordId = :id)")

public class Keyword extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -6180018060965897407L;

    @Size(max = 50)
    @Column(name = "ad", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "aciklama", length = 150)
    private String            aciklama;

    public Keyword() {
    }

    // getters and setters ................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getAd();
    }

    @Override
    public String getTitle() {
        return this.getAd();
    }

}
