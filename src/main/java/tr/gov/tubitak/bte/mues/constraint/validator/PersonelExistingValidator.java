package tr.gov.tubitak.bte.mues.constraint.validator;

import java.io.Serializable;
import java.util.List;
import java.util.ResourceBundle;

import javax.enterprise.context.Dependent;
import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.validator.FacesValidator;
import javax.faces.validator.ValidatorException;
import javax.inject.Inject;
import javax.persistence.EntityManager;

import org.omnifaces.validator.ValueChangeValidator;

import tr.gov.tubitak.bte.mues.model.ExternalUser;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.Sahis;

@Dependent
@FacesValidator("personelExistingValidator")
public class PersonelExistingValidator extends ValueChangeValidator<String> implements Serializable {

    private static final long serialVersionUID = 1217679250128925579L;

    @Inject
    ResourceBundle            bundle;

    @Inject
    protected EntityManager   em;

    // 1-Personel 2-Sahis 3-ExternalUser
    @Override
    public void validateChangedObject(final FacesContext context, final UIComponent component, final String submittedValue) {

        final String personType = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("personType");
        List<?> resultList = null;
        if ("1".equals(personType)) {
            resultList = this.em.createNamedQuery("Personel.findByTCKN", Personel.class).setParameter("tckn", submittedValue).getResultList();

        } else if ("2".equals(personType)) {
            resultList = this.em.createNamedQuery("Sahis.findByTCNo", Sahis.class).setParameter("tcNo", submittedValue).getResultList();

        } else if ("3".equals(personType)) {
            resultList = this.em.createNamedQuery("ExternalUser.findByTCNo", ExternalUser.class).setParameter("tcNo", submittedValue).getResultList();

        }

        if ((resultList != null) && !resultList.isEmpty()) {
            final FacesMessage messageInfo = new FacesMessage();

            messageInfo.setSummary("Sistemde Kullanici mevcut. Pasif edilmiş veya silinmiş kullanıcılar yada diğer modüllerden çekme seçeneğini deneyiniz.");
            messageInfo.setSeverity(FacesMessage.SEVERITY_INFO);

            FacesContext.getCurrentInstance().addMessage(null, messageInfo);
            final FacesMessage messageErr = new FacesMessage();
            messageErr.setSummary("TCKN sistemde mevcut");
            messageErr.setSeverity(FacesMessage.SEVERITY_ERROR);
            throw new ValidatorException(messageErr);
        }
    }

}