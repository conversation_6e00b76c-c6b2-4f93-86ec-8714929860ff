package tr.gov.tubitak.bte.mues.shiro;

import java.io.IOException;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.json.Json;
import javax.json.JsonObject;
import javax.json.JsonReader;
import javax.sql.DataSource;

import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.realm.AuthenticatingRealm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class OAuth2Realm extends AuthenticatingRealm {

    private static final String ACCCESS_TOKEN     = "access_token";

    private static final String AUTH_SUCCESS_CODE = "EDV09.000";

    private static final String RESULT_CODE       = "sonucKodu";

    private static final Logger logger            = LoggerFactory.getLogger(OAuth2Realm.class);

    private String              accessTokenUrl;

    private String              clientId;

    private String              clientSecret;

    private String              grantType;

    private String              redirectUrl;

    private String              userInfoUrl;

    private String              scope;

    private DataSource          dataSource;

    private String              userActiveQuery;

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(final AuthenticationToken token) {
        final String code = ((OAuth2Token) token).getAuthCode();
        try {
            // token request
            final HttpClient client = HttpClientBuilder.create().build();
            final HttpPost tokenRequest = new HttpPost(this.accessTokenUrl);
            List<NameValuePair> urlParameters = new ArrayList<>();
            urlParameters.add(new BasicNameValuePair("grant_type", this.grantType));
            urlParameters.add(new BasicNameValuePair("client_id", this.clientId));
            urlParameters.add(new BasicNameValuePair("client_secret", this.clientSecret));
            urlParameters.add(new BasicNameValuePair("code", code));
            urlParameters.add(new BasicNameValuePair("redirect_uri", this.redirectUrl));
            logger.debug("[doGetAuthenticationInfo] : {} - {}", this.accessTokenUrl, urlParameters);

            tokenRequest.setEntity(new UrlEncodedFormEntity(urlParameters));
            HttpResponse response = client.execute(tokenRequest);

            JsonObject object = this.getResponseJsonObject(response);
            if (object.getString(ACCCESS_TOKEN) == null) {
                throw new AuthenticationException("Kullanıcı doğrulanamadı: " + object.getString(RESULT_CODE));
            }
            // validate credentials
            final HttpPost loginRequest = new HttpPost(this.userInfoUrl);
            urlParameters = new ArrayList<>();
            urlParameters.add(new BasicNameValuePair("accessToken", object.getString(ACCCESS_TOKEN)));
            urlParameters.add(new BasicNameValuePair("clientId", this.clientId));
            urlParameters.add(new BasicNameValuePair("resourceId", "1"));
            urlParameters.add(new BasicNameValuePair("kapsam", this.scope));
            logger.debug("[doGetAuthenticationInfo] : {} - {}", this.userInfoUrl, urlParameters);

            loginRequest.setEntity(new UrlEncodedFormEntity(urlParameters));
            response = client.execute(loginRequest);

            object = this.getResponseJsonObject(response);
            if (AUTH_SUCCESS_CODE.equals(object.getString(RESULT_CODE))) {
                this.checkIfUserIsActiveOnDB(object.getString("kimlikNo"));
                logger.info("[doGetAuthenticationInfo] : Giriş başarılı {}", object);
                return new SimpleAuthenticationInfo(object.getString("kimlikNo"), code, this.getName());
            }
            throw new AuthenticationException("Kullanıcı doğrulanamadı: " + object.getString(RESULT_CODE));

        } catch (final IOException e) {
            throw new AuthenticationException("Kullanıcı doğrulanamadı", e);
        }
    }

    private void checkIfUserIsActiveOnDB(final String userName) {
        try (Connection conn = this.dataSource.getConnection(); PreparedStatement ps = this.createUserActivePreparedStatement(conn, userName); ResultSet rs = ps.executeQuery();) {
            // because it is a count query, looping is not required
            rs.next();
            if (rs.getInt(1) > 1) {
                throw new AuthenticationException("HATA: Birden fazla kullanıcı kaydı döndü. Kullanıcı adı: [" + userName + "]");
            }
            if (rs.getInt(1) == 0) {
                throw new AuthenticationException("HATA: Kullanıcı aktif değil ya da silinmiş. Kullanıcı adı: [" + userName + "]");
            }
        } catch (final SQLException e) {
            final String message = "There was an SQL error while authenticating user [" + userName + "]";
            logger.error("[checkIfUserIsActiveOnDB] : Hata : {}", message, e);
            // Rethrow any SQL errors as an authentication exception
            throw new AuthenticationException(message, e);
        }
    }

    private PreparedStatement createUserActivePreparedStatement(final Connection conn, final String userName) throws SQLException {
        final PreparedStatement ps = conn.prepareStatement(this.userActiveQuery);
        ps.setString(1, userName);
        return ps;
    }

    private JsonObject getResponseJsonObject(final HttpResponse response) throws IOException {
        if ((response.getStatusLine() == null) || (response.getStatusLine().getStatusCode() != 200)) {
            throw new AuthenticationException("Doğrulama sunucusuna bağlanılamadı");
        }
        final JsonReader jsonReader = Json.createReader(new InputStreamReader(response.getEntity().getContent()));
        final JsonObject object = jsonReader.readObject();
        jsonReader.close();
        return object;
    }

    @Override
    public boolean supports(final AuthenticationToken token) {
        return token instanceof OAuth2Token;
    }

    public void setAccessTokenUrl(final String accessTokenUrl) {
        this.accessTokenUrl = accessTokenUrl;
    }

    public void setClientId(final String clientId) {
        this.clientId = clientId;
    }

    public void setClientSecret(final String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public void setGrantType(final String grantType) {
        this.grantType = grantType;
    }

    public void setRedirectUrl(final String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    public void setScope(final String scope) {
        this.scope = scope;
    }

    public void setUserInfoUrl(final String userInfoUrl) {
        this.userInfoUrl = userInfoUrl;
    }

    public void setDataSource(final DataSource dataSource) {
        this.dataSource = dataSource;
    }

    public void setUserActiveQuery(final String userActiveQuery) {
        this.userActiveQuery = userActiveQuery;
    }

}
