package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import tr.gov.tubitak.bte.mues.model.Monogram;

@RequestScoped
public class MonogramFacade extends AbstractFacade<Monogram> {

    public MonogramFacade() {
        super(Monogram.class);
    }

    public List<Monogram> filterByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Monogram.findByNameAndAciklama", Monogram.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
