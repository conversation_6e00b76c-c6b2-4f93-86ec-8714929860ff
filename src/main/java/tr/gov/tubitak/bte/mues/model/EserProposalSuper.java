package tr.gov.tubitak.bte.mues.model;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Transient;

import org.hibernate.annotations.Where;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
 * halis.yilboga
 */

@MappedSuperclass

@NamedQuery(name = "EserProposal.findAllByApplicationTypeAndPermission", query = "SELECT e FROM EserProposal e  LEFT JOIN FETCH e.anaFotograf  LEFT JOIN FETCH e.eserOneriPersonel LEFT JOIN FETCH e.eserOneriMudurluk m  LEFT JOIN FETCH e.eserOneriResearcher  where e.applicationType = :applicationType and m.id in :mudurluks ORDER BY e.silinmis, e.aktif DESC, e.id ")
@NamedQuery(name = "EserProposal.findActive", query = "SELECT e FROM EserProposal e WHERE e.aktif = true AND e.silinmis = false ORDER BY e.id")

@NamedQuery(name = "EserProposal.findEagerById", query = "SELECT e FROM EserProposal e "
                                                         + "LEFT JOIN FETCH e.eserHarekets eh "
                                                         + "LEFT JOIN FETCH e.cag c LEFT JOIN FETCH c.kronoloji "
                                                         + "LEFT JOIN FETCH e.donem do LEFT JOIN FETCH do.cag dc LEFT JOIN FETCH dc.kronoloji "
                                                         + "LEFT JOIN FETCH e.eserMalzemeYapimTeknigis yt LEFT JOIN FETCH yt.malzeme LEFT JOIN FETCH yt.yapimTeknigi LEFT JOIN FETCH yt.renks "
                                                         + "LEFT JOIN FETCH e.eserMalzemeSuslemeTeknigis st LEFT JOIN FETCH st.malzeme LEFT JOIN FETCH st.suslemeTeknigi LEFT JOIN FETCH st.renks "
                                                         + "LEFT JOIN FETCH e.transcriptions t LEFT JOIN FETCH t.yaziDili LEFT JOIN FETCH t.yaziTipi "
                                                         + "LEFT JOIN FETCH e.eserMeasures m LEFT JOIN FETCH m.measure mm LEFT JOIN FETCH mm.type "
                                                         + "LEFT JOIN FETCH e.eserKeywords k LEFT JOIN FETCH k.keyword "
                                                         + "LEFT JOIN FETCH e.eserAltTur at LEFT JOIN FETCH at.eserTur "
                                                         + "LEFT JOIN FETCH e.eserStils s LEFT JOIN FETCH s.stil "
                                                         + "LEFT JOIN FETCH e.eserAtolyes a LEFT JOIN FETCH a.atolye "
                                                         + "LEFT JOIN FETCH e.eserYayinLiteraturs ly LEFT JOIN FETCH ly.literatur "
                                                         + "LEFT JOIN FETCH e.eserKaynakLiteraturs lk LEFT JOIN FETCH lk.literatur "
                                                         + "LEFT JOIN FETCH e.iliskilendirme i LEFT JOIN FETCH i.tur LEFT JOIN FETCH i.groupedArtifacts ia LEFT JOIN FETCH ia.eserFotografs LEFT JOIN FETCH ia.eserHarekets "
                                                         + "LEFT JOIN FETCH e.eserFotografs f LEFT JOIN FETCH f.ilgiliYuz "
                                                         + "LEFT JOIN FETCH e.anaFotograf "
                                                         + "LEFT JOIN FETCH e.eserZimmets z LEFT JOIN FETCH z.zimmetPersonel "
                                                         + "LEFT JOIN FETCH e.eserCizims "
                                                         + "LEFT JOIN FETCH e.kondisyonDurumu "
                                                         + "LEFT JOIN FETCH e.sikkeDarpYonu "
                                                         + "LEFT JOIN FETCH e.yazmaBasmaSecimi "
                                                         + "LEFT JOIN FETCH e.islamiGayriSecimi "
                                                         + "LEFT JOIN FETCH e.elisiDokumaSecimi "
                                                         + "LEFT JOIN FETCH e.hukumdar "
                                                         + "LEFT JOIN FETCH e.uygarlik "
                                                         + "LEFT JOIN FETCH e.yaptiranVip "
                                                         + "LEFT JOIN FETCH e.bagislayanVip "
                                                         + "LEFT JOIN FETCH e.yapanVip "
                                                         + "LEFT JOIN FETCH e.kullanacakVip "
                                                         + "LEFT JOIN FETCH e.kullananVip "
                                                         + "LEFT JOIN FETCH e.tasinirMalYonKod "
                                                         + "LEFT JOIN FETCH e.uretimYeri "
                                                         + "LEFT JOIN FETCH e.uretimBolgesi "
                                                         + "LEFT JOIN FETCH e.darpYeri "
                                                         + "LEFT JOIN FETCH e.eserSerhs "
                                                         + "LEFT JOIN FETCH e.eserOneriPersonel "
                                                         + "LEFT JOIN FETCH e.eserOneriResearcher "
                                                         + "LEFT JOIN FETCH e.eserOneriMudurluk "
                                                         + "WHERE e.id = :id")
@NamedQuery(name = "EserProposal.findByEserProposalIdAndSearchParameters", query = "SELECT e FROM EserProposal e  LEFT JOIN FETCH e.anaFotograf LEFT JOIN FETCH e.eserOneriPersonel WHERE (e.id = :eserProposalId AND ((e.dateCreated >= :startDate AND e.dateCreated <= :endDate) OR e.dateCreated = NULL)) AND e.silinmis = FALSE AND e.aktif = TRUE ORDER BY e.silinmis, e.aktif DESC, e.id ")
@NamedQuery(name = "EserProposal.findByEserProposalAndSearchParameters", query = "SELECT e FROM EserProposal e  LEFT JOIN FETCH e.anaFotograf LEFT JOIN FETCH e.eserOneriPersonel WHERE ((e.dateCreated >= :startDate AND e.dateCreated <= :endDate) OR e.dateCreated = NULL) AND e.silinmis = FALSE AND e.aktif = TRUE ORDER BY e.silinmis, e.aktif DESC, e.id ")
@NamedQuery(name = "EserProposal.getLastEserProposalOfPersonelId", query = "SELECT e FROM EserProposal e "
                                                                           + "LEFT JOIN FETCH e.eserOneriMudurluk em "
                                                                           + "LEFT JOIN FETCH e.eserOneriPersonel ep "
                                                                           + " WHERE ep.id = :userId ORDER BY e.dateUpdated DESC ")
@NamedQuery(name = "EserProposal.getLastEserProposalOfResearcherId", query = "SELECT e FROM EserProposal e "
                                                                             + "LEFT JOIN FETCH e.eserOneriMudurluk em "
                                                                             + "LEFT JOIN FETCH e.eserOneriResearcher er "
                                                                             + " WHERE er.id = :userId ORDER BY e.dateUpdated DESC ")
public class EserProposalSuper extends EserSuper
{

    private static final long                      serialVersionUID = 5814980331509052387L;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalFotograf>              eserFotografs;

    @OneToMany(mappedBy = "eser", fetch = FetchType.LAZY)
    @Where(clause = " ANA_FOTOGRAF = 1 ")
    private Set<EserProposalFotograf>              anaFotograf;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalMeasure>               eserMeasures;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalCizim>                 eserCizims;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalTranscription>         transcriptions;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalKeyword>               eserKeywords;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserProposalMalzemeYapimTeknigi>   eserMalzemeYapimTeknigis;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserProposalMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigis;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalStil>                  eserStils;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalAtolye>                eserAtolyes;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalKaynakLiteratur>       eserKaynakLiteraturs;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<EserProposalYayinLiteratur>        eserYayinLiteraturs;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserProposalSerh>                  eserSerhs;

    @JoinColumn(name = "KONDISYON_DURUMU", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                               kondisyonDurumu;

    @JoinColumn(name = "SIKKE_DARP_YONU", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                               sikkeDarpYonu;

    @JoinColumn(name = "YAZMA_BASMA_SECIMI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                               yazmaBasmaSecimi;

    @JoinColumn(name = "ISLAMI_GAYRI_SECIMI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                               islamiGayriSecimi;

    @JoinColumn(name = "ELISI_DOKUMA_SECIMI", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.MERGE)
    private MuesPick                               elisiDokumaSecimi;

    @JoinColumn(name = "iliskilendirmeId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Iliskilendirme                         iliskilendirme;
    // PROPOSAL FIELDS
    @JoinColumn(name = "eserOneriPersonel", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView                           eserOneriPersonel;

    @JoinColumn(name = "researcherId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Researcher                             eserOneriResearcher;

    /// PROPOSAL FIELDS ENDS
    @JoinColumn(name = "uretimYeriId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private UretimYeri                             uretimYeri;

    @JoinColumn(name = "uretimBolgesiId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private UretimBolgesi                          uretimBolgesi;

    @JoinColumn(name = "darpYeriId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private DarpYeri                               darpYeri;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eser", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<EserProposalHareket>               eserHarekets;

    @Column(name = "applicationType")
    private Integer                                applicationType;

    @Column(name = "eserOneriYapanKisiOzet", length = 100)
    private String                                 eserOneriYapanKisiOzet;

    @Column(name = "artifactId")
    private Integer                                artifactId;

    @Transient
    private Integer                                signumStart;

    @Transient
    private Integer                                signumEnd;

    @Transient
    private Integer                                signumUretim;

    /**
     * Instantiates a new eser.
     */
    public EserProposalSuper() {
        // empty constructor for generation of model
    }

    // getters and setters ....................................................

    public MuesPick getSikkeDarpYonu() {
        return this.sikkeDarpYonu;
    }

    public void setSikkeDarpYonu(final MuesPick sikkeDarpYonu) {
        this.sikkeDarpYonu = sikkeDarpYonu;
    }

    public MuesPick getYazmaBasmaSecimi() {
        return this.yazmaBasmaSecimi;
    }

    public void setYazmaBasmaSecimi(final MuesPick yazmaBasmaSecimi) {
        this.yazmaBasmaSecimi = yazmaBasmaSecimi;
    }

    public MuesPick getIslamiGayriSecimi() {
        return this.islamiGayriSecimi;
    }

    public void setIslamiGayriSecimi(final MuesPick islamiGayriSecimi) {
        this.islamiGayriSecimi = islamiGayriSecimi;
    }

    public MuesPick getElisiDokumaSecimi() {
        return this.elisiDokumaSecimi;
    }

    public void setElisiDokumaSecimi(final MuesPick elisiDokumaSecimi) {
        this.elisiDokumaSecimi = elisiDokumaSecimi;
    }

    @Override
    public Iliskilendirme getIliskilendirme() {
        return this.iliskilendirme;
    }

    @Override
    public void setIliskilendirme(final Iliskilendirme iliskilendirme) {
        this.iliskilendirme = iliskilendirme;
    }

    @Override
    public UretimYeri getUretimYeri() {
        return this.uretimYeri;
    }

    @Override
    public void setUretimYeri(final UretimYeri uretimYeri) {
        this.uretimYeri = uretimYeri;
    }

    @Override
    public UretimBolgesi getUretimBolgesi() {
        return this.uretimBolgesi;
    }

    @Override
    public void setUretimBolgesi(final UretimBolgesi uretimBolgesi) {
        this.uretimBolgesi = uretimBolgesi;
    }

    @Override
    public DarpYeri getDarpYeri() {
        return this.darpYeri;
    }

    @Override
    public void setDarpYeri(final DarpYeri darpYeri) {
        this.darpYeri = darpYeri;
    }

    public List<EserProposalMeasure> getListFromEserMeasure(final Set<EserProposalMeasure> eserMeasures) {
        return new ArrayList<>(eserMeasures);
    }

    public List<EserProposalMalzemeYapimTeknigi> getListFromEserMalzemeYapimTeknigis(final Set<EserProposalMalzemeYapimTeknigi> eserMalzemeYapimTeknigis) {
        return new ArrayList<>(eserMalzemeYapimTeknigis);
    }

    @Override
    public void setSignumUretim(final Integer signumUretim) {
        this.signumUretim = signumUretim;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.getEserId()).orElse(this.getEserOzelAdi());
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.getEserId(), MuesUtil.surroundWithParanthesis(this.getEserOzelAdi()));
    }

    @Override
    public String getName() {
        return Optional.ofNullable(this.getEserOzelAdi()).orElse("" + this.getId());
    }

    public Set<EserProposalKeyword> getEserKeywords() {
        return this.eserKeywords;
    }

    public void setEserKeywords(final Set<EserProposalKeyword> eserKeywords) {
        this.eserKeywords = eserKeywords;
    }

    public Set<EserProposalFotograf> getEserFotografs() {
        if (this.eserFotografs == null) {
            this.eserFotografs = new LinkedHashSet<>();
        }
        return this.eserFotografs;
    }

    public void setEserFotografs(final Set<EserProposalFotograf> eserFotografs) {
        this.eserFotografs = eserFotografs;
    }

    public Set<EserProposalFotograf> getAnaFotograf() {
        return this.anaFotograf;
    }

    public void setAnaFotograf(final Set<EserProposalFotograf> anaFotograf) {
        this.anaFotograf = anaFotograf;
    }

    public MuesPick getKondisyonDurumu() {
        return this.kondisyonDurumu;
    }

    public void setKondisyonDurumu(final MuesPick kondisyonDurumu) {
        this.kondisyonDurumu = kondisyonDurumu;
    }

    @Override
    public String getEserId() {
        if (this.getId() == null) {
            return "---";
        }
        return "Ö.E." + this.getId();
    }

    public Set<EserProposalMeasure> getEserMeasures() {
        return this.eserMeasures;
    }

    public void setEserMeasures(final Set<EserProposalMeasure> eserMeasures) {
        this.eserMeasures = eserMeasures;
    }

    public Set<EserProposalCizim> getEserCizims() {
        return this.eserCizims;
    }

    public void setEserCizims(final Set<EserProposalCizim> eserCizims) {
        this.eserCizims = eserCizims;
    }

    public Set<EserProposalTranscription> getTranscriptions() {
        return this.transcriptions;
    }

    public void setTranscriptions(final Set<EserProposalTranscription> transcriptions) {
        this.transcriptions = transcriptions;
    }

    public Set<EserProposalMalzemeYapimTeknigi> getEserMalzemeYapimTeknigis() {
        return this.eserMalzemeYapimTeknigis;
    }

    public void setEserMalzemeYapimTeknigis(final Set<EserProposalMalzemeYapimTeknigi> eserMalzemeYapimTeknigis) {
        this.eserMalzemeYapimTeknigis = eserMalzemeYapimTeknigis;
    }

    public Set<EserProposalMalzemeSuslemeTeknigi> getEserMalzemeSuslemeTeknigis() {
        return this.eserMalzemeSuslemeTeknigis;
    }

    public void setEserMalzemeSuslemeTeknigis(final Set<EserProposalMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigis) {
        this.eserMalzemeSuslemeTeknigis = eserMalzemeSuslemeTeknigis;
    }

    public Set<EserProposalStil> getEserStils() {
        return this.eserStils;
    }

    public void setEserStils(final Set<EserProposalStil> eserStils) {
        this.eserStils = eserStils;
    }

    public Set<EserProposalAtolye> getEserAtolyes() {
        return this.eserAtolyes;
    }

    public void setEserAtolyes(final Set<EserProposalAtolye> eserAtolyes) {
        this.eserAtolyes = eserAtolyes;
    }

    public Set<EserProposalKaynakLiteratur> getEserKaynakLiteraturs() {
        return this.eserKaynakLiteraturs;
    }

    public void setEserKaynakLiteraturs(final Set<EserProposalKaynakLiteratur> eserKaynakLiteraturs) {
        this.eserKaynakLiteraturs = eserKaynakLiteraturs;
    }

    public Set<EserProposalYayinLiteratur> getEserYayinLiteraturs() {
        return this.eserYayinLiteraturs;
    }

    public void setEserYayinLiteraturs(final Set<EserProposalYayinLiteratur> eserYayinLiteraturs) {
        this.eserYayinLiteraturs = eserYayinLiteraturs;
    }

    public Set<EserProposalSerh> getEserSerhs() {
        return this.eserSerhs;
    }

    public void setEserSerhs(final Set<EserProposalSerh> eserSerhs) {
        this.eserSerhs = eserSerhs;
    }

    public Integer getApplicationType() {
        return this.applicationType;
    }

    public void setApplicationType(final Integer applicationType) {
        this.applicationType = applicationType;
    }

    public Set<EserProposalHareket> getEserHarekets() {
        return this.eserHarekets;
    }

    public void setEserHarekets(final Set<EserProposalHareket> eserHarekets) {
        this.eserHarekets = eserHarekets;
    }

    public PersonelView getEserOneriPersonel() {
        return this.eserOneriPersonel;
    }

    public void setEserOneriPersonel(final PersonelView eserOneriPersonel) {
        this.eserOneriPersonel = eserOneriPersonel;
    }

    public String getEserOneriYapanKisiOzet() {
        return this.eserOneriYapanKisiOzet;
    }

    public void setEserOneriYapanKisiOzet(final String eserOneriYapanKisiOzet) {
        this.eserOneriYapanKisiOzet = eserOneriYapanKisiOzet;
    }

    public Researcher getEserOneriResearcher() {
        return this.eserOneriResearcher;
    }

    public void setEserOneriResearcher(final Researcher eserOneriResearcher) {
        this.eserOneriResearcher = eserOneriResearcher;
    }

    public Integer getArtifactId() {
        return this.artifactId;
    }

    public void setArtifactId(final Integer artifactId) {
        this.artifactId = artifactId;
    }

}
