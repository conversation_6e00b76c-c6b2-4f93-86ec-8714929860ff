package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "ALAN_KONUMU_TUR")
@NamedQuery(name = "AlanKonumuTur.findEagerById", query = "SELECT a FROM AlanKonumuTur a WHERE a.id = :id")
@NamedQuery(name = "AlanKonumuTur.findAll", query = "SELECT a FROM AlanKonumuTur a ORDER BY a.silinmis, a.aktif DESC, a.ad")
@NamedQuery(name = "AlanKonumuTur.findActive", query = "SELECT a FROM AlanKonumuTur a WHERE a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedNativeQuery(name = "AlanKonumuTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ALAN_KONUMU WHERE SILINMIS = 0 AND ALAN_KONUMU_TUR_ID = :id) + "
                                                                       + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_Alan_Konumu WHERE SILINMIS = 0 AND ALAN_KONUMU_TUR_ID = :id)")
public class AlanKonumuTur extends AbstractEntity implements DeleteValidatable {

    private static final long      serialVersionUID = -6334882566041353281L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                 ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                 kod;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                 aciklama;

    public AlanKonumuTur() {
    	//blank constructor
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }


    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
