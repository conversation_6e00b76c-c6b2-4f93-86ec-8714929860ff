package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalMalzemeYapimTeknigi;
import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.YapimTeknigi;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class EserProposalMalzemeYapimTeknigiFacade extends AbstractFacade<EserProposalMalzemeYapimTeknigi> {

    public EserProposalMalzemeYapimTeknigiFacade() {
        super(EserProposalMalzemeYapimTeknigi.class);
    }

    public List<YapimTeknigi> filterByNameAndAciklamaAndMalzeme(final String query, final Malzeme malzeme) {
        return this.em.createNamedQuery("YapimTeknigi.findByNameAndAciklamaAndMalzeme", YapimTeknigi.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("malzeme", malzeme)
                      .getResultList();
    }

}
