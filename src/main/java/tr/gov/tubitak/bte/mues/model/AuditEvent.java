package tr.gov.tubitak.bte.mues.model;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.util.MuesException;

public enum AuditEvent {

    Ekleme(0, "Ekleme"),

    <PERSON><PERSON><PERSON><PERSON>(1, "<PERSON><PERSON><PERSON><PERSON><PERSON>"),

    <PERSON><PERSON><PERSON>(2, "<PERSON><PERSON><PERSON>"),

    <PERSON><PERSON><PERSON>kle<PERSON>(3, "<PERSON>ser Ekleme"),

    <PERSON>serGuncelleme(4, "Eser Güncelleme"),

    <PERSON>serSilme(5, "Eser Silme"),

    <PERSON><PERSON><PERSON>G<PERSON>s(6, " <PERSON><PERSON><PERSON> G<PERSON>ş"),

    ConfigurationEkleme(7, "Konfigürasyon Ekleme"),

    ConfigurationGuncelleme(8, "Konfigürasyon Güncelleme"),

    ConfigurationSilme(9, "Konfigürasyon Silme"),

    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(10, "<PERSON><PERSON><PERSON><PERSON><PERSON>"),

    ArastirmaGuncelleme(11, "Araş<PERSON><PERSON>rma Güncelleme"),

    ArastirmaSilme(12, "Araş<PERSON><PERSON><PERSON> Silme"),

    At<PERSON>eEkleme(13, "Atölye Ekleme"),

    <PERSON><PERSON>eGuncelleme(14, "Atölye Güncelleme"),

    AtolyeSilme(15, "Atölye Silme"),

    IzinEkleme(16, "İzin Ekleme"),

    IzinGuncelleme(17, "İzin Güncelleme"),

    IzinSilme(18, "İzin Silme"),

    KeywordEkleme(22, "Anahtar Kelime Ekleme"),

    KeywordGuncelleme(23, "Anahtar Kelime Güncelleme"),

    KeywordSilme(24, "Anahtar Kelime Silme"),

    PersonelEkleme(25, "Personel Ekleme"),

    PersonelGuncelleme(26, "Personel Güncelleme"),

    PersonelViewGuncelleme(26, "Personel Güncelleme"),

    PersonelSilme(27, "Personel Silme"),

    PersonelGorevEkleme(28, "Personel Görev Ekleme"),

    PersonelGorevGuncelleme(29, "Personel Görev Güncelleme"),

    PersonelGorevSilme(30, "Personel Görev Silme"),

    RenkEkleme(31, "Renk Ekleme"),

    RenkGuncelleme(32, "Renk Güncelleme"),

    RenkSilme(33, "Renk Silme"),

    RolEkleme(34, "Rol Ekleme"),

    RolGuncelleme(35, "Rol Güncelleme"),

    RolSilme(36, "Rol Silme"),

    RolIzinEkleme(37, "Rol İzin Ekleme"),

    RolIzinGuncelleme(38, "Rol İzin Güncelleme"),

    RolIzinSilme(39, "Rol İzin Silme"),

    UnvanEkleme(40, "Unvan Ekleme"),

    UnvanGuncelleme(41, "Unvan Güncelleme"),

    UnvanSilme(42, "Unvan Silme"),

    BagliBirimTurEkleme(47, "Bağlı Birim Tür Ekleme"),

    BagliBirimTurGuncelleme(48, "Bağlı Birim Tür Güncelleme"),

    BagliBirimTurSilme(49, "Bağlı Birim Tür Silme"),

    SistemeGirisSSO(50, "Sisteme Giriş SSO"),

    SistemeGirisHata(51, "Sisteme Giriş Hata"),

    SistemeGirisSSOHata(52, "Sisteme Giriş SSO Hata"),

    EPostaGonder(53, "E-Posta Gönderme"),

    EPostaGonderHata(54, "E-Posta Gönderme Hata"),

    EserGoruntuleme(55, "Eser Görüntüleme"),

    EserGoruntulemeHata(56, "Eser Görüntüleme Hata"),

    KaziEkleme(57, "Kazı Ekleme"),

    KaziGuncelleme(58, "Kazı Güncelleme"),

    KaziSilme(59, "Kazı Silme"),

    EserFotografEkleme(60, "Eser Fotoğraf Ekleme"),

    EserFotografGuncelleme(61, "Eser Fotoğraf Güncelleme"),

    EserFotografSilme(62, "Eser Fotoğraf Silme"),

    EserSerhEkleme(63, "Eser Şerh Ekleme"),

    EserSerhGuncelleme(64, "Eser Şerh Güncelleme"),

    EserSerhSilme(65, "Eser Şerh Silme"),

    EserDepoEkleme(66, "Eser Depo Ekleme"),

    EserDepoGuncelleme(67, "Eser Depo Güncelleme"),

    EserDepoSilme(68, "Eser Depo Silme"),

    EserMeasureEkleme(69, "Eser Ölçü Ekleme"),

    EserMeasureGuncelleme(70, "Eser Ölçü Güncelleme"),

    EserMeasureSilme(71, "Eser Ölçü Silme"),

    IliskilendirmeEkleme(72, "Eser İlişkilendirme Ekleme"),

    IliskilendirmeGuncelleme(73, "Eser İlişkilendirme Güncelleme"),

    IliskilendirmeSilme(74, "Eser İlişkilendirme Silme"),

    EserAtolyeEkleme(75, "Eser Atölye Ekleme"),

    EserAtolyeGuncelleme(76, "Eser Atölye Güncelleme"),

    EserAtolyeSilme(77, "Eser Atölye Silme"),

    EserHareketEkleme(78, "Eser Hareket Ekleme"),

    EserHareketGuncelleme(79, "Eser Hareket Güncelleme"),

    EserHareketSilme(80, "Eser Hareket Silme"),

    EserHareketSahisEkleme(81, "Eser Hareket Şahıs Ekleme"),

    EserHareketSahisGuncelleme(82, "Eser Hareket Şahıs Güncelleme"),

    EserHareketSahisSilme(83, "Eser Hareket Şahıs Silme"),

    EserMalzemeSuslemeTeknigiEkleme(84, "Eser Malzeme Süsleme Tekniği Ekleme"),

    EserMalzemeSuslemeTeknigiGuncelleme(85, "Eser Malzeme Süsleme Tekniği Güncelleme"),

    EserMalzemeSuslemeTeknigiSilme(86, "Eser Malzeme Süsleme Tekniği Silme"),

    EserMalzemeYapimTeknigiEkleme(87, "Eser Malzeme Yapım Tekniği Ekleme"),

    EserMalzemeYapimTeknigiGuncelleme(88, "Eser Malzeme Yapım Tekniği Güncelleme"),

    EserMalzemeYapimTeknigiSilme(89, "Eser Malzeme Yapım Tekniği Silme"),

    EserStilEkleme(90, "Eser Stil Ekleme Ekleme"),

    EserStilGuncelleme(91, "Eser Stil Güncelleme"),

    EserStilSilme(92, "Eser Stil Silme"),

    // EserYayinEkleme(93, "Eser Yayın Ekleme"),

    // EserYayinGuncelleme(94, "Eser Yayın Güncelleme"),

    // EserYayinSilme(95, "Eser Yayın Silme"),

    KullaniciEkleme(96, "Kullanıcı Ekleme"),

    KullaniciGuncelleme(97, "Kullanıcı Güncelleme"),

    KullaniciSilme(98, "Kullanıcı Silme"),

    KullaniciBirimRolEkleme(99, "Kullanıcı Birim Rol Ekleme"),

    KullaniciBirimRolGuncelleme(100, "Kullanıcı Birim Rol Güncelleme"),

    KullaniciBirimRolSilme(101, "Kullanıcı Birim Rol Silme"),

    LiteraturEkleme(102, "Literatür Ekleme"),

    LiteraturGuncelleme(103, "Literatür Güncelleme"),

    LiteraturSilme(104, "Literatür Silme"),

    EserKaynakLiteraturEkleme(105, "Eser Kaynak Literatür Ekleme"),

    EserKaynakLiteraturGuncelleme(106, "Eser Kaynak Literatür Güncelleme"),

    EserKaynakLiteraturSilme(107, "Eser Kaynak Literatür Silme"),

    EserYayinLiteraturEkleme(108, "Eser Yayın Literatür Ekleme"),

    EserYayinLiteraturGuncelleme(109, "Eser Yayın Literatür Güncelleme"),

    EserYayinLiteraturSilme(110, "Eser Yayın Literatür Silme"),

    MudurlukEkleme(111, "Müze Müdürlüğü Ekleme"),

    MudurlukGuncelleme(112, "Müze Müdürlüğü Güncelleme"),

    MudurlukSilme(113, "Müze Müdürlüğü Silme"),

    BagliBirimEkleme(114, "Bağlı Birim Ekleme"),

    BagliBirimGuncelleme(115, "Bağlı Birim Güncelleme"),

    BagliBirimSilme(116, "Bağlı Birim Silme"),

    BinaEkleme(117, "Bina Ekleme"),

    BinaGuncelleme(118, "Bina Güncelleme"),

    BinaSilme(119, "Bina Silme"),

    AlanEkleme(120, "Alan Ekleme"),

    AlanGuncelleme(121, "Alan Güncelleme"),

    AlanSilme(122, "Alan Silme"),

    AlanKonumuEkleme(123, "Alan Konumu Ekleme"),

    AlanKonumuGuncelleme(124, "Alan Konumu Güncelleme"),

    AlanKonumuSilme(125, "Alan Konumu Silme"),

    BagliBirimAltTurEkleme(126, "Bağlı Birim Alt Tür Ekleme"),

    BagliBirimAltTurGuncelleme(127, "Bağlı Birim Alt Tür Güncelleme"),

    BagliBirimAltTurSilme(128, "Bağlı Birim Alt Tür Silme"),

    BagliBirimBagliBirimAltTurEkleme(129, "Bağlı Birim Bağlı Birim Alt Tür Ekleme"),

    BagliBirimBagliBirimAltTurGuncelleme(130, "Bağlı Birim Bağlı Birim Alt Tür Güncelleme"),

    BagliBirimBagliBirimAltTurSilme(131, "Bağlı Birim Bağlı Birim Alt Tür Silme"),

    AlanTurEkleme(132, "Alan Tür Ekleme"),

    AlanTurGuncelleme(133, "Alan Tür Güncelleme"),

    AlanTurSilme(134, "Alan Tür Silme"),

    EserKeywordEkleme(135, "Eser Anahtar Kelime Ekleme"),

    EserKeywordGuncelleme(136, "Eser Anahtar Kelime Güncelleme"),

    EserKeywordSilme(137, "Eser Anahtar Kelime Silme"),

    EserZimmetEkleme(138, "Eser Zimmet Ekleme"),

    EserZimmetGuncelleme(139, "Eser Zimmet Güncelleme"),

    EserZimmetSilme(140, "Eser Zimmet Silme"),

    TranscriptionEkleme(141, "Transkripsiyon Ekleme"),

    TranscriptionGuncelleme(142, "Transkripsiyon Güncelleme"),

    TranscriptionSilme(143, "Transkripsiyon Silme"),

    EserBirlestirmeEkleme(144, "Eser Birleştirme Ekleme"),

    EserBirlestirmeGuncelleme(145, "Eser Birleştirme Güncelleme"),

    EserBirlestirmeSilme(146, "Eser Birleştirme Silme"),

    BirlestirmeEkleme(147, "Birleştirme Ekleme"),

    BirlestirmeGuncelleme(148, "Birleştirme Güncelleme"),

    BirlestirmeSilme(149, "Birleştirme Silme"),

    EserCizimEkleme(150, "Eser Çizim Ekleme"),

    EserCizimGuncelleme(151, "Eser Çizim Güncelleme"),

    EserCizimSilme(152, "Eser Çizim Silme"),

    WorkflowEkleme(153, "İş Akışı Ekleme"),

    WorkflowGuncelleme(154, "İş Akışı Güncelleme"),

    WorkflowSilme(155, "İş Akışı Silme"),

    MuzeMudurluguIlEkleme(156, "Müze Müdürlüğü İl Ekleme"),

    MuzeMudurluguIlGuncelleme(157, "Müze Müdürlüğü İl Güncelleme"),

    GeneralInformationEkleme(158, "Müze Bilgi Sistemi Genel Bilgiler Ekleme"),

    TeamListEkleme(159, "Müze Bilgi Sistemi Ekip Ekleme"),

    AllocationFacilityEkleme(160, "Müze Bilgi Sistemi Tahsis Olanakları Ekleme"),

    DisplayInformationEkleme(161, "Müze Bilgi Sistemi Teshir Bilgileri Ekleme"),

    OtherAreasStatusEkleme(163, "Müze Bilgi Sistemi Diğer Alanlar Durumu Ekleme"),

    VisitorInformationEkleme(164, "Müze Bilgi Sistemi Ziyaretçi Ekleme"),

    EquipmentStatusEkleme(165, "Müze Bilgi Sistemi Teçhizat Durumu Ekleme"),

    VehicleStatusEkleme(166, "Müze Bilgi Sistemi Taşıt Durumu Ekleme"),

    SecurityStatusEkleme(167, "Müze Bilgi Sistemi Güvenlik Durumu Ekleme"),

    MuseumExpenseEkleme(168, "Müze Bilgi Sistemi Gider Ekleme"),

    TemporaryExhibitionEkleme(169, "Müze Bilgi Sistemi Geçici Sergi Ekleme"),

    EnergySourceEkleme(170, "Müze Bilgi Sistemi Enerji Kaynakları Ekleme"),

    AreaStatusEkleme(171, "Müze Bilgi Sistemi Depo Durumu Ekleme"),

    BuildingStatusEkleme(172, "Müze Bilgi Sistemi Bina Durumu Ekleme"),

    GeneralInformationSilme(173, "Müze Bilgi Sistemi Genel Bilgiler Silme"),

    TeamListSilme(174, "Müze Bilgi Sistemi Ekip Silme"),

    AllocationFacilitySilme(175, "Müze Bilgi Sistemi Tahsis Olanakları Silme"),

    DisplayInformationSilme(176, "Müze Bilgi Sistemi Teshir Bilgileri Silme"),

    OtherAreasStatusSilme(178, "Müze Bilgi Sistemi Diğer Alanlar Durumu Silme"),

    VisitorInformationSilme(179, "Müze Bilgi Sistemi Ziyaretçi Silme"),

    EquipmentStatusSilme(180, "Müze Bilgi Sistemi Teçhizat Durumu Silme"),

    SecurityStatusSilme(182, "Müze Bilgi Sistemi Güvenlik Durumu Silme"),

    MuseumExpenseSilme(183, "Müze Bilgi Sistemi Gider Silme"),

    TemporaryExhibitionSilme(184, "Müze Bilgi Sistemi Geçici Sergi Silme"),

    EnergySourceSilme(185, "Müze Bilgi Sistemi Enerji Kaynakları Silme"),

    AreaStatusSilme(186, "Müze Bilgi Sistemi Depo Durumu Silme"),

    BuildingStatusSilme(187, "Müze Bilgi Sistemi Bina Durumu Silme"),

    GeneralInformationGuncelleme(188, "Müze Bilgi Sistemi Genel Bilgiler Güncelleme"),

    TeamListGuncelleme(189, "Müze Bilgi Sistemi Ekip Güncelleme"),

    AllocationFacilityGuncelleme(190, "Müze Bilgi Sistemi Tahsis Olanakları Güncelleme"),

    DisplayInformationGuncelleme(191, "Müze Bilgi Sistemi Teshir Bilgileri Güncelleme"),

    OtherAreasStatusGuncelleme(193, "Müze Bilgi Sistemi Diğer Alanlar Durumu Güncelleme"),

    VisitorInformationGuncelleme(194, "Müze Bilgi Sistemi Ziyaretçi Güncelleme"),

    EquipmentStatusGuncelleme(195, "Müze Bilgi Sistemi Teçhizat Durumu Güncelleme"),

    VehicleStatusGuncelleme(196, "Müze Bilgi Sistemi Taşıt Durumu Güncelleme"),

    SecurityStatusGuncelleme(197, "Müze Bilgi Sistemi Güvenlik Durumu Güncelleme"),

    MuseumExpenseGuncelleme(198, "Müze Bilgi Sistemi Gider Güncelleme"),

    TemporaryExhibitionGuncelleme(199, "Müze Bilgi Sistemi Geçici Sergi Güncelleme"),

    EnergySourceGuncelleme(200, "Müze Bilgi Sistemi Enerji Kaynakları Güncelleme"),

    AreaStatusGuncelleme(201, "Müze Bilgi Sistemi Depo Durumu Güncelleme"),

    BuildingStatusGuncelleme(202, "Müze Bilgi Sistemi Bina Durumu Güncelleme"),

    ArtifactMoulageInformationEkleme(203, "Müze Bilgi Sistemi Eser Mulaj Bilgileri Ekleme"),

    ArtifactMoulageInformationSilme(204, "Müze Bilgi Sistemi Eser Mulaj Bilgileri Silme"),

    ArtifactMoulageInformationGuncelleme(205, "Müze Bilgi Sistemi Eser Mulaj Bilgileri Güncelleme"),

    TemporaryExhibitionDocumentEkleme(206, "Müze Bilgi Sistemi Geçici Sergi Doküman Ekleme"),

    TemporaryExhibitionDocumentGuncelleme(207, "Müze Bilgi Sistemi Geçici Sergi Doküman Güncelleme"),

    TemporaryExhibitionDocumentSilme(208, "Müze Bilgi Sistemi Geçici Sergi Doküman Silme"),

    TemporaryExhibitionPhotographEkleme(209, "Müze Bilgi Sistemi Geçici Sergi Fotoğraf Ekleme"),

    TemporaryExhibitionPhotographGuncelleme(210, "Müze Bilgi Sistemi Geçici Sergi Fotoğraf Güncelleme"),

    TemporaryExhibitionPhotographSilme(211, "Müze Bilgi Sistemi Geçici Sergi Fotoğraf Silme"),

    VehicleStatusSilme(212, "Müze Bilgi Sistemi Taşıt Durumu Silme"),

    TemporaryExhibitionGoruntuleme(213, "Müze Bilgi Sistemi Geçici Sergi Görüntüleme"),

    TemporaryExhibitionGoruntulemeHata(214, "Müze Bilgi Sistemi Geçici Sergi Görüntüleme Hata"),

    ActivityEkleme(215, "Müze Bilgi Sistemi Faaliyet Ekleme"),

    ActivitySilme(216, "Müze Bilgi Sistemi Faaliyet Silme"),

    ActivityGuncelleme(217, "Müze Bilgi Sistemi Faaliyet Güncelleme"),

    TemporaryExhibitionPartnerEkleme(218, "Müze Bilgi Sistemi Geçici Sergi Ortağı Ekleme"),

    TemporaryExhibitionPartnerSilme(219, "Müze Bilgi Sistemi Geçici Sergi Ortağı Silme"),

    TemporaryExhibitionPartnerGuncelleme(220, "Müze Bilgi Sistemi Geçici Sergi Ortağı Güncelleme"),

    MuseumPhotographEkleme(221, "Müze Bilgi Sistemi Genel Bilgiler Görsel Ekleme"),

    MuseumPhotographSilme(222, "Müze Bilgi Sistemi Genel Bilgiler Görsel Silme"),

    MuseumPhotographGuncelleme(223, "Müze Bilgi Sistemi Genel Bilgiler Görsel Güncelleme"),

    SponsorVolunteerEkleme(224, "Müze Bilgi Sistemi Genel Bilgiler Sponsor/Gönüllü Ekleme"),

    SponsorVolunteerSilme(225, "Müze Bilgi Sistemi Genel Bilgiler Sponsor/Gönüllü Silme"),

    SponsorVolunteerGuncelleme(226, "Müze Bilgi Sistemi Genel Bilgiler Sponsor/Gönüllü Güncelleme"),

    SocialMediaEkleme(227, "Müze Bilgi Sistemi Genel Bilgiler Sosyal Medya Ekleme"),

    SocialMediaSilme(228, "Müze Bilgi Sistemi Genel Bilgiler Sosyal Medya Silme"),

    SocialMediaGuncelleme(229, "Müze Bilgi Sistemi Genel Bilgiler Sosyal Medya Güncelleme"),

    MuseumSuggestionEkleme(230, "Müze Bilgi Sistemi Genel Bilgiler Öneri Ekleme"),

    MuseumSuggestionSilme(231, "Müze Bilgi Sistemi Genel Bilgiler Öneri Silme"),

    MuseumSuggestionGuncelleme(232, "Müze Bilgi Sistemi Genel Bilgiler Öneri Güncelleme"),

    PersonelUzmanlikAlaniEkleme(233, "Personel Uzmanlık Alanı Ekleme"),

    PersonelUzmanlikAlaniSilme(234, "Personel Uzmanlık Alanı Silme"),

    PersonelUzmanlikAlaniGuncelleme(235, "Personel Uzmanlık Alanı Güncelleme"),

    ResearcherEkleme(236, "Araştırmacı ekleme"),

    ResearcherSilme(237, "Araştırmacı Silme"),

    ResearcherGuncelleme(238, "Araştırmacı Güncelleme"),

    RequestOfResearcherEkleme(239, "Araştırmacı İzni Ekleme"),

    RequestOfResearcherSilme(240, "Araştırmacı İzni Silme"),

    RequestOfResearcherGuncelleme(241, "Araştırmacı İzni Güncelleme"),

    ResearcherRequestArtifactEkleme(242, "Araştırmacı İzni Eser Ekleme"),

    ResearcherRequestArtifactGuncelleme(243, "Araştırmacı İzni Eser Güncelleme"),

    ResearcherRequestArtifactSilme(244, "Araştırmacı İzni Eser Silme"),

    MuseumDocumentEkleme(245, "Müze Bilgi Sistemi Müze Dokümanı Ekleme"),

    MuseumDocumentGuncelleme(246, "Müze Bilgi Sistemi Müze Dokümanı Güncelleme"),

    MuseumDocumentSilme(247, "Müze Bilgi Sistemi Müze Dokümanı Silme"),

    GeneralInformaionGoruntuleme(248, "Müze Bilgi Sistemi Genel Bilgiler Görüntüleme"),

    GeneralInformaionGoruntulemeHata(249, "Müze Bilgi Sistemi Genel Bilgiler Görüntüleme Hata"),

    PersonelYabanciDilEkleme(250, "Personel Yabancı Dil Ekleme"),

    PersonelYabanciDilGuncelleme(251, "Personel Yabancı Dil Güncelleme"),

    PersonelYabanciDilSilme(252, "Personel Yabancı Dil Silme"),

    ArtifactCountingEkleme(253, "Eser Sayım Ekleme"),

    ArtifactCountingGuncelleme(254, "Eser Sayım Güncelleme"),

    ArtifactCountingResultEkleme(255, "Eser Sayım Sonuç Ekleme"),

    ArtifactCountingResultGuncelleme(256, "Eser Sayım Sonuç Güncelleme"),

    ArtifactCountingResultSilme(257, "Eser Sayım Sonuç Silme"),

    OriginalPhotographRequestEkleme(258, "Eser Fotoğraf Başvurusu Ekleme"),

    OriginalPhotographRequestGuncelleme(259, "Eser Fotoğraf Başvurusu Güncellme"),

    OriginalPhotographRequestSilme(260, "Eser Fotoğraf Başvurusu Silme"),

    SuggestionOfResearcherEkleme(261, "Araştırmacı Katkı Ekleme"),

    SuggestionOfResearcherGuncelleme(262, "Araştırmacı Katkı Güncellme"),

    SuggestionOfResearcherSilme(263, "Araştırmacı Katkı Silme"),

    ArtifactPhotoAnnotationEkleme(264, "Eser Foto Yorum Ekleme"),

    ArtifactPhotoAnnotationSilme(265, "Eser Foto Yorum Silme"),

    ArtifactPhotoAnnotationGuncelleme(266, "Eser Foto Yorum Güncelleme"),

    PackEkleme(267, "Müzeler Arası Devir Ekleme"),

    PackSilme(268, "Müzeler Arası Devir Silme"),

    PackGuncelleme(269, "Müzeler Arası Devir Güncelleme"),

    PackArtifactEkleme(270, "Müzeler Arası Devir Eser Ekleme"),

    PackArtifactGuncelleme(271, "Müzeler Arası Devir Eser Güncelleme"),

    PackArtifactSilme(272, "Paket Eseri Silme"),

    SeparationEkleme(273, "Eser Ayırma Ekleme"),

    SeparationGuncelleme(274, "Eser Ayırma Güncelleme"),

    SeparationSilme(275, "Eser Ayırma Silme"),

    SeparationEserEkleme(276, "Ayırma Eser Ekleme"),

    SeparationEserGuncelleme(277, "Ayırma Eser Güncelleme"),

    SeparationEserSilme(278, "Ayırma Eser Silme"),

    EserProposalGoruntuleme(288, "Eser Proposal Görüntüleme"),

    EserProposalGoruntulemeHata(289, "Eser Proposal Görüntüleme Hata"),

    AnnouncementEkleme(290, "Duyuru Ekleme"),

    AnnouncementGuncelleme(291, "Duyuru Güncelleme"),

    AnnouncementSilme(292, "Duyuru Sil"),

    DeliveredItemEkleme(296, "Kültür ve Tabiat Varlığı Ekleme"),

    DeliveredItemGuncelleme(297, "Kültür ve Tabiat Varlığı Güncelleme"),

    DeliveredItemSilme(298, "Kültür ve Tabiat Varlığı Silme"),

    DeliveredItemMeasureEkleme(299, "Kültür ve Tabiat Varlığı Ölçü Ekleme"),

    DeliveredItemMeasureGuncelleme(300, "Kültür ve Tabiat Varlığı Ölçü Güncelleme"),

    DeliveredItemMeasureSilme(301, "Kültür ve Tabiat Varlığı Ölçü Silme"),

    DeliveredItemPhotoEkleme(302, "Kültür ve Tabiat Varlığı Fotoğraf Ekleme"),

    DeliveredItemPhotoGuncelleme(303, "Kültür ve Tabiat Varlığı Fotoğraf Güncelleme"),

    DeliveredItemPhotoSilme(304, "Kültür ve Tabiat Varlığı Fotoğraf Silme"),

    DeliveredItemDocumentEkleme(305, "Kültür ve Tabiat Varlığı Belge Ekleme"),

    DeliveredItemDocumentGuncelleme(306, "Kültür ve Tabiat Varlığı Belge Güncelleme"),

    DeliveredItemDocumentSilme(307, "Kültür ve Tabiat Varlığı Belge Silme"),

    CommissionGuncelleme(309, "Komisyon Güncelleme"),

    CommissionSilme(310, "Komisyon Sil"),

    CommissionEkleme(311, "Komisyon Ekle"),

    CommissionCommentEkleme(312, "Komisyon Yorum Ekleme"),

    CommissionMemberEkleme(313, "Komisyon Üyesi Ekleme"),

    CommissionMemberSilme(314, "Komisyon Üyesi Silme"),

    CommissionMemberGuncelleme(315, "Komisyon Üyesi Güncelleme"),

    TemporaryAdmissionReceiptEkleme(316, "Geçici Alındı Belgesi Ekleme"),

    TemporaryAdmissionReceiptGuncelleme(317, "Geçici Alındı Belgesi Güncelleme"),

    TemporaryAdmissionReceiptSilme(318, "Geçici Alındı Belgesi Silme"),

    TemporaryAdmissionReceiptPhotographEkleme(319, "Geçici Alındı Belgesi Fotoğraf Ekleme"),

    TemporaryAdmissionReceiptPhotographGuncelleme(320, "Geçici Alındı Belgesi Fotoğraf Güncelleme"),

    TemporaryAdmissionReceiptPhotographSilme(321, "Geçici Alındı Belgesi Fotoğraf Silme"),

    TemporaryAdmissionReceiptDocumentEkleme(322, "Geçici Alındı Belgesi Belge Ekleme"),

    TemporaryAdmissionReceiptDocumentGuncelleme(323, "Geçici Alındı Belgesi Belge Güncelleme"),

    TemporaryAdmissionReceiptDocumentSilme(324, "Geçici Alındı Belgesi Belge Silme"),

    TemporaryAdmissionReceiptMediaEkleme(325, "Geçici Alındı Belgesi Media Ekleme"),

    TemporaryAdmissionReceiptMediaGuncelleme(326, "Geçici Alındı Belgesi Media Güncelleme"),

    TemporaryAdmissionReceiptMediaSilme(327, "Geçici Alındı Belgesi Media Silme"),

    TemporaryAdmissionReceiptDelivererPersonnelEkleme(328, "Geçici Alındı Belgesi Teslim Eden Personel Ekleme"),

    TemporaryAdmissionReceiptDelivererPersonnelGuncelleme(329, "Geçici Alındı Belgesi Teslim Eden Personel Güncelleme"),

    TemporaryAdmissionReceiptDelivererPersonnelSilme(330, "Geçici Alındı Belgesi Teslim Eden Personel Silme"),

    TemporaryAdmissionReceiptRecipientPersonnelEkleme(331, "Geçici Alındı Belgesi Teslim Alan Personel Ekleme"),

    TemporaryAdmissionReceiptRecipientPersonnelGuncelleme(332, "Geçici Alındı Belgesi Teslim Alan Personel Güncelleme"),

    TemporaryAdmissionReceiptRecipientPersonnelSilme(333, "Geçici Alındı Belgesi Teslim Alan Personel Silme"),

    TemporaryAdmissionReceiptSahisEkleme(334, "Geçici Alındı Belgesi Şahıs Ekleme"),

    TemporaryAdmissionReceiptSahisGuncelleme(335, "Geçici Alındı Belgesi Şahıs Güncelleme"),

    TemporaryAdmissionReceiptSahisSilme(336, "Geçici Alındı Belgesi Şahıs Silme"),

    TemporaryAdmissionReceiptTuzelKisiEkleme(337, "Geçici Alındı Belgesi Tüzel Kişi Ekleme"),

    TemporaryAdmissionReceiptTuzelKisiGuncelleme(338, "Geçici Alındı Belgesi Tüzel Kişi Güncelleme"),

    TemporaryAdmissionReceiptTuzelKisiSilme(339, "Geçici Alındı Belgesi Tüzel Kişi Silme"),

    TemporaryAdmissionReceiptSerialNumberEkleme(340, "Geçici Alındı Belgesi Seri/Sıra No Ekleme"),

    TemporaryAdmissionReceiptSerialNumberGuncelleme(341, "Geçici Alındı Belgesi Seri/Sıra No Güncelleme"),

    TemporaryAdmissionReceiptSerialNumberSilme(342, "Geçici Alındı Belgesi Seri/Sıra No Silme"),

    HeritageObjectEkleme(360, "Kom. Objesi Ekleme"),

    HeritageObjectGuncelleme(361, "Kom. Objesi Güncelleme"),

    HeritageObjectSilme(362, "Kom. Objesi Silme"),

    ObjectDocumentEkleme(363, "Kom. Objesi Belge Ekleme"),

    ObjectDocumentGuncelleme(364, "Kom. Objesi Belge Güncelleme"),

    ObjectDocumentSilme(365, "Kom. Objesi Belge Silme"),

    ObjectPhotoEkleme(366, "Kom. Objesi Foto Ekleme"),

    ObjectPhotoGuncelleme(367, "Kom. Objesi Foto Güncelleme"),

    ObjectPhotoSilme(368, "Kom. Objesi Foto Silme"),

    ObjectMeasureEkleme(369, "Kom. Objesi Ölçü Ekleme"),

    ObjectMeasureGuncelleme(370, "Kom. Objesi Ölçü Güncelleme"),

    ObjectMeasureSilme(371, "Kom. Objesi Ölçü Silme"),

    CommissionObjectEkleme(372, "Komisyon-Objesi Ekleme"),

    CommissionObjectGuncelleme(373, "Komisyon-Objesi Güncelleme"),

    CommissionObjectSilme(374, "Komisyon-Objesi Silme"),

    CommissionObjectDecisionEkleme(375, "Komisyon-Objesi Karar Ekleme"),

    CommissionObjectDecisionGuncelleme(376, "Komisyon-Objesi Karar Güncelleme"),

    CommissionObjectDecisionSilme(377, "Komisyon-Objesi Karar Silme"),

    CommissionDocumentEkleme(378, "Komisyon Belge Ekleme"),

    CommissionDocumentGuncelleme(379, "Komisyon Belge Güncelleme"),

    CommissionDocumentSilme(380, "Komisyon Belge Silme"),

    CommissionCountingEkleme(381, "Sayım Komisyonu Ekleme"),

    CommissionCountingGuncelleme(382, "Sayım Komisyonu Güncelleme"),

    CommissionCountingSilme(383, "Sayım Komisyonu Silme"),

    CommissionEserEkleme(384, "Komisyon-Eser Ekleme"),

    CommissionEserGuncelleme(385, "Komisyon-Eser Güncelleme"),

    CommissionEserSilme(386, "Komisyon-Eser Silme"),

    CommissionEserDecisionEkleme(387, "Komisyon-Eser Karar Ekleme"),

    CommissionEserDecisionGuncelleme(388, "Komisyon-Eser Karar Güncelleme"),

    CommissionEserDecisionSilme(389, "Komisyon-Eser Karar Silme"),

    CommissionMuseumAccountingEkleme(390, "Komisyon Müze Muhasebe Ekleme"),

    CommissionMuseumAccountingGuncelleme(391, "Komisyon Müze Muhasebe Güncelleme"),

    CommissionMuseumAccountingSilme(392, "Komisyon Müze Muhasebe Silme"),

    GeneralDirectorateReviewCommissionEkleme(393, "Genel Müdürlük inceleme Komisyon Ekleme"),

    GeneralDirectorateReviewCommissionGuncelleme(394, "Genel Müdürlük inceleme Komisyon Güncelleme"),

    CommissionSerhEkleme(395, "Komisyon Şerh Ekleme"),

    CommissionSerhGüncelleme(396, "Komisyon Şerh Güncelleme"),

    CommissionSerhSilme(397, "Komisyon Şerh Silme"),

    InspectionCommitteeEkleme(413, "Teftiş Kurulu Ekleme"),

    InspectionCommitteeGuncelleme(414, "Teftiş Kurulu Güncelleme"),

    InspectionCommitteeSilme(415, "Teftiş Kurulu Silme"),

    InspectionCommitteePersonnelEkleme(416, "Teftiş Kurulu Personel Ekleme"),

    InspectionCommitteePersonnelGuncelleme(417, "Teftiş Kurulu Personel Güncelleme"),

    InspectionCommitteePersonnelSilme(418, "Teftiş Kurulu Personel Silme"),

    InspectionCommitteeArtifactEkleme(419, "Teftiş Kurulu Eser Ekleme"),

    InspectionCommitteeArtifactGuncelleme(420, "Teftiş Kurulu Eser Güncelleme"),

    InspectionCommitteeArtifactSilme(421, "Teftiş Kurulu Eser Silme"),

    DisplayInformationPhotographEkleme(422, "Teşhir Bilgisi Fotoğraf Ekleme"),

    DisplayInformationPhotographGüncelleme(423, "Teşhir Bilgisi Fotoğraf Güncelleme"),

    DisplayInformationPhotographSilme(424, "Teşhir Bilgisi Fotoğraf Silme"),

    GeneralDataProtectionRegulationEkleme(425, "KVKK Metni Ekleme"),

    GeneralDataProtectionRegulationGuncelleme(426, "KVKK Metni Güncelle"),

    GeneralDataProtectionRegulationSilme(427, "KVKK Metni Silme"),

    BudgetUnitEkleme(428, "Bütçe Birimi Ekleme"),

    BudgetUnitGüncelleme(429, "Bütçe Birimi Güncelleme"),

    BudgetUnitSilme(430, "Bütçe Birimi Silme"),

    KVKKOnaylama(434, "KVKK Onaylama"),

    TemplateFormManagementEkleme(438, "Matbu Form Ekleme"),

    TemplateFormManagementGuncelleme(439, "Matbu Form Güncelleme"),

    TemplateFormManagementSilme(440, "Matbu Form Silme"),

    PersonelGoruntuleme(441, "Personel görüntüleme"),

    CommissionRemarkEkleme(442, "Komisyon Şerh Ekleme"),

    CommissionRemarkGuncelleme(443, "Komisyon Şerh Güncelleme"),

    CommissionRemarkSilme(444, "Komisyon Şerh Silme"),

    ActivityDocumentEkleme(445, "Faaliyet Ekleme"),

    ActivityDocumentGuncelleme(446, "Faaliyet Güncelleme"),

    ActivityDocumentSilme(447, "Faaliyet Silme"),

    CommissionUpdateApprovalDocumentEkleme(448, "Güncelleme Onay Belgesi Ekleme"),

    CommissionUpdateApprovalDocumentGuncelleme(449, "Güncelleme Onay Belgesi Güncelleme"),

    CommissionUpdateApprovalDocumentSilme(450, "Güncelleme Onay Belgesi Silme"),

    EserKopyalama(451, "Eser Kopyalama"),

    EserMediaEkleme(452, "Eser Media Ekleme"),

    EserMediaGuncelleme(453, "Eser Media Güncelleme"),

    EserMediaSilme(454, "Eser Media Silme"),

    SolrLastIndexTimesEkleme(455, "Solr Last Index Time Ekleme"),

    SolrLastIndexTimesGuncelleme(457, "Solr Last Index Time Güncelleme"),

    SolrLastIndexTimesSilme(458, "Solr Last Index Time Silme"),

    EserGroupZimmetOnayGonderme(459, "Toplu Eser Zimmet Devir Onaya Gönderme"),

    EserGroupZimmetShareOnayGonderme(460, "Toplu Eser Zimmet Paylaşımı Onaya Gönderme"),

    LIABILITY_CHANGE(461, "Çoklu Zimmet Devir İşlemi"),

    LIABILITY_SHARE(462, "Çoklu Zimmet Paylaşım İşlemi"),

    LIABILITY_DROP(463, "Çoklu Zimmetten Düşme İşlemi"),

    LIABILITY_CHANGE_REQUEST_APPROVED(464, "Çoklu Zimmet Onayladı"),

    LIABILITY_CHANGE_REQUEST_REJECTED(465, "Çoklu Zimmet Reddedildi"),

    MUSEUM_TRANSFER_PENDING_APPROVED(466, "Müzeler Arası Devir - Devreden Müzede Onaylandı"),

    MUSEUM_TRANSFER_RECEIVER_APPROVED(467, "Müzeler Arası Devir - Devralan Müzede Onaylandı"),

    BuildingStatusDocumentEkleme(468, "Bina Durum Belgesi Ekleme"),

    BuildingStatusDocumentGuncelleme(469, "Bina Durum Belgesi Güncelleme"),

    BuildingStatusDocumentSilme(470, "Bina Durum Belgesi Silme"),

    MonogramEkleme(471, "Monogram Ekleme"),

    MonogramGuncelleme(472, "Monogram Güncelleme"),

    MonogramSilme(473, "Monogram Silme"),

    MUSEUM_TRANSFER_PENDING_REJECTED(474, "Müzeler Arası Devir - Devreden Müze Reddetti"),

    MUSEUM_TRANSFER_RECEIVER_REJECTED(475, "Müzeler Arası Devir - Devralan Müze Reddetti"),;

    private static final Logger                   logger   = LoggerFactory.getLogger(AuditEvent.class);

    private static final Map<Integer, AuditEvent> CODE_MAP = new HashMap<>();
    private static final Map<String, AuditEvent>  NAME_MAP = new HashMap<>();

    private final Integer                         code;

    private final String                          label;

    private AuditEvent(final Integer code, final String label) {
        this.code = code;
        this.label = label;
    }

    public static AuditEvent parseByCode(final int number) {

        final AuditEvent event = AuditEvent.CODE_MAP.get(number);

        if (event != null) {
            return event;
        }

        AuditEvent.logger.error("[AuditEvent.parse] : Hata : {} kodu ile hic AuditEvent tipi bulunamadi", number);
        return null;
    }

    public static AuditEvent parseByName(final String str) {
        final AuditEvent event = AuditEvent.NAME_MAP.get(str);
        if (event != null) {
            return event;
        }
        AuditEvent.logger.error("[AuditEvent.parseByName] : Hata : {} ismi ile hic AuditEvent tipi bulunamadi", str);
        throw new MuesException("Unhandled Event Log: " + str);
    }

    // getters ................................................................

    public static String[] getEventNames() {
        return AuditEvent.NAME_MAP.keySet().toArray(new String[0]);
    }

    public Integer getCode() {
        return this.code;
    }

    public String getLabel() {
        return this.label;
    }

    static {
        for (final AuditEvent event : AuditEvent.values()) {

            AuditEvent.CODE_MAP.put(event.getCode(), event);

            if (AuditEvent.NAME_MAP.containsKey(event.name())) {
                AuditEvent.logger.error("Duplicate name detected in AuditEvent enum: Name {} is assigned to {} and {}", event.name(), AuditEvent.NAME_MAP.get(event.name()).name(), event.name());
                throw new IllegalStateException("Duplicate name in AuditEvent enum: " + event.name());
            }

            AuditEvent.NAME_MAP.put(event.name(), event);
        }
    }

}
