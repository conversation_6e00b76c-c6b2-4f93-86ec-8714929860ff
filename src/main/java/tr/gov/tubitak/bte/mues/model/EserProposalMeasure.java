package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

@Entity
@Table(name = "EP_Eser_Measure")
@NamedQuery(name = "EserProposalMeasure.findEagerById", query = "SELECT x FROM EserProposalMeasure x LEFT JOIN FETCH x.measure m LEFT JOIN FETCH m.type WHERE x.id = :id")
@NamedQuery(name = "EserProposalMeasure.findAll", query = "SELECT x FROM EserProposalMeasure x")
@NamedQuery(name = "EserProposalMeasure.findActive", query = "SELECT x FROM EserProposalMeasure x WHERE x.aktif = true AND x.silinmis = false")
public class EserProposalMeasure extends EserMeasureSuper {

    private static final long serialVersionUID = -6747721462524302089L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalMeasure() {
        // default constructor
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
