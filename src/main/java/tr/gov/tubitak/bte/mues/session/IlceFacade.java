package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.Ilce;

/**
 *
*
 */
@RequestScoped
public class IlceFacade extends AbstractFacade<Ilce> {

    public IlceFacade() {
        super(Ilce.class);
    }

    public List<Ilce> filterByNameAndIl(final String query, final Il il) {
        return this.em.createNamedQuery("Ilce.findByNameAndIl", Ilce.class)
                      .setParameter("ad", "%" + query + "%")
                      .setParameter("il", il)
                      .getResultList();
    }

}
