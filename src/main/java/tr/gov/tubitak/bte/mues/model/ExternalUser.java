package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "ExternalUser")
@NamedQuery(name = "ExternalUser.findEagerById", query = "SELECT u FROM ExternalUser u LEFT JOIN FETCH u.institution where u.id = :id")
@NamedQuery(name = "ExternalUser.findAll", query = "SELECT u FROM ExternalUser u LEFT JOIN FETCH u.institution ORDER BY u.silinmis, u.aktif DESC, u.id DESC")
@NamedQuery(name = "ExternalUser.findByTCNo", query = "SELECT e FROM ExternalUser e WHERE e.tcIdentityNo = :tcNo")
@NamedQuery(name = "ExternalUser.findActive", query = "SELECT u FROM ExternalUser u LEFT JOIN FETCH u.institution where u.aktif = true and u.silinmis = false ORDER BY u.silinmis, u.aktif DESC, u.id DESC")
public class ExternalUser extends ExternalUserSuper {

    private static final long serialVersionUID = -1450954668803950143L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "institutionId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Institution       institution;

    public ExternalUser() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Institution getInstitution() {
        return this.institution;
    }

    public void setInstitution(final Institution institution) {
        this.institution = institution;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.getAd()).orElse("" + this.getId());
    }

}
