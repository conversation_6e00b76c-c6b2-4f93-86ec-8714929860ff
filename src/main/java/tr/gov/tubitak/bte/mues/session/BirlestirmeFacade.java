package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Birlestirme;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.Mudurluk;

/**
 *
 * 
 */
@RequestScoped
public class BirlestirmeFacade extends AbstractFacade<Birlestirme> {

    private static final long serialVersionUID = -9014554596155588616L;

    public BirlestirmeFacade() {
        super(Birlestirme.class);
    }

    public List<Birlestirme> findByMudurluk(final List<Mudurluk> muzeMudurluks) {
        return this.em.createNamedQuery("Birlestirme.findByMudurluk", Birlestirme.class).setParameter("muzeler", muzeMudurluks).getResultList();
    }

    public List<Birlestirme> findEagerByEser(final Eser eser) {
        return this.em.createNamedQuery("Birlestirme.findEagerByEser", Birlestirme.class).setParameter("eser", eser).getResultList();
    }

    public List<Eser> filterByNameAndMudurluk(final String query, final List<Mudurluk> muzeMudurluks, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("Eser.findByNameAndMudurlukForJoin", Eser.class)
                      .setParameter("str", "%" + query + "%")
                      .setParameter("muzeler", muzeMudurluks)
                      .setParameter("ids", excludedIds)
                      .getResultList();
    }

}
