package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.MuzeMudurluguIl;
import tr.gov.tubitak.bte.mues.session.MuzeMudurluguIlFacade;

@Named
@ViewScoped
public class MuzeMudurluguIlController extends AbstractController<MuzeMudurluguIl> {

    private static final long     serialVersionUID = -8920677339741916613L;

    @Inject
    private MuzeMudurluguIlFacade facade;

    public MuzeMudurluguIlController() {
        super(MuzeMudurluguIl.class);
    }

    @Override
    public void setModel(final MuzeMudurluguIl item) {
        super.setModel(item);
    }

    @Override
    public void newRecord() {
        super.newRecord();
    }

    public void handleTurGrubuSelection() {
        this.getModel().setIl(null);
    }

    // getters and setters ....................................................

    @Override
    public MuzeMudurluguIlFacade getFacade() {
        return this.facade;
    }

}
