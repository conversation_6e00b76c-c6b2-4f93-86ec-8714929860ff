package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.TasinirMalYonetmeligiKod;
import tr.gov.tubitak.bte.mues.session.TasinirMalYonetmeligiKodFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class TasinirMalYonetmeligiKodController extends AbstractController<TasinirMalYonetmeligiKod> implements SingleFileUploadable {

    private static final long              serialVersionUID = 4819590514956229370L;

    @Inject
    private TasinirMalYonetmeligiKodFacade facade;

    @Inject
    private FileUploadHelper               fileUploadHelper;

    public TasinirMalYonetmeligiKodController() {
        super(TasinirMalYonetmeligiKod.class);
    }
    
    public List<TasinirMalYonetmeligiKod> getItemsForArtifact() {
        return super.getItems().stream().filter(x -> x.getApplicationType() == null).collect(Collectors.toList());
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
    }

    public List<TasinirMalYonetmeligiKod> filterByNameCodeAndAciklama(final String query) {
        return this.facade.findByNameCodeAndAciklama(query).stream().filter(x -> x.getApplicationType() == null).collect(Collectors.toList());

    }
    
    // getters and setters ....................................................

    @Override
    public TasinirMalYonetmeligiKodFacade getFacade() {
        return this.facade;
    }

}
