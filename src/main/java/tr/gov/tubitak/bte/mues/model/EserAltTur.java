package tr.gov.tubitak.bte.mues.model;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Entity
@Table(name = "ESER_ALT_TUR")
@NamedQuery(name = "EserAltTur.findEagerById", query = "SELECT e FROM EserAltTur e LEFT JOIN FETCH e.eserTur WHERE e.id = :id")
@NamedQuery(name = "EserAltTur.findAll", query = "SELECT e FROM EserAltTur e LEFT JOIN FETCH e.eserTur ORDER BY e.silinmis, e.aktif DESC, e.ad")
@NamedQuery(name = "EserAltTur.findActive", query = "SELECT e FROM EserAltTur e LEFT JOIN FETCH e.eserTur WHERE e.aktif = true AND e.silinmis = false ORDER BY e.ad")
@NamedQuery(name = "EserAltTur.findByName", query = "SELECT e FROM EserAltTur e LEFT JOIN FETCH e.eserTur ee WHERE e.aktif = true AND e.silinmis = false AND e.ad LIKE :ad ORDER BY ee.ad, e.ad")
@NamedQuery(name = "EserAltTur.findByNameAndAciklamaAndUstTur", query = "SELECT a FROM EserAltTur a LEFT JOIN FETCH a.eserTur t WHERE a.aktif = true AND a.silinmis = false AND (a.ad LIKE :str OR a.aciklama LIKE :str OR t.ad LIKE :str) ORDER BY t.ad, a.ad")
@NamedNativeQuery(name = "EserAltTur.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER WHERE SILINMIS = 0 AND ESER_ALT_TUR_ID = :id)")
public class EserAltTur extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 4033224398087422928L;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "ESER_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserTur           eserTur;

    public EserAltTur() {
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public EserTur getEserTur() {
        return this.eserTur;
    }

    public void setEserTur(final EserTur eserTur) {
        this.eserTur = eserTur;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Stream.of(this.eserTur.getAd(), this.ad).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" > "));
    }

}
