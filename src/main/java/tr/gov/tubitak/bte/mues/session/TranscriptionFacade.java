package tr.gov.tubitak.bte.mues.session;


import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Transcription;

 
@RequestScoped
public class TranscriptionFacade extends AbstractFacade<Transcription> {

    public TranscriptionFacade() {
        super(Transcription.class);
    }

	public List<Transcription> findByEserAndTranscriptionInfo(String query) {
		return this.em.createNamedQuery("Transcription.findByEserIdAndTranscription", Transcription.class).setParameter("str", "%" + query + "%").getResultList();
	}

     

}
