package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.AlanTur;
import tr.gov.tubitak.bte.mues.model.BagliBirim;
import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.session.AlanFacade;
import tr.gov.tubitak.bte.mues.session.AlanKonumuFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class AlanController extends AbstractController<Alan> implements SingleFileUploadable {

    private static final long serialVersionUID = 6840732350727683982L;

    @Inject
    private AlanFacade        facade;

    @Inject
    private FileUploadHelper  fileUploadHelper;

    @Inject
    private AlanKonumuFacade  alanKonumuFacade;

    @Inject
    private SessionBean       sessionBean;

    private Mudurluk          mudurluk;

    private BagliBirim        birim;

    private Bina              bina;

    private String            dwgFileToDelete;

    public AlanController() {
        super(Alan.class);
    }

    @PostConstruct
    private void init() {
        this.mudurluk = this.sessionBean.filterByPermission("alan:guncelle");
        this.dwgFileToDelete = "";
    }

    @Override
    public DBOperationResult update() {
        final DBOperationResult result = super.update();
        if ((this.getDwgFileToDelete() != null) && !this.getDwgFileToDelete().isBlank() && result.isSuccess()) {
            this.fileUploadHelper.deleteDwgFilePermanently(this.dwgFileToDelete);
        }
        return result;
    }

    @Override
    public void showDetail(final Alan item) {
        super.showDetail(item);
        this.birim = this.getModel().getBina().getBagliBirim();
        this.mudurluk = this.getModel().getBina().getBagliBirim().getMudurluk();
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.birim = null;
        this.getModel().setBina(null);
        this.mudurluk = this.sessionBean.filterByPermission("alan:guncelle");
        this.changeUnitIfOnlyOne();
    }

    @Override
    public void toggleActive(final Alan alan) {
        this.facade.toggleActiveSelfAndDescendants(alan);
        this.setModel(alan);
        this.getModel().setAktif(!this.getModel().getAktif());
    }

    public void handleMudurlukChange(final SelectEvent<Mudurluk> event) {
        this.setMudurluk(event.getObject());
        this.setBirim(null);
        this.changeUnitIfOnlyOne();
        this.getModel().setBina(null);
        this.getModel().setAlanTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);
    }

    public void handleBirimChange(final SelectEvent<BagliBirim> event) {
        this.setBirim(event.getObject());
        this.getModel().setBina(null);
        this.getModel().setAlanTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);
    }

    public void handleBinaChange(final SelectEvent<Bina> event) {
        this.getModel().setBina(event.getObject());
        this.getModel().setAlanTur(null);
        this.getModel().setKod(null);
        this.getModel().setAd(null);
        this.getModel().setAciklama(null);
    }

    public void handleAlanTurChange(final SelectEvent<AlanTur> event) {
        this.getModel().setAlanTur(event.getObject());
        this.getModel().setKod(this.getModel().getAlanTur().getKod());
        this.getModel().setAd(this.getModel().getAlanTur().getAd());
        this.getModel().setAciklama(null);
    }

    // Sets unit by mudurluk if only one
    public void changeUnitIfOnlyOne() {
        if (this.mudurluk != null) {
            final List<BagliBirim> bagliBirimList = this.filterByNameAndMudurluk("");
            this.setBirim(null);
            if (bagliBirimList.size() == 1) {
                this.setBirim(bagliBirimList.iterator().next());
            }
        }
    }

    public List<BagliBirim> filterByNameAndMudurluk(final String query) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndMudurluk(query, this.mudurluk);
    }

    public List<Bina> filterByNameAndBagliBirim(final String value) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndBagliBirim(value, this.birim);
    }

    public List<Alan> filterByNameAndBina(final String query) {
        return this.alanKonumuFacade.findByNameAndAciklamaAndBina(query, this.bina);
    }

    public void uploadDocumentToTempFolder(final FileUploadEvent event) {
        this.getModel().setPdfPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDwgDocumentToTempFolder(final FileUploadEvent event) {
        if ((this.getModel().getDwgPath() != null) && !this.getModel().getDwgPath().isBlank()) {
            this.setDwgFileToDelete(this.getModel().getDwgPath());
        }
        this.getModel().setDwgPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void deleteDwgFileFromModel() {
        this.setDwgFileToDelete(this.getModel().getDwgPath());
        this.getModel().setDwgPath(null);
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getPdfPath() != null) {
            this.getModel().setPdfPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getPdfPath(), FolderType.OTHER));
        }
        if (this.getModel().getDwgPath() != null) {
            this.getModel().setDwgPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDwgPath(), FolderType.DWG));
        }
    }

    // getters and setters ....................................................

    @Override
    public AlanFacade getFacade() {
        return this.facade;
    }

    @Override
    public List<Alan> getItems() {
        if (this.items == null) {
            final List<Mudurluk> mudurluguListByPermissionName = this.sessionBean.fetchMudurlukListByPermission("alan:listele");

            if (mudurluguListByPermissionName != null) {
                this.items = this.getFacade().findByMudurluk(mudurluguListByPermissionName);
            }
        }
        return this.items;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public BagliBirim getBirim() {
        return this.birim;
    }

    public void setBirim(final BagliBirim birim) {
        this.birim = birim;
    }

    public Bina getBina() {
        return this.bina;
    }

    public void setBina(final Bina bina) {
        this.bina = bina;
    }

    public String getDwgFileToDelete() {
        return this.dwgFileToDelete;
    }

    public void setDwgFileToDelete(final String dwgFile) {
        this.dwgFileToDelete = dwgFile;
    }
}
