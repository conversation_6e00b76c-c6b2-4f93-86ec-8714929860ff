package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.constraint.Email;
import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.TCKN;
import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "Sahis.findEagerById", query = "SELECT s FROM Sahis s LEFT JOIN FETCH s.il LEFT JOIN FETCH s.ilce LEFT JOIN FETCH s.createdBy WHERE s.id = :id")
@NamedQuery(name = "Sahis.findAll", query = "SELECT s FROM Sahis s LEFT JOIN FETCH s.il LEFT JOIN FETCH s.ilce LEFT JOIN FETCH s.createdBy ORDER BY s.silinmis, s.aktif DESC, s.ad, s.soyad")
@NamedQuery(name = "Sahis.findActive", query = "SELECT s FROM Sahis s LEFT JOIN FETCH s.il LEFT JOIN FETCH s.ilce WHERE s.aktif = true AND s.silinmis = false ORDER BY s.ad, s.soyad")
@NamedQuery(name = "Sahis.findByTCNo", query = "SELECT s FROM Sahis s WHERE s.tcKimlikNo = :tcNo")
@NamedQuery(name = "Sahis.findSilinmemisByName", query = "SELECT s FROM Sahis s WHERE s.silinmis = false AND (s.ad LIKE :str OR s.soyad LIKE :str) ORDER BY s.ad")
@NamedQuery(name = "Sahis.findByFullNameAndAciklama", query = "SELECT s FROM Sahis s WHERE s.aktif = true AND s.silinmis = false AND (REPLACE(concat(s.ad, s.soyad), ' ', '') LIKE :str OR s.aciklama LIKE :str) ORDER BY s.ad, s.soyad")
@NamedQuery(name = "Sahis.filterByFullNameAndAciklamaPreventDuplicate", query = "SELECT s FROM Sahis s LEFT JOIN FETCH s.il LEFT JOIN FETCH s.ilce WHERE s.id NOT IN :ids AND s.aktif = true AND s.silinmis = false AND (REPLACE(concat(s.ad, s.soyad), ' ', '') LIKE :str OR s.aciklama LIKE :str) ORDER BY s.ad, s.soyad")

public class SahisSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 2450526640902372096L;

    @TCKN
    @Size(max = 11)
    @Column(name = "TC_KIMLIK_NO", length = 11)
    private String            tcKimlikNo;

    @Size(max = 20)
    @Column(name = "PASAPORT_NO", length = 20)
    private String            pasaportNo;

    @Size(max = 11)
    @Column(name = "YABANCI_KIMLIK_NO", length = 11)
    private String            yabanciKimlikNo;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @ValidName
    @Size(max = 50)
    @Column(name = "SOYAD", length = 50)
    private String            soyad;

    @Size(max = 50)
    @Column(name = "DOGUM_YERI", length = 50)
    private String            dogumYeri;

    @Column(name = "DOGUM_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              dogumTarihi;

    @Size(max = 24)
    @Column(name = "IBAN_NO", length = 24)
    private String            ibanNo;

    @Column(name = "MESLEK_ID")
    private Integer           meslekId;

    @Size(max = 150)
    @Column(name = "ADRES", length = 150)
    private String            adres;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_IS", length = 25)
    private String            telefonIs;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_EV", length = 25)
    private String            telefonEv;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_CEP", length = 25)
    private String            telefonCep;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_CEP2", length = 25)
    private String            telefonCep2;

    @Email
    @Size(max = 50)
    @Column(name = "EPOSTA_KURUMSAL", length = 50)
    private String            epostaKurumsal;

    @Email
    @Size(max = 150)
    @Column(name = "EPOSTA_KISISEL", length = 150)
    private String            epostaKisisel;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "FOTOGRAF_PATH", length = 250)
    private String            fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_YURT_DISI", length = 25)
    private String            telefonYurtDisi;

    @JoinColumn(name = "PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView      createdBy;

    @NotAudited
    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                il;

    @NotAudited
    @JoinColumn(name = "ILCE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Ilce              ilce;

    public SahisSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getTcKimlikNo() {
        return this.tcKimlikNo;
    }

    public void setTcKimlikNo(final String tcKimlikNo) {
        this.tcKimlikNo = tcKimlikNo;
    }

    public String getPasaportNo() {
        return this.pasaportNo;
    }

    public void setPasaportNo(final String pasaportNo) {
        this.pasaportNo = pasaportNo;
    }

    public String getYabanciKimlikNo() {
        return this.yabanciKimlikNo;
    }

    public void setYabanciKimlikNo(final String yabanciKimlikNo) {
        this.yabanciKimlikNo = yabanciKimlikNo;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getSoyad() {
        return this.soyad;
    }

    public void setSoyad(final String soyad) {
        this.soyad = soyad;
    }

    public String getDogumYeri() {
        return this.dogumYeri;
    }

    public void setDogumYeri(final String dogumYeri) {
        this.dogumYeri = dogumYeri;
    }

    public Date getDogumTarihi() {
        return this.dogumTarihi;
    }

    public void setDogumTarihi(final Date dogumTarihi) {
        this.dogumTarihi = dogumTarihi;
    }

    public String getIbanNo() {
        return this.ibanNo;
    }

    public void setIbanNo(final String ibanNo) {
        this.ibanNo = ibanNo;
    }

    public Integer getMeslekId() {
        return this.meslekId;
    }

    public void setMeslekId(final Integer meslekId) {
        this.meslekId = meslekId;
    }

    public String getAdres() {
        return this.adres;
    }

    public void setAdres(final String adres) {
        this.adres = adres;
    }

    public String getTelefonIs() {
        return this.telefonIs;
    }

    public void setTelefonIs(final String telefonIs) {
        this.telefonIs = telefonIs;
    }

    public String getTelefonEv() {
        return this.telefonEv;
    }

    public void setTelefonEv(final String telefonEv) {
        this.telefonEv = telefonEv;
    }

    public String getTelefonCep() {
        return this.telefonCep;
    }

    public void setTelefonCep(final String telefonCep) {
        this.telefonCep = telefonCep;
    }

    public String getTelefonCep2() {
        return this.telefonCep2;
    }

    public void setTelefonCep2(final String telefonCep2) {
        this.telefonCep2 = telefonCep2;
    }

    public String getEpostaKurumsal() {
        return this.epostaKurumsal;
    }

    public void setEpostaKurumsal(final String epostaKurumsal) {
        if (epostaKurumsal != null) {
            this.epostaKurumsal = epostaKurumsal.toLowerCase();
        }
    }

    public String getEpostaKisisel() {
        return this.epostaKisisel;
    }

    public void setEpostaKisisel(final String epostaKisisel) {
        if (epostaKisisel != null) {
            this.epostaKisisel = epostaKisisel.toLowerCase();
        }
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getTelefonYurtDisi() {
        return this.telefonYurtDisi;
    }

    public void setTelefonYurtDisi(final String telefonYurtDisi) {
        this.telefonYurtDisi = telefonYurtDisi;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public Ilce getIlce() {
        return this.ilce;
    }

    public void setIlce(final Ilce ilce) {
        this.ilce = ilce;
    }

    public PersonelView getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(final PersonelView createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.ad, this.soyad);
    }

}
