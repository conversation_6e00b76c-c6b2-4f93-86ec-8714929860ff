package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "ESER_MALZEME_GRUBU")
@NamedQuery(name = "MalzemeGrubu.findEagerById", query = "SELECT m FROM MalzemeGrubu m WHERE m.id = :id")
@NamedQuery(name = "MalzemeGrubu.findAll", query = "SELECT m FROM MalzemeGrubu m ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "MalzemeGrubu.findActive", query = "SELECT m FROM MalzemeGrubu m WHERE m.aktif = true AND m.silinmis = false ORDER BY m.ad")
@NamedNativeQuery(name = "MalzemeGrubu.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME WHERE SILINMIS = 0 AND MALZEME_GRUBU_ID = :id)")
public class MalzemeGrubu extends AbstractEntity implements DeleteValidatable {

    private static final long   serialVersionUID = 1674390506500008611L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String              ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String              deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String              aciklama;

    @OneToMany(mappedBy = "grup")
    private Collection<Malzeme> malzemeCollection;

    public MalzemeGrubu() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<Malzeme> getMalzemeCollection() {
        return this.malzemeCollection;
    }

    public void setMalzemeCollection(final Collection<Malzeme> malzemeCollection) {
        this.malzemeCollection = malzemeCollection;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
