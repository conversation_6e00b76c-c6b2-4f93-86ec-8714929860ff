package tr.gov.tubitak.bte.mues.search;

import javax.persistence.Entity;
import javax.persistence.Table;

import tr.gov.tubitak.bte.mues.model.Metadata;

@Entity
@Table(name = "SearchDefinitionColumns")
public class SearchDefinitionColumns extends SearchDefinitionColumnsSuper {

    private static final long serialVersionUID = 1691480360993126153L;

    public SearchDefinitionColumns() {
    }

    public SearchDefinitionColumns(final Metadata metadata) {
        super(metadata);
    }

}
