package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Kazi;

@RequestScoped
public class KaziFacade extends AbstractFacade<Kazi> {

    public KaziFacade() {
        super(Kazi.class);
    }

    public List<Kazi> findByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Kazi.findByNameAndAciklama", Kazi.class).setParameter("str", "%" + query + "%").getResultList();
    }
}
