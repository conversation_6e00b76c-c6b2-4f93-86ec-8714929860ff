package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Meslek;
import tr.gov.tubitak.bte.mues.session.MeslekFacade;

@Named
@ViewScoped
public class MeslekController extends AbstractController<Meslek> {

    private static final long serialVersionUID = 7812190227989766349L;

    @Inject
    private MeslekFacade      facade;

    public MeslekController() {
        super(Meslek.class);
    }

    @Override
    public MeslekFacade getFacade() {
        return this.facade;
    }

}
