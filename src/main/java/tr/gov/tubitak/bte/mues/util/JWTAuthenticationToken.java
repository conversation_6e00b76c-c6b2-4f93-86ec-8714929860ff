package tr.gov.tubitak.bte.mues.util;

import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.RememberMeAuthenticationToken;

public class JWTAuthenticationToken implements AuthenticationToken, RememberMeAuthenticationToken {

    private static final long serialVersionUID = 1L;

    private Object            userId;

    private String            token;

    public JWTAuthenticationToken(final Object userId, final String token) {
        this.userId = userId;
        this.token = token;
    }

    @Override
    public Object getPrincipal() {
        return this.getUserId();
    }

    @Override
    public Object getCredentials() {
        return this.getToken();
    }

    @Override
    public boolean isRememberMe() {
        return true;
    }

    public Object getUserId() {
        return this.userId;
    }

    public void setUserId(final long userId) {
        this.userId = userId;
    }

    public String getToken() {
        return this.token;
    }

    public void setToken(final String token) {
        this.token = token;
    }

}