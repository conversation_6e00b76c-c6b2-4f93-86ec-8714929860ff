package tr.gov.tubitak.bte.mues.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "RolIzin.findAll", query = "SELECT x FROM RolIzin x LEFT JOIN FETCH x.izin LEFT JOIN FETCH x.rol")
public class RolIzinSuper implements Serializable {

    private static final long serialVersionUID = -4370667765545873137L;

    @EmbeddedId
    protected RolIzinPK       rolIzinPK;

    @Size(max = 50)
    @Column(name = "ACIKLAMA", length = 50)
    private String            aciklama;

    @JoinColumn(name = "IZIN_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Izin              izin;

    @JoinColumn(name = "ROL_ID", referencedColumnName = "ID", insertable = false, updatable = false)
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Rol               rol;

    public RolIzinSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public RolIzinSuper(final RolIzinPK rolIzinPK) {
        this.rolIzinPK = rolIzinPK;
    }

    public RolIzinSuper(final int rolId, final int izinId) {
        this.rolIzinPK = new RolIzinPK(rolId, izinId);
    }

    public RolIzinPK getRolIzinPK() {
        return this.rolIzinPK;
    }

    public void setRolIzinPK(final RolIzinPK rolIzinPK) {
        this.rolIzinPK = rolIzinPK;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Izin getIzin() {
        return this.izin;
    }

    public void setIzin(final Izin izin) {
        this.izin = izin;
    }

    public Rol getRol() {
        return this.rol;
    }

    public void setRol(final Rol rol) {
        this.rol = rol;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (this.rolIzinPK != null ? this.rolIzinPK.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(final Object object) {
        if (!(object instanceof RolIzinSuper)) {
            return false;
        }
        final RolIzinSuper other = (RolIzinSuper) object;
        if (((this.rolIzinPK == null) && (other.rolIzinPK != null)) || ((this.rolIzinPK != null) && !this.rolIzinPK.equals(other.rolIzinPK))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return this.rolIzinPK.toString();
    }

}
