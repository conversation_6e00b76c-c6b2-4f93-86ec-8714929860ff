package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.OriginalPhotographRequest;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class OriginalPhotographRequestFacade extends AbstractFacade<OriginalPhotographRequest> {

    public OriginalPhotographRequestFacade() {
        super(OriginalPhotographRequest.class);
    }

    public List<OriginalPhotographRequest> findByResearcher(final Integer researcher) {
        return this.getEM().createNamedQuery("OriginalPhotographRequest.findByResearcher", OriginalPhotographRequest.class).setParameter("researcher", researcher).getResultList();
    }

    public List<OriginalPhotographRequest> findByMudurluk(final List<Mudurluk> mudurlukList) {
        return this.getEM().createNamedQuery("OriginalPhotographRequest.findByMudurluk", OriginalPhotographRequest.class).setParameter("mudurlukList", mudurlukList).getResultList();
    }

    public Integer countOfApprovedPhotoRequest(final Integer researcher, final String path) {
        final String qry = " SELECT COUNT(ID) FROM OriginalPhotographRequest opr WHERE opr.state=:state and opr.researcherRequestId in (Select ID From RequestOfResearcher WHERE researcherId =:researcher ) "
                           + " and opr.photoId in (SELECT ID from ESER_FOTOGRAF where FOTOGRAF_PATH =:path) ";
        return (Integer) this.getEM().createNativeQuery(qry).setParameter("researcher", researcher).setParameter("path", path).setParameter("state", 1).getSingleResult();
    }
}
