package tr.gov.tubitak.bte.mues.session;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import javax.enterprise.context.RequestScoped;
import javax.persistence.EntityManager;
import javax.persistence.EntityNotFoundException;
import javax.persistence.ManyToOne;
import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.PersistenceContext;
import javax.persistence.criteria.JoinType;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

import org.hibernate.Hibernate;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.RevisionType;
import org.hibernate.envers.exception.AuditException;
import org.hibernate.envers.exception.NotAuditedException;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQueryCreator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.util.audits.Audit;

@RequestScoped
public class AuditedEntityRepositoryImpl {

    @PersistenceContext
    private EntityManager  em;

    protected final Logger logger = LoggerFactory.getLogger(AuditedEntityRepositoryImpl.class);

    protected AuditReader getAuditReader() {
        return AuditReaderFactory.get(this.em);
    }

    protected AuditQueryCreator getAuditQuery() {
        return this.getAuditReader().createQuery();
    }

    @Transactional(value = TxType.REQUIRED)
    public <T extends AbstractEntity> T showDetailHistory(final Object[] item) {

        final AuditReader reader = this.getAuditReader();

        final T find = (T) reader.find(item[0].getClass(), ((Eser) item[0]).getId(), ((Audit) item[1]).getId());

        this.traverseLazyFields2(find, item[0].getClass(), 0);

        return find;

    }

    // second trial temp code.
    @Transactional(value = TxType.REQUIRED)
    public <T extends AbstractEntity> T showDetailHistory2(final Object[] item) {

        final AuditReader reader = this.getAuditReader();

        final List<Eser> hasil = reader.createQuery()
                                       .forEntitiesAtRevision(item[0].getClass(), ((Audit) item[1]).getId())
                                       .traverseRelation("updatedBy", JoinType.LEFT, "p")
                                       .traverseRelation("deletedBy", JoinType.LEFT, "de")
                                       .traverseRelation("createdBy", JoinType.LEFT, "d")
                                       .traverseRelation("onaylayanKullanici", JoinType.LEFT, "c")
                                       // .forRevisionsOfEntity(this.getEntityClass(), true, true)
                                       .add(AuditEntity.revisionProperty("id").eq(((Audit) item[1]).getId()))

                                       .add(AuditEntity.id().eq(((Audit) item[1]).getId()))
                                       .add(AuditEntity.revisionProperty("id").eq(((Audit) item[1]).getId()))
                                       .addOrder(AuditEntity.revisionProperty("timestamp").desc())

                                       .setMaxResults(1)
                                       .getResultList();

        this.traverseLazyFields2(hasil.get(0), hasil.get(0).getClass(), 0);

        return null;

    }

    @Transactional(value = TxType.REQUIRED)
    public AbstractEntity traverseLazyFields2(final AbstractEntity entity, final Class<?> class1, final int derinlik) {

        final List<Field> allFields = getAllFields(new LinkedList<>(), class1);

        for (final Field field : allFields) {

            final OneToOne oneToOne = field.getAnnotation(OneToOne.class);
            final ManyToOne manyToOne = field.getAnnotation(ManyToOne.class);
            final OneToMany oneToMany = field.getAnnotation(OneToMany.class);

            try {

                if (((oneToOne != null) || (manyToOne != null) || (oneToMany != null))) {
                    final Method m = new PropertyDescriptor(field.getName(), class1).getReadMethod();

                    final Object invoke = m.invoke(entity);

                    Hibernate.initialize(invoke);

                    if (invoke instanceof Set) {
                        final Set<?> aa = ((Set<?>) invoke);

                        for (final Iterator<?> iterator = aa.iterator(); iterator.hasNext();) {
                            final Object object = iterator.next();

                            if ((object instanceof AbstractEntity) && (derinlik <= 3)) {
                                this.traverseLazyFields2((AbstractEntity) object, object.getClass(), derinlik + 1);
                                object.toString();
                            }
                        }
                    } else if ((invoke instanceof AbstractEntity) && (derinlik <= 3)) {

                        this.traverseLazyFields2((AbstractEntity) invoke, invoke.getClass(), derinlik + 1);
                        invoke.toString();
                    }

                }
            } catch (final EntityNotFoundException | IntrospectionException | IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                this.logger.error("[findEagerByIdViaReflection] : Hata : {}", class1 + " _ " + field.getName());
            }
        }

        return entity;

    }

    public static List<Field> getAllFields(final List<Field> fields, final Class<?> type) {
        fields.addAll(Arrays.asList(type.getDeclaredFields()));

        if (type.getSuperclass() != null) {
            getAllFields(fields, type.getSuperclass());
        }

        return fields;
    }

    @Transactional()
    public <T extends AbstractEntity> Optional<T> findAuditedEntity(final Class<T> entityClass, final Object entityId, final Long revisionNumber) {
        Object auditedEntity = null;
        try {
            auditedEntity = this.getAuditQuery().forEntitiesModifiedAtRevision(entityClass, revisionNumber).add(AuditEntity.id().eq(entityId)).getSingleResult();
        } catch (AuditException | NonUniqueResultException | NoResultException ex) {
            this.logger.warn(String.format("Not found audited entity... revisionNumber : %s, entityClass : %s, entityId : %s", revisionNumber, entityClass, entityId), ex);
        } catch (final Exception ex) {
            throw new RuntimeException(String.format("Unexpected exception... revisionNumber : %s, entityClass : %s, entityId : %s", revisionNumber, entityClass, entityId), ex);
        } finally {
            return Optional.ofNullable(entityClass.cast(auditedEntity));
        }
    }

    @Transactional()
    public <T extends AbstractEntity> Optional<T> findAuditedEntity(final Class<T> entityClass, final Object entityId, final Long revisionNumber, final RevisionType revisionType) {
        Object auditedEntity = null;
        try {
            auditedEntity = this.getAuditQuery()
                                .forEntitiesModifiedAtRevision(entityClass, revisionNumber)
                                .add(AuditEntity.id().eq(entityId))
                                .add(AuditEntity.revisionType().eq(revisionType))
                                .getSingleResult();
        } catch (AuditException | NonUniqueResultException | NoResultException ex) {
            this.logger.warn(String.format("Not found audited entity... revisionNumber : %s, entityClass : %s, entityId : %s", revisionNumber, entityClass, entityId), ex);
        } catch (final Exception ex) {
            throw new RuntimeException(String.format("Unexpected exception... revisionNumber : %s, entityClass : %s, entityId : %s", revisionNumber, entityClass, entityId), ex);
        } finally {
            return Optional.ofNullable(entityClass.cast(auditedEntity));
        }
    }

    @Transactional()
    public <T extends AbstractEntity> Optional<T> findPreAuditedEntity(final Class<T> entityClass, final Object entityId, final Long revisionNumber) {
        T auditedEntity = null;
        try {
            auditedEntity = this.getAuditReader().find(entityClass, entityId, revisionNumber - 1);
        } catch (IllegalArgumentException | NotAuditedException | IllegalStateException ex) {
            final String errorMessage = String.format("Not found audited entity... revisionNumber : %s, entityClass : %s, entityId : %s", revisionNumber, entityClass, entityId);
            this.logger.warn(errorMessage, ex);
        } catch (final Exception ex) {
            final String errorMessage = String.format("Unexpected exception... revisionNumber : %s, entityClass : %s, entityId : %s", revisionNumber, entityClass, entityId);
            this.logger.error(errorMessage, ex);
            throw new RuntimeException(errorMessage, ex);
        } finally {
            return Optional.ofNullable(auditedEntity);
        }
    }
}