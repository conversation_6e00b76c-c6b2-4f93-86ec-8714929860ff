package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Ulke;
import tr.gov.tubitak.bte.mues.session.UlkeFacade;

@Named
@ViewScoped
public class UlkeController extends AbstractController<Ulke> {

    private static final long serialVersionUID = -44894647704198227L;

    @Inject
    private UlkeFacade        facade;

    public UlkeController() {
        super(Ulke.class);
    }

    @Override
    public UlkeFacade getFacade() {
        return this.facade;
    }

}
