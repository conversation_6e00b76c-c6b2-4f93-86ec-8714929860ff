package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.MuzeMudurluk;
import tr.gov.tubitak.bte.mues.session.MuzeMudurlukFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

@Named
@ViewScoped
public class MuzeMudurlukController extends AbstractController<MuzeMudurluk> {

    private static final long  serialVersionUID = 1687581587267109998L;

    @Inject
    private MuzeMudurlukFacade facade;

    @Inject
    private IlceController     ilceController;

    private List<String>       selectedMudurluks;

    public MuzeMudurlukController() {
        super(MuzeMudurluk.class);
    }

    public void onIlSelected(final SelectEvent<Il> event) {
        this.ilceController.setIl(event.getObject());
        this.getModel().setIlce(null);
    }

    @Override
    public List<MuzeMudurluk> filterByName(final String query) {
        return this.facade.filterByName(query);
    }

    public List<MuzeMudurluk> filterByNameAndEmptyLab(final String query) {
        return this.facade.filterByNameAndEmptyLab(query);
    }

    public List<MuzeMudurluk> findActiveMuseums() {
        return this.facade.findActive();
    }

    /***
     * Muze mudurlugu icin laboratuvari set ederek update islemi yapar
     */
    @Override
    public DBOperationResult create() {
        // detached entity hatasi icin muzeMudurluk tekrar cekildi
        final MuzeMudurluk muzeMudurluk = this.facade.findById(this.getModel().getId());
        muzeMudurluk.setLaboratoryDirectorate(this.getModel().getLaboratoryDirectorate());
        this.setModel(muzeMudurluk);
        final DBOperationResult result = this.update();
        this.setItems(null);
        return result;
    }

    public DBOperationResult detachLabDirectorateFromMuseumDirectorate(final MuzeMudurluk muzeMudurlugu) {
        this.setModel(this.facade.findEagerById(muzeMudurlugu.getId()));
        final MuzeMudurluk muzeMudurluk = this.getModel();
        muzeMudurluk.setLaboratoryDirectorate(null);
        this.setModel(muzeMudurluk);
        final DBOperationResult result = this.update();
        this.setItems(null);
        return result;
    }

    // getters and setters ....................................................

    @Override
    public MuzeMudurlukFacade getFacade() {
        return this.facade;
    }

    public List<String> getSelectedMudurluks() {
        return this.selectedMudurluks;
    }

    public void setSelectedMudurluks(final List<String> selectedMudurluks) {
        this.selectedMudurluks = selectedMudurluks;
    }
}