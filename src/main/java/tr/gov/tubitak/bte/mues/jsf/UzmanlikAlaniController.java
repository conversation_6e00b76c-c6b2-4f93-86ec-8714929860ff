package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.UzmanlikAlani;
import tr.gov.tubitak.bte.mues.session.UzmanlikAlaniFacade;

@Named
@ViewScoped
public class UzmanlikAlaniController extends AbstractController<UzmanlikAlani> {

    private static final long   serialVersionUID = -2042580873446260285L;

    @Inject
    private UzmanlikAlaniFacade facade;

    public UzmanlikAlaniController() {
        super(UzmanlikAlani.class);
    }

    @Override
    public UzmanlikAlaniFacade getFacade() {
        return this.facade;
    }

}
