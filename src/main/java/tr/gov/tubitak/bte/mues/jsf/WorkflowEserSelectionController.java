package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.EserVersion;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Transcription;
import tr.gov.tubitak.bte.mues.model.mapping.WorkflowEserView;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyWorkflowEserDataModel;
import tr.gov.tubitak.bte.mues.search.SearchConstants;
import tr.gov.tubitak.bte.mues.session.WorkflowEserViewLazyLoadFacade;

@Named
@ViewScoped
public class WorkflowEserSelectionController implements Serializable {

    private static final long                     serialVersionUID = -6556196882598791067L;

    @Inject
    private SessionBean                           sessionBean;

    @Inject
    private WorkflowEserViewLazyLoadFacade        facade;

    @Inject
    EserDepoController                            eserDepoController;

    private Eser                                  artifact;

    private transient DataModel<WorkflowEserView> lazyWorkflowEserDataModel;

    private transient String                      removeIds;

    private transient Mudurluk                    mudurluk;

    private transient String                      eskiEnvanterNo;

    private List<WorkflowEserView>                selectionList;
    
    
    final StringBuilder condition = new StringBuilder();

    public WorkflowEserSelectionController() {

        // blank constructor
    }

    public void handleChange(final SelectEvent<Transcription> event) {
    	 Transcription transcription = event.getObject();
    	 condition.setLength(0);
    	 
	    		condition.append("o.eserTranskripsiyonMonogramPath = '"+ transcription.getMonogram().getFotografPath()+"'" );
	    	
    }
    
    public void loadDataTable() {
        final String customQuery = this.constructCustomConditions();
        this.facade.setCustomQuery(customQuery);
        this.lazyWorkflowEserDataModel = new LazyWorkflowEserDataModel(this.facade);
    }

    // lab vb. moduller icin
    public void loadByMudurluk(final List<Mudurluk> mudurlukList) {
        final StringBuilder condition = new StringBuilder();
        condition.append("o.reviewId = ").append(EserVersion.APPROVED.getCode()).append(" ");
        condition.append(SearchConstants.AND);
        condition.append("o.mudurlukId in ").append(getMudurlukInExp(mudurlukList));
        this.facade.setCustomQuery(condition.toString());
        this.lazyWorkflowEserDataModel = new LazyWorkflowEserDataModel(this.facade);
    }

	    public void loadDataTableMonogramArtifacts() {
	    	if(condition.length()==0) {
	    		
	    		condition.append("o.eserTranskripsiyonMonogramPath IS NOT NULL ");
	    	}
	    	
	    	this.facade.setCustomQuery(condition.toString());
	    	this.lazyWorkflowEserDataModel = new LazyWorkflowEserDataModel(this.facade);
	    }
	    
	    
    	public void loadDataTableTemporaryArtifacts() {
        
    	if (this.getMudurluk() != null) {
            this.eserDepoController.setMudurluk(this.getMudurluk());
        }
        
    	final StringBuilder condition = new StringBuilder();
        condition.append("o.mudurlukId = ").append(this.eserDepoController.getMudurluk().getId()).append(" ");
        condition.append(SearchConstants.AND);
        if (this.eserDepoController.getBirim() != null) {
            condition.append("o.bagliBirimId = ").append(this.eserDepoController.getBirim().getId()).append(" ");
            condition.append(SearchConstants.AND);
        }
        if (this.eserDepoController.getBina() != null) {
            condition.append("o.binaId = ").append(this.eserDepoController.getBina().getId()).append(" ");
            condition.append(SearchConstants.AND);
        }
        if (this.eserDepoController.getAlan() != null) {
            condition.append("o.alanId = ").append(this.eserDepoController.getAlan().getId()).append(" ");
            condition.append(SearchConstants.AND);
        }
        if (this.eserDepoController.getModel().getAlanKonumu() != null) {
            condition.append("o.alanKonumuId = ").append(this.eserDepoController.getModel().getAlanKonumu().getId()).append(" ");
            condition.append(SearchConstants.AND);
        }
        if (this.getEskiEnvanterNo() != null) {
            condition.append("o.eserEnvanterNo = ").append("'" + this.getEskiEnvanterNo() + "'").append(" ");
            condition.append(SearchConstants.AND);
        }
        if (this.getRemoveIds() != null) {
            condition.append(this.getRemoveIds());
            condition.append(SearchConstants.AND);
        }
        condition.delete(condition.length() - 4, condition.length() - 1);

        this.facade.setCustomQuery(condition.toString());
        this.lazyWorkflowEserDataModel = new LazyWorkflowEserDataModel(this.facade);
    }

    // for specific muesumDirectorate
    public String constructCustomConditions(final Integer muesumDirectorateId) {
        final StringBuilder queryStr = new StringBuilder();

        queryStr.append("o.mudurlukId = ").append(muesumDirectorateId.toString());
        queryStr.append(" ");

        return queryStr.toString();
    }

    public static String getMudurlukInExp(final List<Mudurluk> list) {
        if (!list.isEmpty()) {
            final StringBuilder stringBuilder = new StringBuilder("(");
            list.stream().forEach(x -> stringBuilder.append(x.getId()).append(","));
            stringBuilder.deleteCharAt(stringBuilder.lastIndexOf(","));
            stringBuilder.append(")");
            return stringBuilder.toString();
        }
        return "(0)";
    }

    public String constructCustomConditions() {
        final StringBuilder queryStr = new StringBuilder();

        queryStr.append("o.mudurlukId in ").append(getMudurlukInExp(this.sessionBean.fetchByPermission("eser:ekle")));
        queryStr.append(SearchConstants.AND);
        queryStr.append("o.reviewId = ").append(EserVersion.APPROVED.getCode());
        queryStr.append(" ");

        return queryStr.toString();
    }

    public void resetSearchFields() {
        this.eserDepoController.newRecord();
        this.setLazyWorkflowEserDataModel(null);
        this.setEskiEnvanterNo(null);
    }

    // finders ................................................................

    public Eser getArtifact() {
        return this.artifact;
    }

    public void setArtifact(final Eser artifact) {
        this.artifact = artifact;
    }

    public void setTabIndex(final int tabIndex) {
        this.sessionBean.setTabIndex(tabIndex);
    }

    public DataModel<WorkflowEserView> getLazyWorkflowEserDataModel() {
        return this.lazyWorkflowEserDataModel;
    }

    public void setLazyWorkflowEserDataModel(final DataModel<WorkflowEserView> lazyWorkflowEserDataModel) {
        this.lazyWorkflowEserDataModel = lazyWorkflowEserDataModel;
    }

    public String getRemoveIds() {
        return this.removeIds;
    }

    public void setRemoveIds(final String removeIds) {
        this.removeIds = removeIds;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public String getEskiEnvanterNo() {
        return this.eskiEnvanterNo;
    }

    public void setEskiEnvanterNo(final String eskiEnvanterNo) {
        this.eskiEnvanterNo = eskiEnvanterNo;
    }

    public List<WorkflowEserView> getSelectionList() {
        if (this.selectionList == null) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;
    }

    public void setSelectionList(final List<WorkflowEserView> selectionList) {
        this.selectionList = selectionList;
    }

}
