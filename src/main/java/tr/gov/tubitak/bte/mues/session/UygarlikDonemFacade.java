package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.persistence.NoResultException;

import tr.gov.tubitak.bte.mues.model.Donem;
import tr.gov.tubitak.bte.mues.model.Uygarlik;
import tr.gov.tubitak.bte.mues.model.UygarlikDonem;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class UygarlikDonemFacade extends AbstractFacade<UygarlikDonem> {

    public UygarlikDonemFacade() {
        super(UygarlikDonem.class);
    }

    public List<Uygarlik> filterByNameAndDonem(final String value, final Donem donem) {
        return this.em.createNamedQuery("UygarlikDonem.findByNameAndDonem", Uygarlik.class).setParameter("ad", "%" + value + "%").setParameter("donem", donem).getResultList();
    }

    public List<UygarlikDonem> findByDonemAndUygarlik(final Donem donem, final Uygarlik uygarlik) {
        return this.em.createNamedQuery("UygarlikDonem.findByDonemAndUygarlik", UygarlikDonem.class).setParameter("donem", donem).setParameter("uygarlik", uygarlik).getResultList();
    }

    public UygarlikDonem findByDonem(final Donem donem) {
        try {
            return this.em.createNamedQuery("UygarlikDonem.findByDonem", UygarlikDonem.class).setParameter("donem", donem).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }

    public List<UygarlikDonem> findByUygarlik(final Uygarlik uygarlik) {
        return this.em.createNamedQuery("UygarlikDonem.findByUygarlik", UygarlikDonem.class).setParameter("uygarlik", uygarlik).getResultList();
    }

    public List<UygarlikDonem> findAllUygarlik() {
        return this.em.createNamedQuery("UygarlikDonem.findAllUygarlik", UygarlikDonem.class).getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> findDonemTermDatesByCagId(final Integer cagId) {
        final String qry = "  SELECT MIN(ud.DONEM_BASLANGIC_YIL) as 'Min', MAX(ud.DONEM_BITIS_YIL) as 'Max', ud.DONEM_ID FROM UYGARLIK_DONEM ud LEFT JOIN Donem d ON d.ID = ud.DONEM_ID LEFT JOIN Cag c ON c.ID = d.cagId WHERE c.ID = :cagId GROUP BY ud.DONEM_ID ; ";
        return this.getEM().createNativeQuery(qry).setParameter("cagId", cagId).getResultList();
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> findAllDonemWithDates() {
        final String qry = " SELECT DISTINCT d.ID, d.AD, c.ID AS CagID, MIN(ud.DONEM_BASLANGIC_YIL) AS termStart, MAX(ud.DONEM_BITIS_YIL) AS termEnd, d.fotografPath FROM UYGARLIK_DONEM ud "
                           + "LEFT JOIN Donem d ON ud.DONEM_ID = d.ID "
                           + "LEFT JOIN Cag c ON d.cagId = c.ID "
                           + "GROUP BY d.ID, d.AD, c.ID, d.fotografPath; ";
        return this.getEM().createNativeQuery(qry).getResultList();
    }

}
