package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

@Audited
@Entity
@Table(name = "PickGroup")
@NamedNativeQuery(name = "PickGroup.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM Pick WHERE SILINMIS = 0 AND grup = :id)")
public class PickGroup extends PickGroupSuper {

    private static final long serialVersionUID = 3125557658469369629L;

    public PickGroup() {
        // blank constructor
    }

}
