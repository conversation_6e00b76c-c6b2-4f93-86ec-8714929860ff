package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.BagliBirimBagliBirimAltTur;
import tr.gov.tubitak.bte.mues.session.BagliBirimBagliBirimAltTurFacade;

@Named
@ViewScoped
public class BagliBirimBagliBirimAltTurController extends AbstractController<BagliBirimBagliBirimAltTur> {

    private static final long                serialVersionUID = -8920677339741916613L;

    @Inject
    private BagliBirimBagliBirimAltTurFacade facade;

    @Inject
    private BagliBirimController             bagliBirimController;

    public BagliBirimBagliBirimAltTurController() {
        super(BagliBirimBagliBirimAltTur.class);
    }

    @Override
    public void setModel(final BagliBirimBagliBirimAltTur item) {
        super.setModel(item);
        this.bagliBirimController.setBagliBirimTur(this.getModel().getBagliBirimAltTur().getBagliBirimTur());
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.getModel().setBagliBirim(this.bagliBirimController.getModel());
        this.bagliBirimController.setBagliBirimTur(null);
    }

    public void handleTurGrubuSelection() {
        this.getModel().setBagliBirimAltTur(null);
    }

    // getters and setters ....................................................

    @Override
    public BagliBirimBagliBirimAltTurFacade getFacade() {
        return this.facade;
    }

}
