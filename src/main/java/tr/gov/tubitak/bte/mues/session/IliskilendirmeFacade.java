package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.Iliskilendirme;
import tr.gov.tubitak.bte.mues.model.IliskilendirmeTur;
import tr.gov.tubitak.bte.mues.model.IliskilendirmeTurGrubu;
import tr.gov.tubitak.bte.mues.model.Mudurluk;

/**
 *
 * 
 */
@RequestScoped
public class IliskilendirmeFacade extends AbstractFacade<Iliskilendirme> {

    private static final long serialVersionUID = -7723721365503087248L;

    public IliskilendirmeFacade() {
        super(Iliskilendirme.class);
    }

    public List<Iliskilendirme> filterByNameOrInventoryNo(final String query) {
        return this.em.createNamedQuery("Iliskilendirme.findByNameOrInventoryNo", Iliskilendirme.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<IliskilendirmeTur> findByNameAndAciklamaAndTurGrubu(final String query, final IliskilendirmeTurGrubu turGrubu) {
        return this.em.createNamedQuery("IliskilendirmeTur.findByNameAndAciklamaAndTur", IliskilendirmeTur.class)
                      .setParameter("group", turGrubu)
                      .setParameter("str", "%" + query + "%")
                      .getResultList();
    }

    public List<Iliskilendirme> findByMudurluk(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("Iliskilendirme.findByMudurluk", Iliskilendirme.class).setParameter("muzeler", muzelist).getResultList();
    }
    
    public List<Iliskilendirme> findEagerByEser(final Eser eser) {
        return this.em.createNamedQuery("Iliskilendirme.findEagerByEser", Iliskilendirme.class).setParameter("eser", eser).getResultList();
    }

}
