package tr.gov.tubitak.bte.mues.util;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import javax.enterprise.context.RequestScoped;
import javax.inject.Named;

/**
 * Ta<PERSON>h ve zaman operasyonları için statik methodları içerir.
 *
 */
@Named
@RequestScoped
public final class DateUtil {

    private static DateFormat       ddMMyyyyFormat   = new SimpleDateFormat("dd.MM.yyyy");

    private static DateFormat       dd_MM_yyyyFormat = new SimpleDateFormat("dd-MM-yyyy");

    private static DateFormat       yyyyMMddFormat   = new SimpleDateFormat("yyyy-MM-dd");

    private static SimpleDateFormat outputFormat     = new SimpleDateFormat("yyyy MMMM", new Locale("tr", "TR"));

    /**
     * Instantiates a new date util.
     */
    private DateUtil() {
    }

    /**
     * * Verilen Date nesnesini saat bazında sıfırlar. Ör: gelen date 19.11.2012 23:45:12 ise 19.11.2012 00:00:00 olarak döndürür. DateTime alanlar
     * için kullanılabilir.
     *
     * @param date the date
     * @return the start date
     */
    public static synchronized Date getStartDate(Date date) {
        if (date == null) {
            final Calendar instance = Calendar.getInstance();
            instance.set(Calendar.YEAR, 2000);
            instance.set(Calendar.MONTH, 0);
            instance.set(Calendar.DAY_OF_MONTH, 1);
            date = instance.getTime();

        }
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMinimum(Calendar.MILLISECOND));
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen Date nesnesini saat bazında maximize eder. Ör: gelen date 19.11.2012 13:45:12 ise 19.11.2012 23:59:59 olarak döndürür. DateTime
     * alanlar için kullanılabilir.
     *
     * @param date the date
     * @return the end date
     */
    public static synchronized Date getEndDate(Date date) {
        if (date == null) {
            date = Calendar.getInstance().getTime();
        }
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));
        return (Date) calendar.getTime().clone();
    }

    /***
     * Verilen date icin saat dakika saniye gibi bilgileri sifirlar
     * 
     * @param date
     * @return trimmed date
     */
    public static Date trimDate(final Date date) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesini gün.ay.yıl şeklinde formatlar
     *
     * @param date the date
     * @return date string değeri
     */
    public static synchronized String getDateDDMMYYYY(final Date date) {
        return ddMMyyyyFormat.format(date);
    }

    public static synchronized String getDateTurkishMonthName(final Date date) {

        return outputFormat.format(date);
    }

    /**
     * * Verilen gün.ay.yıl şeklinde formatlanmış katarı date olarak döndürür
     *
     * @param dateStr the dateStr
     * @return date java.util.Date
     */
    public static synchronized Date getDateDDMMYYYYString(final String dateStr) {
        if (dateStr == null) {
            return null;
        }
        try {
            return ddMMyyyyFormat.parse(dateStr);
        } catch (final ParseException e) {
            System.err.println(e.getMessage());
            return null;
        }
    }

    /**
     * * Verilen gün-ay-yıl şeklinde formatlanmış katarı date olarak döndürür
     *
     * @param dateStr the dateStr
     * @return date java.util.Date
     */
    public static synchronized Date getDateDD_MM_YYYYString(final String dateStr) {
        if (dateStr == null) {
            return null;
        }
        try {
            return dd_MM_yyyyFormat.parse(dateStr);
        } catch (final ParseException e) {
            System.err.println(e.getMessage());
            return null;
        }
    }

    /**
     * * zamanı döner
     *
     * @return date değeri
     */
    public static synchronized Date getCurrentTime() {
        final Calendar cal = Calendar.getInstance();
        return cal.getTime();

    }

    /**
     * * Verilen date nesnesini yıl.ay.gün şeklinde formatlar
     *
     * @param date the date
     * @return date string değeri
     */
    public static synchronized String getDateYYYYMMDD(final Date date) {
        return yyyyMMddFormat.format(date);
    }

    public static String getDateYYYYMMDDHHMMSS(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * * Verilen date nesnesini verilen formata göre formatlar.
     *
     * @param date the date
     * @param format (dd.MM.yyyy gibi)
     * @return the date with format
     */
    public static String getDateWithFormat(final Object date, final String format) {
        if (date instanceof Date) {
            final DateFormat df = new SimpleDateFormat(format);
            return df.format(date);
        }
        return "";
    }

    /**
     * Şu anki zamanı verir.
     *
     * @return Current date
     */
    public static synchronized Date getCurrentDate() {
        return new Date();
    }

    /**
     * * Yarınki zamanı verir.
     *
     * @param date the date
     * @return the next day
     */
    public static synchronized Date getTomorrow() {
        return getNextDay(new Date());
    }

    /**
     * * Verilen date nesnesine göre sonraki zamanı verir.
     *
     * @param date the date
     * @return the next day
     */
    public static synchronized Date getNextDay(final Date date) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        return (Date) calendar.getTime().clone();
    }

    public static synchronized Date getPreviousDay(final Date date) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesinden n gün sonraki zamanı verir. Geçmişi hesaplatmak için n gün değeri negatif verilir.
     *
     * @param date the date
     * @param n Gün sayısı
     * @return the next n day
     */
    public static synchronized Date getNextNDay(final Date date, final int n) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, n);
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesinden n ay sonraki zamanı verir. Geçmişi hesaplatmak için n ay değeri negatif verilir.
     *
     * @param date referans günü
     * @param n ay sayısı
     * @return n ay sonraki tarih
     */
    public static synchronized Date getNextNMonth(final Date date, final int n) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, n);
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesinden n yıl sonraki zamanı verir. Geçmişi hesaplatmak için n yıl değeri negatif verilir.
     *
     * @param date referans günü
     * @param n yıl sayısı
     * @return n yıl sonraki tarih
     */
    public static synchronized Date getNextNYear(final Date date, final int n) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, n);
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesine göre ayın ilk gününü verir. months değeri > 0 ise months değeri kadar sonraki ayın ilk gününü verir.
     *
     * @param date the date
     * @param months the months
     * @return the begin of month
     */
    public static synchronized Date getBeginOfMonth(final Date date, final int months) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (months != 0) {
            calendar.add(Calendar.MONTH, months);
        }
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesine göre ayın son gününü verir. months değeri > 0 ise months değeri kadar sonraki ayın son gününü verir.
     *
     * @param date the date
     * @param months the months
     * @return the end of month
     */
    public static synchronized Date getEndOfMonth(final Date date, final int months) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        if (months != 0) {
            calendar.add(Calendar.MONTH, months);
        }
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesine ait yılın ilk gününü verir.
     *
     * @param date the date
     * @return the begin of year
     */
    public static synchronized Date getBeginOfYear(final Date date) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, calendar.getActualMinimum(Calendar.MONTH));
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMinimum(Calendar.MILLISECOND));
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesine ait yılın son gününü verir.
     *
     * @param date the date
     * @return the end of year
     */
    public static synchronized Date getEndOfYear(final Date date) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, calendar.getActualMaximum(Calendar.MONTH));
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen date nesnesinin yıl değerini int olarak verir.
     *
     * @param date the date
     * @return the year of date
     */
    public static synchronized int getYearOfDate(final Date date) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * * Verilen yıl değerinin son zamanını (date) verir.
     *
     * @param year the year
     * @return the end of year
     */
    public static synchronized Date getEndOfYear(final int year) {
        final Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, calendar.getActualMaximum(Calendar.MONTH));
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
        calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
        calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
        calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));
        return (Date) calendar.getTime().clone();
    }

    /**
     * * Verilen iki date nesnesi arasındaki ay farkını verir.
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return the int
     */
    public static synchronized int monthsBetween(final Date startDate, final Date endDate) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        final int startMonth = calendar.get(Calendar.MONTH);
        final int startYear = calendar.get(Calendar.YEAR);
        calendar.setTime(endDate);
        final int endMonth = calendar.get(Calendar.MONTH);
        final int endYear = calendar.get(Calendar.YEAR);
        return ((endYear - startYear) * 12) + (endMonth - startMonth);
    }

    /**
     * * Verilen iki date nesnesi arasındaki yıl farkını verir.
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return the int
     */
    public static synchronized int yearsBetween(final Date startDate, final Date endDate) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);
        final Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(endDate);
        return calendar2.get(Calendar.YEAR) - calendar.get(Calendar.YEAR);
    }

    /**
     * * Verilen iki date nesnesi arasındaki gün farkını verir.
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return the int
     */
    public static synchronized int daysBetween(final Date startDate, final Date endDate) {
        final Calendar c1 = Calendar.getInstance();
        final Calendar c2 = Calendar.getInstance();
        c1.setTime(startDate);
        c2.setTime(endDate);
        return daysBetween(c1, c2);
    }

    /**
     * * Verilen iki calendar nesnesi arasındaki gün farkını verir.
     *
     * @param early the early
     * @param late the late
     * @return the int
     */
    public static synchronized int daysBetween(final Calendar early, final Calendar late) {
        return (int) (toJulian(late) - toJulian(early));
    }

    /**
     * * Verilen calendar nesnesinin float julian değerini verir.
     *
     * @param calendar the c
     * @return the float
     */
    public static synchronized float toJulian(final Calendar calendar) {
        final int year = calendar.get(Calendar.YEAR);
        final int month = calendar.get(Calendar.MONTH);
        final int date = calendar.get(Calendar.DATE);
        final int a = year / 100;
        final int b = a / 4;
        final int c = (2 - a) + b;
        final float e = (int) (365.25f * (year + 4716));
        final float f = (int) (30.6001f * (month + 1));
        return (c + date + e + f) - 1524.5f;
    }

    public static synchronized Date getRolledDate(final int minute) {
        final Calendar c = Calendar.getInstance();
        c.add(Calendar.MINUTE, (-1) * minute);
        return c.getTime();
    }

    public static synchronized java.sql.Date getCurrentSqlDate() {
        return new java.sql.Date(getCurrentDate().getTime());
    }

    /**
     * * Verilen date nesnesine year(int) değerini ekleyerek yıl bilgisini verir.
     *
     * @param date the date
     * @param year the year
     * @return the n year of date
     */
    public static synchronized int getNYearOfDate(final Date date, final int year) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, year);
        return calendar.get(Calendar.YEAR);
    }

    /***
     * Verilen yyyy-MM-dd stingi parse ederek Date nesnesi verir.
     *
     * @param dateStr yyyy-MM-dd stringi
     * @return date
     */
    public static synchronized Date parseDate(final String dateStr) {
        try {
            return yyyyMMddFormat.parse(dateStr);
        } catch (final ParseException e) {
            System.err.println(e.getMessage());
            return null;
        }
    }

    /**
     * solr tarih tipine kayıpsız çevirme için kullanılmıştır. UTC tarih tipine dönüşümü yapar.
     *
     * @param dateStr the date str
     * @return the string
     */
    public static String toUtcDate(final Object dateStr) {
        if (dateStr == null) {
            return null;
        }
        final SimpleDateFormat out = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        // Add other parsing formats to try as you like:
        final String[] dateFormats = { "yyyy-MM-dd", "dd-MMM-yyyy H:mm:ss", "MMM dd, yyyy hh:mm:ss Z" };
        for (final String dateFormat : dateFormats) {
            try {

                return out.format(new SimpleDateFormat(dateFormat, Locale.ENGLISH).parse(dateStr.toString()));
            } catch (final ParseException ignore) {
                System.out.println(ignore);
            }
        }
        throw new IllegalArgumentException("Invalid date: " + dateStr);
    }

    public static synchronized int getYearOfCurrentDate() {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        return calendar.get(Calendar.YEAR);
    }
    
    public static Date addDays(Date date, int days) {
        if (date == null) {
            throw new IllegalArgumentException("Date cannot be null");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }
}
