/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2017© */
package tr.gov.tubitak.bte.mues.constraint.validator;

import java.util.ResourceBundle;

import javax.inject.Inject;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

import tr.gov.tubitak.bte.mues.constraint.ValidAuthorization;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;

/**
*
 *
 */
public class ValidAuthorizationValidator implements ConstraintValidator<ValidAuthorization, KullaniciBirimRol> {

    @Inject
    private transient ResourceBundle bundle;

    public ValidAuthorizationValidator() {
    }

    /* (non-Javadoc)
     * @see javax.validation.ConstraintValidator#initialize(java.lang.annotation.Annotation)
     */
    @Override
    public void initialize(final ValidAuthorization constraintAnnotation) {
        // no need for initialization
    }

    @Override
    public boolean isValid(final KullaniciBirimRol authorization, final ConstraintValidatorContext context) {
        boolean returnValue = true;
        if (authorization == null) {
            return true;
        }
        // skip this as it will be checked by required=true
        if (authorization.getRol() == null) {
            return true;
        }
        switch (authorization.getRol().getKod()) {
            case "SUPERUSER":
            case "sistem_merkez_yoneticisi":
            case "ktb_merkez_yoneticisi":
                if ((authorization.getMudurluk() != null) && !authorization.getMudurluk().getKod().startsWith("00.")) {
                    // TODO: geçici süreliğine nasıl olacak diye koyulmuştur. İlerde direk kaldırıladabilir.
                    this.raiseFlag(this.bundle.getString("valid.role.directorate.select.not"), context);
                    returnValue = false;
                }
                break;
            case "muzeler_dairesi":
                if (!authorization.getMudurluk().getKod().startsWith("00.")) {
                    // TODO: geçici süreliğine nasıl olacak diye koyulmuştur. İlerde direk kaldırıladabilir.
                    this.raiseFlag(this.bundle.getString("valid.role.directorate.select.centralOrganization"), context);
                    returnValue = false;
                }
                break;
            default: {
                if (authorization.getMudurluk() == null) {
                    this.raiseFlag(this.bundle.getString("valid.role.directorate.select"), context);
                    returnValue = false;
                }
            }
        }
        return returnValue;
    }

    private void raiseFlag(final String message, final ConstraintValidatorContext context) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }

}
