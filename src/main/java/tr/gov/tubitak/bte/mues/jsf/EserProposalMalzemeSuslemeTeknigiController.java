package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.EserMalzemeSuslemeTeknigi;
import tr.gov.tubitak.bte.mues.model.EserProposalMalzemeSuslemeTeknigi;
import tr.gov.tubitak.bte.mues.model.Malzeme;
import tr.gov.tubitak.bte.mues.model.MalzemeSuslemeTeknigi;
import tr.gov.tubitak.bte.mues.model.Renk;
import tr.gov.tubitak.bte.mues.model.SuslemeTeknigi;
import tr.gov.tubitak.bte.mues.session.EserProposalMalzemeSuslemeTeknigiFacade;
import tr.gov.tubitak.bte.mues.session.MalzemeFacade;
import tr.gov.tubitak.bte.mues.session.MalzemeSuslemeTeknigiFacade;

@Named
@ViewScoped
public class EserProposalMalzemeSuslemeTeknigiController extends AbstractController<EserProposalMalzemeSuslemeTeknigi>
{

    private static final long                       serialVersionUID = 3464182755348863018L;

    @Inject
    private EserProposalMalzemeSuslemeTeknigiFacade facade;

    @Inject
    private MalzemeSuslemeTeknigiFacade             malzemeSuslemeTeknigiFacade;

    @Inject
    private MalzemeFacade                           malzemeFacade;
    
    @Inject
    private RenkController                  renkController;

    private List<MalzemeSuslemeTeknigi>             malzemeSuslemeTeknigiList;

    private List<EserProposalMalzemeSuslemeTeknigi> eserMalzemeTeknikList;

    private EserProposalMalzemeSuslemeTeknigi       itemForColor;

    private Integer                                 itemIndex;

    public EserProposalMalzemeSuslemeTeknigiController() {
        super(EserProposalMalzemeSuslemeTeknigi.class);
    }

    public void handleMalzemeSelect(final SelectEvent<Malzeme> event) {
        this.getModel().setMalzeme(event.getObject());
        this.getModel().setSuslemeTeknigi(null);
    }

    public List<SuslemeTeknigi> filterByNameAndAciklamaAndMalzeme(final String query) {
        return this.facade.findByNameAndAciklamaAndMalzeme(query, this.getModel().getMalzeme());
    }
    
    public void addMultiColour() {

        final EserProposalMalzemeSuslemeTeknigi emModel = this.itemForColor != null ? this.itemForColor : this.getModel();

        for (final Renk tempRenk : this.renkController.getSelectionList()) {
            if (!(emModel
                         .getRenks()
                         .stream()
                         .anyMatch(x -> x.getId().equals(tempRenk.getId())))) {

                this.getModel().getRenks().add(tempRenk);
            }
        }
        emModel.getRenks()
               .removeIf(x -> this.renkController.getSelectionList()
                                                 .stream()
                                                 .noneMatch(s -> s.getId().equals(x.getId())));

        if (this.getItemIndex() != null) {
            PrimeFaces.current().ajax().update(":formEserMultipleSusleme:multipleDataTableSusleme:" + this.itemIndex + ":renkSecimSusleme");
        }

    }

    public void handleColorSelect(final Renk renk) {
        this.getModel().getRenks().add(renk);
        if (this.itemForColor != null) {
            this.itemForColor.getRenks().add(renk);
        }
        // updating the row that is selected for color determination
        PrimeFaces.current().ajax().update(":formEserMultipleSusleme:multipleDataTableSusleme:" + this.itemIndex + ":renkSecimSusleme");
    }

    public List<MalzemeSuslemeTeknigi> filterByMalzeme(final Malzeme malzeme) {
        if (malzeme != null) {
            return this.getMalzemeSuslemeTeknigiList().stream().filter(x -> x.getMalzeme().getAd().equals(malzeme.getAd())).collect(Collectors.toList());
        }
        return this.getMalzemeSuslemeTeknigiList();
    }

    public List<EserProposalMalzemeSuslemeTeknigi> getSelectedEserMalzemeSuslemeTeknigis() {
        return this.getEserMalzemeTeknikList()
                   .stream()
                   .filter(x -> ((x.getMalzeme() != null) && (x.getMalzeme().getAd() != null) && !x.getMalzeme().getAd().equals(""))
                                && ((x.getSuslemeTeknigi() != null)
                                    && !x.getSuslemeTeknigi().toString().equals("")
                                    && ((x.getSuslemeTeknigi().getAd() != null) && !x.getSuslemeTeknigi().getAd().equals(""))))
                   .collect(Collectors.toList());
    }

    public void selectItemForColor(final EserProposalMalzemeSuslemeTeknigi itemForColor, final Integer itemIndex) {
        this.setItemForColor(itemForColor);
        this.setItemIndex(itemIndex);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalMalzemeSuslemeTeknigiFacade getFacade() {
        return this.facade;
    }

    public List<MalzemeSuslemeTeknigi> getMalzemeSuslemeTeknigiList() {
        if (this.malzemeSuslemeTeknigiList == null) {
            this.malzemeSuslemeTeknigiList = this.malzemeSuslemeTeknigiFacade.findActive();
        }
        return this.malzemeSuslemeTeknigiList;
    }

    public void setMalzemeSuslemeTeknigiList(final List<MalzemeSuslemeTeknigi> malzemeSuslemeTeknigiList) {
        this.malzemeSuslemeTeknigiList = malzemeSuslemeTeknigiList;
    }

    public List<EserProposalMalzemeSuslemeTeknigi> getEserMalzemeTeknikList() {
        if (this.eserMalzemeTeknikList == null) {
            this.eserMalzemeTeknikList = new ArrayList<>();
            for (final Malzeme malzeme : this.malzemeFacade.findActive()) {
                final EserProposalMalzemeSuslemeTeknigi eserMalzemeSuslemeTeknigi = new EserProposalMalzemeSuslemeTeknigi();
                eserMalzemeSuslemeTeknigi.setMalzeme(malzeme);
                this.eserMalzemeTeknikList.add(eserMalzemeSuslemeTeknigi);
            }
        }
        return this.eserMalzemeTeknikList;
    }

    public void setEserProposalMalzemeTeknikList(final List<EserProposalMalzemeSuslemeTeknigi> eserMalzemeTeknikList) {
        this.eserMalzemeTeknikList = eserMalzemeTeknikList;
    }

    public void resetInputFieldsLists() {
        this.eserMalzemeTeknikList = null;
        super.setModel(null);
    }

    public EserProposalMalzemeSuslemeTeknigi getItemForColor() {
        return this.itemForColor;
    }

    public void setItemForColor(final EserProposalMalzemeSuslemeTeknigi itemForColor) {
        this.itemForColor = itemForColor;
    }

    public Integer getItemIndex() {
        return this.itemIndex;
    }

    public void setItemIndex(final Integer itemIndex) {
        this.itemIndex = itemIndex;
    }

}
