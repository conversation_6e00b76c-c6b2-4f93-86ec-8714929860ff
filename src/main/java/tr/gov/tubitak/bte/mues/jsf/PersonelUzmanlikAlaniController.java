package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.PersonelUzmanlikAlani;
import tr.gov.tubitak.bte.mues.model.UzmanlikAlani;
import tr.gov.tubitak.bte.mues.session.PersonelUzmanlikAlaniFacade;
import tr.gov.tubitak.bte.mues.session.UzmanlikAlaniFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class PersonelUzmanlikAlaniController extends AbstractController<PersonelUzmanlikAlani> {

    private static final long           serialVersionUID = -8920677339741916613L;

    private Set<UzmanlikAlani>          selectedUzmanlikAlanlari;

    private List<UzmanlikAlani>         uzmanlikAlanlari;

    @Inject
    private PersonelController          personelController;

    @Inject
    private PersonelUzmanlikAlaniFacade facade;

    @Inject
    private UzmanlikAlaniFacade         uzmanlikAlaniFacade;

    public PersonelUzmanlikAlaniController() {
        super(PersonelUzmanlikAlani.class);
    }

    public List<UzmanlikAlani> filterByFullNameAndAciklamaPreventDuplicate(final String query) {

        final List<UzmanlikAlani> uzmanlikAlaniList = new ArrayList<>();

        if (this.personelController.getModel().getPersonelUzmanlikAlani() != null) {
            for (final PersonelUzmanlikAlani ehs : this.personelController.getModel().getPersonelUzmanlikAlani()) {
                uzmanlikAlaniList.add(ehs.getUzmanlikAlani());
            }
        }

        return this.getFacade().filterByFullNameAndAciklamaPreventDuplicate(query, MuesUtil.toIds(uzmanlikAlaniList));
    }

    @PostConstruct
    private void init() {
        this.uzmanlikAlanlari = this.uzmanlikAlaniFacade.findAll();
    }

    @Override
    public void setModel(final PersonelUzmanlikAlani item) {
        super.setModel(item);
    }

    @Override
    public void newRecord() {
        super.newRecord();
    }

    // getters and setters ....................................................

    @Override
    public PersonelUzmanlikAlaniFacade getFacade() {
        return this.facade;
    }

    public Set<UzmanlikAlani> getSelectedUzmanlikAlanlari() {

        if (this.selectedUzmanlikAlanlari == null) {
            this.selectedUzmanlikAlanlari = new LinkedHashSet<>();
        }

        return this.selectedUzmanlikAlanlari;
    }

    public void setSelectedUzmanlikAlanlari(final Set<UzmanlikAlani> selectedUzmanlikAlanlari) {

        this.selectedUzmanlikAlanlari = selectedUzmanlikAlanlari;
    }

    public List<UzmanlikAlani> getUzmanlikAlanlari() {
        return this.uzmanlikAlanlari;
    }

    public void setUzmanlikAlanlari(final List<UzmanlikAlani> uzmanlikAlanlari) {
        this.uzmanlikAlanlari = uzmanlikAlanlari;
    }

}
