package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserKaynakLiteratur;
import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.session.EserKaynakLiteraturFacade;

@Named
@ViewScoped
public class EserKaynakLiteraturController extends AbstractController<EserKaynakLiteratur> {

    private static final long         serialVersionUID = -8692714386176081472L;

    @Inject
    private EserKaynakLiteraturFacade facade;

    public EserKaynakLiteraturController() {
        super(EserKaynakLiteratur.class);
    }

    public void handleLiteraturSelect(final Literatur literatur) {
        this.getModel().setLiteratur(literatur);
    }

    // getters and setters ....................................................

    @Override
    public EserKaynakLiteraturFacade getFacade() {
        return this.facade;
    }

}