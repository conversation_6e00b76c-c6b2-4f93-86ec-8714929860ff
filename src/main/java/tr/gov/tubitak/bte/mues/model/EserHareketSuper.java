package tr.gov.tubitak.bte.mues.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "EserHareket.findEagerByEserId", query = "SELECT h FROM EserHareket h LEFT JOIN FETCH h.teslimEdenSahis ht LEFT JOIN FETCH ht.sahis LEFT JOIN FETCH h.arastirma LEFT JOIN FETCH h.eserGelisSekli LEFT JOIN FETCH h.iadeEdenUlke LEFT JOIN FETCH h.kazi LEFT JOIN FETCH h.mudurluk LEFT JOIN FETCH h.teslimAlanPersonel LEFT JOIN FETCH h.teslimEdenPersonel LEFT JOIN FETCH h.teslimEdenTuzelKisi WHERE h.eser.id = :id")
public abstract class EserHareketSuper extends AbstractEntity {

    private static final long serialVersionUID = -4077839579035405418L;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Size(max = 50)
    @Column(name = "ENVANTER_NO", length = 50)
    protected String          envanterNo;

    @Size(max = 20)
    @Column(name = "ONAY_SAYISI", length = 20)
    protected String          onaySayisi;

    @Column(name = "ONAY_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    protected Date            onayTarihi;

    @Size(max = 150)
    @Column(name = "ELE_GECIRME_YERI", length = 150)
    private String            eleGecirmeYeri;

    @Column(name = "CIKARILMA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              eleGecirmeTarihi;

    @Column(name = "MUZEYE_GELIS_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              muzeyeGelisTarihi;
    
    @Column(name = "ENVANTERE_ALINMA_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date              envantereAlinmaTarihi;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ARASTIRMA_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    protected Arastirma       arastirma;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "KAZI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    protected Kazi            kazi;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "iadeEdenUlke", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    protected Ulke            iadeEdenUlke;

    @Column(name = "oldArtifactId")
    private Integer           oldArtifactId;

    public EserHareketSuper() {
        // default Constructor
    }

    // getters and setters ....................................................

    public String getEnvanterNo() {
        return this.envanterNo;
    }

    public void setEnvanterNo(final String envanterNo) {
        this.envanterNo = envanterNo;
    }

    public Date getEleGecirmeTarihi() {
        return this.eleGecirmeTarihi;
    }

    public void setEleGecirmeTarihi(final Date eleGecirmeTarihi) {
        this.eleGecirmeTarihi = eleGecirmeTarihi;
    }

    public Date getOnayTarihi() {
        return this.onayTarihi;
    }

    public void setOnayTarihi(final Date onayTarihi) {
        this.onayTarihi = onayTarihi;
    }

    public String getOnaySayisi() {
        return this.onaySayisi;
    }

    public void setOnaySayisi(final String onaySayisi) {
        this.onaySayisi = onaySayisi;
    }

    public Date getMuzeyeGelisTarihi() {
        return this.muzeyeGelisTarihi;
    }

    public void setMuzeyeGelisTarihi(final Date muzeyeGelisTarihi) {
        this.muzeyeGelisTarihi = muzeyeGelisTarihi;
    }

	public Date getEnvantereAlinmaTarihi() {
        return this.envantereAlinmaTarihi;
    }

    public void setEnvantereAlinmaTarihi(final Date envantereAlinmaTarihi) {
        this.envantereAlinmaTarihi = envantereAlinmaTarihi;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getEleGecirmeYeri() {
        return this.eleGecirmeYeri;
    }

    public void setEleGecirmeYeri(final String eleGecirmeYeri) {
        this.eleGecirmeYeri = eleGecirmeYeri;
    }

    public Arastirma getArastirma() {
        return this.arastirma;
    }

    public void setArastirma(final Arastirma arastirma) {
        this.arastirma = arastirma;
    }

    public Kazi getKazi() {
        return this.kazi;
    }

    public void setKazi(final Kazi kazi) {
        this.kazi = kazi;
    }

    public Ulke getIadeEdenUlke() {
        return this.iadeEdenUlke;
    }

    public void setIadeEdenUlke(final Ulke iadeEdenUlke) {
        this.iadeEdenUlke = iadeEdenUlke;
    }

    public Integer getOldArtifactId() {
        return this.oldArtifactId;
    }

    public void setOldArtifactId(final Integer oldArtifactId) {
        this.oldArtifactId = oldArtifactId;
    }

    public abstract Object getTeslimAlanPersonel();

    public abstract Object getTeslimEdenPersonel();

}
