package tr.gov.tubitak.bte.mues.search;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;

import org.primefaces.PrimeFaces;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.util.MuesException;

/**
 * <PERSON><PERSON> kriterler oluşturmak için fabrika sınıfı.
 */
public abstract class CriterionFactory {

    private static Map<Integer, Class<? extends AbstractCriterion>> criterionRegistry = new HashMap<>();

    private static final Logger                                     logger            = LoggerFactory.getLogger(CriterionFactory.class);

    static {
        criterionRegistry.put(CriterionEnum.TEXT.getCode(), TextCriterion.class);
        criterionRegistry.put(CriterionEnum.NUMBER.getCode(), NumericCriterion.class);
        criterionRegistry.put(CriterionEnum.DATE.getCode(), DateCriterion.class);
        criterionRegistry.put(CriterionEnum.FREETEXT.getCode(), TextCriterion.class);
        criterionRegistry.put(CriterionEnum.BOOLEAN.getCode(), BooleanCriterion.class);
        criterionRegistry.put(CriterionEnum.COMPOUND.getCode(), CompoundCriterion.class);
        criterionRegistry.put(CriterionEnum.MULTI_LIST.getCode(), MultiValuedListCriterion.class);
        criterionRegistry.put(CriterionEnum.MULTI_NUMBERIC.getCode(), MultiValuedListCriterion.class);
        criterionRegistry.put(CriterionEnum.MULTI_DATE.getCode(), DateCriterion.class);

    }

    /**
     * Yeni kriter üreten metot.
     *
     * @param model kriter modeli
     * @return kriter
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    public static ICriterion createCriterion(final CriterionModel model) {
        try {

            return criterionRegistry.get(model.getType()).getDeclaredConstructor().newInstance().setModel(model);

        } catch (final InstantiationException | IllegalAccessException | IllegalArgumentException | InvocationTargetException |

                NoSuchMethodException | SecurityException e) {

            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Arama alanı için criter oluşturulamadı.'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine başvurunuz '"
                                     + ", 'severity':'Error'})");

            logger.error("[addCriterion] : Hata : {}", e.getMessage(), e);

            throw new MuesException("");

        }
    }

}
