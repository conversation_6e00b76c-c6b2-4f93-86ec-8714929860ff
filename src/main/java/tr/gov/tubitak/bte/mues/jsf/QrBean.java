/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.enterprise.inject.Model;
import javax.inject.Inject;

import org.primefaces.PrimeFaces;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.util.Encryptor;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * 
 *
 */
@Model
public class QrBean {

    private static final Logger       logger = LoggerFactory.getLogger(QrBean.class);

    @Inject
    private ArtifactViewingController artifactViewingController;

    public QrBean() {
        // blank constructor
    }

    public void redirect() throws IOException {
        String encodedText = MuesUtil.getRequestParameter("i");
        logger.info("[redirect] : Read QR data: {}", encodedText);

        String decoded = null;
        try {
            encodedText = encodedText.replace("--", "=="); // to equalize the data between the terminal and captured camera data
            decoded = Encryptor.decrypt(encodedText);
        } catch (InvalidKeyException | UnsupportedEncodingException | NoSuchAlgorithmException | NoSuchPaddingException | InvalidAlgorithmParameterException | IllegalBlockSizeException
                | BadPaddingException | IllegalArgumentException e) {
            logger.error("[redirect] : Hata : {}", e.getMessage(), e);
            PrimeFaces.current().executeScript("PF('growlMessageWidget').renderMessage({'summary':" + "'QR Bulunamadı'" + ", 'detail':" + "'Encoded: " + encodedText + "'" + ", 'severity':'error'})");
            PrimeFaces.current().executeScript("showFadingMessage(false);");
        }

        if (!MuesUtil.isEmptyOrNull(decoded)) {
            PrimeFaces.current().executeScript("showFadingMessage(true);");
            this.artifactViewingController.showDetailByPermanent(Integer.parseInt(decoded));
        }
    }

}
