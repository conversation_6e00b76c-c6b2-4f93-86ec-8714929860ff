package tr.gov.tubitak.bte.mues.session;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.enterprise.context.Dependent;
import javax.inject.Inject;
import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import javax.persistence.criteria.Order;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

import org.primefaces.model.FilterMeta;
import org.primefaces.model.SortMeta;

/**
 *
 * <AUTHOR>
 */
@Dependent
public abstract class AbstractLazyLoadFacade<T> implements Serializable {

    private static final long serialVersionUID = -568096064186647607L;

    @Inject
    protected EntityManager   em;

    private final Class<T>    entityClass;

    protected AbstractLazyLoadFacade(final Class<T> entityClass) {
        this.entityClass = entityClass;
    }

    public List<T> fetchData(final int first, final int pageSize, final Map<String, SortMeta> multiSortMeta, final Map<String, FilterMeta> filters) {
        final CriteriaBuilder cb = this.em.getCriteriaBuilder();
        final CriteriaQuery<T> cq = cb.createQuery(this.getEntityClass());
        final Root<T> root = cq.from(this.getEntityClass());

        cq.select(root);

        this.addFetches(root); // must be overridden
        final List<Predicate> predicates = this.createPredicates(cb, root, filters); // must be overridden

        final Predicate predicate = cb.and(predicates.toArray(new Predicate[] {}));
        cq.where(predicate);

        final List<Order> orders = this.createOrders(cb, root, multiSortMeta); // must be overridden
        cq.orderBy(orders);

        return this.em.createQuery(cq).setFirstResult(first).setMaxResults(pageSize).getResultList();
    }

    public Integer count(final Map<String, FilterMeta> filters) {
    	
        final CriteriaBuilder cb = this.em.getCriteriaBuilder();
        final CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        final Root<T> root = cq.from(this.getEntityClass());

        cq.select(cb.count(root)).distinct(true);

        this.addJoinsForCount(root); // must be overridden

        final List<Predicate> predicates = this.createPredicates(cb, root, filters); // must be overridden

        final Predicate predicate = cb.and(predicates.toArray(new Predicate[] {}));
        cq.where(predicate);

        return Integer.valueOf(this.em.createQuery(cq).getSingleResult().intValue());
    }

    protected EntityManager getEM() {
        return this.em;
    }

    public Class<T> getEntityClass() {
        return this.entityClass;
    }

   
    public <U> Path<Object> getPath(final SortMeta sortMeta, final Root<U> root) {

        final String[] fieldParts = sortMeta.getField().split("\\.");

        Path<?> currentPath = root;

        for (int i = 0; i < fieldParts.length - 1; i++) {
            currentPath = ((From<?, ?>) currentPath).join(fieldParts[i]);  
        }

        return currentPath.get(fieldParts[fieldParts.length - 1]);
    }
    
    
    public abstract List<Order> createOrders(CriteriaBuilder cb, Root<T> root, final Map<String, SortMeta> multiSortMeta);

    public abstract List<Predicate> createPredicates(final CriteriaBuilder cb, final Root<T> root, final Map<String, FilterMeta> filters);

    public abstract void addFetches(final Root<T> root);

    public abstract void addJoinsForCount(final Root<T> root);

}
