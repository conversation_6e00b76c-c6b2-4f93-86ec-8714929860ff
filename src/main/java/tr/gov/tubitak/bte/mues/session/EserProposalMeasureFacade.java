package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalMeasure;
import tr.gov.tubitak.bte.mues.model.Measure;
import tr.gov.tubitak.bte.mues.model.MeasureType;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class EserProposalMeasureFacade extends AbstractFacade<EserProposalMeasure> {

    public EserProposalMeasureFacade() {
        super(EserProposalMeasure.class);
    }

    public List<Measure> findByNameAndType(final String value, final MeasureType type) {
        return this.em.createNamedQuery("Measure.findByNameAndMeasureType", Measure.class)
                      .setParameter("ad", "%" + value + "%")
                      .setParameter("type", type)
                      .getResultList();
    }

}
