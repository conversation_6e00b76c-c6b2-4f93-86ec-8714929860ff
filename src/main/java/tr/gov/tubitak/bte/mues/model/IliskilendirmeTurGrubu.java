package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 * 
*
 */
@Entity
@Table(name = "IliskilendirmeTurGrubu")
@NamedQuery(name = "IliskilendirmeTurGrubu.findEagerById", query = "SELECT g FROM IliskilendirmeTurGrubu g WHERE g.id = :id")
@NamedQuery(name = "IliskilendirmeTurGrubu.findAll", query = "SELECT g FROM IliskilendirmeTurGrubu g ORDER BY g.silinmis, g.aktif DESC, g.ad")
@NamedQuery(name = "IliskilendirmeTurGrubu.findActive", query = "SELECT g FROM IliskilendirmeTurGrubu g WHERE g.aktif = true AND g.silinmis = false ORDER BY g.ad")
@NamedQuery(name = "IliskilendirmeTurGrubu.findByNameAndAciklama", query = "SELECT g FROM IliskilendirmeTurGrubu g WHERE g.aktif = true AND g.silinmis = false AND (g.ad LIKE :str OR g.aciklama LIKE :str) ORDER BY g.ad, g.aciklama")
public class IliskilendirmeTurGrubu extends AbstractEntity {

    private static final long serialVersionUID = 5798427338682818328L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public IliskilendirmeTurGrubu() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
