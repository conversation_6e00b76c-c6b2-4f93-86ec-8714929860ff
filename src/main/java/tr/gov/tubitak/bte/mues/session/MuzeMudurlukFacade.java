package tr.gov.tubitak.bte.mues.session;

import java.util.Collections;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.MuzeMudurluk;

@RequestScoped
public class MuzeMudurlukFacade extends AbstractFacade<MuzeMudurluk> {

    public MuzeMudurlukFacade() {
        super(MuzeMudurluk.class);
    }

    @Override
    public List<MuzeMudurluk> findAll() {
        final List<MuzeMudurluk> firstList = super.findAll();
        final List<MuzeMudurluk> tempList = new LinkedList<>();
        for (final Iterator<MuzeMudurluk> iterator = firstList.iterator(); iterator.hasNext();) {
            final MuzeMudurluk muzeMudurluk = iterator.next();
            if (muzeMudurluk.getLaboratoryDirectorate() != null) {
                tempList.add(muzeMudurluk);
                iterator.remove();
            }
        }
        if (tempList.isEmpty()) {
            return firstList;
        } else {
            Collections.sort(tempList, (m1, m2) -> m1.getLaboratoryDirectorate().getAd().compareTo(m2.getLaboratoryDirectorate().getAd()));
        }

        return Stream.concat(tempList.stream(), firstList.stream()).collect(Collectors.toList());

    }

    public List<MuzeMudurluk> filterByName(final String query) {
        return this.em.createNamedQuery("MuzeMudurluk.findByName", MuzeMudurluk.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<MuzeMudurluk> findByIdsAndName(final String query, final List<Integer> ids) {
        return this.em.createNamedQuery("MuzeMudurluk.findByIdsAndName", MuzeMudurluk.class).setParameter("str", "%" + query + "%").setParameter("ids", ids).getResultList();
    }

    public List<MuzeMudurluk> filterByNameAndEmptyLab(final String query) {
        return this.em.createNamedQuery("MuzeMudurluk.filterByNameAndEmptyLab", MuzeMudurluk.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<MuzeMudurluk> filterByLaboratoryDirectorateAndName(final String query, final List<Integer> ids) {
        return this.em.createNamedQuery("MuzeMudurluk.filterByLaboratoryDirectorateAndName", MuzeMudurluk.class).setParameter("str", "%" + query + "%").setParameter("ids", ids).getResultList();
    }

}
