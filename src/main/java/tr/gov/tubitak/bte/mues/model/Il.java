package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "IL")
@NamedQuery(name = "Il.findEagerById", query = "SELECT i FROM Il i WHERE i.id = :id")
@NamedQuery(name = "Il.findAll", query = "SELECT i FROM Il i ORDER BY i.silinmis, i.aktif DESC, i.ad")
@NamedQuery(name = "Il.findActive", query = "SELECT i FROM Il i WHERE i.aktif = true AND i.silinmis = false ORDER BY i.ad")
@NamedNativeQuery(name = "Il.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ILCE WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM KAZI WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM MUZE_MUDURLUGU WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM SAHIS WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_Collectioner WHERE SILINMIS = 0 AND provinceId = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_Laboratory WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_LaboratoryDirectorate WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_CommissionMember WHERE SILINMIS = 0 AND IL_ID = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM Lab_PrivateMuseum WHERE SILINMIS = 0 AND provinceId = :id) + "
                                                            + "(SELECT case when count(1) > 0 then 1 else 0 end FROM TUZEL_KISI WHERE SILINMIS = 0 AND IL_ID = :id)")
public class Il extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -5304566565823194477L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String            deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Basic(optional = false)
    @NotNull
    @Size(min = 1, max = 2)
    @Column(name = "KOD")
    private String            kod;

    public Il() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    @Override
    public String toString() {
        return this.getAd();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
