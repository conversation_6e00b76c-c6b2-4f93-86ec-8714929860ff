package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.MeasureType;
import tr.gov.tubitak.bte.mues.session.MeasureTypeFacade;

@Named
@ViewScoped
public class MeasureTypeController extends AbstractController<MeasureType> {

    private static final long serialVersionUID = 460125983574507369L;

    @Inject
    private MeasureTypeFacade facade;

    public MeasureTypeController() {
        super(MeasureType.class);
    }

    public List<MeasureType> filterByNameAndAciklama(final String query) {
        return this.getFacade().findByNameAndAciklama(query);
    }

    // getters and setters ....................................................

    @Override
    public MeasureTypeFacade getFacade() {
        return this.facade;
    }

}
