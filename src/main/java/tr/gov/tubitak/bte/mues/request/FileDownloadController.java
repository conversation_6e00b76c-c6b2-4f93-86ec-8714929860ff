package tr.gov.tubitak.bte.mues.request;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.file.Paths;
import java.util.UUID;

import javax.enterprise.context.RequestScoped;
import javax.faces.application.FacesMessage;
import javax.faces.context.FacesContext;
import javax.faces.event.PhaseId;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.util.Faces;
import org.primefaces.PrimeFaces;
import org.primefaces.model.DefaultStreamedContent;
import org.primefaces.model.StreamedContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 */
@Named
@RequestScoped
public class FileDownloadController implements Serializable {

    private static final String CONTENT_SERVER_URL = "/ContentServer?id=";

    private static final long   serialVersionUID   = 8046498399800283661L;

    private static final Logger logger             = LoggerFactory.getLogger(FileDownloadController.class);

    @Inject
    private AbstractParameters  parameters;

    public FileDownloadController() {
        // default constructor
    }

    public StreamedContent getImage() {
        if (FacesContext.getCurrentInstance().getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
            // So, we're rendering the HTML. Return a stub StreamedContent so that it will generate right URL.
            return new DefaultStreamedContent();
        }
        final String file = Faces.getRequestParameter("file");
        if ((file != null) && (file.length() > 0)) {
            final String fileFinal = file.startsWith(this.parameters.getTempDir()) ? file : this.parameters.getImageUsageCopyPath(file);
            return DefaultStreamedContent.builder().contentType("image/png").stream(() -> {
                try {
                    return new FileInputStream(fileFinal);

                } catch (final Exception e) {
                    logger.debug("Ilgili Foto bulunamadi");
                }
                return null;
            }).build();
        }
        return null;
    }

    public void download(String file, final FolderType type, final String fileExtension) throws IOException {

        final String escapedFilename = "Dosya." + fileExtension;
        if (!file.startsWith(this.parameters.getTempDir())) {
            file = this.parameters.getAbsolutePath(file, type).toString();
        }

        try {
            final InputStream is = new FileInputStream(file);
            Faces.sendFile(is, escapedFilename, true);
            is.close();
            logger.info("[download] : dosya indirildi");
            MuesUtil.showMessage(FacesMessage.SEVERITY_INFO, "Dosya başarılı bir şekilde indirildi", "Dosya başarılı bir şekilde indirildi");

        } catch (final Exception e) {
            MuesUtil.showMessage(FacesMessage.SEVERITY_ERROR, "Dosya indirilirken hata oluştu", "Dosya indirilirken hata oluştu");
            logger.info(e.getMessage() + ": dosya indirilirken hata oluştu");
        }

    }

    public StreamedContent imageByFolderType() {

        if (FacesContext.getCurrentInstance().getCurrentPhaseId() == PhaseId.RENDER_RESPONSE) {
            // So, we're rendering the HTML. Return a stub StreamedContent so that it will generate right URL.
            return new DefaultStreamedContent();
        }
        final String file = Faces.getRequestParameter("file");
        final String folderType = Faces.getRequestParameter("folderType");

        if ((file != null) && (file.length() > 0)) {

            final String fileFinal = file.startsWith(this.parameters.getTempDir()) ? file : this.parameters.getImageByFolderType(file, folderType);

            return DefaultStreamedContent.builder().contentType("image/png").stream(() -> {

                try {
                    return new FileInputStream(fileFinal);

                } catch (final Exception e) {
                    logger.debug("Ilgili Foto bulunamadi");
                }
                return null;

            }).build();
        }

        return null;

    }

    public void showImage(final String file) {
        this.showImage(file, FolderType.IMAGE_AK);
    }

    public void showMainImage() {
        final String path = Faces.getRequestParameter("fotografPath");

        logger.debug("image Changed {}", path);
        this.showImage(path, FolderType.IMAGE_AK);
    }

    public void showImage(String path, final FolderType folderType) {
        final String dataId = UUID.randomUUID().toString();

        if (!path.startsWith(this.parameters.getTempDir())) {
            path = this.parameters.getAbsolutePath(path, folderType).toString();
        }

        MuesUtil.setSessionMapParameter(dataId, path);
        PrimeFaces.current().executeScript(this.prepareHtmlScriptForImage(dataId));
    }

    public void showImage(String file, final FolderType folderType, final String path) {
        final String dataId = UUID.randomUUID().toString();
        if (!file.startsWith(this.parameters.getTempDir())) {
            file = this.parameters.getAbsolutePath(file, folderType, path).toString();
        }
        MuesUtil.setSessionMapParameter(dataId, file);
        PrimeFaces.current().executeScript(this.prepareHtmlScriptForImage(dataId));
    }

    public void showImageByBaseFolder(final String baseFolder, String path) {
        final String dataId = UUID.randomUUID().toString();
        if (!path.startsWith(this.parameters.getTempDir())) {
            path = this.parameters.retrieveMainCopyURL(baseFolder, path).toString();
        }
        MuesUtil.setSessionMapParameter(dataId, path);
        PrimeFaces.current().executeScript(this.prepareHtmlScriptForImage(dataId));
    }
    
    public void showPdf(final String file, final FolderType type) {
        this.handlePdfShow(file, type, "contentContainer");
    }

    public void showMedia(final String file, final FolderType type) {
        String href = null;

        // henüz kaydedilmemiş video/ses için server temp dizininden gosterim
        if (file.startsWith(this.parameters.getTempDir())) {
            final String dataId = UUID.randomUUID().toString();
            MuesUtil.setSessionMapParameter(dataId, file);
            href = Faces.getRequest().getContextPath() + CONTENT_SERVER_URL + dataId;

            // kaydedilmiş video/ses için gosterim
        } else {
            href = this.parameters.get(AbstractParameters.IMAGES_BASE_URL) + type.getFolderPath() + "/" + file;
        }

        final String script = "$('#contentContainer').append($('<video width=\"100%\" height=\"100%\" controls>" + "<source src=\"" + href + "\" type=\"video/mp4\"> </video>'))";

        PrimeFaces.current().executeScript(script);
    }

    public void showAnnouncement(final String file, final FolderType type) {
        this.handlePdfShow(file, type, "anouncementContainer");
    }

    private void handlePdfShow(String file, final FolderType type, final String clientId) {
        final String dataId = UUID.randomUUID().toString();

        if (!file.startsWith(this.parameters.getTempDir())) {
            file = this.parameters.getAbsolutePath(file, type).toString();
        }

        MuesUtil.setSessionMapParameter(dataId, file);

        PrimeFaces.current().executeScript(this.prepareHtmlScriptForPdf(dataId, clientId));
    }

    public void handlePdfShow(String file, final FolderType type, final String clientId, final String basefolder) {
        final String dataId = UUID.randomUUID().toString();

        if (!file.startsWith(this.parameters.getTempDir())) {
            file = Paths.get(basefolder, type.getFolderPath(), file).toString();
        }

        MuesUtil.setSessionMapParameter(dataId, file);

        PrimeFaces.current().executeScript(this.prepareHtmlScriptForPdf(dataId, clientId));
    }

    public StreamedContent getPdfFile() throws IOException {

        final String file = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("file");
        final String name = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("name");
        String escapedFilename = "Dosya.pdf";

        try {
            // Encoding
            escapedFilename = URLEncoder.encode(name, "UTF-8").replaceAll("\\+", "%20");

        } catch (final UnsupportedEncodingException e) {
            logger.error("[getPdfFile] : Hata : {}", e.getMessage(), e);
        }
        try (final FileInputStream ios = new FileInputStream(this.parameters.getAbsolutePath(file, FolderType.HELP).toFile())) {

            return DefaultStreamedContent.builder().stream(() -> ios).contentType("application/pdf").name(escapedFilename).build();
        }
    }

    public void downloadOrShowFile(final String file, final FolderType type, final String extension) throws IOException {
        if (extension.equals("pdf")) {
            this.showPdf(file, type);
        } else {
            if (this.fetchFileExtension(file).equals("pdf")) {
                this.showPdf(file, type);
            } else {
                this.downloadFile(file, type, extension);
            }
        }
    }

    public void downloadFile(String file, final FolderType type, final String extension) throws IOException {

        final String dataId = UUID.randomUUID().toString();

        if (!file.startsWith(this.parameters.getTempDir())) {
            file = this.parameters.getAbsolutePath(file, type).toString();
        }
        final String file1 = file;
        MuesUtil.setSessionMapParameter(dataId, file);
        final File tempFile = new File(file1);
        File renamedTempFile;
        if ((extension == null) || extension.isBlank()) {
            renamedTempFile = new File(file1);
        } else {
            renamedTempFile = new File(file1 + "." + extension);
        }
        tempFile.renameTo(renamedTempFile);
        Faces.sendFile(renamedTempFile, true);

    }

    public String fetchFileExtension(final String fileName) {
        final int dotIndex = fileName.lastIndexOf('.');
        if ((dotIndex > 0) && (dotIndex < (fileName.length() - 1))) {
            return fileName.substring(dotIndex + 1).toLowerCase();
        }
        return "";
    }

    private String prepareHtmlScriptForPdf(final String dataId, final String clientId) {
        final String href = Faces.getRequest().getContextPath() + CONTENT_SERVER_URL + dataId;
        return "$('#"
               + clientId
               + "').html($('<div style=\\\"width: 100%; height: 100%; border: none; min-height: 810px;\\\"><object id=\"iframedocument\" type=\"application/pdf\" width=\"100%\" height=\"100%\" data=\""
               + href
               + "\"   >Kullanılan tarayıcı PDF gösteremediği için <a href=\""
               + href
               + "\" target=\"_blank\" style=\"color: blue;\">bu linki</a> tıklayarak PDF görüntüleyebilirsiniz.</object></div>'))";
    }

    private String prepareHtmlScriptForImage(final String dataId) {
        return "$('#contentContainer').html($('<a target=\"_blank\" href=\""
               + Faces.getRequest().getContextPath()
               + CONTENT_SERVER_URL
               + dataId
               + "\"> <img id=\"iframedocument\" src=\""
               + Faces.getRequest().getContextPath()
               + CONTENT_SERVER_URL
               + dataId
               + "\" style=\"border: none; max-width: 710px;max-height: 800px; height: auto;\" /> </a>'))";
    }

}