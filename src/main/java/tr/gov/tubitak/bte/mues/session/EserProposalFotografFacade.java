package tr.gov.tubitak.bte.mues.session;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.EserFotograf;
import tr.gov.tubitak.bte.mues.model.EserProposalFotograf;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class EserProposalFotografFacade extends AbstractFacade<EserProposalFotograf> {

    public EserProposalFotografFacade() {
        super(EserProposalFotograf.class);
    }

}
