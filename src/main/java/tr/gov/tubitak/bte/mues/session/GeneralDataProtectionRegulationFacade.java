package tr.gov.tubitak.bte.mues.session;

import javax.enterprise.context.RequestScoped;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceException;
import javax.validation.ConstraintViolationException;

import tr.gov.tubitak.bte.mues.model.GeneralDataProtectionRegulation;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

/**
 *
*
 */
@RequestScoped
public class GeneralDataProtectionRegulationFacade extends AbstractFacade<GeneralDataProtectionRegulation> {

    public GeneralDataProtectionRegulationFacade() {
        super(GeneralDataProtectionRegulation.class);
    }

    public GeneralDataProtectionRegulation findByMaxId() {
        try {
            return this.em.createNamedQuery("GeneralDataProtectionRegulation.findByMaxId", GeneralDataProtectionRegulation.class).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }

    public DBOperationResult updateAllKVKKStatus(final Integer id) {
        try {
            this.em.createNativeQuery("Update KULLANICI set isGDPRApproved = 0 FROM KULLANICI where (SELECT top 1 ID FROM GeneralDataProtectionRegulation a ORDER BY a.ID DESC) =:id OR :id is null")
                   .setParameter("id", id)
                   .executeUpdate();

            this.em.createNativeQuery("Update Lab_Kullanici set isGDPRApproved = 0 FROM Lab_Kullanici where (SELECT top 1 ID FROM GeneralDataProtectionRegulation a ORDER BY a.ID DESC) =:id OR :id is null")
                   .setParameter("id", id)
                   .executeUpdate();

            this.em.createNativeQuery("Update Kam_Kullanici set isGDPRApproved = 0 FROM Kam_Kullanici where (SELECT top 1 ID FROM GeneralDataProtectionRegulation a ORDER BY a.ID DESC) =:id OR :id is null")
                   .setParameter("id", id)
                   .executeUpdate();

            this.em.createNativeQuery("Update Omk_Kullanici set isGDPRApproved = 0 FROM Omk_Kullanici where (SELECT top 1 ID FROM GeneralDataProtectionRegulation a ORDER BY a.ID DESC) =:id OR :id is null")
                   .setParameter("id", id)
                   .executeUpdate();

        } catch (final ConstraintViolationException e) {
            return DBOperationResult.failure(e.getConstraintViolations().iterator().next().getMessage());

        } catch (final PersistenceException e) {
            return this.handleException(e);
        }
        return DBOperationResult.success();

    }

}
