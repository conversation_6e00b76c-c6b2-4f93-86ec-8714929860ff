package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;

import org.hibernate.envers.Audited;

@Audited
@MappedSuperclass
@NamedQuery(name = "PickGroup.findEagerById", query = "SELECT s FROM PickGroup s WHERE s.id = :id")
@NamedQuery(name = "PickGroup.findAll", query = "SELECT s FROM PickGroup s ORDER BY s.silinmis, s.aktif DESC")
@NamedQuery(name = "PickGroup.findActive", query = "SELECT s FROM PickGroup s WHERE s.aktif = true AND s.silinmis = false")

public class PickGroupSuper extends AbstractEntity {

    private static final long serialVersionUID = 3125557658469369629L;

    @Column(name = "AD")
    private String            ad;

    @Column(name = "kod", unique = true)
    private Integer           kod;

    @Column(name = "ACIKLAMA")
    private String            aciklama;

    public PickGroupSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public Integer getKod() {
        return this.kod;
    }

    public void setKod(final Integer kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
