/* TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2016© */
package tr.gov.tubitak.bte.mues.jsf;

import java.io.File;
import java.io.Serializable;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Properties;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.enterprise.event.Observes;
import javax.inject.Inject;
import javax.servlet.ServletContext;

import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.event.ConfigurationChangeEvent;
import tr.gov.tubitak.bte.mues.jsf.event.SessionVariablesChangeEvent;
import tr.gov.tubitak.bte.mues.model.Configuration;
import tr.gov.tubitak.bte.mues.model.Pick;
import tr.gov.tubitak.bte.mues.model.PickGroup;
import tr.gov.tubitak.bte.mues.session.ConfigurationFacade;
import tr.gov.tubitak.bte.mues.session.PickFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

/**
 * Abstract class of Parameters. Must be used at the backend only as it has no @Named annotation.
 * 
 */
public abstract class AbstractParameters implements Serializable {

    public static final String                        IMAGES_BASE_FOLDER         = "images.baseFolder";

    public static final String                        IMAGES_BASE_URL            = "images.baseURL";
    
    public static final String                        LAB_IMAGES_BASE_URL        = "labImages.baseURL";

    private static final long                         serialVersionUID           = 2779338159701650065L;

    protected static final Logger                     logger                     = LoggerFactory.getLogger(AbstractParameters.class);

    private static final int                          PREHISTORY_THRESHOLD       = 15000;

    /** Dolaşım kopya uzantısı */
    public static final String                        IMAGE_EXTENSION            = ".png";

    public static final String                        SLASH                      = "/";

    private static Map<PickGroup, Map<Integer, Pick>> picks;

    private static int                                prehistoryThreshold;

    @Inject
    private transient ConfigurationFacade             configurationFacade;

    @Inject
    private transient PickFacade                      secenekFacade;

    @Inject
    private ServletContext                            servletContext;

    private Map<String, String>                       params;

    /** Keep track of logged in users */
    private final transient Map<String, Subject>      loginMap                   = new HashMap<>();

    protected final List<String>                      mainPageGalleriaImageNames = new ArrayList<>();

    public AbstractParameters() {
        // parameterless constructor
    }

    @PostConstruct
    public void loadInitialParameters() {
        logger.debug("[loadInitialParameters] : Entered");

        this.populateConfigParams();
        this.populatePicks();

        for (int i = 1; i <= 26; i++) {
            this.mainPageGalleriaImageNames.add("img(" + i + ").JPG");
        }

        logger.info("[loadInitialParameters] : Parameters loaded from the database");

    }

    private synchronized void populateConfigParams() {
        this.params = this.configurationFacade.findAll().stream().collect(Collectors.toMap(Configuration::getClue, Configuration::getValue));

        try {
            // prehistory is negative by nature
            prehistoryThreshold = -Integer.parseInt(this.params.get("threshold.prehistory"));

        } catch (final NumberFormatException e) {
            logger.error("Değer tamsayı değil: {} {}", this.params.get("threshold.prehistory"), e.getMessage());
            prehistoryThreshold = -PREHISTORY_THRESHOLD;
        }
    }

    private synchronized void populatePicks() {
        final List<Pick> seceneks = this.secenekFacade.findActive();

        picks = seceneks.stream().collect(Collectors.groupingBy(Pick::getGrup, Collectors.toMap(Pick::getRank, x -> x)));
    }

    public void refreshParameters(final @Observes ConfigurationChangeEvent event) {
        if (logger.isInfoEnabled()) {
            logger.info("[refreshParameters] : {}", event.getParams());
        }
        this.params = event.getParams().stream().collect(Collectors.toMap(Configuration::getClue, Configuration::getValue));
    }

    public void refreshApplicationVariables(final @Observes SessionVariablesChangeEvent event) {
        if (logger.isInfoEnabled()) {
            logger.info("[refreshApplicationVariables] :{} ", event.getClass().getName());
        }
        this.populatePicks();
    }

    public Path generateFilePathViaFilename(final String fileName, final FolderType type) {
        return this.generateImagePath(fileName, type.getFolderPath());
    }

    public Path generateInventoryPath(final String fileName) {
        return this.generateImagePath(fileName, FolderType.INVENTORY.getFolderPath());
    }

    public Path generateImagePath(final String fileName, final String copyType) {
        final Path root = Paths.get(this.get(IMAGES_BASE_FOLDER) + File.separator + copyType);
        final Path relativeFolderHierarchy = Paths.get(fileName.substring(0, 1), fileName.substring(1, 2), fileName.substring(2, 3));
        final Path absoluteFolderHierarchy = root.resolve(relativeFolderHierarchy);
        return absoluteFolderHierarchy.resolve(fileName);
    }

    protected List<Pick> filterPickByName(final Map<Integer, Pick> picks, final String query) {
        return Optional.ofNullable(picks.values())
                       .orElse(Collections.emptyList())
                       .stream()
                       .filter(x -> x.getAd().toLowerCase().contains(query.toLowerCase()) || Optional.ofNullable(x.getAciklama()).orElse("").toLowerCase().contains(query.toLowerCase()))
                       .collect(Collectors.toList());
    }

    // getters ................................................................

    public String get(final String key) {
        return this.params.get(key);
    }

    public String get(final String key, final String defaultValue) {
        return this.params.get(key) != null ? this.params.get(key) : defaultValue;
    }

    public Properties getStartsWith(final String prefix) {
        final Properties prop = new Properties();
        this.params.entrySet().stream().filter(x -> x.getKey().startsWith(prefix)).forEach(x -> prop.setProperty(x.getKey(), x.getValue()));
        return prop;
    }

    /**
     * Don't use this method for now. We may not need thumbnails, we may use usage copies everywhere because usage copy size is small
     */
    // public String getImageThumbnailUrl() {
    // final String baseURL = this.params.get("images.baseURL");
    // if (baseURL.startsWith(slash)) {
    // return baseURL + THUMBNAIL + slash;
    // } else {
    // return baseURL + slash + THUMBNAIL + slash;
    // }
    // }

    public Path getAbsolutePath(final String filePath, final FolderType type) {
        return Paths.get(this.get(IMAGES_BASE_FOLDER), type.getFolderPath(), filePath);
    }

    public Path getAbsolutePath(final String filePath, final FolderType type, final String baseFolder) {
        return Paths.get(baseFolder, type.getFolderPath(), filePath);
    }

    public String getImageMainCopyUrl(final String url) {
        return this.getImageURL(url, FolderType.IMAGE_AK.getFolderPath());
    }

    public String getUsageCopyBaseUrl() {
        return this.params.get(IMAGES_BASE_URL) + FolderType.IMAGE_DK.getFolderPath() + SLASH;
    }

    public String getImageUsageCopyUrl(final String url) {
        return this.getImageURL(url, FolderType.IMAGE_DK.getFolderPath(), IMAGE_EXTENSION);
    }

    public String getImageTranscriptUrl(final String filePath) {
        return this.get(IMAGES_BASE_FOLDER) + File.separator + FolderType.TRANSCRIPT.getFolderPath() + File.separator + filePath;
    }

    public String getImageURL(final String url, final String copyType, final String extension) {
        return this.getImageURL(url, copyType) + extension;
    }

    public String getImageURL(final String url, final String copyType) {
        final String baseURL = this.params.get(IMAGES_BASE_URL);
        if (url.startsWith(SLASH)) {
            return baseURL + copyType + url;
        }
        return baseURL + copyType + SLASH + url;
    }

    // for the other modules
    public Path retrieveMainCopyURL(final String imageBaseFolder, final String url) {
        return this.getAbsolutePath(url, FolderType.IMAGE_AK, this.params.get(imageBaseFolder));
    }

    public Path retriveCopyURLWithFolderType(final String imageBaseFolder, final FolderType folderType, final String url) {
        return this.getAbsolutePath(url, folderType, this.params.get(imageBaseFolder));
    }

    public String retrieveImageUsageCopyURL(final String imageBaseURL, final String url) {
        final String baseURL = this.params.get(imageBaseURL);
        return this.retrieveImageURL(baseURL, url, FolderType.IMAGE_DK.getFolderPath(), IMAGE_EXTENSION);
    }

    public String retrieveImageURL(final String baseURL, final String url, final String copyType, final String extension) {
        return this.retrieveImageURL(baseURL, url, copyType) + extension;
    }

    public String retrieveImageURL(final String baseURL, final String url, final String copyType) {
        if (url.startsWith(SLASH)) {
            return baseURL + copyType + url;
        }
        return baseURL + copyType + SLASH + url;
    }

    public String getImageMainCopyPath() {
        return this.params.get(IMAGES_BASE_URL);
    }

    public String getHelpFilesPath() {
        return this.get("help.baseFolder", "\\\\10.1.37.70\\Paylasim\\mues\\mues-help-files\\");
    }

    public String getImageUsageCopyPath(final String filePath) {
        return this.get(IMAGES_BASE_FOLDER) + File.separator + FolderType.IMAGE_DK.getFolderPath() + File.separator + filePath + IMAGE_EXTENSION;
    }

    public String getImageByFolderType(final String filePath, final String folderType) {
        return this.get(IMAGES_BASE_FOLDER) + File.separator + folderType + File.separator + filePath;
    }

    public String getTempDir() {
        return this.servletContext.getAttribute(ServletContext.TEMPDIR).toString();
    }

    public Path generateMainCopyPath(final String string, final FolderType type) {
        return this.generateImagePath(string, type.getFolderPath());
    }

    public static Map<PickGroup, Map<Integer, Pick>> getPicks() {
        return picks;
    }

    public static Map<Integer, Pick> getPicksByGroupCode(final Integer number) {
        return picks.get(picks.keySet().stream().filter(x -> Objects.equals(x.getKod(), number)).findFirst().get());
    }

    public List<String> getMainPageGalleriaImageNames() {
        return this.mainPageGalleriaImageNames;
    }

    public static int getPrehistoryThreshold() {
        return prehistoryThreshold;
    }

    public String getParam(final String key) {
        return this.params.get(key);
    }

    public Map<String, Subject> getLoginMap() {
        return this.loginMap;
    }

}