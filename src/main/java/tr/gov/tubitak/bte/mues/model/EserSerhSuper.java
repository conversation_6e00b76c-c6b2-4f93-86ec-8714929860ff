package tr.gov.tubitak.bte.mues.model;

import java.util.Date;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

@Audited
@MappedSuperclass
public class EserSerhSuper extends AbstractEntity {

    private static final long serialVersionUID = 5262644200337308054L;

    @Size(max = 50)
    @Column(name = "olurSayi", length = 50)
    private String            olurSayi;

    @Temporal(TemporalType.DATE)
    @Column(name = "olurTarih")
    private Date              olurTarih;

    @Size(max = 500)
    @Column(name = "metin", length = 500)
    private String            metin;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "docPath", length = 250)
    private String            docPath;

    public EserSerhSuper() {
        // defualt constructor
    }

    // getters and setters ....................................................

    public String getMetin() {
        return this.metin;
    }

    public void setMetin(final String metin) {
        this.metin = metin;
    }

    public String getDocPath() {
        return this.docPath;
    }

    public void setDocPath(final String docPath) {
        this.docPath = docPath;
    }

    public String getOlurSayi() {
        return this.olurSayi;
    }

    public void setOlurSayi(final String olurSayi) {
        this.olurSayi = olurSayi;
    }

    public Date getOlurTarih() {
        return this.olurTarih;
    }

    public void setOlurTarih(final Date olurTarih) {
        this.olurTarih = olurTarih;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.metin).orElse("" + this.olurSayi + " Sayılı Şerh");
    }

}
