package tr.gov.tubitak.bte.mues.jsf;

import javax.enterprise.event.Event;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.jsf.event.ConfigurationChangeEvent;
import tr.gov.tubitak.bte.mues.model.Configuration;
import tr.gov.tubitak.bte.mues.session.ConfigurationFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

@Named
@ViewScoped
public class ConfigurationController extends AbstractController<Configuration> {

    private static final long               serialVersionUID = 5674238528961050396L;

    @Inject
    private ConfigurationFacade             facade;

    @Inject
    private Event<ConfigurationChangeEvent> event;

    public ConfigurationController() {
        super(Configuration.class);
    }

    @Override
    public ConfigurationFacade getFacade() {
        return this.facade;
    }

    @Override
    public DBOperationResult create() {
        final DBOperationResult result = super.create();
        this.fireConfigurationChangeEvent();
        return result;
    }

    @Override
    public DBOperationResult update() {
        final DBOperationResult result = super.update();
        this.fireConfigurationChangeEvent();
        return result;
    }

    @Override
    public DBOperationResult update(final String message) {
        final DBOperationResult result = super.update(message);
        this.fireConfigurationChangeEvent();
        return result;
    }

    @Override
    public void delete() {
        super.delete();
        this.fireConfigurationChangeEvent();
    }

    private void fireConfigurationChangeEvent() {
        final ConfigurationChangeEvent payload = new ConfigurationChangeEvent();
        payload.setParams(this.getFacade().findAll());
        this.event.fire(payload);
    }

}
