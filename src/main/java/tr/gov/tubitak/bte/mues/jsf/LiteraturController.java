package tr.gov.tubitak.bte.mues.jsf;

import javax.faces.context.FacesContext;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.shiro.SecurityUtils;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazyLiteraturDataModel;
import tr.gov.tubitak.bte.mues.session.LiteraturFacade;
import tr.gov.tubitak.bte.mues.session.LiteraturLazyLoadFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.enums.ApplicationType;

@Named
@ViewScoped
public class LiteraturController extends AbstractController<Literatur> implements SingleFileUploadable {

    private static final long serialVersionUID = -8049755954148177647L;

    @Inject
    private LiteraturFacade   facade;

    @Inject
    private SessionBean       sessionBean;

    @Inject
    private FileUploadHelper  fileUploadHelper;

    @Inject
    private MuesParameters    muesParameters;

    public LiteraturController() {
        super(Literatur.class);
    }

    @Inject
    private LiteraturLazyLoadFacade        literaturLazyLoadFacade;

    private transient DataModel<Literatur> lazyKaynakDataModel;

    @Override
    public void newRecord() {
        super.newRecord();

        if (this.sessionBean.getApplicationType().equals(ApplicationType.ENVANTER)) {
            this.getModel().setAddedBy(this.sessionBean.getCurrentUser().getPersonelView());
        }
        if (SecurityUtils.getSubject().hasRole("arastirmaci")) {
            this.getModel().setYazar(this.sessionBean.getCurrentUser().getTitle());
        }
    }

    public void loadDataTable(final String Suffix) {
        this.resetTable(Suffix);
        this.setLazyKaynakDataModel(new LazyLiteraturDataModel(this.literaturLazyLoadFacade));
    }

    public void resetTable(final String suffix) {
        final DataTable dataTable = ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent(":eser" + suffix + "Literatur:kaynakSecimiForm:kaynakTableId"));
        if (dataTable != null) {
            dataTable.reset();
        }
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setLiteratureFilePath((this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString()));
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getLiteratureFilePath() != null) {
            this.getModel().setLiteratureFilePath(this.muesParameters.writeMainCopyToFile(this.getModel().getLiteratureFilePath(), FolderType.OTHER));
        }
    }

    public boolean isDisabled(final AbstractEntity literatur) {

        if ((!SecurityUtils.getSubject().hasRole("arastirmaci")) || this.isNewMode()) {
            return false;
        } else if (SecurityUtils.getSubject().hasRole("arastirmaci")) {
            if (((Literatur) literatur).getAddedBy() != null) {
                return this.sessionBean.getCurrentUser().getPersonelView().equals(((Literatur) literatur).getAddedBy());
            } else {
                return true;
            }
        }
        return false;
    }

    // setter getter

    @Override
    public LiteraturFacade getFacade() {
        return this.facade;
    }

    public DataModel<Literatur> getLazyKaynakDataModel() {
        return this.lazyKaynakDataModel;
    }

    public void setLazyKaynakDataModel(final DataModel<Literatur> lazyKaynakDataModel) {
        this.lazyKaynakDataModel = lazyKaynakDataModel;
    }

}
