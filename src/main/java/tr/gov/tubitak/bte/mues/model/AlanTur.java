package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
@Entity
@Table(name = "ALAN_TUR")
public class AlanTur extends AlanTurSuper
{

    private static final long serialVersionUID = 2380899740091308381L;

    public AlanTur() {
        // blank constructor
    }

}
