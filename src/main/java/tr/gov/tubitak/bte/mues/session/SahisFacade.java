package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.persistence.NoResultException;

import tr.gov.tubitak.bte.mues.model.Sahis;

/**
 *
*
 */
@RequestScoped
public class SahisFacade extends AbstractFacade<Sahis> {

    /**
     * 
     */
    private static final long serialVersionUID = -6885344376559647011L;

    public SahisFacade() {
        super(Sahis.class);
    }

    public List<Sahis> filterUndeletedByName(final String query) {
        return this.em.createNamedQuery("Sahis.findSilinmemisByName", Sahis.class).setParameter("str", "%" + query + "%").getResultList();
    }

    public List<Sahis> filterByFullNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Sahis.findByFullNameAndAciklama", Sahis.class).setParameter("str", "%" + query.replaceAll("\\s+", "") + "%").getResultList();
    }

    public List<Sahis> filterByFullNameAndAciklamaPreventDuplicate(final String query, final List<Integer> excludedIds) {
        return this.em.createNamedQuery("Sahis.filterByFullNameAndAciklamaPreventDuplicate", Sahis.class).setParameter("str", "%" + query + "%").setParameter("ids", excludedIds).getResultList();
    }

    public Sahis findByTCNo(final String tcNo) {
        try {
            return this.em.createNamedQuery("Sahis.findByTCNo", Sahis.class).setParameter("tcNo", tcNo.trim()).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }

}
