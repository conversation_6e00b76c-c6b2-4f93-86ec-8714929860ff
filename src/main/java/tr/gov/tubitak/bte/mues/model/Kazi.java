package tr.gov.tubitak.bte.mues.model;

import java.math.BigDecimal;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "KAZI")
@NamedQuery(name = "Kazi.findEagerById", query = "SELECT k FROM Kazi k LEFT JOIN FETCH k.il LEFT JOIN FETCH k.ilce LEFT JOIN FETCH k.kaziTur WHERE k.id = :id")
@NamedQuery(name = "Kazi.findAll", query = "SELECT k FROM Kazi k LEFT JOIN FETCH k.il i LEFT JOIN FETCH k.ilce LEFT JOIN FETCH k.kaziTur ORDER BY k.silinmis, k.aktif DESC, i.ad")
@NamedQuery(name = "Kazi.findActive", query = "SELECT k FROM Kazi k LEFT JOIN FETCH k.il LEFT JOIN FETCH k.ilce LEFT JOIN FETCH k.kaziTur WHERE k.aktif = true AND k.silinmis = false ORDER BY k.ad")
@NamedQuery(name = "Kazi.findByNameAndAciklama", query = "SELECT k FROM Kazi k WHERE k.aktif = true AND k.silinmis = false AND (k.ad LIKE :str OR k.aciklama LIKE :str) ORDER BY k.ad, k.aciklama")
@NamedNativeQuery(name = "Kazi.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_HAREKET WHERE SILINMIS = 0 AND KAZI_ID = :id)")
public class Kazi extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -5901924270870308359L;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String            kod;

    @Size(max = 25)
    @Column(name = "KAZI_KODU", length = 25)
    private String            kaziKodu;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @Size(max = 50)
    @Column(name = "BELDE_ADI", length = 50)
    private String            beldeAdi;

    @Min(value = -90)
    @Max(value = 90)
    @Column(name = "ENLEM")
    private BigDecimal        enlem;

    @Min(value = -180)
    @Max(value = 180)
    @Column(name = "BOYLAM")
    private BigDecimal        boylam;

    @Size(max = 15)
    @Column(name = "pafta")
    private String            pafta;

    @Min(value = 0)
    @Max(value = 10000000)
    @Column(name = "ada")
    private Integer           ada;

    @Min(value = 0)
    @Max(value = 10000000)
    @Column(name = "parsel")
    private Integer           parsel;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                il;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ILCE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Ilce              ilce;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "KAZI_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private KaziTur           kaziTur;

    public Kazi() {
    }

    // getters and setters ....................................................

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getBeldeAdi() {
        return this.beldeAdi;
    }

    public void setBeldeAdi(final String beldeAdi) {
        this.beldeAdi = beldeAdi;
    }

    public String getKaziKodu() {
        return this.kaziKodu;
    }

    public void setKaziKodu(final String kaziKodu) {
        this.kaziKodu = kaziKodu;
    }

    public BigDecimal getEnlem() {
        return this.enlem;
    }

    public void setEnlem(final BigDecimal enlem) {
        this.enlem = enlem;
    }

    public BigDecimal getBoylam() {
        return this.boylam;
    }

    public void setBoylam(final BigDecimal boylam) {
        this.boylam = boylam;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public Ilce getIlce() {
        return this.ilce;
    }

    public void setIlce(final Ilce ilce) {
        this.ilce = ilce;
    }

    public KaziTur getKaziTur() {
        return this.kaziTur;
    }

    public void setKaziTur(final KaziTur kaziTur) {
        this.kaziTur = kaziTur;
    }

    public String getPafta() {
        return this.pafta;
    }

    public void setPafta(final String pafta) {
        this.pafta = pafta;
    }

    public Integer getAda() {
        return this.ada;
    }

    public void setAda(final Integer ada) {
        this.ada = ada;
    }

    public Integer getParsel() {
        return this.parsel;
    }

    public void setParsel(final Integer parsel) {
        this.parsel = parsel;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
