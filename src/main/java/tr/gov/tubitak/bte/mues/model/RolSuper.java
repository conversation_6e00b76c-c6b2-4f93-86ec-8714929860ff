package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "Rol.findEagerById", query = "SELECT r FROM Rol r WHERE r.id = :id")
// TODO: aynur.toparlak role by name and aciklama kullaniyoruz biz. zaten o da var.
@NamedQuery(name = "Rol.findByCode", query = "SELECT r FROM Rol r WHERE r.kod = :kod")
@NamedQuery(name = "Rol.findAll", query = "SELECT r FROM Rol r ORDER BY r.col<PERSON>rder, r.silin<PERSON>, r.aktif DESC, r.ad")
@NamedQuery(name = "Rol.findByNameAndRank", query = "SELECT r FROM Rol r WHERE r.ad LIKE :ad AND r.rank <= (SELECT MAX(r.rank) FROM Rol r WHERE r in (SELECT kbr.rol FROM KullaniciBirimRol kbr WHERE kbr.kullanici.id = :userId AND kbr.aktif = true AND kbr.silinmis = false)) AND r.aktif = true AND r.silinmis = false ORDER BY r.silinmis, r.aktif DESC, r.rank desc")
@NamedQuery(name = "Rol.findActive", query = "SELECT r FROM Rol r WHERE r.aktif = true AND r.silinmis = false ORDER BY r.colOrder")
public class RolSuper extends AbstractEntity implements DeleteValidatable {

    private static final long   serialVersionUID = 8324413649114320370L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String              ad;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String              kod;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String              aciklama;

    @Column(name = "SISTEM")
    private boolean             sistem;

    @Column(name = "RANK")
    private Integer             rank;

    @Column(name = "colOrder")
    private Integer             colOrder;

    @Column(name = "multiple")
    private boolean             multiple;

    @Column(name = "renderMuseum")
    private boolean             renderMuseum;

    @OneToMany(mappedBy = "rol")
    private Collection<RolIzin> rolIzinCollection;

    public RolSuper() {
        // blank constructor
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Collection<RolIzin> getRolIzinCollection() {
        return this.rolIzinCollection;
    }

    public void setRolIzinCollection(final Collection<RolIzin> rolIzinCollection) {
        this.rolIzinCollection = rolIzinCollection;
    }

    public boolean isSistem() {
        return this.sistem;
    }

    public void setSistem(final boolean sistem) {
        this.sistem = sistem;
    }

    public Integer getRank() {
        return this.rank;
    }

    public void setRank(final Integer rank) {
        this.rank = rank;
    }

    public Integer getColOrder() {
        return this.colOrder;
    }

    public void setColOrder(final Integer colOrder) {
        this.colOrder = colOrder;
    }

    public boolean isMultiple() {
        return this.multiple;
    }

    public void setMultiple(final boolean multiple) {
        this.multiple = multiple;
    }

    public boolean isRenderMuseum() {
        return this.renderMuseum;
    }

    public void setRenderMuseum(final boolean renderMuseum) {
        this.renderMuseum = renderMuseum;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
