package tr.gov.tubitak.bte.mues.jsf;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.EserProposalYayinLiteratur;
import tr.gov.tubitak.bte.mues.model.Literatur;
import tr.gov.tubitak.bte.mues.session.EserProposalYayinLiteraturFacade;

@Named
@ViewScoped
public class EserProposalYayinLiteraturController extends AbstractController<EserProposalYayinLiteratur> {

    private static final long                serialVersionUID = -8049755954148177647L;

    @Inject
    private EserProposalYayinLiteraturFacade facade;

    public EserProposalYayinLiteraturController() {
        super(EserProposalYayinLiteratur.class);
    }

    public void handleLiteraturSelect(final Literatur literatur) {
        this.getModel().setLiteratur(literatur);
    }

    // getters and setters ....................................................

    @Override
    public EserProposalYayinLiteraturFacade getFacade() {
        return this.facade;
    }

}
