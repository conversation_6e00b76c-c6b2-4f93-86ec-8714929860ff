package tr.gov.tubitak.bte.mues.model;

import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@MappedSuperclass
public class EserKeywordSuper extends AbstractEntity {

    private static final long serialVersionUID = 5262644200337308054L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "keywordId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Keyword           keyword;

    public EserKeywordSuper() {
        // default constructor.
    }

    // getters and setters ....................................................

    public Keyword getKeyword() {
        return this.keyword;
    }

    public void setKeyword(final Keyword keyword) {
        this.keyword = keyword;
    }

    @Override
    public String getTitle() {
        return null;
    }

}
