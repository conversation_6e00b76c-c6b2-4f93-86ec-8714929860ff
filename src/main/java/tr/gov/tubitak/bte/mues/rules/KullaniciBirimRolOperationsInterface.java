package tr.gov.tubitak.bte.mues.rules;

import java.util.List;
import tr.gov.tubitak.bte.mues.model.KullaniciBirimRol;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;

public interface KullaniciBirimRolOperationsInterface {
    DBOperationResult createKullaniciBirimRolDetails(KullaniciBirimRol model, List<AbstractEntity> entities);
    DBOperationResult updateKullaniciBirimRol(KullaniciBirimRol model, List<AbstractEntity> entities);
    String deleteKullaniciBirimRol(KullaniciBirimRol model);
    List<PersonelView> filterByFullNameFromPersonelPool(String query);
}
