package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import tr.gov.tubitak.bte.mues.model.EserZimmet;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Personel;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.util.ApplicationSpecificQueries;

/**
 *
*
 */
@RequestScoped
public class PersonelFacade extends AbstractFacade<Personel> {

    @Inject
    ApplicationSpecificQueries applicationSpecificQueries;

    public PersonelFacade() {
        super(Personel.class);
    }

    public List<Personel> filterByNonKullaniciAndName(final String query) {
        return this.em.createNamedQuery("Personel.findByNonKullaniciAndName", Personel.class).setParameter("str", "%" + query.replaceAll("\\s+", "") + "%").getResultList();
    }

    public List<Personel> findByNameAndMuseumDirectorate(final Integer muzeMudurlukId) {
        return this.em.createNamedQuery("Personel.findByMuseumDirectorate", Personel.class).setParameter("id", muzeMudurlukId).getResultList();
    }

    public List<Personel> findAllUndeletedPersoenelByNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Personel.findAllUndeletedPersoenelByNameAndAciklama", Personel.class).setParameter("str", "%" + query.replaceAll("\\s+", "") + "%").getResultList();
    }

    public List<Personel> filterByFullNameAndAciklama(final String query) {
        return this.em.createNamedQuery("Personel.findByFullNameAndAciklama", Personel.class).setParameter("str", "%" + query.replaceAll("\\s+", "") + "%").getResultList();
    }

    public List<Personel> findActivePersonnelWithDetail(final List<Integer> directorates) {
        return this.em.createNamedQuery("Personel.findActivePersonnelWithDetail", Personel.class).setParameter("directorates", directorates).getResultList();
    }

    public List<Personel> filterByFullNameAndAciklamaAndMuseumDirectorates(final String query, final List<Mudurluk> muzeMudurlux) {
        return this.em.createNamedQuery("Personel.findByFullNameAndAciklamaAndMuseumDirectorates", Personel.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("muzes", muzeMudurlux)
                      .getResultList();
    }

    public List<Personel> findAllFromDirectoratesExcludeIds(final String query, final List<Integer> ids, final List<Integer> directorates) {
        return this.em.createNamedQuery("Personel.findAllFromDirectoratesExcludeIds", Personel.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("ids", ids)
                      .setParameter("directorates", directorates)
                      .getResultList();
    }

    public List<Object[]> historyOfMuseumDirectorateByNativeQuery(final Integer personelId) {

        @SuppressWarnings("unchecked")
        final List<Object[]> objeListesi = this.getEM().createNativeQuery(this.applicationSpecificQueries.personelDirectorateHistoryQuery()).setParameter("personelId", personelId).getResultList();
        return objeListesi;
    }

    public List<Object[]> historyOfUnvanByNativeQuery(final Integer personelId) {

        @SuppressWarnings("unchecked")
        final List<Object[]> objeListesi = this.getEM().createNativeQuery(this.applicationSpecificQueries.unvanByHistoryNativeQuery()).setParameter("personelId", personelId).getResultList();
        return objeListesi;
    }

    public List<Object[]> historyOfKadroDurumuByNativeQuery(final Integer personelId) {

        @SuppressWarnings("unchecked")
        final List<Object[]> objeListesi = this.getEM().createNativeQuery(this.applicationSpecificQueries.kadroDurumuHistoryByNativeQuery()).setParameter("personelId", personelId).getResultList();
        return objeListesi;
    }

    public List<Object[]> historyOfMeslekByNativeQuery(final Integer personelId) {

        @SuppressWarnings("unchecked")
        final List<Object[]> objeListesi = this.getEM().createNativeQuery(this.applicationSpecificQueries.meslekHistoryByNativeQuery()).setParameter("personelId", personelId).getResultList();
        return objeListesi;
    }

    public List<PersonelView> filterPersonelPoolNoneThisAppUser(final String query, final Integer applicationType) {

        return this.em.createNamedQuery("Personel.filterPersonelPoolNoneThisAppUser", PersonelView.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("applicationType", applicationType)
                      .getResultList();

    }

    public List<Personel> findAllPersonnelPreventDuplicate(final String query, final List<Integer> excludedPersonel) {
        return this.em.createNamedQuery("Personel.findAllPersonnelPreventDuplicate", Personel.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("exp", excludedPersonel)
                      .getResultList();
    }

    public List<Personel> findAllPersonnelPreventDuplicateWithMuseum(final String query, final List<Integer> excludedPersonel, final Integer museumId) {
        return this.em.createNamedQuery("Personel.findAllPersonnelPreventDuplicateWithMuseum", Personel.class)
                      .setParameter("str", "%" + query.replaceAll("\\s+", "") + "%")
                      .setParameter("exp", excludedPersonel)
                      .setParameter("museumId", museumId)
                      .getResultList();
    }

    public List<Personel> findAllInspectors() {
        return this.em.createNamedQuery("Personel.findAllInspectors", Personel.class).getResultList();
    }

    public List<EserZimmet> checkBeforeUnauthorizing(final Integer id) {
        return this.em.createNamedQuery("EserZimmet.checkBeforeUnauthorizing", EserZimmet.class).setParameter("id", id).getResultList();
    }

}
