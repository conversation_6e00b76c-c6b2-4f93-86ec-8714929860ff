package tr.gov.tubitak.bte.mues.model;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@Entity
@Table(name = "Eser_Measure")
public class EserMeasure extends EserMeasureSuper {

    private static final long       serialVersionUID = -6747724162524312089L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Eser                    eser;


    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "annotationId", referencedColumnName = "ID")
    @OneToOne(fetch = FetchType.LAZY, cascade = { CascadeType.MERGE, CascadeType.PERSIST })
    private ArtifactPhotoAnnotation annotation;

    public EserMeasure() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Eser getEser() {
        return this.eser;
    }
   
    public void setEser(final Eser eser) {
        this.eser = eser;
    }

    public ArtifactPhotoAnnotation getAnnotation() {
        return this.annotation;
    }

    public void setAnnotation(final ArtifactPhotoAnnotation annotation) {
        this.annotation = annotation;
    }

}
