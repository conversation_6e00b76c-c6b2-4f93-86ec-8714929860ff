package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@NamedQuery(name = "Alan.findEagerById", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur JOIN FETCH a.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE a.id = :id")
@NamedQuery(name = "Alan.findByMuseumDirectorate", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur JOIN FETCH a.bina b JOIN FETCH b.bagliBirim bb WHERE a.alanTur.id IN (2,3,6) AND bb.mudurluk.id = :id AND a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedQuery(name = "Alan.findTeshirSalonuByMuseumDirectorate", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur JOIN FETCH a.bina b JOIN FETCH b.bagliBirim bb WHERE bb.mudurluk.id = :id AND a.aktif = true AND a.silinmis = false AND a.alanTur.id = 1 ORDER BY a.ad")
@NamedQuery(name = "Alan.findAll", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur JOIN FETCH a.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk m ORDER BY a.silinmis, a.aktif DESC, m.ad ASC")
@NamedQuery(name = "Alan.findByMudurluk", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur  JOIN FETCH a.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE bb.mudurluk in :muzeler ORDER BY a.silinmis, a.aktif DESC, a.ad")
@NamedQuery(name = "Alan.findActive", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur  JOIN FETCH a.bina b JOIN FETCH b.bagliBirim bb JOIN FETCH bb.mudurluk WHERE a.aktif = true AND a.silinmis = false ORDER BY a.ad")
@NamedQuery(name = "Alan.findByNameAndAciklamaAndBina", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur JOIN FETCH a.bina b JOIN FETCH b.bagliBirim WHERE a.aktif = true AND a.silinmis = false AND (a.ad LIKE :str OR a.aciklama LIKE :str) AND a.bina = :bina ORDER BY a.ad")
@NamedQuery(name = "Alan.filterByNameAndBinaPreventDuplicate", query = "SELECT a FROM Alan a JOIN FETCH a.alanTur WHERE a.id NOT IN :ids AND a.aktif = true AND a.silinmis = false AND (a.ad LIKE :str OR a.aciklama LIKE :str) AND a.bina = :bina ORDER BY a.ad")
@MappedSuperclass
public class AlanSuper extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 311096275077294371L;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String            kod;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    @JoinColumn(name = "ALAN_TUR_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private AlanTur           alanTur;

    @JoinColumn(name = "BINA_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Bina              bina;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "pdfPath", length = 250)
    private String            pdfPath;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "dwgPath", length = 250)
    private String            dwgPath;

    public AlanSuper() {
        // intentionally left blank
    }

    // getters and setters ....................................................

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public AlanTur getAlanTur() {
        return this.alanTur;
    }

    public void setAlanTur(final AlanTur alanTur) {
        this.alanTur = alanTur;
    }

    public Bina getBina() {
        return this.bina;
    }

    public void setBina(final Bina bina) {
        this.bina = bina;
    }

    public String getPdfPath() {
        return this.pdfPath;
    }

    public void setPdfPath(final String pdfPath) {
        this.pdfPath = pdfPath;
    }

    public String getDwgPath() {
        return this.dwgPath;
    }

    public void setDwgPath(final String dwgPath) {
        this.dwgPath = dwgPath;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return this.ad;
    }

}
