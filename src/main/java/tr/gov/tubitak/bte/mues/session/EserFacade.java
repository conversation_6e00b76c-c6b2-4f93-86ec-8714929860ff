package tr.gov.tubitak.bte.mues.session;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.NoResultException;
import javax.persistence.ParameterMode;
import javax.persistence.StoredProcedureQuery;
import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.AuditEvent;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.EserHareket;
import tr.gov.tubitak.bte.mues.model.EserProposal;
import tr.gov.tubitak.bte.mues.model.EserVersion;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Workflow;
import tr.gov.tubitak.bte.mues.model.mapping.LabRestorationView;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Named
@RequestScoped
public class EserFacade extends AbstractFacade<Eser> {
    @Inject
    AuditedEntityRepositoryImpl auditedEntityRepositoryImpl;

    public EserFacade() {
        super(Eser.class);
    }

    public Integer fetchNextPermanentId() {
        return (Integer) this.em.createNativeQuery("SELECT NEXT VALUE FOR InventoryId AS value1").getSingleResult();
    }

    public List<Eser> findByNameAndMuseumDirectorate(final Integer mudurlukId) {
        return this.getEM().createNamedQuery("Eser.findByNameAndMuseumDirectorate", Eser.class).setParameter("id", mudurlukId).getResultList();
    }

    public Eser getLastEserOfUserId(final Integer userId) {
        final List<Eser> resultList = this.getEM().createNamedQuery("Eser.getLastEserOfUserId", Eser.class).setParameter("userId", userId).getResultList();
        if (!resultList.isEmpty()) {
            return resultList.get(0);
        }
        return null;
    }

    public List<Eser> findByEnvanterNoAndMuseumDirectorate(final Integer mudurlukId, final String envanterNo) {
        return this.getEM().createNamedQuery("Eser.findByEnvanterNoAndMuseumDirectorate", Eser.class).setParameter("id", mudurlukId).setParameter("envNo", envanterNo).getResultList();
    }

    public Integer findNumberOfApprovedEsers() {
        return ((Integer) this.em.createNativeQuery("SELECT COUNT(*) FROM ESER WHERE VERSIYON = 1 AND AKTIF = 1 AND SILINMIS = 0").getSingleResult());
    }

    public Integer findCountOfApprovedEsersByMudurluk(final Integer mudurlukId) {
        return ((Integer) this.em.createNativeQuery("SELECT COUNT(e.ID) FROM ESER e LEFT JOIN Workflow w on e.ID = w.eserId "
                                                    + " WHERE w.review = 1 AND e.AKTIF = 1 AND e.SILINMIS = 0 AND w.mudurlukId =:mudurlukId")
                                 .setParameter("mudurlukId", mudurlukId)
                                 .getSingleResult());
    }

    public Integer findCountOfPendingEsersByMudurluk(final Integer mudurlukId) {
        return ((Integer) this.em.createNativeQuery("SELECT COUNT(e.ID) FROM ESER e LEFT JOIN Workflow w on e.ID = w.eserId "
                                                    + " WHERE w.review IN (:pendingList) AND w.review = 1 AND e.AKTIF = 1 AND e.SILINMIS = 0 AND w.mudurlukId =:mudurlukId")
                                 .setParameter("pendingList", EserVersion.getPendinglist())
                                 .setParameter("mudurlukId", mudurlukId)
                                 .getSingleResult());
    }

    public List<Eser> filterByNameAndMudurlukExcludeIds(final String query, final List<Mudurluk> muzeMudurluks, final List<Integer> eserIds) {
        return this.em.createNamedQuery("Eser.findByNameAndMudurlukExcludeIds", Eser.class)
                      .setParameter("ad", "%" + query + "%")
                      .setParameter("muzeler", muzeMudurluks)
                      .setParameter("ids", eserIds)
                      .getResultList();
    }

    public Workflow findByArtifact(final Eser artifact) {
        try {
            return this.getEM().createNamedQuery("Workflow.findByArtifact", Workflow.class).setParameter("eser", artifact).getSingleResult();
        } catch (final NoResultException e) {
            return null;
        }
    }

    // Envers history Function
    @SuppressWarnings("unchecked")
    public List<Object[]> history(final Eser eser, final Date latestApprovalTime) {
        if (latestApprovalTime != null) {
            return AuditReaderFactory.get(this.getEM())
                                     .createQuery()
                                     .forRevisionsOfEntity(Eser.class, false, true)
                                     .add(AuditEntity.id().eq(eser.getId()))
                                     .add(AuditEntity.revisionProperty("timestamp").gt(latestApprovalTime.getTime()))
                                     .add(AuditEntity.revisionProperty("revType").eq(AuditEvent.EserGuncelleme.getCode()))
                                     .addOrder(AuditEntity.revisionProperty("timestamp").asc())
                                     .setMaxResults(1)
                                     .getResultList();
        }
        return Collections.emptyList();
    }

    public Eser findApprovedArtifactByPermanentId(final Integer permanentId) {
        return this.getEM().createNamedQuery("Eser.findApprovedArtifactByPermanentId", Eser.class).setParameter("permanentId", permanentId).getResultList().stream().findFirst().orElse(null);
    }

    /***
     * Eseri eager sekilde (hareketler detaylarini ayrica) yukleyerek doner
     * 
     */
    public Eser findEagerByPermanentId(final Integer permanentId) {
        Eser eser;
        try {
            eser = this.getEM().createNamedQuery("Eser.findEagerByPermanentId", Eser.class).setParameter("permanentId", permanentId).getSingleResult();
            if (eser != null) {
                eser.getEserHarekets().addAll(this.getEM().createNamedQuery("EserHareket.findEagerByEserId", EserHareket.class).setParameter("id", eser.getId()).getResultList());
            }
        } catch (final NoResultException e) {
            eser = null;
        }
        return eser;
    }

    /***
     * Eseri eager sekilde (hareket detaylarini ayrica) yukleyerek doner
     * 
     * Eser.findEagerById sorgsusunda EserHareket detaylari ile ilgili LEFT JOIN'ler yapinca sorgunun yanit suresi anlayamadigimiz sekilde 2 dk'yi
     * bulabiliyordu o yuzden override ederek bunu 2 sorguyla yukleyecek sekilde ayirdik
     */

    @Override
    public Eser findEagerById(final Integer id) {
        try {
            final Eser eser = this.em.createNamedQuery(this.getEntityClass().getSimpleName() + ".findEagerById", this.getEntityClass()).setParameter("id", id).getSingleResult();
            if (eser != null) {
                eser.getEserHarekets().addAll(this.getEM().createNamedQuery("EserHareket.findEagerByEserId", EserHareket.class).setParameter("id", id).getResultList());
            }
            return eser;

        } catch (final NoResultException nre) {
            MuesUtil.showMessage("Nesne bulunamadı", "Sorgulanan nesne: " + id, FacesMessage.SEVERITY_WARN);
            this.logger.debug("Sorgulanan nesne bulunamadı {}", nre.getMessage());

        }
        return null;

    }

    @Transactional(value = TxType.REQUIRED)
    public Eser showDetailHistory(final Object[] item) {

        return this.auditedEntityRepositoryImpl.showDetailHistory(item);

    }

    @Transactional
    public DBOperationResult transferEserProposalToEserTable(final Integer kullaniciId, final EserProposal eserProposal, final Workflow tempWorkflow) {

        final Integer epEserId = eserProposal.getId();
        final List<AbstractEntity> entities = new ArrayList<>(2);

        final StoredProcedureQuery storedProcedure = this.em.createStoredProcedureQuery("copyEserProposalTableToEser");
        // set parameters
        storedProcedure.registerStoredProcedureParameter("EP_ESER_ID", Integer.class, ParameterMode.IN);
        storedProcedure.registerStoredProcedureParameter("KULLANICI_ID", Integer.class, ParameterMode.IN);
        storedProcedure.registerStoredProcedureParameter("ID", Integer.class, ParameterMode.OUT);
        storedProcedure.setParameter("EP_ESER_ID", epEserId);
        storedProcedure.setParameter("KULLANICI_ID", kullaniciId);
        // execute SP
        storedProcedure.execute();

        // get result
        final Integer eserId = (Integer) storedProcedure.getOutputParameterValue("ID");

        final Eser eser = this.findEagerById(eserId);

        eser.setUpdateInProgress(true);
        eser.setVersiyon(tempWorkflow.getReview());
        eser.setDateCreated(new Date());
        eser.setCreateSessionId(MuesUtil.fetchSessionId());

        tempWorkflow.setArtifact(eser);

        eserProposal.setSilinmis(true);
        entities.add(eserProposal);
        entities.add(eser);
        entities.add(tempWorkflow);
        final DBOperationResult dbOperationResult = this.update(entities);
        if (dbOperationResult.isSuccess()) {

            return DBOperationResult.success(" G." + eserId);
        } else {
            return DBOperationResult.failure(dbOperationResult.getMessage());
        }
    }

    public List<Object[]> historyOfEnvanterNoByNativeQuery(final Integer eserId) {
        final String query = "SELECT DISTINCT m.AD, e.ENVANTER_NO "
                             + "FROM audit_ESER e "
                             + "LEFT JOIN audit_Workflow aw ON aw.eserId = e.ID "
                             + "LEFT JOIN MUZE_MUDURLUGU m ON m.ID = aw.mudurlukId "
                             + "WHERE e.ID = :eserId "
                             + "AND e.ENVANTER_NO IS NOT NULL AND m.ID IS NOT NULL";
        @SuppressWarnings("unchecked")
        final List<Object[]> objeListesi = this.getEM().createNativeQuery(query).setParameter("eserId", eserId).getResultList();
        return objeListesi;
    }

    @SuppressWarnings("unchecked")
    public List<LabRestorationView> fetchVouchersById(final Integer eserId) {
        if (eserId != null) {
            final String query = "SELECT rv.eserId, rv.aktif, rv.silinmis, rv.aciklama, rv.voucherNo, rv.docPath, rv.laboratuvarMudurlugu, rv.talepId, rv.envanterNo, rv.uzman, rv.id, rv.artifactId  FROM Lab_RestorationView rv WHERE rv.eserId = :eserId";
            return this.em.createNativeQuery(query, "LabRestorationView").setParameter("eserId", eserId).getResultList();
        }
        return null;
    }
    
    public Integer checkImplementation(final Integer eserId) {
        return ((Integer) this.em.createNativeQuery("IF EXISTS (SELECT 1 FROM SecurityImplementation WHERE eserId = :eserId) BEGIN SELECT 1 END ELSE BEGIN SELECT 0 END ")
                                 .setParameter("eserId", eserId)
                                 .getSingleResult());
    }

}
