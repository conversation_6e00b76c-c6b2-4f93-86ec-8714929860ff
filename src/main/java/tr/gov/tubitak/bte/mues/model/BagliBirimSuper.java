package tr.gov.tubitak.bte.mues.model;

import java.math.BigDecimal;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
*
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "BagliBirim.findEagerById", query = "SELECT b FROM BagliBirim b LEFT JOIN FETCH b.il LEFT JOIN FETCH b.ilce LEFT JOIN FETCH b.mudurluk LEFT JOIN FETCH b.bagliBirimBagliBirimAltTurs bat LEFT JOIN FETCH bat.bagliBirim LEFT JOIN FETCH bat.bagliBirimAltTur batb LEFT JOIN FETCH batb.bagliBirimTur WHERE b.id = :id")
@NamedQuery(name = "BagliBirim.findVisibleMuseums", query = "SELECT b FROM BagliBirim b LEFT JOIN FETCH b.il LEFT JOIN FETCH b.ilce LEFT JOIN FETCH b.mudurluk m LEFT JOIN FETCH b.bagliBirimBagliBirimAltTurs bat LEFT JOIN FETCH bat.bagliBirim LEFT JOIN FETCH bat.bagliBirimAltTur batb LEFT JOIN FETCH batb.bagliBirimTur WHERE m.isVisibleFromMap = 1")
@NamedQuery(name = "BagliBirim.findAll", query = "SELECT DISTINCT b FROM BagliBirim b LEFT JOIN FETCH b.il LEFT JOIN FETCH b.ilce LEFT JOIN FETCH b.mudurluk m LEFT JOIN FETCH b.bagliBirimBagliBirimAltTurs bb LEFT JOIN FETCH bb.bagliBirimAltTur bba LEFT JOIN FETCH bba.bagliBirimTur ORDER BY b.silinmis, b.aktif DESC")
@NamedQuery(name = "BagliBirim.findByMudurluk", query = "SELECT DISTINCT b FROM BagliBirim b LEFT JOIN FETCH b.il LEFT JOIN FETCH b.ilce LEFT JOIN FETCH b.mudurluk m LEFT JOIN FETCH b.bagliBirimBagliBirimAltTurs bb LEFT JOIN FETCH bb.bagliBirimAltTur bba LEFT JOIN FETCH bba.bagliBirimTur WHERE b.mudurluk in :muzeler ORDER BY b.silinmis, b.aktif DESC, m.ad ASC")
@NamedQuery(name = "BagliBirim.findByMudurlukId", query = "SELECT DISTINCT b FROM BagliBirim b LEFT JOIN FETCH b.mudurluk m WHERE m.id = :mudurlukId ORDER BY b.kod DESC")
@NamedQuery(name = "BagliBirim.findActive", query = "SELECT b FROM BagliBirim b LEFT JOIN FETCH b.il LEFT JOIN FETCH b.ilce LEFT JOIN FETCH b.mudurluk LEFT JOIN FETCH b.bagliBirimBagliBirimAltTurs bbt LEFT JOIN FETCH bbt.bagliBirimAltTur bat LEFT JOIN FETCH bat.bagliBirimTur WHERE b.aktif = true AND b.silinmis = false ORDER BY b.ad")
@NamedQuery(name = "BagliBirim.findByNameAndAciklamaAndMudurluk", query = "SELECT b FROM BagliBirim b WHERE b.aktif = true AND b.silinmis = false AND (b.ad LIKE :str OR b.aciklama LIKE :str) AND b.mudurluk = :muze ORDER BY b.ad")
public class BagliBirimSuper extends AbstractEntity implements DeleteValidatable {

    private static final long               serialVersionUID = -2348343102784137703L;

    @Size(max = 50)
    @Column(name = "KOD", length = 50)
    private String                          kod;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String                          ad;

    @Size(max = 150)
    @Column(name = "ADRES", length = 150)
    private String                          adres;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_1", length = 25)
    private String                          telefon1;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_2", length = 25)
    private String                          telefon2;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_3", length = 25)
    private String                          telefon3;

    @Size(max = 25)
    @Column(name = "FAKS_1", length = 25)
    private String                          faks1;

    @Size(max = 25)
    @Column(name = "FAKS_2", length = 25)
    private String                          faks2;

    @Pattern(regexp = "^[a-zA-Z0-9_\\.+-]+", message = "Geçerli bir e-posta giriniz!")
    @Size(max = 50)
    @Column(name = "EPOSTA_KURUMSAL", length = 50)
    private String                          epostaKurumsal;

    @Size(max = 50)
    @Column(name = "WEB_SAYFASI", length = 50)
    private String                          webAdres;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "FOTOGRAF_PATH", length = 250)
    private String                          fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                          aciklama;

    @Min(value = -90)
    @Max(value = 90)
    @Column(name = "ENLEM")
    private BigDecimal                      enlem;

    @Min(value = -180)
    @Max(value = 180)
    @Column(name = "BOYLAM")
    private BigDecimal                      boylam;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "pdfPath", length = 250)
    private String                          pdfPath;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "dwgPath", length = 250)
    private String                          dwgPath;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "IL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Il                              il;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "ILCE_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Ilce                            ilce;

    @JoinColumn(name = "MUZE_MUDURLUGU_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Mudurluk                        mudurluk;

    @OneToMany(mappedBy = "bagliBirim", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<BagliBirimBagliBirimAltTur> bagliBirimBagliBirimAltTurs;

    public BagliBirimSuper() {
        // Blank Constructor
    }

    // getters and setters ....................................................

    public String getKod() {
        return this.kod;
    }

    public void setKod(final String kod) {
        this.kod = kod;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAdres() {
        return this.adres;
    }

    public void setAdres(final String adres) {
        this.adres = adres;
    }

    public String getTelefon1() {
        return this.telefon1;
    }

    public void setTelefon1(final String telefon1) {
        this.telefon1 = telefon1;
    }

    public String getTelefon2() {
        return this.telefon2;
    }

    public void setTelefon2(final String telefon2) {
        this.telefon2 = telefon2;
    }

    public String getTelefon3() {
        return this.telefon3;
    }

    public void setTelefon3(final String telefon3) {
        this.telefon3 = telefon3;
    }

    public String getFaks1() {
        return this.faks1;
    }

    public void setFaks1(final String faks1) {
        this.faks1 = faks1;
    }

    public String getFaks2() {
        return this.faks2;
    }

    public void setFaks2(final String faks2) {
        this.faks2 = faks2;
    }

    public String getEpostaKurumsal() {
        return this.epostaKurumsal;
    }

    public void setEpostaKurumsal(final String epostaKurumsal) {
        if (epostaKurumsal != null) {
            this.epostaKurumsal = epostaKurumsal.toLowerCase();
        }
    }

    public String getWebAdres() {
        return this.webAdres;
    }

    public void setWebAdres(final String webAdres) {
        this.webAdres = webAdres;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public BigDecimal getEnlem() {
        return this.enlem;
    }

    public void setEnlem(final BigDecimal enlem) {
        this.enlem = enlem;
    }

    public BigDecimal getBoylam() {
        return this.boylam;
    }

    public void setBoylam(final BigDecimal boylam) {
        this.boylam = boylam;
    }

    public String getPdfPath() {
        return this.pdfPath;
    }

    public void setPdfPath(final String pdfPath) {
        this.pdfPath = pdfPath;
    }

    public String getDwgPath() {
        return this.dwgPath;
    }

    public void setDwgPath(final String dwgPath) {
        this.dwgPath = dwgPath;
    }

    public Il getIl() {
        return this.il;
    }

    public void setIl(final Il il) {
        this.il = il;
    }

    public Ilce getIlce() {
        return this.ilce;
    }

    public void setIlce(final Ilce ilce) {
        this.ilce = ilce;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Set<BagliBirimBagliBirimAltTur> getBagliBirimBagliBirimAltTurs() {
        return this.bagliBirimBagliBirimAltTurs;
    }

    public void setBagliBirimBagliBirimAltTurs(final Set<BagliBirimBagliBirimAltTur> bagliBirimBagliBirimAltTurs) {
        this.bagliBirimBagliBirimAltTurs = bagliBirimBagliBirimAltTurs;
    }

    public String getBagliBirimAltTurTitles() {
        final StringJoiner sj = new StringJoiner(", ");
        this.bagliBirimBagliBirimAltTurs.stream().forEach(x -> sj.add(x.getBagliBirimAltTur().getAd()));
        return sj.toString();
    }

    public String getBagliBirimTurTitles() {
        final StringJoiner sj = new StringJoiner(", ");
        this.bagliBirimBagliBirimAltTurs.stream().forEach(x -> sj.add(x.getBagliBirimAltTur().getBagliBirimTur().getAd()));
        return sj.toString();
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Stream.of(this.ad, MuesUtil.surroundWithParanthesis(this.kod)).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }

}
