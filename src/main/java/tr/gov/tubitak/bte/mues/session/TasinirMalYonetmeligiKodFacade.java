package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.TasinirMalYonetmeligiKod;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class TasinirMalYonetmeligiKodFacade extends AbstractFacade<TasinirMalYonetmeligiKod> {

    public TasinirMalYonetmeligiKodFacade() {
        super(TasinirMalYonetmeligiKod.class);
    }

    public List<TasinirMalYonetmeligiKod> findByNameCodeAndAciklama(final String query) {
        return this.em.createNamedQuery("TasinirMalYonetmeligiKod.findByNameCodeAndAciklama", TasinirMalYonetmeligiKod.class)
                      .setParameter("str", "%" + query + "%")
                      .getResultList();
    }
    
}
