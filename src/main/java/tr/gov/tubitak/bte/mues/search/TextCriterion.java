package tr.gov.tubitak.bte.mues.search;

import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

/**
 * <PERSON>in tabanlı kriterleri tutan sınıf.
 */
@Entity
@DiscriminatorValue("1")
public class TextCriterion extends AbstractSimpleCriterion {

    private static final long serialVersionUID = 1730516269129283067L;

    @Column(name = "textValue1")
    private String            firstValue;

    @Column(name = "textValue2")
    private String            secondValue;

    /**
     * Yapıcı metot.
     */
    public TextCriterion() {
    }

    public TextCriterion(final Integer id) {
        super(id);
    }

    @Override
    @Transient
    public String getFirstValue() {
        return this.firstValue;
    }

    @Override
    @Transient
    public String getSecondValue() {
        return this.secondValue;
    }

    @Override
    public ICriterion setModel(final CriterionModel model) {
        super.setSuperModel(model);
        this.firstValue = model.getTextValue1();
        this.secondValue = model.getTextValue2();
        return this;
    }

    @Override
    @Transient
    public String getFirstValueText() {
        return SearchConstants.QUOTE + this.firstValue + SearchConstants.QUOTE;
    }

    @Override
    @Transient
    public String getSecondValueText() {
        return SearchConstants.QUOTE + this.secondValue + SearchConstants.QUOTE;
    }

    public String getTextValue1() {
        return this.firstValue;
    }

    public void setTextValue1(final String textValue1) {
        this.firstValue = textValue1;
    }

    public String getTextValue2() {
        return this.secondValue;
    }

    public void setTextValue2(final String textValue2) {
        this.secondValue = textValue2;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.firstValue, this.secondValue);
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final TextCriterion other = (TextCriterion) obj;
        return Objects.equals(this.firstValue, other.firstValue) && Objects.equals(this.secondValue, other.secondValue);
    }

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }

}
