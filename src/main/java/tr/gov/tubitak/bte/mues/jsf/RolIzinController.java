package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.RowEditEvent;

import tr.gov.tubitak.bte.mues.model.Izin;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.session.IzinFacade;
import tr.gov.tubitak.bte.mues.session.RolFacade;
import tr.gov.tubitak.bte.mues.session.RolIzinFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class RolIzinController implements Serializable
{

    private static final long            serialVersionUID = 3762621029220009318L;

    @Inject
    private transient RolIzinFacade      rolIzinFacade;

    @Inject
    private transient RolFacade          rolFacade;

    @Inject
    private transient IzinFacade         izinFacade;

    private List<Rol>                    rolList;

    private List<Izin>                   izinList;

    private Map<Izin, Map<Rol, Boolean>> izinRolMap;

    public RolIzinController() {
        // default Constructor
    }

    @PostConstruct
    public void init() {
        this.izinList = this.izinFacade.findActive();
        this.rolList = this.rolFacade.findActive();
        this.izinRolMap = this.rolIzinFacade.fetchAllRolIzin();
    }

    public void onRowEdit(final RowEditEvent<Izin> event) {
        final Izin izin = event.getObject();
        final boolean result = this.rolIzinFacade.updateIzinRols(izin, this.izinRolMap.get(izin));
        if (!result) {
            MuesUtil.showMessage("Bir hata oluştu", "İşleminiz gerçekleştirilemedi!", FacesMessage.SEVERITY_ERROR);
        }
    }

    // getters and setters ....................................................

    public List<Rol> getRolList() {
        return this.rolList;
    }

    public void setRolList(final List<Rol> rolList) {
        this.rolList = rolList;
    }

    public List<Izin> getIzinList() {
        return this.izinList;
    }

    public void setIzinList(final List<Izin> izinList) {
        this.izinList = izinList;
    }

    public Map<Izin, Map<Rol, Boolean>> getIzinRolMap() {
        return this.izinRolMap;
    }

    public void setIzinRolMap(final Map<Izin, Map<Rol, Boolean>> izinRolMap) {
        this.izinRolMap = izinRolMap;
    }

    public Boolean getResultAsBoolean(final Izin izin, final Rol rol) {
        return this.izinRolMap.get(izin).get(rol);
    }

}
