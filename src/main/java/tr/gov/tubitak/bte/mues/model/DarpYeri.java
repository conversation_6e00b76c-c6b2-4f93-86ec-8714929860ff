package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "DarpYeri")
@NamedQuery(name = "DarpYeri.findEagerById", query = "SELECT d FROM DarpYeri d WHERE d.id = :id")
@NamedQuery(name = "DarpYeri.findAll", query = "SELECT d FROM DarpYeri d ORDER BY d.silinmis, d.aktif DESC, d.ad")
@NamedQuery(name = "DarpYeri.findActive", query = "SELECT d FROM DarpYeri d WHERE d.aktif = true AND d.silinmis = false ORDER BY d.ad")
@NamedQuery(name = "DarpYeri.findByNameAndAciklama", query = "SELECT d FROM DarpYeri d WHERE d.aktif = true AND d.silinmis = false AND (d.ad LIKE :str OR d.aciklama LIKE :str) ORDER BY d.ad, d.aciklama")
@NamedNativeQuery(name = "DarpYeri.validateBeforeDelete", query = "SELECT (SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END FROM ESER WHERE SILINMIS = 0 AND darpYeriId = :id)")
public class DarpYeri extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 5938613886497748588L;

    @JoinColumn(name = "uygarlik", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Uygarlik          uygarlik;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public DarpYeri() {
        // intentially left blank
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Uygarlik getUygarlik() {
        return this.uygarlik;
    }

    public void setUygarlik(final Uygarlik uygarlik) {
        this.uygarlik = uygarlik;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
