package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

@Entity
@Table(name = "EP_Eser_Atolye")
@NamedQuery(name = "EserProposalAtolye.findEagerById", query = "SELECT x FROM EserProposalAtolye x LEFT JOIN FETCH x.eser LEFT JOIN FETCH x.atolye WHERE x.id = :id")
@NamedQuery(name = "EserProposalAtolye.findAll", query = "SELECT a FROM EserProposalAtolye a")
@NamedQuery(name = "EserProposalAtolye.findActive", query = "SELECT a FROM EserProposalAtolye a WHERE a.aktif = true AND a.silinmis = false")

public class EserProposalAtolye extends EserAtolyeSuper {

    private static final long serialVersionUID = 5222644200337308054L;

    @JoinColumn(name = "eserId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private EserProposal      eser;

    public EserProposalAtolye() {
    }

    // getters and setters ....................................................

    public EserProposal getEser() {
        return this.eser;
    }

    public void setEser(final EserProposal eser) {
        this.eser = eser;
    }

}
