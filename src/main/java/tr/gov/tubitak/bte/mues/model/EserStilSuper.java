package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

@Audited
@MappedSuperclass
public class EserStilSuper extends AbstractEntity {

    private static final long serialVersionUID = 5262644200337308054L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "styleId", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Stil              stil;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public EserStilSuper() {
    }

    // getters and setters ....................................................

    public Stil getStil() {
        return this.stil;
    }

    public void setStil(final Stil style) {
        this.stil = style;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.stil.getAd()).orElse("" + this.getId());
    }

}
