package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.shaded.json.JSONArray;
import org.primefaces.shaded.json.JSONObject;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardCharacter;
import tr.gov.tubitak.bte.mues.model.VirtualKeyboardLanguage;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardCharacterFacade;
import tr.gov.tubitak.bte.mues.session.VirtualKeyboardLanguageFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class VirtualKeyboardCharacterController extends AbstractController<VirtualKeyboardCharacter> {

    private static final long              serialVersionUID = 5315937074953322347L;

    @Inject
    private VirtualKeyboardCharacterFacade facade;

    @Inject
    private VirtualKeyboardLanguageFacade  virtualKeyboardLanguageFacade;

    private Integer                        languageId;

    private String                         pageTitle;

    private String                         selectedChar;

    private List<VirtualKeyboardCharacter> virtKeyCharList;

    public VirtualKeyboardCharacterController() {
        super(VirtualKeyboardCharacter.class);
    }

    public void assignTitle() {
        final VirtualKeyboardLanguage language = this.virtualKeyboardLanguageFacade.findEagerById(this.languageId);
        this.setPageTitle(language.getDil().getAd());
    }

    @Transactional
    public void createKeyboard() throws IOException {

        final String jsonStr = MuesUtil.getRequestParameter("jsonData");
        final JSONObject jsonObj = new JSONObject(jsonStr);
        final JSONArray jsonArr = jsonObj.getJSONArray("chars");
        final JSONArray jsonArr2 = jsonObj.getJSONArray("latins");

        final List<AbstractEntity> entities = new ArrayList<>();

        final VirtualKeyboardLanguage language = this.virtualKeyboardLanguageFacade.findById(this.languageId);

        for (int i = 0, size = jsonArr.length(); i < size; i++) {
            final VirtualKeyboardCharacter vChar = new VirtualKeyboardCharacter();
            vChar.setAktif(true);
            vChar.setSilinmis(false);
            vChar.setBoardOrder(i);

            vChar.setVirtualKeyboardLanguage(language);

            if (((String) jsonArr.get(i)).equals("&amp;")) {
                vChar.setCharacter("&");
                vChar.setLatin("&");
            } else if (((String) jsonArr.get(i)).equals("&lt;")) {
                vChar.setCharacter("<");
                vChar.setLatin("<");
            } else {
                vChar.setCharacter((String) jsonArr.get(i));
                vChar.setLatin((String) jsonArr2.get(i));
            }

            entities.add(vChar);
        }

        this.facade.deleteCharsByLanguage(this.languageId);
        this.facade.create(entities);
        PrimeFaces.current().executeScript("PF('growlMessageWidget').renderMessage({'summary':" + "'Kayıt Başarılı !'" + ", 'detail':" + "'Sanal Klavye Kaydedildi.'" + ", 'severity':'info'})");

    }

    public String backToPreviousPage() {
        return "/liste/sanal-klavye?faces-redirect=true";
    }

    public void dataProcess() {
        this.virtKeyCharList = this.facade.findByLanguageId(this.languageId);
        this.virtKeyCharList.stream().filter(letter -> letter.getLatin() == null).forEach(letter -> letter.setLatin(letter.getCharacter()));
    }

    public Integer getLanguageId() {
        return this.languageId;
    }

    public void setLanguageId(final Integer languageId) {
        if (languageId == null) {
            return;
        }
        if ((this.languageId == null) || !Objects.equals(this.languageId, languageId)) {
            this.languageId = languageId;
            this.assignTitle();
            this.dataProcess();
        }
    }

    @Override
    public VirtualKeyboardCharacterFacade getFacade() {
        return this.facade;
    }

    public String getPageTitle() {
        return this.pageTitle;
    }

    public void setPageTitle(final String pageTitle) {
        this.pageTitle = pageTitle;
    }

    public List<VirtualKeyboardCharacter> getVirtKeyCharList() {
        return this.virtKeyCharList;
    }

    public void setVirtKeyCharList(final List<VirtualKeyboardCharacter> virtKeyCharList) {
        this.virtKeyCharList = virtKeyCharList;
    }

    public String getSelectedChar() {
        return this.selectedChar;
    }

    public void setSelectedChar(final String selectedChar) {
        this.selectedChar = selectedChar;
    }

}