package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import tr.gov.tubitak.bte.mues.model.Alan;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.util.ApplicationSpecificQueries;

/**
 *
*
 */
@RequestScoped
public class AlanFacade extends AbstractFacade<Alan> {

    @Inject
    ApplicationSpecificQueries applicationSpecificQueries;

    public AlanFacade() {
        super(Alan.class);
    }

    public List<Alan> findByMudurluk(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("Alan.findByMudurluk", Alan.class).setParameter("muzeler", muzelist).getResultList();
    }

    public List<Alan> findByNameAndMuseumDirectorate(final Integer id) {
        return this.em.createNamedQuery("Alan.findByMuseumDirectorate", Alan.class).setParameter("id", id).getResultList();
    }

    public List<Alan> findTeshirSalonuByNameAndMuseumDirectorate(final Integer id) {
        return this.em.createNamedQuery("Alan.findTeshirSalonuByMuseumDirectorate", Alan.class).setParameter("id", id).getResultList();
    }

    public void toggleActiveSelfAndDescendants(final Alan alan) {
        this.getEM().createNativeQuery(this.applicationSpecificQueries.activeSelfDescendantsAlan()).setParameter(1, !alan.getAktif()).setParameter(2, alan.getId()).executeUpdate();
    }

}
