package tr.gov.tubitak.bte.mues.jsf.util;

import java.util.HashMap;
import java.util.Map;

import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.faces.convert.Converter;
import javax.faces.convert.FacesConverter;

import tr.gov.tubitak.bte.mues.model.Personel;

@FacesConverter("personelViewConverter")
public class PersonelViewConverter implements Converter<Personel> {

    private static final String KEY   = "tr.gov.tubitak.bte.mues.jsf.util.PersonelViewConverter";

    private static final String EMPTY = "";

    private Map<String, Object> getViewMap(final FacesContext context) {
        final Map<String, Object> viewMap = context.getViewRoot().getViewMap();
        @SuppressWarnings({ "unchecked", "rawtypes" })
        Map<String, Object> idMap = (Map) viewMap.get(KEY);
        if (idMap == null) {
            idMap = new HashMap<>();
            viewMap.put(KEY, idMap);
        }
        return idMap;
    }

    @Override
    public Personel getAsObject(final FacesContext context, final UIComponent c, final String value) {
        if ((value == null) || value.isEmpty()) {
            return null;
        }

        return (Personel) this.getViewMap(context).get(value);
    }

    @Override
    public String getAsString(final FacesContext context, final UIComponent c, final Personel value) {
        if (value == null) {
            return EMPTY;
        }
        final String id = value.hashCode() + value.getClass().getSimpleName();
        this.getViewMap(context).put(id, value);
        return id;
    }

}
