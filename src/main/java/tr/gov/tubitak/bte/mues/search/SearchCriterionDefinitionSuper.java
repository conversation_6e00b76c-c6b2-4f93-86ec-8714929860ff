
package tr.gov.tubitak.bte.mues.search;

import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;

import org.hibernate.envers.Audited;

import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Kullanici;

/**
 * The persistent class for the SearchDefinition database table.
 */
@Audited
@MappedSuperclass
@NamedQuery(name = "SearchCriterionDefinition.findAll", query = "select distinct c from SearchCriterionDefinition c LEFT JOIN FETCH c.criteriaList cl  LEFT JOIN FETCH cl.metadata LEFT JOIN FETCH c.user ")
@NamedQuery(name = "SearchCriterionDefinition.findEagerById", query = "select distinct c from SearchCriterionDefinition c LEFT JOIN FETCH c.criteriaList cl  LEFT JOIN FETCH cl.metadata LEFT JOIN FETCH c.columns cc  LEFT JOIN FETCH  cc.metadata LEFT JOIN FETCH c.user where c.id = :id ")
public class SearchCriterionDefinitionSuper extends AbstractEntity {

    private static final long            serialVersionUID = -2731796850711780715L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId")
    private Kullanici                    user;

    @Column(name = "name")
    private String                       name;

    @Column(name = "description")
    private String                       aciklama;

    @OneToMany(mappedBy = "searchSearchCriterionDefinition", fetch = FetchType.LAZY, cascade = { CascadeType.ALL }, targetEntity = AbstractCriterion.class)
    private Set<ICriterion>              criteriaList;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "searchCriterionDefinition", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID ASC")
    private Set<SearchDefinitionColumns> columns;

    @Column(name = "logicalOperator")
    private Integer                      logicalOperator;

    /**
     * Instantiates a new favorite.
     */
    public SearchCriterionDefinitionSuper() {
        // default constructor
    }

    @Override
    public String toString() {
        return this.name;
    }

    // getters and setters ........................................................................................................

    public Kullanici getUser() {
        return this.user;
    }

    public void setUser(final Kullanici user) {
        this.user = user;
    }

    public String getName() {
        return this.name;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Set<ICriterion> getCriteriaList() {
        return this.criteriaList;
    }

    public void setCriteriaList(final Set<ICriterion> criteriaList) {
        this.criteriaList = criteriaList;
    }

    @Override
    public String getTitle() {
        return this.getName();
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = (prime * result) + ((this.getId() == null) ? 0 : this.getId().hashCode());
        return result;
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final SearchCriterionDefinitionSuper other = (SearchCriterionDefinitionSuper) obj;
        if (this.getId() == null) {
            if (other.getId() != null) {
                return false;
            }
        } else if (!this.getId().equals(other.getId())) {
            return false;
        }
        return true;
    }

    public Set<SearchDefinitionColumns> getColumns() {
        return this.columns;
    }

    public void setColumns(final Set<SearchDefinitionColumns> columns) {
        this.columns = columns;
    }

    public Integer getLogicalOperator() {
        return this.logicalOperator;
    }

    public void setLogicalOperator(final Integer logicalOperatorValue) {
        this.logicalOperator = logicalOperatorValue;
    }
}
