package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.VirtualKeyboardLanguage;

/**
 *
 * <AUTHOR>
 */
@RequestScoped
public class VirtualKeyboardLanguageFacade extends AbstractFacade<VirtualKeyboardLanguage> {

    public VirtualKeyboardLanguageFacade() {
        super(VirtualKeyboardLanguage.class);
    }

    public List<VirtualKeyboardLanguage> findByLanguageId(final int languageId) {
        return this.em.createNamedQuery("VirtualKeyboardLanguage.findByLanguageId", VirtualKeyboardLanguage.class).setParameter("languageId", languageId).getResultList();
    }

    public List<VirtualKeyboardLanguage> filterByLangName(final String query) {
        return this.em.createNamedQuery("VirtualKeyboardLanguage.findByLangName", VirtualKeyboardLanguage.class).setParameter("str", "%" + query + "%").getResultList();
    }

}
