package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 *
 * <AUTHOR>
 */
@Audited
@Entity
@Table(name = "PersonelYabanciDil")
@NamedQuery(name = "PersonelYabanciDil.findEagerById", query = "SELECT p FROM PersonelYabanciDil p LEFT JOIN FETCH p.personel LEFT JOIN FETCH p.dil LEFT JOIN FETCH p.languageLevel WHERE p.id = :id")
@NamedQuery(name = "PersonelYabanciDil.findAll", query = "SELECT p FROM PersonelYabanciDil p LEFT JOIN FETCH p.personel pp LEFT JOIN FETCH p.dil LEFT JOIN FETCH p.languageLevel ORDER BY pp.silinmis, pp.aktif DESC")
@NamedQuery(name = "PersonelYabanciDil.findByName", query = "SELECT p FROM PersonelYabanciDil p LEFT JOIN FETCH p.personel pp LEFT JOIN FETCH p.dil LEFT JOIN FETCH p.languageLevel WHERE (pp.aciklama LIKE :str) ORDER BY pp.silinmis, pp.aktif DESC")
@NamedQuery(name = "PersonelYabanciDil.findActive", query = "SELECT p FROM PersonelYabanciDil p LEFT JOIN FETCH p.personel pp LEFT JOIN FETCH p.dil LEFT JOIN FETCH p.languageLevel WHERE pp.aktif = true AND pp.silinmis = false")
public class PersonelYabanciDil extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = 8582999553642405009L;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "PERSONEL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Personel          personel;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "DIL_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Dil               dil;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "languageLevel", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MuesPick              languageLevel;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public PersonelYabanciDil() {
    }

    // getters and setters ....................................................

    public String getAciklama() {
        return this.aciklama;
    }

    public Personel getPersonel() {
        return this.personel;
    }

    public void setPersonel(final Personel personel) {
        this.personel = personel;
    }

    public Dil getDil() {
        return this.dil;
    }

    public void setDil(final Dil dil) {
        this.dil = dil;
    }

    public MuesPick getLanguageLevel() {
        return this.languageLevel;
    }

    public void setLanguageLevel(final MuesPick languageLevel) {
        this.languageLevel = languageLevel;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.personel.getTitle()).orElse("" + this.getId());
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.personel, MuesUtil.surroundWithParanthesis(this.dil.getAd()));
    }

}
