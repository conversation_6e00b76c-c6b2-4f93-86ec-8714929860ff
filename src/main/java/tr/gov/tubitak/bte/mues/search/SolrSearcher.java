/***********************************************************
 * SolrSearcher.java - MUES Projesi
 *
 * Kullanılan JRE: 1.7.0_25
 *
 * halis.yilboga - 14.Nis.2014
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2012©
 ***********************************************************/
package tr.gov.tubitak.bte.mues.search;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.solr.client.solrj.SolrClient;
import org.apache.solr.client.solrj.SolrQuery;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.Http2SolrClient;
import org.apache.solr.client.solrj.response.FieldStatsInfo;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.apache.solr.common.SolrDocument;
import org.apache.solr.common.SolrException;
import org.apache.solr.common.SolrInputDocument;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.configuration.MetadataFacade;
import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;
import tr.gov.tubitak.bte.mues.solr.parse.JsonParsing;
import tr.gov.tubitak.bte.mues.solr.util.HttpRequestProcessor;
import tr.gov.tubitak.bte.mues.util.IncidentTypeEnum;
import tr.gov.tubitak.bte.mues.util.MuesException;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.SearchUtil;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

/**
 * how to execute a request using SolrJ
 *
 * <AUTHOR>
 */

@Named
@ViewScoped
public class SolrSearcher implements Serializable {

    private static final long            serialVersionUID = 5198700038877220326L;

    private static final Logger          logger           = LoggerFactory.getLogger(SolrSearcher.class);

    @Inject
    private transient MetadataFacade     metadataFacade;

    @Inject
    private transient AbstractParameters parameters;

    private SolrClient                   server;

    private SolrQuery                    solrQuery;

    private long                         totalCount;

    private long                         duration;

    private long                         numFound;

    private Map<String, Metadata>        metaDataMap;

    private List<Metadata>               rtmmList;

    private String                       urlString;

    private Map<String, FieldStatsInfo>  statisticsList;

    public SolrSearcher() {
        // blank constructor
    }

    public void initSolr(final SolrEnum solrEnum, final IncidentTypeEnum incidentTypeEnum, final String dateQuery) {
        this.setServer(new Http2SolrClient.Builder(this.parameters.get(solrEnum.getCoreKey())).build());
        final List<SolrInputDocument> solrInputDocsList = this.constructSQLAndSolrInputDocFromView(incidentTypeEnum, dateQuery);
        this.addRecordToSolr(solrInputDocsList);
    }

    public List<SolrInputDocument> mapMetaDataValuesToSolrInputDocuments(final List<Map<String, Object>> exceuteQueryResponse, final IncidentTypeEnum incidentTypeEnum) {

        final List<SolrInputDocument> documentList = new ArrayList<>();

        final List<Map<String, Object>> children = this.findChildren(incidentTypeEnum);

        for (final Map<String, Object> map : exceuteQueryResponse) {
            final SolrInputDocument document = new SolrInputDocument();

            for (final Map.Entry<String, Object> entry : map.entrySet()) {
                final String key = entry.getKey();
                final Object val = entry.getValue();

                if (val == null) {
                    continue;
                }

                SearchUtil.constructSolrInputDoc(key, val, document);

                if ((!children.isEmpty()) && key.contains("uid")) { // children if exist

                    final List<SolrInputDocument> childDocs = this.mapMetaDataValuesToSolrInputDocuments(children.stream()
                                                                                                                 .filter(x -> x.get("pid").toString().equals(val.toString()))
                                                                                                                 .collect(Collectors.toList()));

                    if (!childDocs.isEmpty()) {
                        childDocs.forEach(x -> {
                            x.removeField("pid");
                            document.setField("childs", childDocs);
                        }); // remove temporary pid (parentId) not in the solr document

                    }
                }
            }
            documentList.add(document);
        }

        return documentList;
    }

    public List<SolrInputDocument> mapMetaDataValuesToSolrInputDocuments(final List<Map<String, Object>> exceuteQueryResponse) {

        final List<SolrInputDocument> documentList = new ArrayList<>();

        for (final Map<String, Object> map : exceuteQueryResponse) {

            final SolrInputDocument document = new SolrInputDocument();

            for (final Map.Entry<String, Object> entry : map.entrySet()) {
                final String key = entry.getKey();
                final Object val = entry.getValue();

                SearchUtil.constructSolrInputDoc(key, val, document);

            }
            documentList.add(document);

        }

        return documentList;
    }

    public List<Map<String, Object>> findChildren(final IncidentTypeEnum incidentTypeEnum) {
        if (incidentTypeEnum.getChildSQLs() != null) {
            final List<Map<String, Object>> childList = new ArrayList<>();
            for (final String sql : incidentTypeEnum.getChildSQLs()) {
                childList.addAll(this.metadataFacade.exceuteQueryResponse(sql));
            }
            return childList;
        }
        return Collections.emptyList();
    }

    /**
     * for indexing purpose indexes single row value.
     *
     * @param id
     * @param viewName
     * @param childViews
     * @return {@link List} of {@link SolrInputDocument}
     */
    public List<SolrInputDocument> constructSQLAndSolrInputDocFromView(final Integer id, final IncidentTypeEnum incidentTypeEnum) {
        StringBuilder query = new StringBuilder("SELECT * from  ").append(incidentTypeEnum.name());
        if (id != null) {
            query.append(" where uid = ").append(id);
        }
        return this.constructSolrInputDocsFromSQL(query.toString(), incidentTypeEnum);

    }

    public List<SolrInputDocument> constructSQLAndSolrInputDocFromView(final IncidentTypeEnum incidentTypeEnum, final String dateQuery) {
        final String query = "SELECT * from  " + incidentTypeEnum.name() + dateQuery;

        return this.constructSolrInputDocsFromSQL(query, incidentTypeEnum);

    }

    public List<SolrInputDocument> constructSolrInputDocsFromSQL(final String query, final IncidentTypeEnum incidentTypeEnum) {

        final List<Map<String, Object>> exceuteQueryResponse = this.metadataFacade.exceuteQueryResponse(query);

        return this.mapMetaDataValuesToSolrInputDocuments(exceuteQueryResponse, incidentTypeEnum);

    }

    public List<SolrInputDocument> constructSolrInputDocsFromSQL(final String query) {

        final List<Map<String, Object>> exceuteQueryResponse = this.metadataFacade.exceuteQueryResponse(query);

        return this.mapMetaDataValuesToSolrInputDocuments(exceuteQueryResponse);

    }

    public QueryResponse makeSearch() throws SolrServerException, IOException {
        return this.makeSearch(this.solrQuery);

    }

    public QueryResponse makeSearch(final SolrQuery solrQuery) throws SolrServerException, IOException {
        return this.server.query(solrQuery);
    }

    public SolrQuery getQuery() {
        return this.solrQuery;
    }

    public boolean addBeanToSolr(final Object bean) throws SolrServerException, IOException {

        // server.deleteByQuery("*:*")
        final UpdateResponse addBean = this.server.addBean(bean);
        this.server.commit(false, false);

        return addBean.getStatus() == 0;

    }

    public boolean addRecordToSolr(final SolrInputDocument doc) throws SolrServerException, IOException {

        final UpdateResponse addDoc = this.server.add(doc);
        this.server.commit(false, false);
        return addDoc.getStatus() == 0;

    }

    public boolean addRecordToSolr(final List<SolrInputDocument> docs) {
        try {
            if (!docs.isEmpty()) {
                final UpdateResponse addDoc = this.server.add(docs);
                this.server.commit(false, false);
                return addDoc.getStatus() == 0;
            }
        } catch (final SolrServerException | SolrException | IOException e) {

            MuesUtil.showMessage("Solr Sunucusuna Ulaşmada Hata", FacesMessage.SEVERITY_ERROR);
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Solr Sunucusuna Ulaşmada Hata'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine Başvurunuz. '"
                                     + ", 'severity':'error'})");
            logger.error("[solrLoad] : Hata : {}", e.getMessage(), e);
        }
        return false;
    }

    public SolrDocument fetchRecordFromSolrByQuery(final SolrQuery query) throws SolrServerException, IOException {

        query.setFilterQueries("(*:* -aktif:false)");
        query.setRows(1);
        final QueryResponse solrResponse = this.server.query(query);
        if (solrResponse.getResults().isEmpty()) {
            return null;
        }
        this.setNumFound(solrResponse.getResults().getNumFound());
        return solrResponse.getResults().get(0);

    }

    public List<SolrDocument> fetchRecordFromSolrHelpCoreByQuery(final SolrQuery query) throws SolrServerException, IOException {

        final QueryResponse solrResponse = this.server.query(query);
        return solrResponse.getResults();

    }

    /**
     * update single field to solr.
     *
     * @param recordID
     * @param fieldName
     * @param value
     * @return if operation is success
     * @throws IOException
     * @throws SolrServerException
     */
    public boolean updateSingleFieldSolr(final long recordID, final String fieldName, final Object value) throws SolrServerException, IOException {

        final SolrInputDocument doc = new SolrInputDocument();
        doc.addField("id", recordID);
        final HashMap<String, Object> partialUpdate = new HashMap<>();
        partialUpdate.put("set", value);
        doc.addField(fieldName, partialUpdate);

        final UpdateResponse addDoc = this.server.add(doc);
        this.server.commit(false, false);
        return addDoc.getStatus() == 0;

    }

    /**
     * update single field to solr.
     *
     * @param recordID
     * @param fieldName
     * @param value
     * @return if operation is success
     * @throws IOException
     * @throws SolrServerException
     */
    public boolean updateSingleFieldSolrList(final List<Integer> recordIDs, final String fieldName, final Object value) throws SolrServerException, IOException {

        final List<SolrInputDocument> docs = new ArrayList<>();

        for (int i = 0; i < recordIDs.size(); i++) {

            final SolrInputDocument doc = new SolrInputDocument();
            doc.addField("id", recordIDs.get(i));
            final HashMap<String, Object> partialUpdate = new HashMap<>();
            partialUpdate.put("set", value);
            doc.addField(fieldName, partialUpdate);

            docs.add(doc);
        }

        final UpdateResponse addDoc = this.server.add(docs);
        this.server.commit(false, false);
        return addDoc.getStatus() == 0;

    }

    public void setSolrQuery(final SolrQuery solrQuery) {
        this.solrQuery = solrQuery;
    }

    public long getTotalCount() {
        return this.totalCount;
    }

    public void setTotalCount(final long totalCount) {
        this.totalCount = totalCount;
    }

    public void setDuration(final long startTime) {
        this.duration = startTime;
    }

    public double getDuration() {
        return this.duration;
    }

    public static void main(final String[] args) throws Exception {

        final String urlString = "http://10.1.37.80:8983/solr/Eser";
        final Http2SolrClient server = new Http2SolrClient.Builder(urlString).build();

        final JsonParsing jsonParsing = new JsonParsing();

        try (InputStream inputStream = HttpRequestProcessor.getInputStream(urlString + "/dataimport?command=status&indent=on&wt=json")) {
            jsonParsing.parseResponseStatus(inputStream);
        } catch (final Exception e) {

            logger.error("[commitRecordToSolr] : Hata : {}", e.getMessage(), e);

            throw new MuesException("commitRecordToSolr metod");
        }

        final SolrQuery solrQuery = new SolrQuery("id:2");
        final SolrSearcher solrSearcher = new SolrSearcher();

        final SolrSearcher solrSearcher2 = new SolrSearcher();
        solrSearcher2.setUrlString(urlString);

        solrSearcher.setServer(server);
        solrQuery.setRequestHandler("/mlt");
        solrQuery.set("mlt", "true");
        solrQuery.set("mlt.qf", "eserAltTurAd,alanTurAd");
        solrQuery.set("mlt.fl", "eserAltTurAd,alanTurAd");

        solrSearcher2.setUrlString(urlString);

        solrSearcher.setServer(server);

        solrSearcher.setSolrQuery(solrQuery);

        final List<ArtifactsSolrModel> listEser = solrSearcher.makeSearch().getBeans(ArtifactsSolrModel.class);

        for (final ArtifactsSolrModel eser : listEser) {
            logger.debug("{} ", eser);
        }
    }

    public void combineStatisticsFields(final SolrQuery sql, final List<Metadata> statisticsFields) {

        if ((statisticsFields == null) || statisticsFields.isEmpty()) {
            return;
        }

        sql.setGetFieldStatistics(true);
        for (final Metadata metadata : statisticsFields) {
            if (metadata != null) {
                sql.addGetFieldStatistics(metadata.getName());
            }
        }

    }

    public static void prepareAddField(final SolrInputDocument doc, final Object metadataModelValue, final String metadataIndexName) {
        doc.addField(metadataIndexName, metadataModelValue);
    }

    public long getNumFound() {
        return this.numFound;
    }

    public void setNumFound(final long numFound) {
        this.numFound = numFound;
    }

    public SolrClient getServer() {
        return this.server;
    }

    public void setServer(final SolrClient server) {
        this.server = server;
    }

    public List<Metadata> getRtmmList() {
        return this.rtmmList;
    }

    public void setRtmmList(final List<Metadata> rtmmList) {
        this.rtmmList = rtmmList;
    }

    public String getUrlString() {
        return this.urlString;
    }

    public void setUrlString(final String urlString) {
        this.urlString = urlString;
    }

    public String getUrlForSelect() {
        return this.urlString + "/select?indent=on&wt=json";
    }

    public Map<String, Metadata> getMetaDataMap() {
        return this.metaDataMap;
    }

    public void setMetaDataMap(final Map<String, Metadata> metaDataMap) {
        this.metaDataMap = metaDataMap;
    }

    public MetadataFacade getMetadataFacade() {
        return this.metadataFacade;
    }

    public void setMetadataFacade(final MetadataFacade metadataFacade) {
        this.metadataFacade = metadataFacade;
    }

    public Map<String, FieldStatsInfo> getStatisticsList() {

        return this.statisticsList;
    }

    public void setStatisticsList(final Map<String, FieldStatsInfo> statisticsList) {
        this.statisticsList = statisticsList;
    }

}
