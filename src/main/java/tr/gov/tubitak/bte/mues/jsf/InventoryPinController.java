package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.faces.event.ValueChangeEvent;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.EserFotograf;
import tr.gov.tubitak.bte.mues.model.EserHareket;
import tr.gov.tubitak.bte.mues.model.EserSerh;
import tr.gov.tubitak.bte.mues.session.EserFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class InventoryPinController extends AbstractController<Eser> {

    private static final long         serialVersionUID = 1557910340738988065L;

    private static final int          _aciklamaUzunluk = 4000;

    @Inject
    private EserFacade                facade;

    @Inject
    private ArtifactViewingController artifactViewingController;

    private static final Logger       logger           = LoggerFactory.getLogger(InventoryPinController.class);

    private EserFotograf              image;

    private EserFotograf              imageSecond;

    private EserHareket               eserHareket;

    private String                    aciklamaDevam;

    private String                    envanterDefteriPath;

    private Integer                   exporId;

    private List<EserSerh>            eserSerh;

    public InventoryPinController() {
        super(Eser.class);
    }

    @PostConstruct
    private void init() {

        for (final Iterator<EserFotograf> iterator = this.artifactViewingController.getModel().getEserFotografs().iterator(); iterator.hasNext();) {
            final EserFotograf eserFotograf = iterator.next();
            if ((eserFotograf.getAnaFotograf() != null) || eserFotograf.getAnaFotograf()) {
                this.image = eserFotograf;
                break;
            }
        }

        for (final Iterator<EserFotograf> iterator = this.artifactViewingController.getModel().getEserFotografs().iterator(); iterator.hasNext();) {
            final EserFotograf eserFotograf = iterator.next();

            if ((eserFotograf.getAnaFotograf() == null) || !eserFotograf.getAnaFotograf()) {
                this.imageSecond = eserFotograf;
                break;
            }
        }

        this.eserSerh = new ArrayList<>();

        if ((this.artifactViewingController.getModel().getEserSerhs() != null) && this.artifactViewingController.getModel().getEserSerhs().isEmpty())

        {

            for (final Iterator<EserSerh> iterator = this.artifactViewingController.getModel().getEserSerhs().iterator(); iterator.hasNext();) {
                final EserSerh eserFotograf = iterator.next();

                if (this.eserSerh.size() < 2) {
                    this.eserSerh.add(eserFotograf);
                } else {
                    break;
                }

            }
        }

    }

    @Override
    public EserFacade getFacade() {
        return this.facade;
    }

    public EserFotograf getImage() {

        return this.image;

    }

    public EserFotograf getImageSecond() {
        return this.imageSecond;
    }

    public void artifactValueChange(final ValueChangeEvent event) {
        final Object newValue = event.getNewValue();
        logger.debug("[artifactValueChange] : {}", newValue);
        this.setExporId((Integer) newValue);

    }

    public EserHareket getEserGelisSekli() {
        if (this.eserHareket == null) {

            for (final Iterator<EserHareket> iterator = this.artifactViewingController.getModel().getEserHarekets().iterator(); iterator.hasNext();) {
                final EserHareket eserHareket = iterator.next();

                if ((this.eserHareket == null) || (eserHareket.getId() > this.eserHareket.getId())) {
                    this.eserHareket = eserHareket;
                }
            }
        }
        return this.eserHareket;

    }

    public String getGenelAciklama() {
        String aciklama = this.artifactViewingController.getModel().getGenelAciklama();

        if (aciklama != null) {
            aciklama = MuesUtil.html2text(aciklama);
            if (aciklama.length() > _aciklamaUzunluk) {
                this.setAciklamaDevam(aciklama.substring(_aciklamaUzunluk, aciklama.length()));
                aciklama = aciklama.substring(0, _aciklamaUzunluk) + "...";
            }
        }
        return aciklama;
    }

    public String getAciklamaDevam() {
        return this.aciklamaDevam;
    }

    public void setAciklamaDevam(final String aciklamaDevam) {
        this.aciklamaDevam = aciklamaDevam;
    }

    public Integer getExporId() {
        return this.exporId;
    }

    public void setExporId(final Integer exporId) {
        this.exporId = exporId;
    }

    public String getEnvanterDefteriPath() {
        return this.envanterDefteriPath;
    }

    public void setEnvanterDefteriPath(final String envanterDefteriPath) {
        this.envanterDefteriPath = envanterDefteriPath;
    }

    public EserHareket getEserHareket() {
        return this.eserHareket;
    }

    public void setEserHareket(final EserHareket eserHareket) {
        this.eserHareket = eserHareket;
    }

    public List<EserSerh> getEserSerh() {
        return this.eserSerh;
    }

    public void setEserSerh(final List<EserSerh> eserSerh) {
        this.eserSerh = eserSerh;
    }

}
