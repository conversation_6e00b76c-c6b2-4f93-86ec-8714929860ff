package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

/**
 *
*
 */
@Entity
@Table(name = "Ulk<PERSON>")
@NamedQuery(name = "Ulke.findEagerById", query = "SELECT u FROM Ulke u WHERE u.id = :id")
@NamedQuery(name = "Ulke.findAll", query = "SELECT u FROM Ulke u ORDER BY u.silinmis, u.aktif DESC, u.ad")
@NamedQuery(name = "Ulke.findActive", query = "SELECT u FROM Ulke u WHERE u.aktif = true AND u.silinmis = false ORDER BY u.ad")
@NamedNativeQuery(name = "Ulke.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_HAREKET WHERE SILINMIS = 0 AND iadeEdenUlke = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM PERSONEL_GOREV WHERE SILINMIS = 0 AND TUZEL_KISI_ID = :id)")
public class Ulke extends AbstractEntity implements DeleteValidatable {

    private static final long serialVersionUID = -7065066402298476144L;

    @Size(max = 150)
    @Column(name = "AD", length = 150)
    private String            ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Ulke() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).get();
    }

}
