package tr.gov.tubitak.bte.mues.model;

import javax.persistence.DiscriminatorColumn;
import javax.persistence.DiscriminatorType;
import javax.persistence.Entity;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

/**
 *
*
 */
@Audited
@Entity
@Table(name = "PERSONEL")
@DiscriminatorColumn(discriminatorType = DiscriminatorType.INTEGER, name = "applicationType")
public abstract class AbstractPersonel extends PersonelSuper {

    private static final long serialVersionUID = -8622839070092866261L;

}
