package tr.gov.tubitak.bte.mues.jsf;

import java.io.IOException;
import java.io.Serializable;
import java.net.URL;

import javax.enterprise.context.RequestScoped;
import javax.faces.context.FacesContext;
import javax.inject.Named;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor.HSSFColorPredefined;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.primefaces.component.export.ExcelOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.lowagie.text.Cell;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.PageSize;
import com.lowagie.text.Phrase;
import com.lowagie.text.Table;
import com.lowagie.text.alignment.HorizontalAlignment;
import com.lowagie.text.alignment.VerticalAlignment;
import com.lowagie.text.pdf.BaseFont;

@Named
@RequestScoped
public class DataExporterController implements Serializable {

    private static final long            serialVersionUID = -6240351113335292980L;

    private static final Logger          logger           = LoggerFactory.getLogger(DataExporterController.class);

    /** TR Encoding ayarı içindir. */
    private static final String          ENCODING         = "Cp1254";

    private String                       reportTitle;

    private boolean                      landscape;

    private final transient ExcelOptions excelOpt;

    public DataExporterController() {
        this.excelOpt = new ExcelOptions();
        this.excelOpt.setFacetBgColor("#F88017");
        this.excelOpt.setFacetFontSize("10");
        this.excelOpt.setFacetFontColor("#000000");
        this.excelOpt.setFacetFontStyle("BOLD");
        this.excelOpt.setCellFontColor("#051505");
        this.excelOpt.setCellFontSize("8");
        this.excelOpt.setFontName("Verdana");
        this.excelOpt.setStronglyTypedCells(false);
    }

    public void postProcessXLS(final Object document) {
        final HSSFWorkbook wb = (HSSFWorkbook) document;
        final HSSFSheet sheet = wb.getSheetAt(0);
        final HSSFRow header = sheet.getRow(0);

        final HSSFCellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFillForegroundColor(HSSFColorPredefined.GREEN.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        for (int i = 0; i < header.getPhysicalNumberOfCells(); i++) {
            final HSSFCell cell = header.getCell(i);

            cell.setCellStyle(cellStyle);

        }
    }

    public void preProcessPDF(final Object document) {
        try {
            final Document pdf = (Document) document;
            final BaseFont helvetica = BaseFont.createFont(BaseFont.HELVETICA, ENCODING, BaseFont.EMBEDDED);
            final Font font = new Font(helvetica, 12, Font.BOLD);

            pdf.setPageSize(PageSize.A4.rotate());
            pdf.setMargins(10, 10, 10, 10);
            pdf.open();

            final Table table = new Table(2);
            table.setBorder(0);
            table.setWidths(new float[] { 60, 500 });

            final Cell logoCell = new Cell();
            logoCell.setBorder(0);
            logoCell.addElement(Image.getInstance(this.getLogoPath()));
            logoCell.setVerticalAlignment(VerticalAlignment.CENTER);
            table.addCell(logoCell);

            final Cell headerCell = new Cell(new Phrase(this.getReportTitle(), font));
            headerCell.setBorder(0);
            headerCell.setHorizontalAlignment(HorizontalAlignment.CENTER);
            headerCell.setVerticalAlignment(VerticalAlignment.CENTER);
            table.addCell(headerCell);
            pdf.add(table);

        } catch (final DocumentException | IOException e) {
            logger.error("[preProcessPDF] : Hata : {}", e.getMessage(), e);
        }
    }

    public ExcelOptions getExcelOpt() {
        return this.excelOpt;
    }

    public URL getLogoPath() {
        return FacesContext.getCurrentInstance().getApplication().getResourceHandler().createResource("images/travel_logo.png", "mues", "png").getURL();
    }

    public String getReportTitle() {
        return this.reportTitle;
    }

    public void setReportTitle(final String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public boolean isLandscape() {
        return this.landscape;
    }

    public void setLandscape(final boolean landscape) {
        this.landscape = landscape;
    }

}
