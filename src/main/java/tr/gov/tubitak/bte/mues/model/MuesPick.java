package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;

import org.hibernate.envers.Audited;

@Audited
@Entity
@Table(name = "Pick")
@NamedQuery(name = "MuesPick.findActive", query = "SELECT p FROM MuesPick p LEFT JOIN FETCH p.grup g WHERE p.aktif = true AND p.silinmis = false ORDER BY g.kod, p.rank")

public class MuesPick extends PickSuper {

    private static final long serialVersionUID = 2920113423714474511L;

    @JoinColumn(name = "grup", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private MuesPickGroup     grup;

    public MuesPick() {
        // default constructor
    }

    public MuesPickGroup getGrup() {
        return this.grup;
    }

    public void setGrup(final MuesPickGroup grup) {
        this.grup = grup;
    }
}
