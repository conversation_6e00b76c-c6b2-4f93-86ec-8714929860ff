package tr.gov.tubitak.bte.mues.model;

import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Audited
@MappedSuperclass
@Table(name = "Birlestirme")
@NamedQuery(name = "Birlestirme.findEagerById", query = "SELECT DISTINCT b FROM Birlestirme b "
                                                        + "LEFT JOIN FETCH b.anaEser ae "
                                                        + "LEFT JOIN FETCH ae.eserHarekets aeh "
                                                        + "LEFT JOIN FETCH b.mudurluk "
                                                        + "LEFT JOIN FETCH b.joinedArtifacts x "
                                                        + "LEFT JOIN FETCH x.eser e "
                                                        + "LEFT JOIN FETCH e.anaFotograf ef "
                                                        + "LEFT JOIN FETCH e.eserHarekets h LEFT JOIN FETCH h.teslimEdenSahis "
                                                        + "WHERE b.id = :id ORDER BY b.silinmis, b.aktif DESC")

@NamedQuery(name = "Birlestirme.findAll", query = "SELECT DISTINCT b FROM Birlestirme b "
                                                  + "LEFT JOIN FETCH b.anaEser "
                                                  + "LEFT JOIN FETCH b.mudurluk "
                                                  + "LEFT JOIN FETCH b.joinedArtifacts x LEFT JOIN FETCH x.eser e "
                                                  + "LEFT JOIN FETCH e.anaFotograf ef "
                                                  + "ORDER BY b.silinmis, b.aktif DESC")

@NamedQuery(name = "Birlestirme.findEagerByEser", query = "SELECT DISTINCT b FROM Birlestirme b "
                                                          + "LEFT JOIN  b.anaEser a "
                                                          + "LEFT JOIN FETCH b.joinedArtifacts bja "
                                                          + "LEFT JOIN FETCH bja.eser be "
                                                          + "LEFT JOIN FETCH be.eserAltTur ber LEFT JOIN FETCH ber.eserTur  "
                                                          + "LEFT JOIN FETCH be.anaFotograf ef "
                                                          + "WHERE b.anaEser = :eser ORDER BY b.silinmis, b.aktif DESC")

@NamedQuery(name = "Birlestirme.findActive", query = "SELECT b FROM Birlestirme b WHERE b.aktif = true AND b.silinmis = false")

@NamedQuery(name = "Birlestirme.findByMudurluk", query = "SELECT DISTINCT b FROM Birlestirme b "
                                                         + "LEFT JOIN FETCH b.mudurluk "
                                                         + "LEFT JOIN FETCH b.joinedArtifacts x LEFT JOIN FETCH x.eser "
                                                         + "LEFT JOIN FETCH b.anaEser "
                                                         + "WHERE b.mudurluk in :muzeler "
                                                         + "ORDER BY b.silinmis, b.aktif DESC")
@NamedNativeQuery(name = "Birlestirme.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER e join Eser_Birlestirme b on e.ID = b.eserId WHERE e.SILINMIS = 0 AND b.birlestirmeId = :id)")
public class BirlestirmeSuper extends AbstractEntity implements ArtifactJoinable {

    private static final long    serialVersionUID = 7583683422734910620L;

    @Size(max = 10)
    @Column(name = "onaySayisi", length = 10)
    private String               onaySayisi;

    @Column(name = "onayTarihi")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                 onayTarihi;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "onayDocPath", length = 250)
    private String               onayDocPath;

    @Size(max = 500)
    @Column(name = "ACIKLAMA", length = 500)
    private String               aciklama;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "anaEserId", referencedColumnName = "ID")
    @OneToOne(fetch = FetchType.LAZY)
    private Eser                 anaEser;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "birlestirme", fetch = FetchType.LAZY, orphanRemoval = true)
    private Set<EserBirlestirme> joinedArtifacts;

    public BirlestirmeSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public Date getOnayTarihi() {
        return this.onayTarihi;
    }

    public void setOnayTarihi(final Date onayTarihi) {
        this.onayTarihi = onayTarihi;
    }

    public String getOnaySayisi() {
        return this.onaySayisi;
    }

    public void setOnaySayisi(final String onaySayisi) {
        this.onaySayisi = onaySayisi;
    }

    public String getOnayDocPath() {
        return this.onayDocPath;
    }

    public void setOnayDocPath(final String onayDocPath) {
        this.onayDocPath = onayDocPath;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    public Eser getAnaEser() {
        return this.anaEser;
    }

    public void setAnaEser(final Eser anaEser) {
        this.anaEser = anaEser;
    }

    public Set<EserBirlestirme> getJoinedArtifacts() {
        return this.joinedArtifacts;
    }

    public void setJoinedArtifacts(final Set<EserBirlestirme> joinArtifact) {
        this.joinedArtifacts = joinArtifact;
    }

    @Override
    public Set<Eser> getCombinedArtifacts() {
        final Set<Eser> artifacts = new LinkedHashSet<>();
        Optional.ofNullable(this.getJoinedArtifacts()).orElse(Collections.emptySet()).forEach(x -> artifacts.add(x.getEser()));
        return artifacts;
    }

    @Override
    public void setCombinedArtifacts(final Set<Eser> joinArtifact) {
        if (this.getJoinedArtifacts() == null) {
            final Set<EserBirlestirme> artifacts = new LinkedHashSet<>();
            this.setJoinedArtifacts(artifacts);
        }
        for (final Eser eser : joinArtifact) {
            final EserBirlestirme eserBirlestirme = new EserBirlestirme();
            eserBirlestirme.setEser(eser);
            this.getJoinedArtifacts().add(eserBirlestirme);
        }
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.anaEser == null ? this.getTitle() : this.anaEser.getEserOzelAdi();
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.aciklama).orElse("" + this.getId());
    }

    public String getArtifactTitles() {
        final StringJoiner sj = new StringJoiner(", ");
        this.joinedArtifacts.stream().forEach(x -> sj.add(x.getEser().getEserId()));
        return sj.toString();
    }

}
