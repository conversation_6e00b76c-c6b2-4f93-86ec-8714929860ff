package tr.gov.tubitak.bte.mues.search;

import java.util.Date;
import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.persistence.Transient;

import tr.gov.tubitak.bte.mues.util.DateUtil;

/**
 * <PERSON><PERSON><PERSON> tabanlı kriterleri tutan sınıf.
 */
@Entity
@DiscriminatorValue("3")
public class DateCriterion extends AbstractSimpleCriterion {

    private static final long serialVersionUID = -5398798833645856521L;

    private Date              firstValue;

    private Date              secondValue;

    /**
     * Yapıcı metot.
     */
    public DateCriterion() {
    }

    public DateCriterion(final Integer id) {
        super(id);
    }

    @Override
    @Transient
    public String getFirstValue() {
        return SearchConstants.getDateISOFormatStart(this.firstValue);
    }

    @Override
    @Transient
    public String getSecondValue() {
        return SearchConstants.getDateISOFormatEnd(this.secondValue);
    }

    @Transient
    public String getFirstValueMidnight() {
        return SearchConstants.getDateISOFormatEnd(this.firstValue);
    }

    @Override
    public ICriterion setModel(final CriterionModel model) {
        super.setSuperModel(model);
        this.firstValue = model.getDateValue1();
        this.secondValue = model.getDateValue2();
        return this;
    }

    @Override
    @Transient
    public String getSql() {

        if (this.getComparisonOperatorEnum() == ComparisonOperatorEnum.NOP) {

            if ((this.firstValue == null) && (this.secondValue == null)) {
                this.setComparisonOperator(ComparisonOperatorEnum.NOP);
            } else {
                this.setComparisonOperator(ComparisonOperatorEnum.BETWEEN);
            }

        }

        final StringBuilder sb = new StringBuilder();
        if (this.getComparisonOperatorEnum() != ComparisonOperatorEnum.NOP) {
            sb.append(this.getMetadata().getName());

            sb.append(SearchConstants.SOLREQUALSSIGN);
            sb.append(this.getComparisonOperatorEnum().getPrefix(this));

            if ((this.getComparisonOperatorEnum() == ComparisonOperatorEnum.EQUALS)) {
                final DateCriterion dc = this;
                sb.append(SearchConstants.LEFT_BRACKET).append(dc.getFirstValue()).append(SearchConstants.TO).append(dc.getFirstValueMidnight()).append(SearchConstants.RIGHT_BRACKET);
            } else if ((this.getComparisonOperatorEnum() == ComparisonOperatorEnum.BETWEEN)) {
                final DateCriterion dc = this;
                sb.append(SearchConstants.LEFT_BRACKET)
                  .append(dc.getFirstValue() == null ? "* " : dc.getFirstValue())
                  .append(SearchConstants.TO)
                  .append(dc.getSecondValue() == null ? "* " : dc.getSecondValue())
                  .append(SearchConstants.RIGHT_BRACKET);
            } else if (this.getComparisonOperatorEnum() != ComparisonOperatorEnum.ISNULL) {
                sb.append(this.getFirstValue());
            }
            sb.append(this.getComparisonOperatorEnum().getSuffix(this));

            if (this.getComparisonOperatorEnum().equals(ComparisonOperatorEnum.ISNULL)) {
                sb.append(SearchConstants.RIGHT_PARANTHESIS);
            }
        }
        return sb.toString();
    }

    @Override
    @Transient
    public String getFirstValueText() {
        return SearchConstants.QUOTE + DateUtil.getDateDDMMYYYY(this.firstValue) + SearchConstants.QUOTE;
    }

    @Override
    @Transient
    public String getSecondValueText() {
        return SearchConstants.QUOTE + DateUtil.getDateDDMMYYYY(this.secondValue) + SearchConstants.QUOTE;
    }

    @Column(name = "DATE_VALUE1")
    public Date getDateValue1() {
        return this.firstValue;
    }

    public void setDateValue1(final Date dateValue1) {
        this.firstValue = dateValue1;
    }

    @Column(name = "DATE_VALUE2")
    public Date getDateValue2() {
        return this.secondValue;
    }

    public void setDateValue2(final Date dateValue2) {
        this.secondValue = dateValue2;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.firstValue, this.secondValue);
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final DateCriterion other = (DateCriterion) obj;
        return Objects.equals(this.firstValue, other.firstValue) && Objects.equals(this.secondValue, other.secondValue);
    }

    @Override
    public String getTitle() {
        // TODO Auto-generated method stub
        return null;
    }
}
