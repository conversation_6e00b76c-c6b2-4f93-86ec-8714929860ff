package tr.gov.tubitak.bte.mues.jsf;

import java.util.List;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.mues.model.Announcement;
import tr.gov.tubitak.bte.mues.session.AnnouncementFacade;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class AnnouncementController extends AbstractController<Announcement> implements SingleFileUploadable {

    private static final long  serialVersionUID = 818035325233734331L;

    @Inject
    private AnnouncementFacade facade;

    @Inject
    private FileUploadHelper   fileUploadHelper;

    @Inject
    private SessionBean        sessionBean;

    public AnnouncementController() {
        super(Announcement.class);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setDocumentPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getDocumentPath() != null) {
            this.getModel().setDocumentPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDocumentPath(), FolderType.OTHER));
        }
    }

    // getters and setters ....................................................

    @Override
    public List<Announcement> getItems() {
        if (this.items == null) {
            this.items = this.getFacade().findAll(this.facade.findKullaniciRolsCodeByKullanici(this.sessionBean.getCurrentUser()));
        }
        return this.items;
    }

    @Override
    public AnnouncementFacade getFacade() {
        return this.facade;
    }

}
