package tr.gov.tubitak.bte.mues.model;

import java.util.Collection;
import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

/**
 *
*
 */
@Entity
@Table(name = "SUSLEME_TEKNIGI")
@NamedQuery(name = "SuslemeTeknigi.findEagerById", query = "SELECT s FROM SuslemeTeknigi s WHERE s.id = :id")
@NamedQuery(name = "SuslemeTeknigi.findAll", query = "SELECT s FROM SuslemeTeknigi s ORDER BY s.silinmis, s.aktif DESC, s.ad")
@NamedQuery(name = "SuslemeTeknigi.findActive", query = "SELECT s FROM SuslemeTeknigi s WHERE s.aktif = true AND s.silinmis = false ORDER BY s.ad")
@NamedQuery(name = "SuslemeTeknigi.findByNameAndAciklama", query = "SELECT s FROM SuslemeTeknigi s WHERE s.aktif = true AND s.silinmis = false AND (s.ad LIKE :str OR s.aciklama LIKE :str) ORDER BY s.ad, s.aciklama")
@NamedQuery(name = "SuslemeTeknigi.findByNameAndAciklamaAndMalzeme", query = "SELECT x.suslemeTeknigi FROM MalzemeSuslemeTeknigi x WHERE x.aktif = true AND x.silinmis = false AND x.suslemeTeknigi.aktif = true AND x.suslemeTeknigi.silinmis = false AND x.malzeme = :malzeme AND (x.suslemeTeknigi.ad LIKE :str OR x.suslemeTeknigi.aciklama LIKE :str) ORDER BY x.suslemeTeknigi.ad")
@NamedNativeQuery(name = "SuslemeTeknigi.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM ESER_MALZEME_SUSLEME_TEKNIGI WHERE SILINMIS = 0 AND SUSLEME_TEKNIGI_ID = :id) + (SELECT case when count(1) > 0 then 1 else 0 end FROM MALZEME_SUSLEME_TEKNIGI WHERE SILINMIS = 0 AND SUSLEME_TEKNIGI_ID = :id)")
public class SuslemeTeknigi extends AbstractEntity implements DeleteValidatable {

    private static final long                     serialVersionUID = -5358643755676268767L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                                ad;

    @Size(max = 50)
    @Column(name = "DEGER", length = 50)
    private String                                deger;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                                aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String                                fotografPath;

    @OneToMany(mappedBy = "suslemeTeknigi")
    private Collection<MalzemeSuslemeTeknigi>     malzemeSuslemeTeknigiCollection;

    @OneToMany(mappedBy = "suslemeTeknigi")
    private Collection<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigiCollection;

    public SuslemeTeknigi() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getDeger() {
        return this.deger;
    }

    public void setDeger(final String deger) {
        this.deger = deger;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public Collection<MalzemeSuslemeTeknigi> getMalzemeSuslemeTeknigiCollection() {
        return this.malzemeSuslemeTeknigiCollection;
    }

    public void setMalzemeSuslemeTeknigiCollection(final Collection<MalzemeSuslemeTeknigi> malzemeSuslemeTeknigiCollection) {
        this.malzemeSuslemeTeknigiCollection = malzemeSuslemeTeknigiCollection;
    }

    public Collection<EserMalzemeSuslemeTeknigi> getEserMalzemeSuslemeTeknigiCollection() {
        return this.eserMalzemeSuslemeTeknigiCollection;
    }

    public void setEserMalzemeSuslemeTeknigiCollection(final Collection<EserMalzemeSuslemeTeknigi> eserMalzemeSuslemeTeknigiCollection) {
        this.eserMalzemeSuslemeTeknigiCollection = eserMalzemeSuslemeTeknigiCollection;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
