package tr.gov.tubitak.bte.mues.model;

import java.util.Date;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

import org.hibernate.envers.AuditJoinTable;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.mues.constraint.Email;
import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.TCKN;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Audited
@MappedSuperclass
@NamedQuery(name = "Personel.findEagerById", query = "SELECT p FROM Personel p LEFT JOIN FETCH p.meslek LEFT JOIN FETCH p.mudurluk LEFT JOIN FETCH p.unvan LEFT JOIN FETCH p.kadroDurum LEFT JOIN FETCH p.personelUzmanlikAlani pu LEFT JOIN FETCH pu.uzmanlikAlani LEFT JOIN FETCH p.personelYabanciDil py LEFT JOIN FETCH py.dil LEFT JOIN FETCH py.languageLevel WHERE p.id = :id")
@NamedQuery(name = "Personel.findAll", query = "SELECT DISTINCT p FROM Personel p LEFT JOIN FETCH p.mudurluk m LEFT JOIN FETCH p.kadroDurum LEFT JOIN FETCH p.meslek LEFT JOIN FETCH p.unvan LEFT JOIN FETCH p.personelUzmanlikAlani pu LEFT JOIN FETCH pu.uzmanlikAlani LEFT JOIN FETCH p.personelYabanciDil py LEFT JOIN FETCH py.dil LEFT JOIN FETCH py.languageLevel WHERE p.inspector = false ORDER BY p.silinmis, p.aktif DESC, m.ad")
@NamedQuery(name = "Personel.findActive", query = "SELECT p FROM Personel p WHERE p.aktif = true AND p.inspector = false ORDER BY p.ad")
@NamedQuery(name = "Personel.findByMuseumDirectorate", query = "SELECT p FROM Personel p LEFT JOIN FETCH p.mudurluk m LEFT JOIN FETCH p.unvan WHERE p.mudurluk.id = :id AND p.aktif = true AND p.silinmis = false AND p.inspector = false")
@NamedQuery(name = "Personel.findByNonKullaniciAndName", query = "SELECT p FROM Personel p WHERE p.calismaDurumu = true AND p.aktif = true AND p.silinmis = false AND p.inspector = false AND REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str AND p NOT IN (SELECT k.personel from KullaniciBirimRol kbr LEFT JOIN kbr.kullanici k WHERE kbr.silinmis = false) ORDER BY p.ad")
@NamedQuery(name = "Personel.filterPersonelPoolNoneThisAppUser", query = "SELECT p FROM PersonelView p WHERE  p.aktif = true AND p.silinmis = false AND p.inspector = false AND REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str AND p.applicationType != :applicationType ORDER BY p.ad")
@NamedQuery(name = "Personel.findAllUndeletedPersoenelByNameAndAciklama", query = "SELECT p FROM Personel p WHERE p.aktif = true AND p.inspector = false AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad")
@NamedQuery(name = "Personel.findByTCKN", query = "SELECT p FROM Personel p WHERE p.tcKimlikNo=:tckn ORDER BY p.ad")
@NamedQuery(name = "Personel.findByFullNameAndAciklama", query = "SELECT p FROM Personel p "
                                                                 + "WHERE p.aktif = true AND p.silinmis = false AND p.inspector = false "
                                                                 + "AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad, p.soyad")
@NamedQuery(name = "Personel.findByFullNameAndAciklamaAndMuseumDirectorates", query = "SELECT p FROM Personel p "
                                                                                      + "LEFT JOIN FETCH p.unvan "
                                                                                      + "WHERE p.aktif = true AND p.silinmis = false AND p.mudurluk IN :muzes AND p.inspector = false "
                                                                                      + "AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad, p.soyad")
@NamedQuery(name = "Personel.findAllPersonnelPreventDuplicate", query = "SELECT p FROM Personel p "
                                                                        + "LEFT JOIN FETCH p.unvan "
                                                                        + "WHERE p.aktif = true AND p.id NOT IN :exp AND p.inspector = false "
                                                                        + "AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad")
@NamedQuery(name = "Personel.findAllFromDirectoratesExcludeIds", query = "SELECT p FROM Personel p "
                                                                         + "LEFT JOIN FETCH p.unvan "
                                                                         + "WHERE p.aktif = true AND p.silinmis = false AND p.inspector = false "
                                                                         + "AND p.id NOT IN :ids AND p.mudurluk.id IN :directorates AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad, p.soyad")
@NamedQuery(name = "Personel.findActivePersonnelWithDetail", query = "SELECT p FROM Personel p "
                                                                     + "LEFT JOIN FETCH p.unvan "
                                                                     + "LEFT JOIN FETCH p.mudurluk "
                                                                     + "WHERE p.aktif = true AND p.silinmis = false AND p.calismaDurumu = true AND p.inspector = false "
                                                                     + "AND p.mudurluk.id IN :directorates ORDER BY p.ad, p.soyad")
@NamedQuery(name = "Personel.findAllPersonnelPreventDuplicateWithMuseum", query = "SELECT p FROM Personel p "
                                                                                  + "LEFT JOIN FETCH p.unvan "
                                                                                  + "LEFT JOIN FETCH p.mudurluk m "
                                                                                  + "WHERE p.aktif = true AND p.id NOT IN :exp AND m.id = :museumId AND p.inspector = false  "
                                                                                  + "AND (REPLACE(concat(p.ad, p.soyad), ' ', '') LIKE :str OR p.aciklama LIKE :str) ORDER BY p.ad")
@NamedQuery(name = "Personel.findAllInspectors", query = "SELECT DISTINCT p FROM Personel p "
                                                         + "LEFT JOIN FETCH p.meslek "
                                                         + "LEFT JOIN FETCH p.unvan "
                                                         + "WHERE p.inspector = true "
                                                         + "ORDER BY p.silinmis, p.aktif DESC")

public class PersonelSuper extends AbstractEntity implements DeleteValidatable, EditPermissible {

    private static final long          serialVersionUID = -2431886886124326713L;

    @Size(max = 10)
    @Column(name = "SICIL_NO", length = 10)
    private String                     sicilNo;

    @TCKN
    @Size(max = 11)
    @Column(name = "TC_KIMLIK_NO", length = 11)
    private String                     tcKimlikNo;

    @Size(max = 20)
    @Column(name = "PASAPORT_NO", length = 20)
    private String                     pasaportNo;

    @Size(max = 11)
    @Column(name = "YABANCI_KIMLIK_NO", length = 11)
    private String                     yabanciKimlikNo;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                     ad;

    @Size(max = 50)
    @Column(name = "SOYAD", length = 50)
    private String                     soyad;

    @Size(max = 50)
    @Column(name = "DOGUM_YERI", length = 50)
    private String                     dogumYeri;

    @Column(name = "DOGUM_TARIHI")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       dogumTarihi;

    @Size(max = 24)
    @Column(name = "IBAN_NO", length = 24)
    private String                     ibanNo;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_IS", length = 25)
    private String                     telefonIs;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_CEP", length = 25)
    private String                     telefonCep;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_CEP2", length = 25)
    private String                     telefonCep2;

    @Size(max = 50)
    @Column(name = "EPOSTA_KURUMSAL", length = 50)
    private String                     epostaKurumsal;

    @Email
    @Size(max = 150)
    @Column(name = "EPOSTA_KISISEL", length = 150)
    private String                     epostaKisisel;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "FOTOGRAF_PATH", length = 250)
    private String                     fotografPath;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                     aciklama;

    @Column(name = "CALISMA_DURUMU")
    private Boolean                    calismaDurumu;

    @Size(max = 25)
    @Column(name = "TELEFON_NO_YURT_DISI", length = 25)
    private String                     telefonYurtDisi;

    @FilePathCheck
    @Size(max = 150)
    @Column(name = "taahhutPath", length = 150)
    private String                     taahhutPath;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "unvan", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Unvan                      unvan;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "kadroDurum", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private KadroDurum                 kadroDurum;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "MESLEK_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private Meslek                     meslek;

    // @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @AuditJoinTable(name = "audit_PersonelUzmanlikAlani")
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "personel", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<PersonelUzmanlikAlani> personelUzmanlikAlani;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "personel", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<PersonelYabanciDil>    personelYabanciDil;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @JoinColumn(name = "UZMANLIK_ALANI_ID", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private UzmanlikAlani              uzmanlikAlani;

    @Size(max = 100)
    @Column(name = "mudurlukAd", length = 100)
    private String                     mudurlukAd;

    @Column(name = "dateCreated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       dateCreated;

    @Column(name = "dateUpdated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                       dateUpdated;

    @Column(name = "inspector")
    private boolean                    inspector        = false;

    protected PersonelSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getSicilNo() {
        return this.sicilNo;
    }

    public void setSicilNo(final String sicilNo) {
        this.sicilNo = sicilNo;
    }

    public String getTcKimlikNo() {
        return this.tcKimlikNo;
    }

    public void setTcKimlikNo(final String tcKimlikNo) {
        this.tcKimlikNo = tcKimlikNo;
    }

    public String getPasaportNo() {
        return this.pasaportNo;
    }

    public void setPasaportNo(final String pasaportNo) {
        this.pasaportNo = pasaportNo;
    }

    public String getYabanciKimlikNo() {
        return this.yabanciKimlikNo;
    }

    public void setYabanciKimlikNo(final String yabanciKimlikNo) {
        this.yabanciKimlikNo = yabanciKimlikNo;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getSoyad() {
        return this.soyad;
    }

    public void setSoyad(final String soyad) {
        this.soyad = soyad;
    }

    public String getDogumYeri() {
        return this.dogumYeri;
    }

    public void setDogumYeri(final String dogumYeri) {
        this.dogumYeri = dogumYeri;
    }

    public Date getDogumTarihi() {
        return this.dogumTarihi;
    }

    public void setDogumTarihi(final Date dogumTarihi) {
        this.dogumTarihi = dogumTarihi;
    }

    public String getIbanNo() {
        return this.ibanNo;
    }

    public void setIbanNo(final String ibanNo) {
        this.ibanNo = ibanNo;
    }

    public String getTelefonIs() {
        return this.telefonIs;
    }

    public void setTelefonIs(final String telefonIs) {
        this.telefonIs = telefonIs;
    }

    public String getTelefonCep() {
        return this.telefonCep;
    }

    public void setTelefonCep(final String telefonCep) {
        this.telefonCep = telefonCep;
    }

    public String getTelefonCep2() {
        return this.telefonCep2;
    }

    public void setTelefonCep2(final String telefonCep2) {
        this.telefonCep2 = telefonCep2;
    }

    public String getEpostaKurumsal() {
        return this.epostaKurumsal;
    }

    public void setEpostaKurumsal(final String epostaKurumsal) {
        if (epostaKurumsal != null) {
            this.epostaKurumsal = epostaKurumsal.toLowerCase();
        }
    }

    public String getEpostaKisisel() {
        return this.epostaKisisel;
    }

    public void setEpostaKisisel(final String epostaKisisel) {
        if (epostaKisisel != null) {
            this.epostaKisisel = epostaKisisel.toLowerCase();
        }
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getAciklama() {
        return Stream.of(this.mudurlukAd, this.aciklama).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining("-->"));
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public Boolean getCalismaDurumu() {
        return this.calismaDurumu;
    }

    public void setCalismaDurumu(final Boolean calismaDurumu) {
        this.calismaDurumu = calismaDurumu;
    }

    public String getTelefonYurtDisi() {
        return this.telefonYurtDisi;
    }

    public void setTelefonYurtDisi(final String telefonYurtDisi) {
        this.telefonYurtDisi = telefonYurtDisi;
    }

    public Meslek getMeslek() {
        return this.meslek;
    }

    public void setMeslek(final Meslek meslek) {
        this.meslek = meslek;
    }

    public UzmanlikAlani getUzmanlikAlani() {
        return this.uzmanlikAlani;
    }

    public void setUzmanlikAlani(final UzmanlikAlani uzmanlikAlani) {
        this.uzmanlikAlani = uzmanlikAlani;
    }

    public Unvan getUnvan() {
        return this.unvan;
    }

    public void setUnvan(final Unvan unvan) {
        this.unvan = unvan;
    }

    public KadroDurum getKadroDurum() {
        return this.kadroDurum;
    }

    public void setKadroDurum(final KadroDurum kadroDurum) {
        this.kadroDurum = kadroDurum;
    }

    public String getTaahhutPath() {
        return this.taahhutPath;
    }

    public void setTaahhutPath(final String taahhutPath) {
        this.taahhutPath = taahhutPath;
    }

    public Set<PersonelUzmanlikAlani> getPersonelUzmanlikAlani() {
        return this.personelUzmanlikAlani;
    }

    public void setPersonelUzmanlikAlani(final Set<PersonelUzmanlikAlani> personelUzmanlikAlani) {
        this.personelUzmanlikAlani = personelUzmanlikAlani;
    }

    public Set<PersonelYabanciDil> getPersonelYabanciDil() {
        return this.personelYabanciDil;
    }

    public void setPersonelYabanciDil(final Set<PersonelYabanciDil> personelYabanciDil) {
        this.personelYabanciDil = personelYabanciDil;
    }

    public String getUzmanlikAlanlari() {
        final StringJoiner sj = new StringJoiner(", ");
        this.personelUzmanlikAlani.stream().forEach(x -> sj.add(x.getUzmanlikAlani().getAd()));
        return sj.toString();
    }

    public String getYabanciDiller() {
        final StringJoiner sj = new StringJoiner(", ");
        this.personelYabanciDil.stream().forEach(x -> sj.add(x.getDil().getAd()));
        return sj.toString();
    }

    public String getMudurlukAd() {
        return this.mudurlukAd;
    }

    public void setMudurlukAd(final String mudurlukAd) {
        this.mudurlukAd = mudurlukAd;
    }

    @Override
    public String toString() {
        return this.getTitle();
    }

    @Override
    public String getTitle() {
        return Stream.of(this.ad, this.soyad, MuesUtil.surroundWithParanthesis(this.sicilNo)).filter(s -> (s != null) && !s.isEmpty()).collect(Collectors.joining(" "));
    }

    @Override
    public Integer getUserIdentifier() {
        return this.getId();
    }

    public boolean getInspector() {
        return this.inspector;
    }

    public void setInspector(final boolean inspector) {
        this.inspector = inspector;
    }

    public Date getDateCreated() {
        return this.dateCreated;
    }

    public void setDateCreated(final Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Date getDateUpdated() {
        return this.dateUpdated;
    }

    public void setDateUpdated(final Date dateUpdated) {
        this.dateUpdated = dateUpdated;
    }

}
