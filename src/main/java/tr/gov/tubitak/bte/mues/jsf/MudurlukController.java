package tr.gov.tubitak.bte.mues.jsf;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.enterprise.event.Event;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;

import org.apache.shiro.SecurityUtils;
import org.omnifaces.cdi.ViewScoped;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.jsf.event.SessionVariablesChangeEvent;
import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.session.MudurlukFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class MudurlukController extends AbstractController<Mudurluk> implements SingleFileUploadable {

    private static final long                  serialVersionUID = -554866173458518884L;

    @Inject
    private SessionBean                        sessionBean;

    @Inject
    private MudurlukFacade                     facade;

    @Inject
    private IlceController                     ilceController;

    @Inject
    private FileUploadHelper                   fileUploadHelper;

    @Inject
    private Event<SessionVariablesChangeEvent> sessionVariablesChangeEvent;

    private String                             dwgFileToDelete;

    private List<String>                       selectedMudurluks;

    @PostConstruct
    public void init() {
        this.dwgFileToDelete = "";
    }

    public MudurlukController() {
        super(Mudurluk.class);
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.ilceController.setIl(null);
    }

    @Override
    public void showDetail(final Mudurluk item) {
        super.showDetail(item);
        this.ilceController.setIl(item.getIl());
    }

    @Override
    public void toggleActive(final Mudurluk mudurluk) {
        this.facade.toggleActiveSelfAndDescendants(mudurluk);
        this.setModel(mudurluk);
        this.getModel().setAktif(!this.getModel().getAktif());
        this.fireMudurlukChangeEvent();
    }

    @Override
    public DBOperationResult create() {
        this.getModel().setDateCreated(new Date());
        this.getModel().setDateUpdated(new Date());
        final DBOperationResult result = super.create();
        this.fireMudurlukChangeEvent();
        return result;
    }

    @Override
    public DBOperationResult update() {
        this.getModel().setDateUpdated(new Date());
        final DBOperationResult result = super.update();
        if ((this.getDwgFileToDelete() != null) && !this.getDwgFileToDelete().isBlank() && result.isSuccess()) {
            this.fileUploadHelper.deleteDwgFilePermanently(this.dwgFileToDelete);
        }
        this.fireMudurlukChangeEvent();
        return result;
    }

    @Override
    public void delete() {
        super.delete();
        this.fireMudurlukChangeEvent();
    }

    private void fireMudurlukChangeEvent() {
        this.sessionVariablesChangeEvent.fire(new SessionVariablesChangeEvent());
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadLogoToTempFolder(final FileUploadEvent event) {
        this.getModel().setLogoPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDocumentToTempFolder(final FileUploadEvent event) {
        this.getModel().setPdfPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void uploadDwgDocumentToTempFolder(final FileUploadEvent event) {
        if ((this.getModel().getDwgPath() != null) && !this.getModel().getDwgPath().isBlank()) {
            this.setDwgFileToDelete(this.getModel().getDwgPath());
        }
        this.getModel().setDwgPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public void deleteDwgFileFromModel() {
        this.setDwgFileToDelete(this.getModel().getDwgPath());
        this.getModel().setDwgPath(null);
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
        if (this.getModel().getPdfPath() != null) {
            this.getModel().setPdfPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getPdfPath(), FolderType.OTHER));
        }
        if (this.getModel().getDwgPath() != null) {
            this.getModel().setDwgPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getDwgPath(), FolderType.DWG));
        }
        if (this.getModel().getLogoPath() != null) {
            this.getModel().setLogoPath(this.fileUploadHelper.writeMainCopyToFile(this.getModel().getLogoPath(), FolderType.IMAGE_AK));
        }
    }

    public void onIlSelected(final SelectEvent<Il> event) {
        this.ilceController.setIl(event.getObject());
        this.getModel().setIlce(null);
    }

    public List<Mudurluk> filterByNameAndPermission(final String query) {
        final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");
        return Optional.ofNullable(this.sessionBean.fetchByPermission(permission))
                       .orElse(Collections.emptyList())
                       .stream()
                       .filter(x -> x.getAd().toLowerCase(MuesUtil.LOCALE_TR).contains(query.toLowerCase(MuesUtil.LOCALE_TR)))
                       .collect(Collectors.toList());
    }

    public List<Mudurluk> fetchByNameAndPermissionWithRoleCheck(final String query) {
        final List<Mudurluk> muzeMudurluks;
        if (SecurityUtils.getSubject().hasRole("SUPERUSER")) {
            muzeMudurluks = this.facade.findAll();
        } else {
            final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");
            muzeMudurluks = this.sessionBean.fetchByPermission(permission);
        }
        return Optional.ofNullable(muzeMudurluks)
                       .orElse(Collections.emptyList())
                       .stream()
                       .filter(x -> x.getAd().toLowerCase(MuesUtil.LOCALE_TR).contains(query.toLowerCase(MuesUtil.LOCALE_TR)))
                       .collect(Collectors.toList());
    }

    @Override
    public List<Mudurluk> filterByName(final String query) {
        return this.facade.filterByName(query);
    }

    public List<Mudurluk> listByNameAndPermission() {
        if (this.items == null) {
            final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");
            this.items = this.sessionBean.fetchMudurlukListByPermission(permission);
        }
        return this.items;
    }

    public List<Mudurluk> listAllActiveMuseum() {
        if (this.items == null) {
            this.items = this.getFacade().findActive();

        }
        return this.items;
    }

    @PreDestroy
    public void cleanUp() {

        if (this.items != null) {
            this.items.clear();
        }
    }

    public List<Mudurluk> filterByFullNameAndAciklamaPreventDuplicate(final String query, final List<Integer> directoratesList) {
        this.setItems(this.facade.filterByFullNameAndAciklamaPreventDuplicate(query, directoratesList));
        return this.getItems();
    }

    // getters and setter .....................................................

    @Override
    public MudurlukFacade getFacade() {
        return this.facade;
    }

    public String getDwgFileToDelete() {
        return this.dwgFileToDelete;
    }

    public void setDwgFileToDelete(final String dwgFile) {
        this.dwgFileToDelete = dwgFile;
    }

    public List<String> getSelectedMudurluks() {
        return this.selectedMudurluks;
    }

    public void setSelectedMudurluks(final List<String> selectedMudurluks) {
        this.selectedMudurluks = selectedMudurluks;
    }

}