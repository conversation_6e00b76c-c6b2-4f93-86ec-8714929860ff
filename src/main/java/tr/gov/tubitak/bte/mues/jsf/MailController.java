package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.util.List;
import java.util.StringJoiner;

import javax.faces.application.FacesMessage;
import javax.faces.component.UIComponent;
import javax.faces.context.FacesContext;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.Rol;
import tr.gov.tubitak.bte.mues.session.KullaniciFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class MailController implements Serializable {

    private static final long         serialVersionUID = 7392526886694735363L;

    @Inject
    private MailSender                mailSender;

    @Inject
    private transient KullaniciFacade kullaniciFacade;

    @Inject
    private AbstractParameters        params;

    @Inject
    private SessionBean               sessionBean;

    private String                    messageSubject;

    private String                    messageContent;

    private List<Rol>                 selectedRolList;

    private List<Mudurluk>            selectedMudurlukList;

    private List<Kullanici>           selectedKullaniciList;

    private boolean                   singleMuzeMail   = true;

    public MailController() {
        // Blank constructor
    }

    public void send() {
        MuesUtil.showMessage("E-posta gönderiliyor...", FacesMessage.SEVERITY_INFO);
        final StringJoiner sj = new StringJoiner(";");
        this.selectedKullaniciList.stream().forEach(x -> sj.add(x.getPersonel().getEpostaKurumsal() + this.params.get("kurumsal.eposta.uzanti")));

        if (this.mailSender.send(sj.toString(), "", "", this.messageSubject, this.messageContent)) {
            this.messageSubject = null;
            this.messageContent = null;
            MuesUtil.showMessage("E-posta Gönderildi", FacesMessage.SEVERITY_INFO);

        } else {
            MuesUtil.showMessage("E-posta Göndermede Hata", "En az bir kullanıcıya e-posta gönderilemedi!", FacesMessage.SEVERITY_ERROR);
        }
    }

    public List<Kullanici> filterByMudurlukAndRol() {
        // Hiçbir seçim yapılmazsa
        if (((this.selectedMudurlukList == null) || this.selectedMudurlukList.isEmpty())) {
            if ((this.selectedRolList == null) || this.selectedRolList.isEmpty()) {
                this.selectedKullaniciList = null;

                // Rol seçilir, müze seçimi yapılmazsa
            } else {
                this.selectedKullaniciList = this.kullaniciFacade.filterByMudurlukAndRol(this.filterByNameAndPermission(), this.selectedRolList);
            }

            // Müze seçilir, rol seçimi yapılmazsa
        } else {
            if ((this.selectedRolList == null) || this.selectedRolList.isEmpty()) {
                this.selectedKullaniciList = this.kullaniciFacade.filterByMuze(this.selectedMudurlukList);

            } else {
                // Her iki seçim yapılırsa
                this.selectedKullaniciList = this.kullaniciFacade.filterByMudurlukAndRol(this.selectedMudurlukList, this.selectedRolList);
            }
        }

        return this.selectedKullaniciList;
    }

    public void handleSingleMultiEmailToggle() {
        this.selectedKullaniciList = null;
        this.selectedMudurlukList = null;
        this.selectedRolList = null;
    }

    public List<Kullanici> filterByMudurluk() {
        return this.kullaniciFacade.findByMudurluk(this.filterByNameAndPermission());
    }

    private List<Mudurluk> filterByNameAndPermission() {
        final String permission = (String) UIComponent.getCurrentComponent(FacesContext.getCurrentInstance()).getAttributes().get("permission");
        return this.sessionBean.fetchMudurlukListByPermission(permission);
    }

    // getters and setters ....................................................

    public String getMessageSubject() {
        return this.messageSubject;
    }

    public void setMessageSubject(final String messageSubject) {
        this.messageSubject = messageSubject;
    }

    public String getMessageContent() {
        return this.messageContent;
    }

    public void setMessageContent(final String messageContent) {
        this.messageContent = messageContent;
    }

    public boolean isSingleMuzeMail() {
        return this.singleMuzeMail;
    }

    public void setSingleMuzeMail(final boolean singleMuzeMail) {
        this.singleMuzeMail = singleMuzeMail;
    }

    public List<Rol> getSelectedRolList() {
        return this.selectedRolList;
    }

    public void setSelectedRolList(final List<Rol> selectedRolList) {
        this.selectedRolList = selectedRolList;
    }

    public List<Kullanici> getSelectedKullaniciList() {
        return this.selectedKullaniciList;
    }

    public void setSelectedKullaniciList(final List<Kullanici> selectedKullaniciList) {
        this.selectedKullaniciList = selectedKullaniciList;
    }

    public List<Mudurluk> getSelectedMudurlukList() {
        return this.selectedMudurlukList;
    }

    public void setSelectedMudurlukList(final List<Mudurluk> selectedMudurlukList) {
        this.selectedMudurlukList = selectedMudurlukList;
    }

}
