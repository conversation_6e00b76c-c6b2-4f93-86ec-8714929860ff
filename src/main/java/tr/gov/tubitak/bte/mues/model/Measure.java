package tr.gov.tubitak.bte.mues.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;

import com.google.common.base.Joiner;

/**
 *
*
 */
@Entity
@Table(name = "Measure")
@NamedQuery(name = "Measure.findEagerById", query = "SELECT m FROM Measure m LEFT JOIN FETCH m.type WHERE m.id = :id")
@NamedQuery(name = "Measure.findAll", query = "SELECT m FROM Measure m LEFT JOIN FETCH m.type mt ORDER BY m.silinmis, m.aktif DESC, mt.ad")
@NamedQuery(name = "Measure.findActive", query = "SELECT m FROM Measure m LEFT JOIN FETCH m.type WHERE m.aktif = true AND m.silinmis = false ORDER BY m.ad")
@NamedQuery(name = "Measure.findByNameAndMeasureType", query = "SELECT m FROM Measure m LEFT JOIN FETCH m.type WHERE m.type = :type AND (m.ad LIKE :ad OR m.symbol LIKE :ad) AND m.aktif = true AND m.silinmis = false ORDER BY m.ad")
public class Measure extends AbstractEntity {

    private static final long serialVersionUID = 406217481081027477L;

    @JoinColumn(name = "measureType", referencedColumnName = "ID")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private MeasureType       type;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @Size(max = 10)
    @Column(name = "symbol", length = 10)
    private String            symbol;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    public Measure() {
    }

    // getters and setters ....................................................

    public MeasureType getType() {
        return this.type;
    }

    public void setType(final MeasureType type) {
        this.type = type;
    }

    public String getSymbol() {
        return this.symbol;
    }

    public void setSymbol(final String symbol) {
        this.symbol = symbol;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return this.getAd();
    }

    @Override
    public String getTitle() {
        if (this.getId() == null) {
            return null;
        }
        return Joiner.on(" ").skipNulls().join(this.ad, "(" + this.symbol + ")");
    }

}
