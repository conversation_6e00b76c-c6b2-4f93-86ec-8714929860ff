package tr.gov.tubitak.bte.mues.search;

import java.io.IOException;
import java.io.Serializable;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.enterprise.context.ApplicationScoped;
import javax.faces.application.FacesMessage;
import javax.inject.Inject;

import org.apache.solr.client.solrj.SolrClient;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.impl.Http2SolrClient;
import org.apache.solr.client.solrj.response.UpdateResponse;
import org.apache.solr.common.SolrException;
import org.apache.solr.common.SolrInputDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.configuration.MetadataFacade;
import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;
import tr.gov.tubitak.bte.mues.model.SolrLastIndexTimes;
import tr.gov.tubitak.bte.mues.session.SolrLastIndexTimesFacade;
import tr.gov.tubitak.bte.mues.util.DateUtil;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.SearchUtil;
import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

@ApplicationScoped
public abstract class AbstractSolrSearcherIndexingUtil implements Serializable {

    private static final long                  serialVersionUID = 3133696571649223795L;

    private static final Logger                logger           = LoggerFactory.getLogger(AbstractSolrSearcherIndexingUtil.class);

    @Inject
    private transient SolrLastIndexTimesFacade solrLastIndexTimesFacade;

    @Inject
    private transient AbstractParameters       parameters;

    @Inject
    private transient MetadataFacade           metadataFacade;

    SolrClient                                 server;

    public abstract List<Map<String, Object>> findChildren(final String incidentTypeEnum);

    public void recordToSolr(final SolrEnum solrEnum, final String viewName, final String conditionField) {
        final SolrLastIndexTimes solrLastIndexTimes = this.solrLastIndexTimesFacade.findBySolrCore(solrEnum);

        final String queryStr = " WHERE " + conditionField + " >= '" + DateUtil.getDateYYYYMMDDHHMMSS(Date.from(solrLastIndexTimes.getLastIndexTime().toInstant().atZone(ZoneId.systemDefault()).minusMinutes(30).toInstant())) + "'";
        this.initSolr(solrEnum);
        this.addRecordToSolr(this.constructSQLAndSolrInputDocFromView(viewName, queryStr));

        this.solrLastIndexTimesFacade.updateLastIndexTime(solrEnum);
    }

    public void fullRecordToSolr(final SolrEnum solrEnum, final String viewName) {
        this.initSolr(solrEnum);
        this.addRecordToSolr(this.constructSQLAndSolrInputDocFromView(viewName, ""));
    }

    public void initSolr(final SolrEnum solrEnum) {
        this.setServer(new Http2SolrClient.Builder(this.parameters.get(solrEnum.getCoreKey())).build());
    }

    public List<SolrInputDocument> constructSQLAndSolrInputDocFromView(final Integer id, final String viewName) {
        String query = "SELECT * from  " + viewName;
        if (id != null) {
            query += " where uid = " + id;
        }
        return this.constructSolrInputDocsFromSQL(query, viewName);

    }

    public List<SolrInputDocument> constructSQLAndSolrInputDocFromView(final String viewName, final String dateQuery) {
        final String query = "SELECT * from  " + viewName + dateQuery;

        return this.constructSolrInputDocsFromSQL(query, viewName);

    }

    public List<SolrInputDocument> constructSolrInputDocsFromSQL(final String query, final String viewName) {

        final List<Map<String, Object>> exceuteQueryResponse = this.metadataFacade.exceuteQueryResponse(query);

        return this.mapMetaDataValuesToSolrInputDocuments(exceuteQueryResponse, viewName);

    }

    public List<SolrInputDocument> constructSolrInputDocsFromSQL(final String query) {

        final List<Map<String, Object>> exceuteQueryResponse = this.metadataFacade.exceuteQueryResponse(query);

        return this.mapMetaDataValuesToSolrInputDocuments(exceuteQueryResponse);

    }

    public List<SolrInputDocument> mapMetaDataValuesToSolrInputDocuments(final List<Map<String, Object>> exceuteQueryResponse, final String viewName) {

        final List<SolrInputDocument> documentList = new ArrayList<>();

        final List<Map<String, Object>> children = this.findChildren(viewName);

        for (final Map<String, Object> map : exceuteQueryResponse) {
            final SolrInputDocument document = new SolrInputDocument();

            for (final Map.Entry<String, Object> entry : map.entrySet()) {
                final String key = entry.getKey();
                final Object val = entry.getValue();

                SearchUtil.constructSolrInputDoc(key, val, document);

                if ((!children.isEmpty()) && key.contains("uid")) { // children if exist

                    final List<Map<String, Object>> listChildren = children.stream()
                                                                           .filter(

                                                                                   x -> (x.get("pid") != null) && x.get("pid").toString().equals(val.toString())

                                                                           )
                                                                           .collect(Collectors.toList());

                    final List<SolrInputDocument> childDocs = this.mapMetaDataValuesToSolrInputDocuments(listChildren);

                    if (!childDocs.isEmpty()) {
                        childDocs.forEach(x -> x.removeField("pid")); // remove temporary pid (parentId) not in the solr document
                        document.setField("childs", childDocs);
                    }
                }
            }
            documentList.add(document);
        }

        return documentList;
    }

    public List<SolrInputDocument> mapMetaDataValuesToSolrInputDocuments(final List<Map<String, Object>> exceuteQueryResponse) {

        final List<SolrInputDocument> documentList = new ArrayList<>();

        for (final Map<String, Object> map : exceuteQueryResponse) {

            final SolrInputDocument document = new SolrInputDocument();

            for (final Map.Entry<String, Object> entry : map.entrySet()) {
                final String key = entry.getKey();
                final Object val = entry.getValue();

                SearchUtil.constructSolrInputDoc(key, val, document);

            }
            documentList.add(document);

        }

        return documentList;
    }

    public boolean addRecordToSolr(final SolrInputDocument doc) throws SolrServerException, IOException {

        final UpdateResponse addDoc = this.server.add(doc);
        this.server.commit(false, false);
        return addDoc.getStatus() == 0;

    }

    public boolean addRecordToSolr(final List<SolrInputDocument> docs) {
        try {
            if (!docs.isEmpty()) {
                final UpdateResponse addDoc = this.server.add(docs);
                this.server.commit(false, false);
                // TODO buraya log basılacak
                logger.info("İndexleme başarılı");
                // PrimeFaces.current().executeScript("İndexleme Başarılı");
                return addDoc.getStatus() == 0;
            }
        } catch (final SolrServerException | SolrException | IOException e) {

            MuesUtil.showMessage("Solr Sunucusuna Ulaşmada Hata", FacesMessage.SEVERITY_ERROR);
            // PrimeFaces.current()
            // .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
            // + "'Solr Sunucusuna Ulaşmada Hata'"
            // + ", 'detail':"
            // + "'Sistem Yöneticisine Başvurunuz. '"
            // + ", 'severity':'error'})");
            logger.error("[solrLoad] : Hata : {}", e.getMessage(), e);
        }
        return false;
    }

    public SolrClient getServer() {
        return this.server;
    }

    public void setServer(final SolrClient server) {
        this.server = server;
    }

}
