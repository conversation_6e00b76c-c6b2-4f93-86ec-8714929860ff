package tr.gov.tubitak.bte.mues.jsf;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.model.MailEnum;
import tr.gov.tubitak.bte.mues.session.SecurityImplementationUtilFacade;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * The Class SecurityImplementationUtil.
 */
@RequestScoped
public class SecurityImplementationUtil implements Serializable {

    private static final long                serialVersionUID = 241753267941800892L;

    private static final String              EMAIL_EXTENSION  = "kurumsal.eposta.uzanti";

    @Inject
    private SessionBean                      sessionBean;

    @Inject
    private SecurityImplementationUtilFacade securityImplementationUtilFacade;

    @Inject
    private AbstractParameters               params;

    @Inject
    private MailSender                       mailSender;

    public SecurityImplementationUtil() {
        // default constructor
    }

    public void sendMail(final Integer mudurlukId, final List<Integer> eserIdList, final MailEnum mailEnum) {
        this.sendMail(mudurlukId, eserIdList, mailEnum, Boolean.FALSE);
    }

    public void sendMail(final Integer mudurlukId, final List<Integer> eserIdList, final MailEnum mailEnum, final boolean notification) {

        final List<Integer> permanentIdList = this.securityImplementationUtilFacade.findSecurityImplementationByEserIds(eserIdList);

        if (!permanentIdList.isEmpty()) {

            if (notification) {
                this.securityImplementationUtilFacade.insertAsBulk(eserIdList, mudurlukId, this.sessionBean.getCurrentUser().getPersonel().getId(), mailEnum);
            }

            final String eserListAsString = String.join(", ", permanentIdList.stream().map(x -> MuesUtil.getWrappedAssetId(x)).collect(Collectors.toList()));
            final String body = MessageFormat.format(mailEnum.getBody(), eserListAsString);

            final List<String> toList = this.securityImplementationUtilFacade.findEmailByMudurlukAndPermission(mudurlukId, "guvenlikUygulamasiBildirim:listele");
            toList.replaceAll(x -> x == null ? null : (x + this.params.get(EMAIL_EXTENSION)));

            final String to = Joiner.on(",").skipNulls().join(toList);

            if (!to.isEmpty()) {
                this.mailSender.send(to, "", "", mailEnum.getTitle(), body);
            }
        }
    }

}
