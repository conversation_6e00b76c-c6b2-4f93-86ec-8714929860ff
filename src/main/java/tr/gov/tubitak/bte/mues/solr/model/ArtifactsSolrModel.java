package tr.gov.tubitak.bte.mues.solr.model;

import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

import org.apache.solr.client.solrj.beans.Field;

import tr.gov.tubitak.bte.mues.model.IRankedArtifact;

public class ArtifactsSolrModel implements IRankedArtifact<Integer> {

    private static final long serialVersionUID = -7063722044929695152L;

    @Field("id")
    private Integer           id;

    @Field("alanAciklama")
    private String            alanAciklama;

    @Field("alanAd")
    private String            alanAd;

    @Field("alanKod")
    private String            alanKod;

    @Field("alanKonumuAd")
    private String            alanKonumuAd;

    @Field("alanKonumuTurAd")
    private String            alanKonumuTurAd;

    @Field("alanKonumuTurDeger")
    private String            alanKonumuTurDeger;

    @Field("alanTurAd")
    private String            alanTurAd;

    @Field("alanTurDeger")
    private String            alanTurDeger;

    @Field("artifactState")
    private Integer           artifactState;

    @Field("bagliMudurlukAd")
    private String            bagliMudurlukAd;

    @Field("bagliBirimAciklama")
    private String            bagliBirimAciklama;

    @Field("bagliBirimAd")
    private String            bagliBirimAd;

    @Field("bagliBirimAdres")
    private String            bagliBirimAdres;

    @Field("bagliBirimBoylam")
    private String            bagliBirimBoylam;

    @Field("bagliBirimEnlem")
    private String            bagliBirimEnlem;

    @Field("bagliBirimEpostaKurumsal")
    private String            bagliBirimEpostaKurumsal;

    @Field("bagliBirimFaks1")
    private String            bagliBirimFaks1;

    @Field("bagliBirimFotografPath")
    private String            bagliBirimFotografPath;

    @Field("bagliBirimKod")
    private String            bagliBirimKod;

    @Field("bagliBirimTelefonNo1")
    private String            bagliBirimTelefonNo1;

    @Field("bagliBirimWebSayfasi")
    private String            bagliBirimWebSayfasi;

    @Field("binaAciklama")
    private String            binaAciklama;

    @Field("binaAd")
    private String            binaAd;

    @Field("binaKod")
    private String            binaKod;

    @Field("eserAciklama")
    private String            eserAciklama;

    @Field("aktif")
    private Boolean           aktif;

    @Field("eserBirlestirilmisEser")
    private Boolean           eserBirlestirilmisEser;

    @Field("eserDonemBaslangicGo")
    private Boolean           eserDonemBaslangicGo;

    @Field("eserDonemBaslangicYil")
    private Integer           eserDonemBaslangicYil;

    @Field("eserDonemBitisGo")
    private Boolean           eserDonemBitisGo;

    @Field("eser3DFile")
    private Boolean           eser3DFile;

    @Field("eserDonemBitisYil")
    private Integer           eserDonemBitisYil;

    @Field("eserDuzenlemeZamani")
    private Date              eserDuzenlemeZamani;

    @Field("eserElisiDokumaSecimiAd")
    private String            eserElisiDokumaSecimiAd;

    @Field("eserEnvanterDefteriPath")
    private String            eserEnvanterDefteriPath;

    @Field("eserEskiEnvanterNo")
    private String            eserEskiEnvanterNo;

    @Field("eserEserOzelAdi")
    private String            eserEserOzelAdi;

    @Field("eserEseriBagislayanOnemliKisiId")
    private Integer           eserEseriBagislayanOnemliKisiId;

    @Field("eserEseriKullanacakOnemliKisiId")
    private Integer           eserEseriKullanacakOnemliKisiId;

    @Field("eserEseriKullananOnemliKisiId")
    private Integer           eserEseriKullananOnemliKisiId;

    @Field("eserEseriYapanOnemliKisiId")
    private Integer           eserEseriYapanOnemliKisiId;

    @Field("eserEseriYaptiranOnemliKisiId")
    private Integer           eserEseriYaptiranOnemliKisiId;

    @Field("eserGenelAciklama")
    private String            eserGenelAciklama;

    @Field("eserIslamiGayriSecimi")
    private Integer           eserIslamiGayriSecimi;

    @Field("eserIslamiGayriSecimiAd")
    private String            eserIslamiGayriSecimiAd;

    @Field("eserKiymet")
    private Double            eserKiymet;

    @Field("eserEserId")
    private String            eserEserId;

    @Field("eserKondisyonDurumu")
    private Integer           eserKondisyonDurumu;

    @Field("eserSerh")
    private String            eserSerh;

    @Field("eserSikkeDarpGo")
    private Boolean           eserSikkeDarpGo;

    @Field("eserSikkeDarpYeriId")
    private Integer           eserSikkeDarpYeriId;

    @Field("eserSikkeDarpYili")
    private Integer           eserSikkeDarpYili;

    @Field("eserSikkeDarpYonu")
    private Integer           eserSikkeDarpYonu;

    @Field("eserSikkeDarpYonuAd")
    private String            eserSikkeDarpYonuAd;

    @Field("eserSilinmis")
    private Boolean           eserSilinmis;

    @Field("eserSilmeAciklamasi")
    private String            eserSilmeAciklamasi;

    @Field("eserSilmeZamani")
    private Date              eserSilmeZamani;

    @Field("eserTanimlayiciFotografPath")
    private String            eserTanimlayiciFotografPath;

    @Field("eserTasinirIslemFisiNo")
    private String            eserTasinirIslemFisiNo;

    @Field("eserTasinirMalYonId")
    private Integer           eserTasinirMalYonId;

    @Field("eserTorenselDurumu")
    private Boolean           eserTorenselDurumu;

    @Field("eserTurveAltTurAd")
    private String            eserTurveAltTurAd;

    @Field("eserUniklikDurumu")
    private Boolean           eserUniklikDurumu;

    @Field("eserVersiyon")
    private Integer           eserVersiyon;

    @Field("eserYaratmaKullaniciId")
    private Integer           eserYaratmaKullaniciId;

    @Field("eserYaratmaZamani")
    private Date              eserYaratmaZamani;

    @Field("eserOnayZamani")
    private Date              eserOnayZamani;

    @Field("eserYazmaBasmaSecimi")
    private Integer           eserYazmaBasmaSecimi;

    @Field("eserYazmaBasmaSecimiAd")
    private String            eserYazmaBasmaSecimiAd;

    @Field("cagAciklama")
    private String            cagAciklama;

    @Field("cagAd")
    private String            cagAd;

    @Field("donemAd")
    private String            donemAd;

    @Field("iliskilendirmeAciklama")
    private String            iliskilendirmeAciklama;

    @Field("iliskilendirmeAd")
    private String            iliskilendirmeAd;

    @Field("hukumdarAciklama")
    private String            hukumdarAciklama;

    @Field("hukumdarAd")
    private String            hukumdarAd;

    @Field("hukumdarDonemBaslangicGo")
    private Boolean           hukumdarDonemBaslangicGo;

    @Field("hukumdarDonemBaslangicYil")
    private Integer           hukumdarDonemBaslangicYil;

    @Field("hukumdarDonemBitisGo")
    private Boolean           hukumdarDonemBitisGo;

    @Field("hukumdarDonemBitisYil")
    private Integer           hukumdarDonemBitisYil;

    @Field("ilAd")
    private String            ilAd;

    @Field("ilDeger")
    private String            ilDeger;

    @Field("ilceAd")
    private String            ilceAd;

    @Field("kronolojiAciklama")
    private String            kronolojiAciklama;

    @Field("kronolojiAd")
    private String            kronolojiAd;

    @Field("kullaniciKullaniciAdi")
    private String            kullaniciKullaniciAdi;

    @Field("muzeMudurluguAciklama")
    private String            muzeMudurluguAciklama;

    @Field("mudurlukAd")
    private String            mudurlukAd;

    @Field("muzeMudurluguAd")
    private String            muzeMudurluguAd;

    @Field("muzeMudurluguBoylam")
    private String            muzeMudurluguBoylam;

    @Field("muzeMudurluguEnlem")
    private String            muzeMudurluguEnlem;

    @Field("muzeMudurluguKod")
    private String            muzeMudurluguKod;

    @Field("uygarlikAciklama")
    private String            uygarlikAciklama;

    @Field("uygarlikAd")
    private String            uygarlikAd;

    @Field("eserAltTurAd")
    private String            eserAltTurAd;

    @Field("tasinirMalYonetmeligiKodAd")
    private String            tasinirMalYonetmeligiKodAd;

    @Field("eserKondisyonDurumuAd")
    private String            eserKondisyonDurumuAd;

    @Field("eserHareketEserGelisSekli")
    private List<Integer>     eserHareketEserGelisSekli;

    @Field("eserHareketEnvanterNo")
    private List<String>      eserHareketEnvanterNo;

    @Field("eserHareketCikarilmaTarihi")
    private List<Date>        eserHareketCikarilmaTarihi;

    @Field("eserHareketOnayTarihi")
    private List<Date>        eserHareketOnayTarihi;

    @Field("eserHareketOnaySayisi")
    private List<String>      eserHareketOnaySayisi;

    @Field("eserHareketMuzeyeGelisTarihi")
    private List<Date>        eserHareketMuzeyeGelisTarihi;

    @Field("eserHareketEnvantereAlinmaTarihi")
    private List<Date>        eserHareketEnvantereAlinmaTarihi;

    @Field("eserHareketAciklama")
    private List<String>      eserHareketAciklama;

    @Field("eserHareketBuluntuYeri")
    private List<String>      eserHareketBuluntuYeri;

    @Field("eserHareketEleGecirmeYeri")
    private List<String>      eserHareketEleGecirmeYeri;

    @Field("eserHareketAktif")
    private List<Boolean>     eserHareketAktif;

    @Field("eserHareketSilinmis")
    private List<Boolean>     eserHareketSilinmis;

    @Field("kaziTurDeger")
    private List<String>      kaziTurDeger;

    @Field("kaziTurAd")
    private List<String>      kaziTurAd;

    @Field("kaziAd")
    private List<String>      kaziAd;

    @Field("kaziAciklama")
    private List<String>      kaziAciklama;

    @Field("kaziIlId")
    private List<Integer>     kaziIlId;

    @Field("kaziEnlem")
    private List<String>      kaziEnlem;

    @Field("kaziBoylam")
    private List<String>      kaziBoylam;

    @Field("arastirmaTurAd")
    private List<String>      arastirmaTurAd;

    @Field("arastirmaTurAciklama")
    private List<String>      arastirmaTurAciklama;

    @Field("arastirmaAd")
    private List<String>      arastirmaAd;

    @Field("arastirmaAciklama")
    private List<String>      arastirmaAciklama;

    @Field("kaziBeldeAdi")
    private List<String>      kaziBeldeAdi;

    @Field("kaziKaziKodu")
    private List<String>      kaziKaziKodu;

    @Field("eserHareketEserGelisSekliAd")
    private List<String>      eserHareketEserGelisSekliAd;

    @Field("eserAnahtarKelimeAd")
    private List<String>      eserAnahtarKelimeAd;

    @Field("tarihsellik")
    private Boolean           tarihsellik;

    @Field("eserFotografFotografPath")
    private String            eserFotografFotografPath;

    @Field("eserTranskripsiyonMetinCevirisi")
    private List<String>      eserTranskripsiyonMetinCevirisi;

    @Field("eserFotografAciklama")
    private List<String>      eserFotografAciklama;

    @Field("dilAd")
    private List<String>      dilAd;

    @Field("uretimyeriAciklama")
    private String            uretimyeriAciklama;

    @Field("uretimyeriAd")
    private List<String>      uretimyeriAd;

    @Field("eserTurAd")
    private String            eserTurAd;

    @Field("ulkeAd")
    private String            ulkeAd;

    @Field("unvanAd")
    private String            unvanAd;

    @Field("uretimbolgesiAd")
    private List<String>      uretimbolgesiAd;

    @Field("uretimbolgesiAciklama")
    private List<String>      uretimbolgesiAciklama;

    @Field("uzmanlikAlaniAd")
    private List<String>      uzmanlikAlaniAd;

    @Field("uzmanlikAlaniAciklama")
    private List<String>      uzmanlikAlaniAciklama;

    @Field("yayinAd")
    private List<String>      yayinAd;

    @Field("yayinAciklama")
    private List<String>      yayinAciklama;

    @Field("yaziTipiAd")
    private List<String>      yaziTipiAd;

    @Field("yaziTipiAciklama")
    private List<String>      yaziTipiAciklama;

    @Field("sahisAd")
    private List<String>      sahisAd;

    @Field("sahisAdSoyad")
    private List<String>      sahisSoyad;

    @Field("sahisSoyad")
    private List<String>      sahisAdSoyad;

    @Field("onemliKisiAd")
    private String            onemliKisiAd;

    @Field("onemliKisiAciklama")
    private String            onemliKisiAciklama;

    @Field("meslekAd")
    private String            meslekAd;

    @Field("meslekAciklama")
    private String            meslekAciklama;

    @Field("ihtisasElemaniSicilNo")
    private String            ihtisasElemaniSicilNo;

    @Field("olcuAd")
    private List<String>      olcuAd;

    @Field("olcuTipi")
    private List<String>      olcuTipi;

    @Field("olcuDeger")
    private List<String>      olcuDeger;

    @Field("kaynakAd")
    private List<String>      kaynakAd;

    @Field("kaynakAciklama")
    private List<String>      kaynakAciklama;

    @Field("kadroDurumAd")
    private String            kadroDurumAd;

    @Field("iliskilendirmeturgrubuAd")
    private List<String>      iliskilendirmeturgrubuAd;

    @Field("eserserhMetin")
    private List<String>      eserserhMetin;

    @Field("atolyeAd")
    private List<String>      atolyeAd;

    @Field("eserYapimTeknigiAd")
    private List<String>      eserYapimTeknigiAd;

    @Field("eserYapimTeknigiAciklama")
    private List<String>      eserYapimTeknigiAciklama;

    @Field("yapimTeknigiRenkAd")
    private List<String>      yapimTeknigiRenkAd;

    @Field("yapimTeknigiRenkCMYK")
    private List<String>      yapimTeknigiRenkCMYK;

    @Field("yapimTeknigiRenkNCS")
    private List<String>      yapimTeknigiRenkNCS;

    @Field("yapimTeknigiRenkRGB")
    private List<String>      yapimTeknigiRenkRGB;

    @Field("yapimTeknigiRenkMueskod")
    private List<String>      yapimTeknigiRenkMueskod;

    @Field("yapimTeknigiMalzeme")
    private List<String>      yapimTeknigiMalzeme;

    @Field("suslemeTeknigiMalzeme")
    private List<String>      suslemeTeknigiMalzeme;

    @Field("suslemeTeknigiAd")
    private List<String>      suslemeTeknigiAd;

    @Field("suslemeTeknigiAciklama")
    private List<String>      suslemeTeknigiAciklama;

    @Field("suslemeTeknigiRenkAd")
    private List<String>      suslemeTeknigiRenkAd;

    @Field("suslemeTeknigiRenkCMYK")
    private List<String>      suslemeTeknigiRenkCMYK;

    @Field("suslemeTeknigiRenkNCS")
    private List<String>      suslemeTeknigiRenkNCS;

    @Field("suslemeTeknigiRenkRGB")
    private List<String>      suslemeTeknigiRenkRGB;

    @Field("suslemeTeknigiRenkMueskod")
    private List<String>      suslemeTeknigiRenkMueskod;

    @Field("eserZimmetPersonelAd")
    private List<String>      eserZimmetPersonelAd;

    @Field("eserZimmetPersonelSoyad")
    private List<String>      eserZimmetPersonelSoyad;

    @Field("eserZimmetPersonelAdSoyad")
    private List<String>      eserZimmetPersonelAdSoyad;

    @Field("eserZimmetZimmetTarihi")
    private List<Date>        eserZimmetZimmetTarihi;

    @Field("eserMalzemeGrubuAd")
    private List<String>      eserMalzemeGrubuAd;

    @Field("ihtisasElemaniAd")
    private List<String>      ihtisasElemaniAd;

    @Field("ihtisasElemaniSoyad")
    private List<String>      ihtisasElemaniSoyad;

    @Field("ihtisasElemaniAdSoyad")
    private List<String>      ihtisasElemaniAdSoyad;

    @Field("eserSikkeDarpYeriAd")
    private List<String>      eserSikkeDarpYeriAd;

    @Field("tuzelKisiTicariUnvan")
    private List<String>      tuzelKisiTicariUnvan;

    @Field("eserHareketMuzeAd")
    private List<String>      eserHareketMuzeAd;

    @Field("eserHareketTeslimAlanPersonelAdSoyad")
    private List<String>      eserHareketTeslimAlanPersonelAdSoyad;

    @Field("eserHareketTeslimEdenPersonelAdSoyad")
    private List<String>      eserHareketTeslimEdenPersonelAdSoyad;

    @Field("personelYabanciDilAd")
    private List<String>      personelYabanciDilAd;

    @Field("personelYabanciDilAciklama")
    private List<String>      personelYabanciDilAciklama;

    @Field("personelYabanciDilSeviye")
    private List<String>      personelYabanciDilSeviye;

    @Field("sayisi")
    private String            sayisi;

    @Field("webSite")
    private String            webSite;

    private Integer           rank;

    @Override
    public Integer getId() {
        return this.id;
    }

    public String getUniqueId() {
        return this.eserEserId;
    }

    public void setId(final Integer id) {
        this.id = id;
    }

    public String getAlanAciklama() {
        return this.alanAciklama;
    }

    public void setAlanAciklama(final String alanAciklama) {
        this.alanAciklama = alanAciklama;
    }

    public String getAlanAd() {
        return this.alanAd;
    }

    public void setAlanAd(final String alanAd) {
        this.alanAd = alanAd;
    }

    public String getAlanKod() {
        return this.alanKod;
    }

    public void setAlanKod(final String alanKod) {
        this.alanKod = alanKod;
    }

    public String getAlanKonumuAd() {
        return this.alanKonumuAd;
    }

    public void setAlanKonumuAd(final String alanKonumuAd) {
        this.alanKonumuAd = alanKonumuAd;
    }

    public String getAlanKonumuTurAd() {
        return this.alanKonumuTurAd;
    }

    public void setAlanKonumuTurAd(final String alanKonumuTurAd) {
        this.alanKonumuTurAd = alanKonumuTurAd;
    }

    public String getAlanKonumuTurDeger() {
        return this.alanKonumuTurDeger;
    }

    public void setAlanKonumuTurDeger(final String alanKonumuTurDeger) {
        this.alanKonumuTurDeger = alanKonumuTurDeger;
    }

    public String getAlanTurAd() {
        return this.alanTurAd;
    }

    public void setAlanTurAd(final String alanTurAd) {
        this.alanTurAd = alanTurAd;
    }

    public String getAlanTurDeger() {
        return this.alanTurDeger;
    }

    public void setAlanTurDeger(final String alanTurDeger) {
        this.alanTurDeger = alanTurDeger;
    }

    public Integer getArtifactState() {
        return this.artifactState;
    }

    public void setArtifactState(final Integer artifactState) {
        this.artifactState = artifactState;
    }

    public String getBagliMudurlukAd() {
        return this.bagliMudurlukAd;
    }

    public void setBagliMudurlukAd(final String bagliMudurlukAd) {
        this.bagliMudurlukAd = bagliMudurlukAd;
    }

    public String getBagliBirimAciklama() {
        return this.bagliBirimAciklama;
    }

    public void setBagliBirimAciklama(final String bagliBirimAciklama) {
        this.bagliBirimAciklama = bagliBirimAciklama;
    }

    public String getBagliBirimAd() {
        return this.bagliBirimAd;
    }

    public void setBagliBirimAd(final String bagliBirimAd) {
        this.bagliBirimAd = bagliBirimAd;
    }

    public String getBagliBirimAdres() {
        return this.bagliBirimAdres;
    }

    public void setBagliBirimAdres(final String bagliBirimAdres) {
        this.bagliBirimAdres = bagliBirimAdres;
    }

    public String getBagliBirimBoylam() {
        return this.bagliBirimBoylam;
    }

    public void setBagliBirimBoylam(final String bagliBirimBoylam) {
        this.bagliBirimBoylam = bagliBirimBoylam;
    }

    public String getBagliBirimEnlem() {
        return this.bagliBirimEnlem;
    }

    public void setBagliBirimEnlem(final String bagliBirimEnlem) {
        this.bagliBirimEnlem = bagliBirimEnlem;
    }

    public String getBagliBirimEpostaKurumsal() {
        return this.bagliBirimEpostaKurumsal;
    }

    public void setBagliBirimEpostaKurumsal(final String bagliBirimEpostaKurumsal) {
        this.bagliBirimEpostaKurumsal = bagliBirimEpostaKurumsal;
    }

    public String getBagliBirimFaks1() {
        return this.bagliBirimFaks1;
    }

    public void setBagliBirimFaks1(final String bagliBirimFaks1) {
        this.bagliBirimFaks1 = bagliBirimFaks1;
    }

    public String getBagliBirimFotografPath() {
        return this.bagliBirimFotografPath;
    }

    public void setBagliBirimFotografPath(final String bagliBirimFotografPath) {
        this.bagliBirimFotografPath = bagliBirimFotografPath;
    }

    public String getBagliBirimKod() {
        return this.bagliBirimKod;
    }

    public void setBagliBirimKod(final String bagliBirimKod) {
        this.bagliBirimKod = bagliBirimKod;
    }

    public String getBagliBirimTelefonNo1() {
        return this.bagliBirimTelefonNo1;
    }

    public void setBagliBirimTelefonNo1(final String bagliBirimTelefonNo1) {
        this.bagliBirimTelefonNo1 = bagliBirimTelefonNo1;
    }

    public String getBagliBirimWebSayfasi() {
        return this.bagliBirimWebSayfasi;
    }

    public void setBagliBirimWebSayfasi(final String bagliBirimWebSayfasi) {
        this.bagliBirimWebSayfasi = bagliBirimWebSayfasi;
    }

    public String getBinaAciklama() {
        return this.binaAciklama;
    }

    public void setBinaAciklama(final String binaAciklama) {
        this.binaAciklama = binaAciklama;
    }

    public String getBinaAd() {
        return this.binaAd;
    }

    public void setBinaAd(final String binaAd) {
        this.binaAd = binaAd;
    }

    public String getBinaKod() {
        return this.binaKod;
    }

    public void setBinaKod(final String binaKod) {
        this.binaKod = binaKod;
    }

    public String getEserAciklama() {
        return this.eserAciklama;
    }

    public void setEserAciklama(final String eserAciklama) {
        this.eserAciklama = eserAciklama;
    }

    public Boolean getAktif() {
        return this.aktif;
    }

    public void setAktif(final Boolean aktif) {
        this.aktif = aktif;
    }

    public Boolean getEserBirlestirilmisEser() {
        return this.eserBirlestirilmisEser;
    }

    public void setEserBirlestirilmisEser(final Boolean eserBirlestirilmisEser) {
        this.eserBirlestirilmisEser = eserBirlestirilmisEser;
    }

    public Boolean getEserDonemBaslangicGo() {
        return this.eserDonemBaslangicGo;
    }

    public void setEserDonemBaslangicGo(final Boolean eserDonemBaslangicGo) {
        this.eserDonemBaslangicGo = eserDonemBaslangicGo;
    }

    public Integer getEserDonemBaslangicYil() {
        return this.eserDonemBaslangicYil;
    }

    public void setEserDonemBaslangicYil(final Integer eserDonemBaslangicYil) {
        this.eserDonemBaslangicYil = eserDonemBaslangicYil;
    }

    public Boolean getEserDonemBitisGo() {
        return this.eserDonemBitisGo;
    }

    public void setEserDonemBitisGo(final Boolean eserDonemBitisGo) {
        this.eserDonemBitisGo = eserDonemBitisGo;
    }

    public Integer getEserDonemBitisYil() {
        return this.eserDonemBitisYil;
    }

    public void setEserDonemBitisYil(final Integer eserDonemBitisYil) {
        this.eserDonemBitisYil = eserDonemBitisYil;
    }

    public Date getEserDuzenlemeZamani() {
        return this.eserDuzenlemeZamani;
    }

    public void setEserDuzenlemeZamani(final Date eserDuzenlemeZamani) {
        this.eserDuzenlemeZamani = eserDuzenlemeZamani;
    }

    public String getEserElisiDokumaSecimiAd() {
        return this.eserElisiDokumaSecimiAd;
    }

    public void setEserElisiDokumaSecimiAd(final String eserElisiDokumaSecimiAd) {
        this.eserElisiDokumaSecimiAd = eserElisiDokumaSecimiAd;
    }

    public String getEserEnvanterDefteriPath() {
        return this.eserEnvanterDefteriPath;
    }

    public void setEserEnvanterDefteriPath(final String eserEnvanterDefteriPath) {
        this.eserEnvanterDefteriPath = eserEnvanterDefteriPath;
    }

    public String getEserEskiEnvanterNo() {
        return this.eserEskiEnvanterNo;
    }

    public void setEserEskiEnvanterNo(final String eserEskiEnvanterNo) {
        this.eserEskiEnvanterNo = eserEskiEnvanterNo;
    }

    public String getEserEserOzelAdi() {
        return this.eserEserOzelAdi;
    }

    public void setEserEserOzelAdi(final String eserEserOzelAdi) {
        this.eserEserOzelAdi = eserEserOzelAdi;
    }

    public Integer getEserEseriBagislayanOnemliKisiId() {
        return this.eserEseriBagislayanOnemliKisiId;
    }

    public void setEserEseriBagislayanOnemliKisiId(final Integer eserEseriBagislayanOnemliKisiId) {
        this.eserEseriBagislayanOnemliKisiId = eserEseriBagislayanOnemliKisiId;
    }

    public Integer getEserEseriKullanacakOnemliKisiId() {
        return this.eserEseriKullanacakOnemliKisiId;
    }

    public void setEserEseriKullanacakOnemliKisiId(final Integer eserEseriKullanacakOnemliKisiId) {
        this.eserEseriKullanacakOnemliKisiId = eserEseriKullanacakOnemliKisiId;
    }

    public Integer getEserEseriKullananOnemliKisiId() {
        return this.eserEseriKullananOnemliKisiId;
    }

    public void setEserEseriKullananOnemliKisiId(final Integer eserEseriKullananOnemliKisiId) {
        this.eserEseriKullananOnemliKisiId = eserEseriKullananOnemliKisiId;
    }

    public Integer getEserEseriYapanOnemliKisiId() {
        return this.eserEseriYapanOnemliKisiId;
    }

    public void setEserEseriYapanOnemliKisiId(final Integer eserEseriYapanOnemliKisiId) {
        this.eserEseriYapanOnemliKisiId = eserEseriYapanOnemliKisiId;
    }

    public Integer getEserEseriYaptiranOnemliKisiId() {
        return this.eserEseriYaptiranOnemliKisiId;
    }

    public void setEserEseriYaptiranOnemliKisiId(final Integer eserEseriYaptiranOnemliKisiId) {
        this.eserEseriYaptiranOnemliKisiId = eserEseriYaptiranOnemliKisiId;
    }

    public String getEserGenelAciklama() {
        return this.eserGenelAciklama;
    }

    public void setEserGenelAciklama(final String eserGenelAciklama) {
        this.eserGenelAciklama = eserGenelAciklama;
    }

    public Integer getEserIslamiGayriSecimi() {
        return this.eserIslamiGayriSecimi;
    }

    public void setEserIslamiGayriSecimi(final Integer eserIslamiGayriSecimi) {
        this.eserIslamiGayriSecimi = eserIslamiGayriSecimi;
    }

    public Double getEserKiymet() {
        return this.eserKiymet;
    }

    public void setEserKiymet(final Double eserKiymet) {
        this.eserKiymet = eserKiymet;
    }

    public String getEserEserId() {
        return this.eserEserId;
    }

    public void setEserEserId(final String eserEserId) {
        this.eserEserId = eserEserId;
    }

    public Integer getEserKondisyonDurumu() {
        return this.eserKondisyonDurumu;
    }

    public void setEserKondisyonDurumu(final Integer eserKondisyonDurumu) {
        this.eserKondisyonDurumu = eserKondisyonDurumu;
    }

    public String getEserSerh() {
        return this.eserSerh;
    }

    public void setEserSerh(final String eserSerh) {
        this.eserSerh = eserSerh;
    }

    public Boolean getEserSikkeDarpGo() {
        return this.eserSikkeDarpGo;
    }

    public void setEserSikkeDarpGo(final Boolean eserSikkeDarpGo) {
        this.eserSikkeDarpGo = eserSikkeDarpGo;
    }

    public Boolean getEser3DFile() {
        return this.eser3DFile;
    }

    public void setEser3DFile(final Boolean eser3DFile) {
        this.eser3DFile = eser3DFile;
    }

    public Integer getEserSikkeDarpYeriId() {
        return this.eserSikkeDarpYeriId;
    }

    public void setEserSikkeDarpYeriId(final Integer eserSikkeDarpYeriId) {
        this.eserSikkeDarpYeriId = eserSikkeDarpYeriId;
    }

    public Integer getEserSikkeDarpYili() {
        return this.eserSikkeDarpYili;
    }

    public void setEserSikkeDarpYili(final Integer eserSikkeDarpYili) {
        this.eserSikkeDarpYili = eserSikkeDarpYili;
    }

    public Integer getEserSikkeDarpYonu() {
        return this.eserSikkeDarpYonu;
    }

    public void setEserSikkeDarpYonu(final Integer eserSikkeDarpYonu) {
        this.eserSikkeDarpYonu = eserSikkeDarpYonu;
    }

    public Boolean getEserSilinmis() {
        return this.eserSilinmis;
    }

    public void setEserSilinmis(final Boolean eserSilinmis) {
        this.eserSilinmis = eserSilinmis;
    }

    public String getEserSilmeAciklamasi() {
        return this.eserSilmeAciklamasi;
    }

    public void setEserSilmeAciklamasi(final String eserSilmeAciklamasi) {
        this.eserSilmeAciklamasi = eserSilmeAciklamasi;
    }

    public Date getEserSilmeZamani() {
        return this.eserSilmeZamani;
    }

    public void setEserSilmeZamani(final Date eserSilmeZamani) {
        this.eserSilmeZamani = eserSilmeZamani;
    }

    public String getEserTanimlayiciFotografPath() {
        return this.eserTanimlayiciFotografPath;
    }

    public void setEserTanimlayiciFotografPath(final String eserTanimlayiciFotografPath) {
        this.eserTanimlayiciFotografPath = eserTanimlayiciFotografPath;
    }

    public String getEserTasinirIslemFisiNo() {
        return this.eserTasinirIslemFisiNo;
    }

    public void setEserTasinirIslemFisiNo(final String eserTasinirIslemFisiNo) {
        this.eserTasinirIslemFisiNo = eserTasinirIslemFisiNo;
    }

    public Integer getEserTasinirMalYonId() {
        return this.eserTasinirMalYonId;
    }

    public void setEserTasinirMalYonId(final Integer eserTasinirMalYonId) {
        this.eserTasinirMalYonId = eserTasinirMalYonId;
    }

    public Boolean getEserTorenselDurumu() {
        return this.eserTorenselDurumu;
    }

    public void setEserTorenselDurumu(final Boolean eserTorenselDurumu) {
        this.eserTorenselDurumu = eserTorenselDurumu;
    }

    public Boolean getEserUniklikDurumu() {
        return this.eserUniklikDurumu;
    }

    public void setEserUniklikDurumu(final Boolean eserUniklikDurumu) {
        this.eserUniklikDurumu = eserUniklikDurumu;
    }

    public Integer getEserVersiyon() {
        return this.eserVersiyon;
    }

    public void setEserVersiyon(final Integer eserVersiyon) {
        this.eserVersiyon = eserVersiyon;
    }

    public Integer getEserYaratmaKullaniciId() {
        return this.eserYaratmaKullaniciId;
    }

    public void setEserYaratmaKullaniciId(final Integer eserYaratmaKullaniciId) {
        this.eserYaratmaKullaniciId = eserYaratmaKullaniciId;
    }

    public Date getEserYaratmaZamani() {
        return this.eserYaratmaZamani;
    }

    public void setEserYaratmaZamani(final Date eserYaratmaZamani) {
        this.eserYaratmaZamani = eserYaratmaZamani;
    }

    public Integer getEserYazmaBasmaSecimi() {
        return this.eserYazmaBasmaSecimi;
    }

    public void setEserYazmaBasmaSecimi(final Integer eserYazmaBasmaSecimi) {
        this.eserYazmaBasmaSecimi = eserYazmaBasmaSecimi;
    }

    public String getCagAciklama() {
        return this.cagAciklama;
    }

    public void setCagAciklama(final String cagAciklama) {
        this.cagAciklama = cagAciklama;
    }

    public String getCagAd() {
        return this.cagAd;
    }

    public void setCagAd(final String cagAd) {
        this.cagAd = cagAd;
    }

    public List<Date> getEserZimmetZimmetTarihi() {
        return this.eserZimmetZimmetTarihi;
    }

    public void setEserZimmetZimmetTarihi(final List<Date> eserZimmetZimmetTarihi) {
        this.eserZimmetZimmetTarihi = eserZimmetZimmetTarihi;
    }

    public String getDonemAd() {
        return this.donemAd;
    }

    public void setDonemAd(final String donemAd) {
        this.donemAd = donemAd;
    }

    public String getHukumdarAciklama() {
        return this.hukumdarAciklama;
    }

    public void setHukumdarAciklama(final String hukumdarAciklama) {
        this.hukumdarAciklama = hukumdarAciklama;
    }

    public String getHukumdarAd() {
        return this.hukumdarAd;
    }

    public void setHukumdarAd(final String hukumdarAd) {
        this.hukumdarAd = hukumdarAd;
    }

    public Boolean getHukumdarDonemBaslangicGo() {
        return this.hukumdarDonemBaslangicGo;
    }

    public void setHukumdarDonemBaslangicGo(final Boolean hukumdarDonemBaslangicGo) {
        this.hukumdarDonemBaslangicGo = hukumdarDonemBaslangicGo;
    }

    public Integer getHukumdarDonemBaslangicYil() {
        return this.hukumdarDonemBaslangicYil;
    }

    public void setHukumdarDonemBaslangicYil(final Integer hukumdarDonemBaslangicYil) {
        this.hukumdarDonemBaslangicYil = hukumdarDonemBaslangicYil;
    }

    public Boolean getHukumdarDonemBitisGo() {
        return this.hukumdarDonemBitisGo;
    }

    public void setHukumdarDonemBitisGo(final Boolean hukumdarDonemBitisGo) {
        this.hukumdarDonemBitisGo = hukumdarDonemBitisGo;
    }

    public Integer getHukumdarDonemBitisYil() {
        return this.hukumdarDonemBitisYil;
    }

    public void setHukumdarDonemBitisYil(final Integer hukumdarDonemBitisYil) {
        this.hukumdarDonemBitisYil = hukumdarDonemBitisYil;
    }

    public String getIlAd() {
        return this.ilAd;
    }

    public void setIlAd(final String ilAd) {
        this.ilAd = ilAd;
    }

    public String getIlDeger() {
        return this.ilDeger;
    }

    public void setIlDeger(final String ilDeger) {
        this.ilDeger = ilDeger;
    }

    public String getIlceAd() {
        return this.ilceAd;
    }

    public void setIlceAd(final String ilceAd) {
        this.ilceAd = ilceAd;
    }

    public String getKronolojiAciklama() {
        return this.kronolojiAciklama;
    }

    public void setKronolojiAciklama(final String kronolojiAciklama) {
        this.kronolojiAciklama = kronolojiAciklama;
    }

    public String getKronolojiAd() {
        return this.kronolojiAd;
    }

    public void setKronolojiAd(final String kronolojiAd) {
        this.kronolojiAd = kronolojiAd;
    }

    public String getKullaniciKullaniciAdi() {
        return this.kullaniciKullaniciAdi;
    }

    public void setKullaniciKullaniciAdi(final String kullaniciKullaniciAdi) {
        this.kullaniciKullaniciAdi = kullaniciKullaniciAdi;
    }

    public String getMuzeMudurluguAciklama() {
        return this.muzeMudurluguAciklama;
    }

    public void setMuzeMudurluguAciklama(final String muzeMudurluguAciklama) {
        this.muzeMudurluguAciklama = muzeMudurluguAciklama;
    }

    public String getMudurlukAd() {
        return this.mudurlukAd;
    }

    public void setMudurlukAd(final String mudurlukAd) {
        this.mudurlukAd = mudurlukAd;
    }

    public String getMuzeMudurluguAd() {
        return this.muzeMudurluguAd;
    }

    public void setMuzeMudurluguAd(final String muzeMudurluguAd) {
        this.muzeMudurluguAd = muzeMudurluguAd;
    }

    public String getMuzeMudurluguBoylam() {
        return this.muzeMudurluguBoylam;
    }

    public void setMuzeMudurluguBoylam(final String muzeMudurluguBoylam) {
        this.muzeMudurluguBoylam = muzeMudurluguBoylam;
    }

    public String getMuzeMudurluguEnlem() {
        return this.muzeMudurluguEnlem;
    }

    public void setMuzeMudurluguEnlem(final String muzeMudurluguEnlem) {
        this.muzeMudurluguEnlem = muzeMudurluguEnlem;
    }

    public String getMuzeMudurluguKod() {
        return this.muzeMudurluguKod;
    }

    public void setMuzeMudurluguKod(final String muzeMudurluguKod) {
        this.muzeMudurluguKod = muzeMudurluguKod;
    }

    public String getUygarlikAciklama() {
        return this.uygarlikAciklama;
    }

    public void setUygarlikAciklama(final String uygarlikAciklama) {
        this.uygarlikAciklama = uygarlikAciklama;
    }

    public String getUygarlikAd() {
        return this.uygarlikAd;
    }

    public void setUygarlikAd(final String uygarlikAd) {
        this.uygarlikAd = uygarlikAd;
    }

    public String getEserAltTurAd() {
        return this.eserAltTurAd;
    }

    public void setEserAltTurAd(final String eserAltTurAd) {
        this.eserAltTurAd = eserAltTurAd;
    }

    public String getTasinirMalYonetmeligiKodAd() {
        return this.tasinirMalYonetmeligiKodAd;
    }

    public void setTasinirMalYonetmeligiKodAd(final String tasinirMalYonetmeligiKodAd) {
        this.tasinirMalYonetmeligiKodAd = tasinirMalYonetmeligiKodAd;
    }

    public String getEserKondisyonDurumuAd() {
        return this.eserKondisyonDurumuAd;
    }

    public void setEserKondisyonDurumuAd(final String eserKondisyonDurumuAd) {
        this.eserKondisyonDurumuAd = eserKondisyonDurumuAd;
    }

    public List<Integer> getEserHareketEserGelisSekli() {
        return this.eserHareketEserGelisSekli;
    }

    public void setEserHareketEserGelisSekli(final List<Integer> eserHareketEserGelisSekli) {
        this.eserHareketEserGelisSekli = eserHareketEserGelisSekli;
    }

    public List<String> getEserHareketEnvanterNo() {
        return this.eserHareketEnvanterNo;
    }

    public void setEserHareketEnvanterNo(final List<String> eserHareketEnvanterNo) {
        this.eserHareketEnvanterNo = eserHareketEnvanterNo;
    }

    public List<Date> getEserHareketCikarilmaTarihi() {
        return this.eserHareketCikarilmaTarihi;
    }

    public void setEserHareketCikarilmaTarihi(final List<Date> eserHareketCikarilmaTarihi) {
        this.eserHareketCikarilmaTarihi = eserHareketCikarilmaTarihi;
    }

    public List<Date> getEserHareketOnayTarihi() {
        return this.eserHareketOnayTarihi;
    }

    public void setEserHareketOnayTarihi(final List<Date> eserHareketOnayTarihi) {
        this.eserHareketOnayTarihi = eserHareketOnayTarihi;
    }

    public List<String> getEserHareketOnaySayisi() {
        return this.eserHareketOnaySayisi;
    }

    public void setEserHareketOnaySayisi(final List<String> eserHareketOnaySayisi) {
        this.eserHareketOnaySayisi = eserHareketOnaySayisi;
    }

    public List<Date> getEserHareketMuzeyeGelisTarihi() {
        return this.eserHareketMuzeyeGelisTarihi;
    }

    public void setEserHareketMuzeyeGelisTarihi(final List<Date> eserHareketMuzeyeGelisTarihi) {
        this.eserHareketMuzeyeGelisTarihi = eserHareketMuzeyeGelisTarihi;
    }

    public List<Date> getEserHareketEnvantereAlinmaTarihi() {
        return this.eserHareketEnvantereAlinmaTarihi;
    }

    public void setEserHareketEnvantereAlinmaTarihi(final List<Date> eserHareketEnvantereAlinmaTarihi) {
        this.eserHareketEnvantereAlinmaTarihi = eserHareketEnvantereAlinmaTarihi;
    }

    public List<String> getEserHareketAciklama() {
        return this.eserHareketAciklama;
    }

    public void setEserHareketAciklama(final List<String> eserHareketAciklama) {
        this.eserHareketAciklama = eserHareketAciklama;
    }

    public List<String> getEserHareketBuluntuYeri() {
        return this.eserHareketBuluntuYeri;
    }

    public void setEserHareketBuluntuYeri(final List<String> eserHareketBuluntuYeri) {
        this.eserHareketBuluntuYeri = eserHareketBuluntuYeri;
    }

    public List<String> getEserHareketEleGecirmeYeri() {
        return this.eserHareketEleGecirmeYeri;
    }

    public void setEserHareketEleGecirmeYeri(final List<String> eserHareketEleGecirmeYeri) {
        this.eserHareketEleGecirmeYeri = eserHareketEleGecirmeYeri;
    }

    public List<Boolean> getEserHareketAktif() {
        return this.eserHareketAktif;
    }

    public void setEserHareketAktif(final List<Boolean> eserHareketAktif) {
        this.eserHareketAktif = eserHareketAktif;
    }

    public List<Boolean> getEserHareketSilinmis() {
        return this.eserHareketSilinmis;
    }

    public void setEserHareketSilinmis(final List<Boolean> eserHareketSilinmis) {
        this.eserHareketSilinmis = eserHareketSilinmis;
    }

    public List<String> getKaziTurDeger() {
        return this.kaziTurDeger;
    }

    public void setKaziTurDeger(final List<String> kaziTurDeger) {
        this.kaziTurDeger = kaziTurDeger;
    }

    public List<String> getKaziTurAd() {
        return this.kaziTurAd;
    }

    public void setKaziTurAd(final List<String> kaziTurAd) {
        this.kaziTurAd = kaziTurAd;
    }

    public List<String> getKaziAd() {
        return this.kaziAd;
    }

    public void setKaziAd(final List<String> kaziAd) {
        this.kaziAd = kaziAd;
    }

    public List<String> getKaziAciklama() {
        return this.kaziAciklama;
    }

    public void setKaziAciklama(final List<String> kaziAciklama) {
        this.kaziAciklama = kaziAciklama;
    }

    public List<Integer> getKaziIlId() {
        return this.kaziIlId;
    }

    public void setKaziIlId(final List<Integer> kaziIlId) {
        this.kaziIlId = kaziIlId;
    }

    public List<String> getKaziEnlem() {
        return this.kaziEnlem;
    }

    public void setKaziEnlem(final List<String> kaziEnlem) {
        this.kaziEnlem = kaziEnlem;
    }

    public List<String> getKaziBoylam() {
        return this.kaziBoylam;
    }

    public void setKaziBoylam(final List<String> kaziBoylam) {
        this.kaziBoylam = kaziBoylam;
    }

    public List<String> getArastirmaTurAd() {
        return this.arastirmaTurAd;
    }

    public void setArastirmaTurAd(final List<String> arastirmaTurAd) {
        this.arastirmaTurAd = arastirmaTurAd;
    }

    public List<String> getArastirmaTurAciklama() {
        return this.arastirmaTurAciklama;
    }

    public void setArastirmaTurAciklama(final List<String> arastirmaTurAciklama) {
        this.arastirmaTurAciklama = arastirmaTurAciklama;
    }

    public List<String> getArastirmaAd() {
        return this.arastirmaAd;
    }

    public void setArastirmaAd(final List<String> arastirmaAd) {
        this.arastirmaAd = arastirmaAd;
    }

    public List<String> getArastirmaAciklama() {
        return this.arastirmaAciklama;
    }

    public void setArastirmaAciklama(final List<String> arastirmaAciklama) {
        this.arastirmaAciklama = arastirmaAciklama;
    }

    public List<String> getKaziBeldeAdi() {
        return this.kaziBeldeAdi;
    }

    public void setKaziBeldeAdi(final List<String> kaziBeldeAdi) {
        this.kaziBeldeAdi = kaziBeldeAdi;
    }

    public List<String> getKaziKaziKodu() {
        return this.kaziKaziKodu;
    }

    public void setKaziKaziKodu(final List<String> kaziKaziKodu) {
        this.kaziKaziKodu = kaziKaziKodu;
    }

    public List<String> getEserHareketEserGelisSekliAd() {
        return this.eserHareketEserGelisSekliAd;
    }

    public void setEserHareketEserGelisSekliAd(final List<String> eserHareketEserGelisSekliAd) {
        this.eserHareketEserGelisSekliAd = eserHareketEserGelisSekliAd;
    }

    public List<String> getEserAnahtarKelimeAd() {
        return this.eserAnahtarKelimeAd;
    }

    public void setEserAnahtarKelimeAd(final List<String> eserAnahtarKelimeAd) {
        this.eserAnahtarKelimeAd = eserAnahtarKelimeAd;
    }

    public String getEserHareketEserGelisSekliAdList() {
        if ((this.eserHareketEserGelisSekliAd == null) || this.eserHareketEserGelisSekliAd.isEmpty()) {
            return null;
        }
        final StringJoiner sj = new StringJoiner(", ");
        this.eserHareketEserGelisSekliAd.stream().forEach(sj::add);
        return sj.toString();
    }

    public Boolean getTarihsellik() {
        return this.tarihsellik;
    }

    public void setTarihsellik(final Boolean tarihsellik) {
        this.tarihsellik = tarihsellik;
    }

    public String getEserFotografFotografPath() {
        return this.eserFotografFotografPath;
    }

    public void setEserFotografFotografPath(final String eserFotografFotografPath) {
        this.eserFotografFotografPath = eserFotografFotografPath;
    }

    public List<String> getEserTranskripsiyonMetinCevirisi() {
        return this.eserTranskripsiyonMetinCevirisi;
    }

    public void setEserTranskripsiyonMetinCevirisi(final List<String> eserTranskripsiyonMetinCevirisi) {
        this.eserTranskripsiyonMetinCevirisi = eserTranskripsiyonMetinCevirisi;
    }

    public List<String> getEserFotografAciklama() {
        return this.eserFotografAciklama;
    }

    public void setEserFotografAciklama(final List<String> eserFotografAciklama) {
        this.eserFotografAciklama = eserFotografAciklama;
    }

    public List<String> getDilAd() {
        return this.dilAd;
    }

    public void setDilAd(final List<String> dilAd) {
        this.dilAd = dilAd;
    }

    public String getUretimyeriAciklama() {
        return this.uretimyeriAciklama;
    }

    public void setUretimyeriAciklama(final String uretimyeriAciklama) {
        this.uretimyeriAciklama = uretimyeriAciklama;
    }

    public List<String> getUretimyeriAd() {
        return this.uretimyeriAd;
    }

    public void setUretimyeriAd(final List<String> uretimyeriAd) {
        this.uretimyeriAd = uretimyeriAd;
    }

    public String getEserTurAd() {
        return this.eserTurAd;
    }

    public void setEserTurAd(final String eserTurAd) {
        this.eserTurAd = eserTurAd;
    }

    public String getUlkeAd() {
        return this.ulkeAd;
    }

    public void setUlkeAd(final String ulkeAd) {
        this.ulkeAd = ulkeAd;
    }

    public String getUnvanAd() {
        return this.unvanAd;
    }

    public void setUnvanAd(final String unvanAd) {
        this.unvanAd = unvanAd;
    }

    public List<String> getUretimbolgesiAd() {
        return this.uretimbolgesiAd;
    }

    public void setUretimbolgesiAd(final List<String> uretimbolgesiAd) {
        this.uretimbolgesiAd = uretimbolgesiAd;
    }

    public List<String> getUretimbolgesiAciklama() {
        return this.uretimbolgesiAciklama;
    }

    public void setUretimbolgesiAciklama(final List<String> uretimbolgesiAciklama) {
        this.uretimbolgesiAciklama = uretimbolgesiAciklama;
    }

    public List<String> getUzmanlikAlaniAd() {
        return this.uzmanlikAlaniAd;
    }

    public void setUzmanlikAlaniAd(final List<String> uzmanlikAlaniAd) {
        this.uzmanlikAlaniAd = uzmanlikAlaniAd;
    }

    public List<String> getUzmanlikAlaniAciklama() {
        return this.uzmanlikAlaniAciklama;
    }

    public void setUzmanlikAlaniAciklama(final List<String> uzmanlikAlaniAciklama) {
        this.uzmanlikAlaniAciklama = uzmanlikAlaniAciklama;
    }

    public List<String> getYayinAd() {
        return this.yayinAd;
    }

    public void setYayinAd(final List<String> yayinAd) {
        this.yayinAd = yayinAd;
    }

    public List<String> getYayinAciklama() {
        return this.yayinAciklama;
    }

    public void setYayinAciklama(final List<String> yayinAciklama) {
        this.yayinAciklama = yayinAciklama;
    }

    public List<String> getYaziTipiAd() {
        return this.yaziTipiAd;
    }

    public void setYaziTipiAd(final List<String> yaziTipiAd) {
        this.yaziTipiAd = yaziTipiAd;
    }

    public List<String> getYaziTipiAciklama() {
        return this.yaziTipiAciklama;
    }

    public void setYaziTipiAciklama(final List<String> yaziTipiAciklama) {
        this.yaziTipiAciklama = yaziTipiAciklama;
    }

    public String getOnemliKisiAd() {
        return this.onemliKisiAd;
    }

    public void setOnemliKisiAd(final String onemliKisiAd) {
        this.onemliKisiAd = onemliKisiAd;
    }

    public String getOnemliKisiAciklama() {
        return this.onemliKisiAciklama;
    }

    public void setOnemliKisiAciklama(final String onemliKisiAciklama) {
        this.onemliKisiAciklama = onemliKisiAciklama;
    }

    public String getMeslekAd() {
        return this.meslekAd;
    }

    public void setMeslekAd(final String meslekAd) {
        this.meslekAd = meslekAd;
    }

    public String getMeslekAciklama() {
        return this.meslekAciklama;
    }

    public void setMeslekAciklama(final String meslekAciklama) {
        this.meslekAciklama = meslekAciklama;
    }

    public List<String> getOlcuAd() {
        return this.olcuAd;
    }

    public void setOlcuAd(final List<String> olcuAd) {
        this.olcuAd = olcuAd;
    }

    public List<String> getKaynakAd() {
        return this.kaynakAd;
    }

    public void setKaynakAd(final List<String> kaynakAd) {
        this.kaynakAd = kaynakAd;
    }

    public List<String> getKaynakAciklama() {
        return this.kaynakAciklama;
    }

    public void setKaynakAciklama(final List<String> kaynakAciklama) {
        this.kaynakAciklama = kaynakAciklama;
    }

    public String getKadroDurumAd() {
        return this.kadroDurumAd;
    }

    public void setKadroDurumAd(final String kadroDurumAd) {
        this.kadroDurumAd = kadroDurumAd;
    }

    public List<String> getIliskilendirmeturgrubuAd() {
        return this.iliskilendirmeturgrubuAd;
    }

    public void setIliskilendirmeturgrubuAd(final List<String> iliskilendirmeturgrubuAd) {
        this.iliskilendirmeturgrubuAd = iliskilendirmeturgrubuAd;
    }

    public List<String> getEserserhMetin() {
        return this.eserserhMetin;
    }

    public void setEserserhMetin(final List<String> eserserhMetin) {
        this.eserserhMetin = eserserhMetin;
    }

    public List<String> getEserYapimTeknigiAd() {
        return this.eserYapimTeknigiAd;
    }

    public void setEserYapimTeknigiAd(final List<String> eserYapimTeknigiAd) {
        this.eserYapimTeknigiAd = eserYapimTeknigiAd;
    }

    public List<String> getAtolyeAd() {
        return this.atolyeAd;
    }

    public void setAtolyeAd(final List<String> atolyeAd) {
        this.atolyeAd = atolyeAd;
    }

    public List<String> getSuslemeTeknigiAd() {
        return this.suslemeTeknigiAd;
    }

    public void setSuslemeTeknigiAd(final List<String> suslemeTeknigiAd) {
        this.suslemeTeknigiAd = suslemeTeknigiAd;
    }

    public List<String> getSuslemeTeknigiAciklama() {
        return this.suslemeTeknigiAciklama;
    }

    public void setSuslemeTeknigiAciklama(final List<String> suslemeTeknigiAciklama) {
        this.suslemeTeknigiAciklama = suslemeTeknigiAciklama;
    }

    public String getIhtisasElemaniSicilNo() {
        return this.ihtisasElemaniSicilNo;
    }

    public void setIhtisasElemaniSicilNo(final String ihtisasElemaniSicilNo) {
        this.ihtisasElemaniSicilNo = ihtisasElemaniSicilNo;
    }

    public List<String> getOlcuDeger() {
        return this.olcuDeger;
    }

    public void setOlcuDeger(final List<String> olcuDeger) {
        this.olcuDeger = olcuDeger;
    }

    public String getEserIslamiGayriSecimiAd() {
        return this.eserIslamiGayriSecimiAd;
    }

    public void setEserIslamiGayriSecimiAd(final String eserIslamiGayriSecimiAd) {
        this.eserIslamiGayriSecimiAd = eserIslamiGayriSecimiAd;
    }

    public String getEserSikkeDarpYonuAd() {
        return this.eserSikkeDarpYonuAd;
    }

    public void setEserSikkeDarpYonuAd(final String eserSikkeDarpYonuAd) {
        this.eserSikkeDarpYonuAd = eserSikkeDarpYonuAd;
    }

    public String getEserYazmaBasmaSecimiAd() {
        return this.eserYazmaBasmaSecimiAd;
    }

    public void setEserYazmaBasmaSecimiAd(final String eserYazmaBasmaSecimiAd) {
        this.eserYazmaBasmaSecimiAd = eserYazmaBasmaSecimiAd;
    }

    public Date getEserOnayZamani() {
        return this.eserOnayZamani;
    }

    public void setEserOnayZamani(final Date eserOnayZamani) {
        this.eserOnayZamani = eserOnayZamani;
    }

    public String getIliskilendirmeAciklama() {
        return this.iliskilendirmeAciklama;
    }

    public void setIliskilendirmeAciklama(final String iliskilendirmeAciklama) {
        this.iliskilendirmeAciklama = iliskilendirmeAciklama;
    }

    public String getIliskilendirmeAd() {
        return this.iliskilendirmeAd;
    }

    public void setIliskilendirmeAd(final String iliskilendirmeAd) {
        this.iliskilendirmeAd = iliskilendirmeAd;
    }

    public List<String> getEserYapimTeknigiAciklama() {
        return this.eserYapimTeknigiAciklama;
    }

    public void setEserYapimTeknigiAciklama(final List<String> eserYapimTeknigiAciklama) {
        this.eserYapimTeknigiAciklama = eserYapimTeknigiAciklama;
    }

    public List<String> getYapimTeknigiRenkAd() {
        return this.yapimTeknigiRenkAd;
    }

    public void setYapimTeknigiRenkAd(final List<String> yapimTeknigiRenkAd) {
        this.yapimTeknigiRenkAd = yapimTeknigiRenkAd;
    }

    public List<String> getYapimTeknigiRenkCMYK() {
        return this.yapimTeknigiRenkCMYK;
    }

    public void setYapimTeknigiRenkCMYK(final List<String> yapimTeknigiRenkCMYK) {
        this.yapimTeknigiRenkCMYK = yapimTeknigiRenkCMYK;
    }

    public List<String> getYapimTeknigiRenkNCS() {
        return this.yapimTeknigiRenkNCS;
    }

    public void setYapimTeknigiRenkNCS(final List<String> yapimTeknigiRenkNCS) {
        this.yapimTeknigiRenkNCS = yapimTeknigiRenkNCS;
    }

    public List<String> getYapimTeknigiRenkRGB() {
        return this.yapimTeknigiRenkRGB;
    }

    public void setYapimTeknigiRenkRGB(final List<String> yapimTeknigiRenkRGB) {
        this.yapimTeknigiRenkRGB = yapimTeknigiRenkRGB;
    }

    public List<String> getYapimTeknigiRenkMueskod() {
        return this.yapimTeknigiRenkMueskod;
    }

    public void setYapimTeknigiRenkMueskod(final List<String> yapimTeknigiRenkMueskod) {
        this.yapimTeknigiRenkMueskod = yapimTeknigiRenkMueskod;
    }

    public List<String> getYapimTeknigiMalzeme() {
        return this.yapimTeknigiMalzeme;
    }

    public void setYapimTeknigiMalzeme(final List<String> yapimTeknigiMalzeme) {
        this.yapimTeknigiMalzeme = yapimTeknigiMalzeme;
    }

    public List<String> getSuslemeTeknigiMalzeme() {
        return this.suslemeTeknigiMalzeme;
    }

    public void setSuslemeTeknigiMalzeme(final List<String> suslemeTeknigiMalzeme) {
        this.suslemeTeknigiMalzeme = suslemeTeknigiMalzeme;
    }

    public List<String> getSuslemeTeknigiRenkAd() {
        return this.suslemeTeknigiRenkAd;
    }

    public void setSuslemeTeknigiRenkAd(final List<String> suslemeTeknigiRenkAd) {
        this.suslemeTeknigiRenkAd = suslemeTeknigiRenkAd;
    }

    public List<String> getSuslemeTeknigiRenkCMYK() {
        return this.suslemeTeknigiRenkCMYK;
    }

    public void setSuslemeTeknigiRenkCMYK(final List<String> suslemeTeknigiRenkCMYK) {
        this.suslemeTeknigiRenkCMYK = suslemeTeknigiRenkCMYK;
    }

    public List<String> getSuslemeTeknigiRenkNCS() {
        return this.suslemeTeknigiRenkNCS;
    }

    public void setSuslemeTeknigiRenkNCS(final List<String> suslemeTeknigiRenkNCS) {
        this.suslemeTeknigiRenkNCS = suslemeTeknigiRenkNCS;
    }

    public List<String> getSuslemeTeknigiRenkRGB() {
        return this.suslemeTeknigiRenkRGB;
    }

    public void setSuslemeTeknigiRenkRGB(final List<String> suslemeTeknigiRenkRGB) {
        this.suslemeTeknigiRenkRGB = suslemeTeknigiRenkRGB;
    }

    public List<String> getSuslemeTeknigiRenkMueskod() {
        return this.suslemeTeknigiRenkMueskod;
    }

    public void setSuslemeTeknigiRenkMueskod(final List<String> suslemeTeknigiRenkMueskod) {
        this.suslemeTeknigiRenkMueskod = suslemeTeknigiRenkMueskod;
    }

    public List<String> getEserZimmetPersonelAd() {
        return this.eserZimmetPersonelAd;
    }

    public void setEserZimmetPersonelAd(final List<String> eserZimmetPersonelAd) {
        this.eserZimmetPersonelAd = eserZimmetPersonelAd;
    }

    public List<String> getEserZimmetPersonelSoyad() {
        return this.eserZimmetPersonelSoyad;
    }

    public void setEserZimmetPersonelSoyad(final List<String> eserZimmetPersonelSoyad) {
        this.eserZimmetPersonelSoyad = eserZimmetPersonelSoyad;
    }

    public List<String> getEserMalzemeGrubuAd() {
        return this.eserMalzemeGrubuAd;
    }

    public void setEserMalzemeGrubuAd(final List<String> eserMalzemeGrubuAd) {
        this.eserMalzemeGrubuAd = eserMalzemeGrubuAd;
    }

    public List<String> getIhtisasElemaniAd() {
        return this.ihtisasElemaniAd;
    }

    public void setIhtisasElemaniAd(final List<String> ihtisasElemaniAd) {
        this.ihtisasElemaniAd = ihtisasElemaniAd;
    }

    public List<String> getIhtisasElemaniSoyad() {
        return this.ihtisasElemaniSoyad;
    }

    public void setIhtisasElemaniSoyad(final List<String> ihtisasElemaniSoyad) {
        this.ihtisasElemaniSoyad = ihtisasElemaniSoyad;
    }

    public List<String> getEserSikkeDarpYeriAd() {
        return this.eserSikkeDarpYeriAd;
    }

    public void setEserSikkeDarpYeriAd(final List<String> eserSikkeDarpYeriAd) {
        this.eserSikkeDarpYeriAd = eserSikkeDarpYeriAd;
    }

    public List<String> getTuzelKisiTicariUnvan() {
        return this.tuzelKisiTicariUnvan;
    }

    public void setTuzelKisiTicariUnvan(final List<String> tuzelKisiTicariUnvan) {
        this.tuzelKisiTicariUnvan = tuzelKisiTicariUnvan;
    }

    public List<String> getEserHareketMuzeAd() {
        return this.eserHareketMuzeAd;
    }

    public void setEserHareketMuzeAd(final List<String> eserHareketMuzeAd) {
        this.eserHareketMuzeAd = eserHareketMuzeAd;
    }

    public List<String> getPersonelYabanciDilAd() {
        return this.personelYabanciDilAd;
    }

    public void setPersonelYabanciDilAd(final List<String> personelYabanciDilAd) {
        this.personelYabanciDilAd = personelYabanciDilAd;
    }

    public List<String> getPersonelYabanciDilAciklama() {
        return this.personelYabanciDilAciklama;
    }

    public void setPersonelYabanciDilAciklama(final List<String> personelYabanciDilAciklama) {
        this.personelYabanciDilAciklama = personelYabanciDilAciklama;
    }

    public List<String> getPersonelYabanciDilSeviye() {
        return this.personelYabanciDilSeviye;
    }

    public void setPersonelYabanciDilSeviye(final List<String> personelYabanciDilSeviye) {
        this.personelYabanciDilSeviye = personelYabanciDilSeviye;
    }

    public String getSayisi() {
        return this.sayisi;
    }

    public void setSayisi(final String sayisi) {
        this.sayisi = sayisi;
    }

    public String getEserTurveAltTurAd() {
        return this.eserTurAd + " > " + this.eserAltTurAd;
    }

    public void setEserTurveAltTurAd(final String eserTurAd, final String eserAltTurAd) {
        this.eserTurveAltTurAd = eserTurAd + " > " + eserAltTurAd;
    }

    public List<String> getSahisAd() {
        return this.sahisAd;
    }

    public void setSahisAd(final List<String> sahisAd) {
        this.sahisAd = sahisAd;
    }

    public List<String> getSahisSoyad() {
        return this.sahisSoyad;
    }

    public void setSahisSoyad(final List<String> sahisSoyad) {
        this.sahisSoyad = sahisSoyad;
    }

    public List<String> getSahisAdSoyad() {
        return this.sahisAdSoyad;
    }

    public void setSahisAdSoyad(final List<String> sahisAdSoyad) {
        this.sahisAdSoyad = sahisAdSoyad;
    }

    public List<String> getEserZimmetPersonelAdSoyad() {
        return this.eserZimmetPersonelAdSoyad;
    }

    public void setEserZimmetPersonelAdSoyad(final List<String> eserZimmetPersonelAdSoyad) {
        this.eserZimmetPersonelAdSoyad = eserZimmetPersonelAdSoyad;
    }

    public List<String> getIhtisasElemaniAdSoyad() {
        return this.ihtisasElemaniAdSoyad;
    }

    public void setIhtisasElemaniAdSoyad(final List<String> ihtisasElemaniAdSoyad) {
        this.ihtisasElemaniAdSoyad = ihtisasElemaniAdSoyad;
    }

    public List<String> getEserHareketTeslimAlanPersonelAdSoyad() {
        return this.eserHareketTeslimAlanPersonelAdSoyad;
    }

    public void setEserHareketTeslimAlanPersonelAdSoyad(final List<String> eserHareketTeslimAlanPersonelAdSoyad) {
        this.eserHareketTeslimAlanPersonelAdSoyad = eserHareketTeslimAlanPersonelAdSoyad;
    }

    public List<String> getEserHareketTeslimEdenPersonelAdSoyad() {
        return this.eserHareketTeslimEdenPersonelAdSoyad;
    }

    public void setEserHareketTeslimEdenPersonelAdSoyad(final List<String> eserHareketTeslimEdenPersonelAdSoyad) {
        this.eserHareketTeslimEdenPersonelAdSoyad = eserHareketTeslimEdenPersonelAdSoyad;
    }

    @Override
    public Integer getRank() {
        return this.rank;
    }

    @Override
    public void setRank(final Integer rank) {
        this.rank = rank;
    }

    @Override
    public Integer getUid() {
        return this.id;
    }

    @Override
    public int hashCode() {
        if (this.id == null) {
            return super.hashCode();
        }
        return this.id.hashCode();
    }

    @Override
    public boolean equals(final Object object) {
        if (this == object) {
            return true;
        }
        if (object == null) {
            return false;
        }
        if (this.getClass() != object.getClass()) {
            return false;
        }
        return this.hashCode() == object.hashCode();
    }

    public String getWebSite() {
        return this.webSite;
    }

    public void setWebSite(final String webSite) {
        this.webSite = webSite;
    }

    public List<String> getOlcuTipi() {
        return this.olcuTipi;
    }

    public void setOlcuTipi(final List<String> olcuTipi) {
        this.olcuTipi = olcuTipi;
    }

}
