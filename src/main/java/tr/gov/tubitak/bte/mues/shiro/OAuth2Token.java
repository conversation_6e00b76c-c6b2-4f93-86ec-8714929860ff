package tr.gov.tubitak.bte.mues.shiro;

import org.apache.shiro.authc.AuthenticationToken;

public class OAuth2Token implements AuthenticationToken {

    private static final long serialVersionUID = 4418328720533142565L;

    public OAuth2Token(final String authCode) {
        this.authCode = authCode;
    }

    private String authCode;

    private String principal;

    public String getAuthCode() {
        return this.authCode;
    }

    public void setAuthCode(final String authCode) {
        this.authCode = authCode;
    }

    @Override
    public String getPrincipal() {
        return this.principal;
    }

    public void setPrincipal(final String principal) {
        this.principal = principal;
    }

    @Override
    public Object getCredentials() {
        return this.authCode;
    }
}
