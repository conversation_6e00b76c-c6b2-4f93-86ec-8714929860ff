package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;

import tr.gov.tubitak.bte.mues.model.Bina;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.util.ApplicationSpecificQueries;

/**
 *
*
 */
@RequestScoped
public class BinaFacade extends AbstractFacade<Bina> {

    @Inject
    ApplicationSpecificQueries applicationSpecificQueries;

    public BinaFacade() {
        super(Bina.class);
    }

    public List<Bina> findByMudurluk(final List<Mudurluk> muzelist) {
        return this.em.createNamedQuery("Bina.findByMudurluk", Bina.class).setParameter("muzeler", muzelist).getResultList();
    }

    public void toggleActiveSelfAndDescendants(final Bina bina) {
        this.getEM().createNativeQuery(this.applicationSpecificQueries.activeSelfDescendantsBina()).setParameter(1, !bina.getAktif()).setParameter(2, bina.getId()).executeUpdate();
    }

}
