package tr.gov.tubitak.bte.mues.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.mues.model.Literatur;

/**
 *
*
 */
@RequestScoped
public class LiteraturFacade extends AbstractFacade<Literatur> {

    public LiteraturFacade() {
        super(Literatur.class);
    }
    
    public List<Literatur> findByIsbnOrIssn(String isbn, String issn) {
		return this.em.createNamedQuery("Literatur.findByIsbnOrIssn", Literatur.class).setParameter("isbn", isbn).setParameter("issn", issn).getResultList();		
    }

}
