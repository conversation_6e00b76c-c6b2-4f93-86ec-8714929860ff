package tr.gov.tubitak.bte.mues.model.mapping.lazytable;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.faces.application.FacesMessage;

import org.apache.solr.client.solrj.SolrQuery.ORDER;
import org.apache.solr.client.solrj.SolrQuery.SortClause;
import org.apache.solr.client.solrj.SolrServerException;
import org.apache.solr.client.solrj.response.QueryResponse;
import org.apache.solr.common.SolrDocumentList;
import org.apache.solr.common.SolrException;
import org.primefaces.PrimeFaces;
import org.primefaces.model.FilterMeta;
import org.primefaces.model.LazyDataModel;
import org.primefaces.model.SortMeta;
import org.primefaces.model.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.model.Metadata;
import tr.gov.tubitak.bte.mues.search.AbstractSimpleCriterion;
import tr.gov.tubitak.bte.mues.search.ComparisonOperatorEnum;
import tr.gov.tubitak.bte.mues.search.CriterionFactory;
import tr.gov.tubitak.bte.mues.search.CriterionModel;
import tr.gov.tubitak.bte.mues.search.SolrSearcher;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * TODO: is stale
 *
 */
public class LazyArtifactsDataModel extends LazyDataModel<ArtifactsSolrModel> {

    private static final long        serialVersionUID = -4739489425974372483L;

    private static final Logger      logger           = LoggerFactory.getLogger(LazyArtifactsDataModel.class);

    private final SolrSearcher       solrSearcher;

    private boolean                  firstTime;

    private List<ArtifactsSolrModel> data;

    public LazyArtifactsDataModel(final SolrSearcher solrSearcher) {
        this.solrSearcher = solrSearcher;
        this.firstTime = true;
    }

    @Override

    public List<ArtifactsSolrModel> load(final int first, final int pageSize, final Map<String, SortMeta> sortBy, final Map<String, FilterMeta> filterBy) {

        if ((filterBy != null)) {
            this.solrSearcher.getQuery().setFilterQueries();

            for (final Map.Entry<String, FilterMeta> entry : filterBy.entrySet()) {

                final String filterProperty = entry.getKey();

                final Object filterValue = entry.getValue().getFilterValue();

                if (filterValue != null) {
                    final Metadata metaData = this.solrSearcher.getMetaDataMap().get(filterProperty);
                    // TODO criterionmodel must behave like its nature or instinct
                    final CriterionModel criterionModel = new CriterionModel();
                    criterionModel.setType(metaData.getDataTypeId());
                    criterionModel.setMetadata(metaData);
                    if (criterionModel.getType() != 2) { // if type is integer
                        criterionModel.setCondition(ComparisonOperatorEnum.CONTAINS);
                    } else {
                        criterionModel.setCondition(ComparisonOperatorEnum.EQUALS);
                    }
                    criterionModel.setTextValue1(filterValue.toString());

                    final AbstractSimpleCriterion abstractSimpleCriterion = (AbstractSimpleCriterion) CriterionFactory.createCriterion(criterionModel);
                    final String sql = abstractSimpleCriterion.getSql();
                    this.solrSearcher.getQuery().addFilterQuery(sql);
                }
            }
        }

        if (this.solrSearcher != null) {
            if (this.firstTime) {
                PrimeFaces.current().executeScript("PF('searchTableWidget').paginator.setPage(0)");
            }
            return this.solrLoad(first, pageSize, sortBy);
        } else {
            return Collections.emptyList();
        }

    }

    @Override
    public int count(final Map<String, FilterMeta> filterBy) {
        return 0; // see the javadoc of this method
    }

    private List<ArtifactsSolrModel> solrLoad(final int first, final int pageSize, final Map<String, SortMeta> sortBy) {

        final List<SortClause> sortClauses = new ArrayList<>();

        if ((sortBy != null) && !sortBy.isEmpty()) {
            for (final Iterator<Entry<String, SortMeta>> it = sortBy.entrySet().iterator(); it.hasNext();) {

                final Map.Entry<String, SortMeta> pair = it.next();
                final SortMeta sortMeta = pair.getValue();

                final Metadata metaData = this.solrSearcher.getMetaDataMap().get(sortMeta.getField());

                ORDER order;

                if (SortOrder.ASCENDING.name().equals(sortMeta.getOrder().name())) {
                    order = ORDER.asc;
                } else {
                    order = ORDER.desc;
                }
                if (metaData.getDataTypeId() == 1) {
                    sortClauses.add(new SortClause(sortMeta.getField() + "_tr", order));
                } else {
                    sortClauses.add(new SortClause(sortMeta.getField(), order));
                }

            }
            this.solrSearcher.getQuery().setSorts(sortClauses);
        }

        return this.solrLoad(first, pageSize);
    }

    private List<ArtifactsSolrModel> solrLoad(final int first, final int pageSize) {
        this.solrSearcher.getQuery().setStart(first);
        this.solrSearcher.getQuery().setRows(pageSize);

        try {
            // logger.debug(this.solrSearcher.getQuery().toString());

            final QueryResponse solrResponse = this.solrSearcher.makeSearch();
            final List<ArtifactsSolrModel> listEser = solrResponse.getBeans(ArtifactsSolrModel.class);

            final SolrDocumentList results = solrResponse.getResults();
            this.setRowCount((int) results.getNumFound());
            this.recalculateFirst(first, pageSize, this.getRowCount());

            if (this.firstTime) {
                this.solrSearcher.setTotalCount(results.getNumFound());
                this.solrSearcher.setDuration(solrResponse.getElapsedTime());
                this.firstTime = false;
            }
            this.data = listEser;
            return listEser;

        } catch (final SolrServerException | SolrException | IOException e) {

            MuesUtil.showMessage("Solr Sunucusuna Ulaşmada Hata", FacesMessage.SEVERITY_ERROR);
            PrimeFaces.current()
                      .executeScript("PF('growlMessageWidget').renderMessage({'summary':"
                                     + "'Solr Sunucusuna Ulaşmada Hata'"
                                     + ", 'detail':"
                                     + "'Sistem Yöneticisine Başvurunuz. '"
                                     + ", 'severity':'error'})");
            logger.error("[solrLoad] : Hata : {}", e.getMessage(), e);
            return Collections.emptyList();
        }

    }

    @Override
    public ArtifactsSolrModel getRowData(final String rowKey) {
        for (final ArtifactsSolrModel artifact : this.data) {
            if (artifact.getId().equals(Integer.valueOf(rowKey))) {
                return artifact;
            }
        }

        return null;
    }

    @Override
    public String getRowKey(final ArtifactsSolrModel solrRecord) {
        return solrRecord.getId().toString();
    }
}
