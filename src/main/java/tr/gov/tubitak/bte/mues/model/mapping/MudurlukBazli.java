package tr.gov.tubitak.bte.mues.model.mapping;

import java.util.Optional;

public class MudurlukBazli {

    private Integer mudurlukId;

    private String  mudurluk;

    private String  fotografPath;

    private Integer eserSayisi;

    private Integer totalArtifactCount;

    private Integer artifactsPendingApprovalCount;

    private Integer artifactsDraftCount;

    private Integer artifactsRejectedCount;

    public MudurlukBazli(final Integer mudurlukId,
                              final String mudurluk,
                              final String fotografPath,
                              final Integer eserSayisi,
                              final Integer totalArtifactCount,
                              final Integer artifactsPendingApprovalCount,
                              final Integer artifactsDraftCount,
                              final Integer artifactsRejectedCount) {
        this.mudurlukId = mudurlukId;
        this.mudurluk = mudurluk;
        this.fotografPath = fotografPath;
        this.eserSayisi = eserSayisi;
        this.totalArtifactCount = totalArtifactCount;
        this.artifactsPendingApprovalCount = artifactsPendingApprovalCount;
        this.artifactsDraftCount = artifactsDraftCount;
        this.artifactsRejectedCount = artifactsRejectedCount;
    }

    // getters and setters ....................................................

    public Integer getMudurlukId() {
        return this.mudurlukId;
    }

    public void setMudurlukId(final Integer mudurlukId) {
        this.mudurlukId = mudurlukId;
    }

    public void setMudurluk(final String mudurluk) {
        this.mudurluk = mudurluk;
    }

    public String getMudurluk() {
        return this.mudurluk;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setEserSayisi(final Integer eserSayisi) {
        this.eserSayisi = eserSayisi;
    }

    public Integer getEserSayisi() {
        return this.eserSayisi;
    }

    public Integer getTotalArtifactCount() {
        if (this.totalArtifactCount == null) {
            return 0;
        }
        return this.totalArtifactCount;
    }

    public void setTotalArtifactCount(final Integer totalArtifactCount) {
        this.totalArtifactCount = totalArtifactCount;
    }

    public Integer getArtifactsPendingApprovalCount() {
        return this.artifactsPendingApprovalCount;
    }

    public void setArtifactsPendingApprovalCount(final Integer artifactsPendingApprovalCount) {
        this.artifactsPendingApprovalCount = artifactsPendingApprovalCount;
    }

    public Integer getArtifactsDraftCount() {
        return this.artifactsDraftCount;
    }

    public void setArtifactsDraftCount(final Integer artifactsDraftCount) {
        this.artifactsDraftCount = artifactsDraftCount;
    }

    public Integer getProgressRegardingMax() {
        if ((this.eserSayisi == null) || (this.totalArtifactCount == null) || (this.eserSayisi == 0) || (this.totalArtifactCount == 0)) {
            return 0;
        }
        int progressValue = (100 * this.eserSayisi) / Optional.ofNullable(this.totalArtifactCount).orElse(this.eserSayisi);
        if (progressValue > 100) {
            progressValue = 100;
        }
        return progressValue;
    }

    public Integer getArtifactsRejectedCount() {
        return this.artifactsRejectedCount;
    }

    public void setArtifactsRejectedCount(final Integer artifactsRejectedCount) {
        this.artifactsRejectedCount = artifactsRejectedCount;
    }

}
