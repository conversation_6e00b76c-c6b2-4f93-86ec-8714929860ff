package tr.gov.tubitak.bte.mues.search.controller;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.mues.search.SearchQualifier;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;

@Named
@ViewScoped
@SearchQualifier
public class SearchCriterionDefinitionController extends AbstractSearchCriterionDefinitionController<ArtifactsSolrModel> {

    private static final long serialVersionUID = 8279594678575644645L;

    @Inject
    public void setSearchController(@SearchQualifier
    final SearchController searchController) {
        this.searchController = searchController;
    }

}
