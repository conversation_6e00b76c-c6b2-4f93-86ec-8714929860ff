package tr.gov.tubitak.bte.mues.model.mapping;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

public abstract class AuditViewSuper implements Serializable {

    private static final long serialVersionUID = -1246091245127551539L;

    private BigInteger        id;

    private String            summary;

    private Integer           eventId;

    private Date              eventTime;

    private String            userName;

    private String            userIp;

    private String            adSoyad;

    private String            mudurluk;

    protected AuditViewSuper(final BigInteger id,
                             final String summary,
                             final Integer eventId,
                             final Date eventTime,
                             final String userName,
                             final String userIp,
                             final String adSoyad,
                             final String mudurluk) {
        this.id = id;
        this.summary = summary;
        this.eventId = eventId;
        this.eventTime = eventTime;
        this.userName = userName;
        this.userIp = userIp;
        this.adSoyad = adSoyad;
        this.mudurluk = mudurluk;
    }

    // getters and setters ....................................................

    public void setId(final BigInteger id) {
        this.id = id;
    }

    public String getSummary() {
        return this.summary;
    }

    public void setSummary(final String summary) {
        this.summary = summary;
    }

    public String getUserName() {
        return this.userName;
    }

    public void setUserName(final String userName) {
        this.userName = userName;
    }

    public String getAdSoyad() {
        return this.adSoyad;
    }

    public void setAdSoyad(final String adSoyad) {
        this.adSoyad = adSoyad;
    }

    public String getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final String mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Integer getEventId() {
        return this.eventId;
    }

    public void setEventId(final Integer eventId) {
        this.eventId = eventId;
    }

    public Date getEventTime() {
        return this.eventTime;
    }

    public void setEventTime(final Date eventTime) {
        this.eventTime = eventTime;
    }

    public String getUserIp() {
        return this.userIp;
    }

    public void setUserIp(final String userIp) {
        this.userIp = userIp;
    }

    public BigInteger getId() {
        return this.id;
    }

}
