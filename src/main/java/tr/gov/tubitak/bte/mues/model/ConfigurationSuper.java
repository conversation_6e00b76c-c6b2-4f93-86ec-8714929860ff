package tr.gov.tubitak.bte.mues.model;

import java.util.Objects;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

@Audited
@MappedSuperclass
@NamedQuery(name = "Configuration.findEagerById", query = "SELECT c FROM Configuration c WHERE c.id = :id")
@NamedQuery(name = "Configuration.findAll", query = "SELECT c FROM Configuration c ORDER BY c.silinmis, c.aktif DESC, c.clue")
@NamedQuery(name = "Configuration.findActive", query = "SELECT c FROM Configuration c WHERE c.aktif = true AND c.silinmis = false ORDER BY c.clue")
@NamedQuery(name = "Configuration.findByKey", query = "SELECT c FROM Configuration c WHERE  c.aktif = true AND c.silinmis = false AND c.clue = :clue")
@NamedQuery(name = "Configuration.findValueByName", query = "SELECT c.value FROM Configuration c WHERE  c.aktif = true AND c.silinmis = false AND c.clue = :clue")
@NamedQuery(name = "Configuration.findByPrefix", query = "SELECT c FROM Configuration c WHERE  c.aktif = true AND c.silinmis = false AND c.clue LIKE :prefix")
public class ConfigurationSuper extends AbstractEntity {

    private static final long serialVersionUID = 1019478583266366160L;

    @NotNull
    @Size(max = 100)
    @Column(name = "CLUE", nullable = false, unique = true, length = 100)
    private String            clue;

    @Size(max = 250)
    @NotNull
    @Column(name = "VALUE")
    private String            value;

    @Size(max = 200)
    @Column(name = "DESCRIPTION", length = 200)
    private String            aciklama;

    public ConfigurationSuper() {
        // blank constructor
    }

    // getters and setters ...............................................................................
    public String getClue() {
        return this.clue;
    }

    public void setClue(final String clue) {
        this.clue = clue;
    }

    public String getValue() {
        return this.value;
    }

    public void setValue(final String value) {
        this.value = value;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    // overriden methods ..................................................................................

    @Override
    public int hashCode() {
        return Objects.hash(this.clue);
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final ConfigurationSuper other = (ConfigurationSuper) obj;
        return Objects.equals(this.getId(), other.getId());
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder();
        sb.append("{key= " + this.clue + ", val= " + this.value + "}");
        return sb.toString();
    }

    @Override
    public String getTitle() {
        return this.clue;
    }

}
