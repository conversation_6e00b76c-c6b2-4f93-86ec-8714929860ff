package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.NamedNativeQuery;
import javax.persistence.NamedQuery;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;

@Entity
@Table(name = "Monogram")
@NamedQuery(name = "Monogram.findEagerById", query = "SELECT m FROM Monogram m WHERE m.id = :id")
@NamedQuery(name = "Monogram.findAll", query = "SELECT m FROM Monogram m ORDER BY m.silinmis, m.aktif DESC, m.ad")
@NamedQuery(name = "Monogram.findByNameAndAciklama", query = "SELECT m FROM Monogram m WHERE m.aktif = true AND m.silinmis = false AND (m.ad LIKE :str OR m.aciklama LIKE :str) ORDER BY m.ad, m.aciklama")
@NamedNativeQuery(name = "Monogram.validateBeforeDelete", query = "SELECT (SELECT case when count(1) > 0 then 1 else 0 end FROM Transcription WHERE SILINMIS = 0 AND monogram = :id)")

public class Monogram extends AbstractEntity implements DeleteValidatable {

    private static final long                     serialVersionUID = 7431416600070239342L;

    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String                                ad;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String                                aciklama;

    @FilePathCheck
    @Size(max = 250)
    @Column(name = "fotografPath", length = 250)
    private String                                fotografPath;

    public Monogram() {
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public String getFotografPath() {
        return this.fotografPath;
    }

    public void setFotografPath(final String fotografPath) {
        this.fotografPath = fotografPath;
    }

    @Override
    public String toString() {
        return this.ad;
    }

    @Override
    public String getTitle() {
        return Optional.ofNullable(this.ad).orElse("" + this.getId());
    }

}
