package tr.gov.tubitak.bte.mues.service;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import javax.enterprise.context.ApplicationScoped;

import tr.gov.tubitak.bte.mues.util.enums.SolrEnum;

@ApplicationScoped
public class SolrLockService {

    private final Map<SolrEnum, Boolean> globalSolrSchedulerLock = Arrays.stream(SolrEnum.values()).collect(Collectors.toConcurrentMap(solrEnum -> solrEnum, solrEnum -> Boolean.FALSE));

    public synchronized void updateSolrSchedulerLock(final SolrEnum solrEnum, final Boolean value) {
        this.globalSolrSchedulerLock.put(solrEnum, value);
    }

    public Boolean getSolrSchedulerLock(final SolrEnum solrEnum) {
        return this.globalSolrSchedulerLock.get(solrEnum);
    }

    public Map<SolrEnum, Boolean> getGlobalSolrSchedulerLock() {
        return this.globalSolrSchedulerLock;
    }

}
