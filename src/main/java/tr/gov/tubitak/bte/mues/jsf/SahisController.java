package tr.gov.tubitak.bte.mues.jsf;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.PreDestroy;
import javax.faces.context.FacesContext;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.component.datatable.DataTable;
import org.primefaces.event.FileUploadEvent;
import org.primefaces.event.SelectEvent;

import tr.gov.tubitak.bte.mues.model.Il;
import tr.gov.tubitak.bte.mues.model.Sahis;
import tr.gov.tubitak.bte.mues.model.mapping.lazytable.LazySahisDataModel;
import tr.gov.tubitak.bte.mues.session.SahisFacade;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;

@Named
@ViewScoped
public class SahisController extends AbstractController<Sahis> implements SingleFileUploadable {

    private static final long serialVersionUID = 6331541422122206617L;

    @Inject
    private SahisFacade       facade;

    @Inject
    private IlceController    ilceController;

    @Inject
    private FileUploadHelper  fileUploadHelper;

    @Inject
    MuesParameters            muesParameters;

    @Inject
    private SessionBean       sessionBean;

    private List<Sahis>       list;

    private List<Sahis>       selectionList;

    private DataModel<Sahis>  lazySahisDataModel;

    public SahisController() {
        super(Sahis.class);
    }

    @Override
    public void newRecord() {
        super.newRecord();
        this.ilceController.setIl(null);
        this.getModel().setCreatedBy(this.sessionBean.getCurrentUser().getPersonelView());
    }

    /***
     * TCNo var ise uyari verir yoksa yeni kayit yapar
     */
    @Override
    public DBOperationResult create() {
        final DBOperationResult result = super.create();
        if (result.isSuccess()) {
            this.getList().add(this.getModel());
        }
        return result;

    }

    @Override
    public DBOperationResult update() {
        this.getModel().setCreatedBy(this.sessionBean.getCurrentUser().getPersonelView());
        return super.update();
    }

    @Override
    public void showDetail(final Integer id) {
        this.setModel(this.getFacade().findEagerById(id));
        this.setNewMode(false);
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.getModel().getFotografPath() != null) {
            this.getModel().setFotografPath(this.muesParameters.writeMainCopyToFile(this.getModel().getFotografPath(), FolderType.IMAGE_AK));
        }
    }

    public void handleIlChange(final SelectEvent<?> event) {
        this.ilceController.setIl((Il) event.getObject());
        this.getModel().setIlce(null);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.getModel().setFotografPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    public List<Sahis> filterUndeletedByName(final String query) {
        return this.getFacade().filterUndeletedByName(query);
    }

    public void loadDataTable() {
        this.resetTable();
        this.setLazySahisDataModel(new LazySahisDataModel(Sahis.class, () -> this.getFacade().getEM(), this));
    }

    public void resetTable() {
        this.lazySahisDataModel = null;
        final DataTable dt = ((DataTable) FacesContext.getCurrentInstance().getViewRoot().findComponent("sahisDialog:multiSahisForm:sahisTableId"));
        if (dt != null) {
            dt.reset();
        }
    }

    /***
     * Sahis listesini DB den bir kere alip kendi scope'unda tutulan listeye atar, gelen texte gore bu listeyi filtreleyip sub list doner
     * 
     * @param text gelen string deger
     * @return sahis sub list
     */
    public List<Sahis> filterByFullNameAndAciklama(final String text) {
        if ((this.getList() == null) || this.getList().isEmpty()) {
            this.setList(this.getFacade().findActive()); // tum sahis listesi
        }
        if (!text.isEmpty()) {
            return this.getList().stream().filter(t -> t.getTitle().toLowerCase().contains(text.toLowerCase())).collect(Collectors.toList());
        }
        return this.getList();
    }

    @PreDestroy
    public void cleanUp() {
        if (this.list != null) {
            this.list.clear();
        }
        if (this.selectionList != null) {
            this.selectionList.clear();
        }
        if (this.items != null) {
            this.items.clear();
        }
    }

    // getters and setters ....................................................

    @Override
    public SahisFacade getFacade() {
        return this.facade;
    }

    public List<Sahis> getList() {
        if ((this.list == null) || this.list.isEmpty()) {
            this.list = this.getFacade().findActive(); // tum sahis listesi
        }
        return this.list;
    }

    public void setList(final List<Sahis> list) {
        this.list = list;
    }

    public List<Sahis> getSelectionList() {
        if ((this.selectionList == null)) {
            this.selectionList = new ArrayList<>();
        }
        return this.selectionList;

    }

    public void setSelectionList(final List<Sahis> selectionList) {
        this.selectionList = selectionList;
    }

    public DataModel<Sahis> getLazySahisDataModel() {
        return this.lazySahisDataModel;
    }

    public void setLazySahisDataModel(final DataModel<Sahis> lazySahisDataModel) {
        this.lazySahisDataModel = lazySahisDataModel;
    }

}
