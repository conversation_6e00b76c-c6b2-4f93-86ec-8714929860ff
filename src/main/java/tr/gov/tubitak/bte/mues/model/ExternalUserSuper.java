package tr.gov.tubitak.bte.mues.model;

import java.util.Optional;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import javax.validation.constraints.Size;

import org.hibernate.envers.Audited;

import com.google.common.base.Joiner;

import tr.gov.tubitak.bte.mues.constraint.Email;
import tr.gov.tubitak.bte.mues.constraint.TCKN;
import tr.gov.tubitak.bte.mues.constraint.ValidName;

/**
 *
 * <AUTHOR>
 */
@Audited
@MappedSuperclass
public abstract class ExternalUserSuper extends AbstractEntity {

    private static final long serialVersionUID = -1450954668803950143L;

    @TCKN
    @Size(max = 11)
    @Column(name = "tcIdentityNo", length = 11)
    private String            tcIdentityNo;

    @ValidName
    @Size(max = 50)
    @Column(name = "AD", length = 50)
    private String            ad;

    @ValidName
    @Size(max = 50)
    @Column(name = "surname", length = 50)
    private String            surname;

    @<PERSON>ze(max = 25)
    @Column(name = "phoneNo", length = 25)
    private String            phoneNo;

    @Email
    @Size(max = 50)
    @Column(name = "personelEMail", length = 50)
    private String            personelEMail;

    @Size(max = 150)
    @Column(name = "address", length = 150)
    private String            address;

    @Size(max = 150)
    @Column(name = "ACIKLAMA", length = 150)
    private String            aciklama;

    ExternalUserSuper() {
        // blank constructor
    }

    // getters and setters ....................................................

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String ad) {
        this.ad = ad;
    }

    public String getPersonelEMail() {
        return this.personelEMail;
    }

    public void setPersonelEMail(final String personelEMail) {
        this.personelEMail = personelEMail;
    }

    public String getTcIdentityNo() {
        return this.tcIdentityNo;
    }

    public void setTcIdentityNo(final String tcIdentityNo) {
        this.tcIdentityNo = tcIdentityNo;
    }

    public String getSurname() {
        return this.surname;
    }

    public void setSurname(final String surname) {
        this.surname = surname;
    }

    public String getPhoneNo() {
        return this.phoneNo;
    }

    public void setPhoneNo(final String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(final String address) {
        this.address = address;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    @Override
    public String toString() {
        return Optional.ofNullable(this.getAd()).orElse("" + this.getId());
    }

    @Override
    public String getTitle() {
        return Joiner.on(" ").skipNulls().join(this.ad, this.surname);
    }

}
