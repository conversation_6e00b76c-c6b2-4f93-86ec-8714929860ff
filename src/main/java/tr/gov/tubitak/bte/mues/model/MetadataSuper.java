/***********************************************************
 * Metadata.java - MUES Projesi
 *
 * Kullanılan JRE: 1.6.0_24
 *
 * taner.aruk - Mar 14, 2012
 *
 * Copyrighted to TUBITAK-BILGEM BTE, Gebze - Kocaeli, 2012©
 ***********************************************************/
package tr.gov.tubitak.bte.mues.model;

import java.util.Objects;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.NamedQuery;
import javax.validation.constraints.NotNull;

import org.apache.poi.ss.formula.eval.NotImplementedException;
import org.hibernate.envers.Audited;

/**
 * Metadata sınıfı Üstveri nesnesinin özelliklerini tutar.
 *
 * <AUTHOR>
 */
@Audited
@MappedSuperclass

@NamedQuery(name = "Metadata.findEagerById", query = "SELECT m FROM Metadata m WHERE m.id = :id")
@NamedQuery(name = "Metadata.findAll", query = "SELECT m FROM Metadata m ORDER BY m.name")
@NamedQuery(name = "Metadata.findActive", query = "SELECT m FROM Metadata m ORDER BY m.name")
@NamedQuery(name = "allMetadatas", query = "SELECT m FROM Metadata m where (m.solrCoreId is null or m.solrCoreId = 0) ORDER BY m.name")
@NamedQuery(name = "allMetadataBySolrCoreId", query = "SELECT m FROM Metadata m where m.solrCoreId = :solrCoreId  ORDER BY m.name")
@NamedQuery(name = "findMetadataById", query = "SELECT m FROM Metadata m WHERE m.id = :metadataId")

public class MetadataSuper implements IEntity<Integer> {

    private static final long serialVersionUID = 9188183714954562608L;

    @Id
    @Basic(optional = false)
    @NotNull
    @Column(name = "ID")
    private Integer           id;

    @Column(name = "INDEX_NAME")
    private String            name;

    @Column(name = "INDEX_FACE_NAME")
    private String            indexFaceName;

    @Column(name = "INDEX_SHORT_NAME")
    private String            indexShortName;

    @Column(name = "DATA_TYPE")
    private Integer           dataTypeId;

    @Column(name = "MAX_CHARS")
    private Integer           maxChars;

    @Column(name = "SOLR_CORE")
    private Integer           solrCoreId;

    @Column(name = "childField")
    private boolean           childField;

    public MetadataSuper() {
        // default constructor
    }

    /**
     * Parametreli yapıcı metotdur.
     *
     * @param metadata the metadata
     */
    public MetadataSuper(final MetadataSuper metadata) {

        this.id = metadata.getId();
        this.name = metadata.getName();
        this.indexFaceName = metadata.getIndexFaceName();
    }

    public Integer getId() {
        return this.id;
    }

    public void setId(final Integer id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(final String name) {
        this.name = name;
    }

    public String getIndexFaceName() {
        return this.indexFaceName;
    }

    public void setIndexFaceName(final String indexFaceName) {
        this.indexFaceName = indexFaceName;
    }

    public String getIndexShortName() {
        return this.indexShortName;
    }

    public void setIndexShortName(final String indexShortName) {
        this.indexShortName = indexShortName;
    }

    public Integer getDataTypeId() {
        return this.dataTypeId;
    }

    public void setDataTypeId(final Integer dataTypeId) {
        this.dataTypeId = dataTypeId;
    }

    public Integer getMaxChars() {
        return this.maxChars;
    }

    public void setMaxChars(final Integer maxChars) {
        this.maxChars = maxChars;
    }

    public Integer getSolrCoreId() {
        return this.solrCoreId;
    }

    public void setSolrCoreId(final Integer solrCoreId) {
        this.solrCoreId = solrCoreId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.name);
    }

    @Override
    public boolean equals(final Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (this.getClass() != obj.getClass()) {
            return false;
        }
        final MetadataSuper other = (MetadataSuper) obj;
        if (this.name == null) {
            if (other.name != null) {
                return false;
            }
        } else if (!this.name.equalsIgnoreCase(other.name)) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "{id= " + this.id + ", name=" + this.name + ", indexFaceName=" + this.indexFaceName + "}";
    }

    @Override
    public String getTitle() {

        return this.name;
    }

    @Override
    public void setSilinmis(final Boolean b) {

    }

    @Override
    public void setAktif(final Boolean b) {
        // TODO Auto-generated method stub

    }

    @Override
    public void setAciklama(final String aciklama) {
        // TODO Auto-generated method stub

    }

    @Override
    public Boolean getAktif() {
        // TODO Auto-generated method stub
        throw new NotImplementedException("getAktif");
    }

    public boolean isChildField() {
        return this.childField;
    }

    public void setChildField(final boolean childField) {
        this.childField = childField;
    }

}
