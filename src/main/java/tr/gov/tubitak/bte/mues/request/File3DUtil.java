package tr.gov.tubitak.bte.mues.request;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.util.Faces;
import org.primefaces.model.file.UploadedFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.jsf.AbstractParameters;

/**
 */
@Named
@RequestScoped
public class File3DUtil implements Serializable {

    private static final long   serialVersionUID = 8046498399800283661L;

    private static final Logger logger           = LoggerFactory.getLogger(File3DUtil.class);

    public static final String  BASE_FOLDER_3D   = "3D.baseFolder";

    public static final String  EXTENSION_3D     = ".ply";

    @Inject
    private AbstractParameters  parameters;

    public File3DUtil() {
        // default constructor
    }

    /***
     * Uploads file to the server as permanent
     * 
     * @param source
     * @param id
     * @return
     */
    public String upload(final UploadedFile source, final String eserId, final String mudurlukCode) {
        final String fileName = eserId + EXTENSION_3D;

        final Path finalAbsolutePath = this.getFinalPath(mudurlukCode, fileName);
        final String filePath = finalAbsolutePath.toString().replace(this.parameters.get(BASE_FOLDER_3D), "").replace("\\", "/");

        try (InputStream input = source.getInputStream()) {

            Files.createDirectories(finalAbsolutePath.getParent());
            Files.copy(input, finalAbsolutePath);

        } catch (final IOException e) {
            logger.error("[writeFileAsPermanent] : Hata : {}", e.getMessage(), e);
        }

        return filePath;
    }

    /***
     * Downloads the file
     * 
     * @param filePath
     * @throws IOException
     */
    public void download(String filePath) throws IOException {
        if (!filePath.startsWith(this.parameters.getTempDir())) {
            filePath = this.getAbsolutePath(filePath).toString();
        }
        final File file3D = new File(filePath);
        Faces.sendFile(file3D, true);
    }

    public Path getFinalPath(final String mudurlukCode, final String fileName) {
        return Paths.get(this.parameters.get(BASE_FOLDER_3D) + File.separator + mudurlukCode + File.separator + fileName);
    }

    public Path getAbsolutePath(final String filePath) {
        return Paths.get(this.parameters.get(BASE_FOLDER_3D) + File.separator + filePath);
    }

}