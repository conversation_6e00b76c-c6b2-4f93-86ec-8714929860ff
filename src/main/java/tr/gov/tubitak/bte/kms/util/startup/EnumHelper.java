package tr.gov.tubitak.bte.kms.util.startup;

import java.io.Serializable;

import javax.enterprise.event.Observes;
import javax.inject.Inject;
import javax.inject.Named;
import javax.inject.Singleton;
import javax.persistence.Table;

import org.omnifaces.cdi.Eager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tr.gov.tubitak.bte.mues.session.UtilOperationsFacade;

@Eager
@Singleton
@Named
public class EnumHelper implements Serializable {

    private static final long      serialVersionUID = 8865378549963109802L;

    protected static final Logger  logger           = LoggerFactory.getLogger(EnumHelper.class);

    @Inject
    protected UtilOperationsFacade facade;

    public void onStartup(final @Observes StartupEvent event) {
        logger.debug("Enum icin guncellemeler icin asagdaki sinifi uncomment yapmaniz gerekiyor. ");
        this.optEnumLookup();
    }

    // call from screen if needed.
    public void optEnumLookup() {

        this.getEnumLookup("tr.gov.tubitak.bte.kms.util.CommissionStateEnum");
        this.getEnumLookup("tr.gov.tubitak.bte.kms.util.CommissionTypeEnum");
        this.getEnumLookup("tr.gov.tubitak.bte.kms.util.KMSStateEnum");
    }

    public void getEnumLookup(final String enumName) {

        try {
            final Class<?> enumClass = Class.forName(enumName);
            final Object[] enumValues = enumClass.getEnumConstants();
            final Table table = enumClass.getAnnotation(Table.class);

            this.facade.insertValues(enumValues, table.name());

        } catch (final ClassNotFoundException e) {
            e.printStackTrace();
        }

    }

}
