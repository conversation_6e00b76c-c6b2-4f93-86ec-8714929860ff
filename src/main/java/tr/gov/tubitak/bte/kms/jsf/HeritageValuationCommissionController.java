package tr.gov.tubitak.bte.kms.jsf;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;
import javax.faces.application.FacesMessage;
import javax.faces.model.DataModel;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import org.primefaces.PrimeFaces;
import org.primefaces.event.FileUploadEvent;

import tr.gov.tubitak.bte.kms.model.Commission;
import tr.gov.tubitak.bte.kms.model.DeliveredItem;
import tr.gov.tubitak.bte.kms.model.TemporaryAdmissionReceipt;
import tr.gov.tubitak.bte.kms.model.mapping.KmsCostCommissionView;
import tr.gov.tubitak.bte.kms.model.mapping.lazytable.LazyCostCommissionDataModel;
import tr.gov.tubitak.bte.kms.session.CommissionDefinitionFacade;
import tr.gov.tubitak.bte.kms.session.CostCommissionLazyLoadFacade;
import tr.gov.tubitak.bte.kms.session.DeliveredItemFacade;
import tr.gov.tubitak.bte.kms.session.TemporaryAdmissionReceiptFacade;
import tr.gov.tubitak.bte.kms.util.CommissionStateEnum;
import tr.gov.tubitak.bte.kms.util.CommissionTypeEnum;
import tr.gov.tubitak.bte.kms.util.IStateChangeable;
import tr.gov.tubitak.bte.kms.util.KMSStateEnum;
import tr.gov.tubitak.bte.mues.jsf.AbstractController;
import tr.gov.tubitak.bte.mues.jsf.FileUploadHelper;
import tr.gov.tubitak.bte.mues.jsf.SingleFileUploadable;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.FolderType;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

/**
 * 
 */
@Named
@ViewScoped
public class HeritageValuationCommissionController extends AbstractController<Commission> implements SingleFileUploadable, IStateChangeable {

    private static final long                          serialVersionUID  = 1044043444045414040L;

    private static final String                        DECIMAL_FORMATTER = "###,##0.00";

    @Inject
    private CommissionDefinitionFacade                 facade;

    @Inject
    private CommissionDefinitionController             commissionDefinitionController;

    @Inject
    private CommissionDocumentController               commissionDocumentController;

    @Inject
    private CommissionObjectController                 commissionObjectController;

    @Inject
    private FileUploadHelper                           fileUploadHelper;

    @Inject
    private CostCommissionLazyLoadFacade               costCommissionLazyLoadFacade;

    @Inject
    private ObjectController                           objectController;

    @Inject
    private TemporaryAdmissionReceiptFacade            tgaFacade;

    @Inject
    private DeliveredItemFacade                        deliveredItemFacade;

    @Inject
    private CommentService                             commentService;

    private transient DataModel<KmsCostCommissionView> lazyCostCommissionDataModel;

    private List<Integer>                              selectedStates;

    final List<AbstractEntity>                         entities          = new ArrayList<>();

    public HeritageValuationCommissionController() {
        super(Commission.class);
    }

    @PostConstruct
    public void init() {
        PrimeFaces.current().executeScript("getCommissionType()");
    }

    @Override
    public void showDetail(final Integer id) {
        this.entities.clear();
        this.commissionObjectController.clearLists();
        this.commissionDefinitionController.showDetail(id);
        // changes commission and detail lists
    }

    public void fetchLazyLoadData(final CommissionTypeEnum commissionType) {
        final StringBuilder queryStr = new StringBuilder();
        queryStr.append("o.commissionType = " + commissionType.getCode());
        this.costCommissionLazyLoadFacade.setCustomQuery(queryStr.toString());
        this.setLazyCostCommissionDataModel(new LazyCostCommissionDataModel(this.costCommissionLazyLoadFacade));
    }

    /***
     * Adds commissionMember to the map & items & commissionEserList
     */
    public void addCommissionMember() {
        this.commissionObjectController.addCommissionMember(new BigDecimal(0));
    }

    /***
     * by choosing eser adding commissionMember to the map & items & commissionEserList
     */
    public void addCommissionEser() {
        this.commissionObjectController.addCommissionEser(new BigDecimal(0));
    }

    /***
     * Saves all commissionObject items & commissionEser list to the DB
     *
     * ObjectDocument, ObjectPhotos, ObjectMeasures, CommissionMembers, CommissionObjectDecisions, CommissionEserDecisions are saved by cascade
     *
     * @return DB Operation Result
     */
    public DBOperationResult save() {

        // Phase 1: Prepare and collect HeritageObjects.
        // These should be processed first by the update method to ensure they have IDs
        // before dependent entities like CommissionObject are processed.
        this.commissionObjectController.getItems().forEach(x ->
            {
                x.getObject().setCost(x.getDecidedCost());
                x.getObject().setMudurluk(x.getCommission().getMudurluk());
                this.entities.add(x.getObject());
            });

        // Phase 2: Prepare and collect CommissionObjects and their Decisions.
        // These are processed after HeritageObjects.
        this.commissionObjectController.getDecisionMap()
                                       .entrySet()
                                       .forEach(x ->
                                           {
                                               final Map<PersonelView, java.lang.Object> personelMap = x.getValue();
                                               x.getKey().getCommissionObjectDecisions().forEach(d -> d.setDecidedCost(this.getValue(String.valueOf(personelMap.get(d.getPersonel())))));
                                               this.entities.add(x.getKey());
                                           });

        // Phase 3: Update DeliveredItem states
        this.commissionObjectController.getItems().forEach(x ->
            {
                x.getObject().getDeliveredItem().setKmsState(this.commissionDefinitionController.getModel().getKmsState());
                this.entities.add(x.getObject().getDeliveredItem());
            });

        // Phase 4: Update TGA states if all deliveredItems are ready to approve
        // updateTGA adds entities directly to the passed list (this.entities)
        this.updateTGA(this.entities);

        // Phase 5: Update eser costs if exists
        // updateEserCost adds entities directly to the passed list (this.entities)
        this.commissionObjectController.updateEserCost(this.entities);

        // Phase 6: Process CommissionEserDecisions

        // add commissionEser list
        this.commissionObjectController.getCommissionEserList().forEach(x ->
            {
                final Map<PersonelView, java.lang.Object> personelMap = this.commissionObjectController.getEserDecisionMap().get(x);
                x.getCommissionEserDecisions().forEach(d -> d.setDecidedCost(this.getValue(String.valueOf(personelMap.get(d.getPersonel())))));
                this.entities.add(x);
            });

        // Phase 7: Add commission model last to ensure all references are properly set
        this.entities.add(this.commissionDefinitionController.getModel());

        final DBOperationResult result = this.update(this.entities);
        this.commissionDefinitionController.setItems(null);
        this.commissionObjectController.clearLists();
        this.entities.clear();

        return result;
    }

    /**
     * Updates TGA(s) state if all deliveredItems are ready to get approved
     *
     * @param entities List of entities to be updated
     */
    public void updateTGA(final List<AbstractEntity> entities) {
        if (this.getItems().isEmpty()) {
            return;
        }

        final List<Integer> tgaIds = this.commissionObjectController.getDistinctTgaIds();
        final List<Integer> deliveredItemIds = this.commissionObjectController.getDeliveredItemIds();

        // Get items that still need cost evaluation using our new descriptive method
        final List<DeliveredItem> pendingItemsRequiringCostEvaluation = this.deliveredItemFacade.findPendingItemsRequiringCostEvaluation(tgaIds, deliveredItemIds);

        if (!pendingItemsRequiringCostEvaluation.isEmpty()) {

            final Map<String, Long> tgaToCount = pendingItemsRequiringCostEvaluation.stream()
                                                                                    .collect(Collectors.groupingBy(
                                                                                                                   di -> di.getTemporaryAdmissionReceipt().getTgaNo(),
                                                                                                                   Collectors.counting()));

            final String message = tgaToCount.entrySet()
                                             .stream()
                                             .map(e -> e.getKey()
                                                       + " numaralı TGA’da "
                                                       + e.getValue()
                                                       + " adet kalem olduğu için müdür onayına gönderilemedi.")
                                             .collect(Collectors.joining("<br/>"))
                                   + "<br/>Bekleyen kalemler tamamlanınca müze müdür onayına gönderilecektir.";

            MuesUtil.showMessage(message, FacesMessage.SEVERITY_WARN);

        }

        if (pendingItemsRequiringCostEvaluation.isEmpty() && !this.commissionObjectController.getItems().isEmpty()) {
            // All items have been properly evaluated - either they've received cost valuations (if inventory)
            // or they've been marked as non-inventory (and thus don't need cost valuation)
            final TemporaryAdmissionReceipt tga = this.commissionObjectController.getItems().iterator().next().getObject().getDeliveredItem().getTemporaryAdmissionReceipt();
            tga.setKmsState(this.commissionDefinitionController.getModel().getKmsState());
            entities.add(tga); // add TGA
        } else {
            // Some items still need evaluation - check each TGA
            final List<TemporaryAdmissionReceipt> tgaList = this.tgaFacade.findByIds(tgaIds);
            tgaList.forEach(tga -> this.commissionObjectController.getItems().forEach(co ->
                {
                    if ((co.getDecidedCost() != null) && tga.getDeliveredItems().contains(co.getObject().getDeliveredItem())) {
                        final List<DeliveredItem> others = tga.getDeliveredItems().stream().filter(i -> !i.getId().equals(co.getObject().getDeliveredItem().getId())).collect(Collectors.toList());
                        // Yarisi bir komisyonda diger yarisi baska bir komisyonda olan TGA nin delivered Itemlerini kontrol ediliyor.
                        final boolean othersApproved = others.stream()
                                                             .allMatch(i -> (i.getKmsState() != null)
                                                                            && (i.getKmsState().ordinal() > KMSStateEnum.TGA_EVALUATION_AND_COST.ordinal()));
                        if (othersApproved) {
                            co.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().setKmsState(this.commissionDefinitionController.getModel().getKmsState());
                            entities.add(co.getObject().getDeliveredItem().getTemporaryAdmissionReceipt()); // add TGA
                        }
                    }
                }));
        }

    }

    private BigDecimal getValue(final String b) {
        if (b.isBlank() || b.equals("null")) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(b);
    }

    public void uploadToTempFolder(final FileUploadEvent event) {
        this.commissionDefinitionController.getModel().setReportPath(this.fileUploadHelper.writeFileToTempFolder(event.getFile()).toString());
    }

    @Override
    public void writeToPermanentFolder() {
        if (this.commissionDefinitionController.getModel().getReportPath() != null) {
            this.commissionDefinitionController.getModel()
                                               .setReportPath(this.fileUploadHelper.writeMainCopyToFile(this.commissionDefinitionController.getModel().getReportPath(), FolderType.COMMISSION));
        }
        if (this.commissionDefinitionController.getModel().getDocuments() != null) {
            this.commissionDefinitionController.getModel().getDocuments().stream().forEach(x -> this.commissionDocumentController.makeFileRelatedOperations(x));
        }

        if (this.commissionDefinitionController.getModel().getAcceptanceReportPath() != null) {
            this.commissionDefinitionController.getModel()
                                               .setAcceptanceReportPath(this.fileUploadHelper.writeMainCopyToFile(this.commissionDefinitionController.getModel().getAcceptanceReportPath(),
                                                                                                                  FolderType.COMMISSION));
        }

        if (this.commissionDefinitionController.getModel().getAcceptedPersonelReportPath() != null) {
            this.commissionDefinitionController.getModel()
                                               .setAcceptedPersonelReportPath(this.fileUploadHelper.writeMainCopyToFile(this.commissionDefinitionController.getModel().getAcceptedPersonelReportPath(),
                                                                                                                        FolderType.COMMISSION));
        }
    }

    @Override
    public CommissionStateEnum getStateEnum() {

        if (this.commissionDefinitionController.getModel() != null) {
            return this.commissionDefinitionController.getModel().getCommissionState();
        }
        return null;
    }

    @Override
    public void saveAsDraft() {
        this.entities.clear();
        this.commissionDefinitionController.getModel().setUpdateInProgress(Boolean.TRUE);
        this.commentService.createComment(this.entities);
        this.save();
    }

    public void saveAsDraftForReport() {
        this.entities.clear();
        this.commissionDefinitionController.getModel().setUpdateInProgress(Boolean.TRUE);
        this.save();
        // diyalog kapanmadigi icin kaydedilen alanlarin refres edilmesi gerekiyor.
        this.commissionDefinitionController.showDetail(this.commissionDefinitionController.getModel());
    }

    @Override
    public void forward() {

        this.entities.clear();

        if (!this.commissionObjectController.getItems().isEmpty()) {
            final TemporaryAdmissionReceipt tga = this.commissionObjectController.getItems().iterator().next().getObject().getDeliveredItem().getTemporaryAdmissionReceipt();

            this.commentService.createComment(this.entities);

            // satin alma disindakilerin bir sonraki asamaya gecmeleri icin uretilen gecici cozum. Sonra if kaldiralacak

            if (KMSStateEnum.TGA_MUSEUM_NON_PURCHASES.equals(tga.getKmsState())) {
                this.commissionDefinitionController.getModel().setKmsState(tga.getKmsState().getNext());
            } else {
                this.commissionDefinitionController.getModel().setKmsState(this.getStateEnum().getNext().getKmsStateEnum());
            }
        }

        this.commissionDefinitionController.getModel().setUpdateInProgress(Boolean.FALSE);
        this.commissionDefinitionController.getModel().setCommissionState(this.getStateEnum().getNext().getCode());
        this.save();
    }

    @Override
    public void back() {
        // this.commissionDefinitionController.getModel().setCommissionState(this.getStateEnum().getPrev());
        // this.save(this.commissionDefinitionController.getModel());
    }

    public void updateObject() {
        this.objectController.handleCreationDetails();
        this.objectController.updateTimeRelatedFields();
        this.objectController.restoreFields();
        this.objectController.writeToPermanentFolder();
    }

    // getters and setters ....................................................

    public DataModel<KmsCostCommissionView> getLazyCostCommissionDataModel() {
        return this.lazyCostCommissionDataModel;
    }

    public void setLazyCostCommissionDataModel(final DataModel<KmsCostCommissionView> lazyCostCommissionDataModel) {
        this.lazyCostCommissionDataModel = lazyCostCommissionDataModel;
    }

    public BigDecimal getTotalCost() {

        return this.costCommissionLazyLoadFacade.getTotalCost();
    }

    public String getFormatedTotalCost() {
        final DecimalFormat formatter = new DecimalFormat(DECIMAL_FORMATTER);
        if (this.getTotalCost() != null) {
            return formatter.format(this.getTotalCost());
        }
        return formatter.format(0);
    }

    @Override
    public CommissionDefinitionFacade getFacade() {
        return this.facade;
    }

    public List<Integer> getSelectedStates() {
        return this.selectedStates;
    }

    public void setSelectedStates(final List<Integer> selectedStates) {
        this.selectedStates = selectedStates;
    }

    public List<CommissionStateEnum> getCostStates(final CommissionStateEnum commissionType) {
        List<CommissionStateEnum> statesList = new ArrayList<>();
        if (commissionType == CommissionStateEnum.COST_COMPLETED) {
            statesList = CommissionStateEnum.getListCostStates();
        } else if (commissionType == CommissionStateEnum.HIGH_COST_COMPLETED) {
            statesList = CommissionStateEnum.getListHighCostStates();
        } else if (commissionType == CommissionStateEnum.RE_COST_COMPLETED) {
            statesList = CommissionStateEnum.getListRecostStates();
        }
        return statesList;
    }

}