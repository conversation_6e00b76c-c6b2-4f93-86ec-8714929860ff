package tr.gov.tubitak.bte.kms.jsf;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.kms.model.Commission;
import tr.gov.tubitak.bte.kms.model.CommissionObject;
import tr.gov.tubitak.bte.kms.model.DeliveredItem;
import tr.gov.tubitak.bte.kms.model.TemporaryAdmissionReceipt;
import tr.gov.tubitak.bte.kms.model.mapping.CommissionReportsView;
import tr.gov.tubitak.bte.kms.session.CommissionDefinitionFacade;
import tr.gov.tubitak.bte.kms.session.CommissionObjectFacade;
import tr.gov.tubitak.bte.kms.session.TemporaryAdmissionReceiptFacade;
import tr.gov.tubitak.bte.kms.util.CommissionTypeEnum;
import tr.gov.tubitak.bte.kms.util.KMSStateEnum;
import tr.gov.tubitak.bte.kms.util.ObjectTypeEnum;
import tr.gov.tubitak.bte.mues.jsf.AbstractController;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class CommissionReportsController extends AbstractController<Commission> {

    private static final long               serialVersionUID = -4675846066618973068L;

    @Inject
    private TGAWorkflowController           tgaWorkflowController;

    @Inject
    private CommissionDefinitionFacade      facade;

    @Inject
    private TemporaryAdmissionReceiptFacade temporaryAdmissionReceiptFacade;

    @Inject
    private CommissionObjectFacade          commissionObjectFacade;

    @Inject
    private CommissionDefinitionController  commissionDefinitionController;

    @Inject
    private CommissionObjectController      commissionObjectController;

    @Inject
    private ForwardTGAFromCommissionUtil    forwardTGAFromCommissionUtil;

    @Inject
    private CommentService                  commentService;

    private List<CommissionReportsView>     commissionReportsViewList;

    private List<TemporaryAdmissionReceipt> temporaryAdmissionReceipts;

    private List<String>                    selectedKmsStates;

    final transient List<AbstractEntity>    entitiesToUpdate = new ArrayList<>();

    private KMSStateEnum                    tgaKmsState;

    protected CommissionReportsController() {
        super(Commission.class);
    }

    public DBOperationResult save() {

        this.entitiesToUpdate.add(this.commissionDefinitionController.getModel());
        this.entitiesToUpdate.addAll(this.temporaryAdmissionReceipts);

        this.entitiesToUpdate.addAll(this.commissionObjectController.getItems());

        final DBOperationResult update = super.update(this.entitiesToUpdate);
        if (update.isSuccess()) {
            this.commissionReportsViewList = null;
            this.entitiesToUpdate.clear();
        }
        return update;

    }

    public List<CommissionReportsView> fetchCommissionReportsByKmsState(final KMSStateEnum kmsStateEnum) {
        final List<KMSStateEnum> kmsStateEnums = new ArrayList<>();
        kmsStateEnums.add(kmsStateEnum);
        this.fetchCommissionReportsByKmsState(kmsStateEnums);
        return this.commissionReportsViewList;
    }

    public List<CommissionReportsView> fetchCommissionReportsByKmsState(final List<KMSStateEnum> kmsStateEnums) {
        if (this.commissionReportsViewList == null) {

            this.commissionReportsViewList = this.commissionObjectFacade.findCommissionReportsByKmsState(kmsStateEnums.stream().map(KMSStateEnum::ordinal).collect(Collectors.toList()),
                                                                                                         ObjectTypeEnum.INVENTORY,
                                                                                                         CommissionTypeEnum.getReviewNeededCommissions());
        }
        return this.commissionReportsViewList;
    }

    public void showDetail(final CommissionReportsView reportsView) {
        this.setModel(this.facade.findEagerById(reportsView.getCommissionId()));
        this.commissionDefinitionController.setModel(this.getModel());
        // bu kisim ve asagisini sorali ihsana sor.
        this.commissionObjectController.setItems(this.commissionObjectFacade.findByCommissionId(this.getModel().getId()));

        final List<Integer> ids = new ArrayList<>();
        this.commissionObjectController.getItems().forEach(x -> ids.add(x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getId()));
        this.temporaryAdmissionReceipts = this.temporaryAdmissionReceiptFacade.findByIdList(MuesUtil.toIds(ids));
        this.assignKmsState();
    }

    public void showCommissionReportDetail(final CommissionReportsView reportsView) {

        this.setModel(this.facade.findEagerById(reportsView.getCommissionId()));
        this.commissionDefinitionController.setModel(this.getModel());
        this.commissionObjectController.setItems(this.commissionObjectFacade.findByCommissionId(reportsView.getCommissionId()));

        final List<Integer> ids = new ArrayList<>();
        this.commissionObjectController.getItems().forEach(x -> ids.add(x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getId()));
        this.temporaryAdmissionReceipts = this.temporaryAdmissionReceiptFacade.findByIdList(MuesUtil.toIds(ids));
        this.assignKmsState();

    }

    public String joinTGAPersons(final CommissionObject commissionObject) {
        final StringJoiner sj = new StringJoiner("<br />");
        if (!commissionObject.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getDelivererSahisList().isEmpty()) {
            commissionObject.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getDelivererSahisList().forEach(x -> sj.add(x.getSahis().getAd() + " " + x.getSahis().getSoyad()));
            return sj.toString();
        }
        return " ";
    }

    public String joinMember(final Commission commission) {
        final StringJoiner sj = new StringJoiner("<br />");
        if (commission.getCommissionMembers() != null) {
            commission.getCommissionMembers().forEach(x -> sj.add(x.getPersonel().getAd() + " " + x.getPersonel().getSoyad()));
        }
        return sj.toString();
    }

    public String joinArtifactBringers(final CommissionReportsView commissionReportsView) {
        final StringJoiner sj = new StringJoiner("\n");
        if (commissionReportsView.getDeliveredPerson() != null) {
            sj.add(commissionReportsView.getDeliveredPerson());
        }
        if (commissionReportsView.getDeliveredTuzelKisi() != null) {
            sj.add(commissionReportsView.getDeliveredTuzelKisi());
        }
        return sj.toString();
    }

    public KMSStateEnum getKmsState() {

        return this.tgaKmsState;
    }

    public void assignKmsState() {
        if ((this.temporaryAdmissionReceipts != null) && !this.temporaryAdmissionReceipts.isEmpty()) {
            this.tgaKmsState = this.temporaryAdmissionReceipts.get(0).getKmsState();
        }

    }

    
    public void autoForwardWaitingCommissions(final List<Integer> forwardedTgaIds) {
        
        if ((forwardedTgaIds == null) || forwardedTgaIds.isEmpty()) {
            return;
        }

        final List<Commission> waitingCommissions = this.facade.findByState(tr.gov.tubitak.bte.kms.util.CommissionStateEnum.WAITING_FOR_OTHER_COMMISSIONS);
        
        for (final Commission commission : waitingCommissions) {
            // Set up the controller/model as needed
            this.setModel(commission);
            this.commissionObjectController.setItems(this.commissionObjectFacade.findByCommissionId(commission.getId()));
            final List<Integer> ids = this.commissionObjectController.getItems()
                                                                     .stream()
                                                                     .map(x -> x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getId())
                                                                     .collect(Collectors.toList());
            // Only process commissions related to the forwarded TGAs
            final boolean related = ids.stream().anyMatch(forwardedTgaIds::contains);
            if (!related) {
                continue;
            }
            this.temporaryAdmissionReceipts = this.temporaryAdmissionReceiptFacade.findByIdList(ids);

            // Use the same logic as in forward()
            final List<TemporaryAdmissionReceipt> blockedTgas = new ArrayList<>();
            for (final TemporaryAdmissionReceipt tga : this.temporaryAdmissionReceipts) {
                final boolean allReady = tga.getDeliveredItems()
                                            .stream()
                                            .allMatch(di -> di.getKmsState().equals(tga.getKmsState().getNext()));
                if (!allReady) {
                    blockedTgas.add(tga);
                }
            }
            if (blockedTgas.isEmpty()) {
                // 1. Move delivered items in this commission to next state
                final List<DeliveredItem> deliveredItems = this.commissionObjectController.getItems()
                                                                                          .stream()
                                                                                          .map(x -> x.getObject().getDeliveredItem())
                                                                                          .collect(Collectors.toList());
                this.temporaryAdmissionReceipts.forEach(x -> x.getDeliveredItems().forEach(y ->
                    {
                        if (deliveredItems.contains(y)) {
                            y.setKmsState(x.getKmsState().getNext());
                        }
                    }));
                // 2. If ALL delivered items of a TGA are in the next state, move the TGA itself
                this.temporaryAdmissionReceipts.stream()
                                               .filter(x -> x.getDeliveredItems().stream().allMatch(y -> y.getKmsState().equals(x.getKmsState().getNext())))
                                               .forEach(z -> z.setKmsState(z.getKmsState().getNext()));
                this.commentService.createComment(this.entitiesToUpdate, this.getKmsState());
                this.commissionDefinitionController.getModel().setKmsState(this.getKmsState().getNext());
                this.save();
            }
        }
    }

    public void forward() {
        final List<DeliveredItem> deliveredItems = this.commissionObjectController.getItems()
                                                                                  .stream()
                                                                                  .map(x -> x.getObject().getDeliveredItem())
                                                                                  .collect(Collectors.toList());

        final List<TemporaryAdmissionReceipt> blockedTgas = new ArrayList<>();
        final List<String> blockingInfo = new ArrayList<>();

        for (final TemporaryAdmissionReceipt tga : this.temporaryAdmissionReceipts) {
            final boolean allReady = tga.getDeliveredItems()
                                        .stream()
                                        .allMatch(di -> di.getKmsState().equals(tga.getKmsState().getNext()));
            if (!allReady) {
                blockedTgas.add(tga);
                final long count = tga.getDeliveredItems()
                                      .stream()
                                      .filter(di -> !di.getKmsState().equals(tga.getKmsState().getNext()))
                                      .count();
                blockingInfo.add(tga.getTgaNo() + " numaralı TGA'da " + count + " adet kalem başka komisyon(lar) tarafından engelleniyor.");
            }
        }

        if (!blockedTgas.isEmpty()) {
            // Set commission to intermediate state
            this.commissionDefinitionController.getModel().setCommissionState(tr.gov.tubitak.bte.kms.util.CommissionStateEnum.WAITING_FOR_OTHER_COMMISSIONS);
            // Show message
            final String message = String.join("<br/>", blockingInfo) + "<br/>Tüm kalemler hazır olduğunda komisyon ilerletilebilecektir.";
            MuesUtil.showMessage(message, FacesMessage.SEVERITY_WARN);
            this.save();
            return;
        }

        // 1. Move delivered items in this commission to next state
        this.temporaryAdmissionReceipts.forEach(x -> x.getDeliveredItems().forEach(y ->
            {
                if (deliveredItems.contains(y)) {
                    y.setKmsState(x.getKmsState().getNext());
                }
            }));

        // 2. If ALL delivered items of a TGA are in the next state, move the TGA itself
        boolean tgaForwarded = false;
        final List<Integer> forwardedTgaIds = new ArrayList<>();
        for (final TemporaryAdmissionReceipt tga : this.temporaryAdmissionReceipts) {
            final boolean allReady = tga.getDeliveredItems().stream().allMatch(y -> y.getKmsState().equals(tga.getKmsState().getNext()));
            if (allReady) {
                tga.setKmsState(tga.getKmsState().getNext());
                tgaForwarded = true;
                forwardedTgaIds.add(tga.getId());
            }
        }

        this.commentService.createComment(this.entitiesToUpdate, this.getKmsState());
        this.commissionDefinitionController.getModel().setKmsState(this.getKmsState().getNext());
        this.save();
        // Only auto-forward if a TGA was forwarded, and only for commissions related to those TGAs
        if (tgaForwarded) {
            this.autoForwardWaitingCommissions(forwardedTgaIds);
        }
    }

    public void redirect() {
        final List<DeliveredItem> deliveredItems = this.commissionObjectController.getItems()
                                                                                  .stream()
                                                                                  .map(x ->
                                                                                      {
                                                                                          final DeliveredItem deliveredItem = x.getObject().getDeliveredItem();
                                                                                          deliveredItem.setKmsState(this.getKmsState().getRedirection());
                                                                                          return deliveredItem;
                                                                                      })

                                                                                  .collect(Collectors.toList());

        // TODO: burada komisyon end state çekilmeli çünkü artık komisyon için akış sona eriyor
        // state enumda bir sekilde bir end state gibi birsey yapmak gerekiyor.
        this.commentService.createComment(this.entitiesToUpdate, this.getKmsState());
        this.commissionDefinitionController.getModel().setKmsState(this.getKmsState().getRedirection());

        this.forwardTGAFromCommissionUtil.updateTGA(this.getKmsState().getRedirection(), this.entitiesToUpdate);
        this.entitiesToUpdate.addAll(deliveredItems);
        this.save();
    }

    public void reject() {

        this.temporaryAdmissionReceipts.forEach(x ->
            {
                x.setKmsState(this.getKmsState().getRejected());

            });

        final List<DeliveredItem> deliveredItems = this.commissionObjectController.getItems()
                                                                                  .stream()
                                                                                  .map(x ->
                                                                                      {
                                                                                          final DeliveredItem deliveredItem = x.getObject().getDeliveredItem();
                                                                                          deliveredItem.setKmsState(this.getKmsState().getRejected());
                                                                                          return deliveredItem;
                                                                                      })

                                                                                  .collect(Collectors.toList());

        this.commissionDefinitionController.getModel().setKmsState(this.getKmsState().getRejected());
        this.entitiesToUpdate.addAll(deliveredItems);

        this.commentService.createComment(this.entitiesToUpdate, this.getKmsState());

        this.save();
    }

    public void request() {

        this.commissionDefinitionController.getModel().setUpdateInProgress(true);
        this.commissionDefinitionController.getModel().setCommissionState(this.commissionDefinitionController.getModel().getCommissionState().getRedirection());

        final List<DeliveredItem> deliveredItems = this.commissionObjectController.getItems()
                                                                                  .stream()
                                                                                  .map(x ->
                                                                                      {
                                                                                          final DeliveredItem deliveredItem = x.getObject().getDeliveredItem();
                                                                                          deliveredItem.setKmsState(this.getKmsState().getRejected());
                                                                                          return deliveredItem;
                                                                                      })

                                                                                  .collect(Collectors.toList());

        this.commentService.createComment(this.entitiesToUpdate, this.getKmsState());

        this.temporaryAdmissionReceipts.forEach(x ->
            {
                Optional.ofNullable(x.getDeliveredItems()).orElse(Collections.emptySet()).forEach(y -> y.setKmsState(this.getKmsState().getRequest()));
                x.setKmsState(this.getKmsState().getRequest());
            });

        this.entitiesToUpdate.addAll(deliveredItems);

        this.save();
        this.tgaWorkflowController.setSelectedTgaWorkflows(null);
        this.tgaWorkflowController.setSelectAdmissionReceipts(null);
    }

    @Override
    public CommissionDefinitionFacade getFacade() {
        return this.facade;
    }

    public List<TemporaryAdmissionReceipt> getTemporaryAdmissionReceipts() {
        return this.temporaryAdmissionReceipts;
    }

    public void setTemporaryAdmissionReceipts(final List<TemporaryAdmissionReceipt> temporaryAdmissionReceipts) {
        this.temporaryAdmissionReceipts = temporaryAdmissionReceipts;
    }

    public List<CommissionReportsView> getCommissionReportsViewList() {
        return this.commissionReportsViewList;
    }

    public void setCommissionReportsViewList(final List<CommissionReportsView> commissionReportsViewList) {
        this.commissionReportsViewList = commissionReportsViewList;
    }

    public List<String> getSelectedKmsStates() {
        return this.selectedKmsStates;
    }

    public void setSelectedKmsStates(final List<String> selectedKmsStates) {
        this.selectedKmsStates = selectedKmsStates;
    }

}
