package tr.gov.tubitak.bte.kms.model;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.validation.constraints.Size;

import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import tr.gov.tubitak.bte.kms.util.CommissionStateEnum;
import tr.gov.tubitak.bte.kms.util.CommissionTypeEnum;
import tr.gov.tubitak.bte.kms.util.KMSStateEnum;
import tr.gov.tubitak.bte.mues.constraint.FilePathCheck;
import tr.gov.tubitak.bte.mues.constraint.ValidCommission;
import tr.gov.tubitak.bte.mues.constraint.validator.CommissionGroup;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.search.SearchConstants;
import tr.gov.tubitak.bte.mues.util.DateUtil;

@Audited
@Entity
@Table(name = "Kms_Commission")
@NamedQuery(name = "Commission.findEagerById", query = "SELECT c FROM Commission c LEFT JOIN FETCH c.mudurluk LEFT JOIN FETCH c.previousCommission "
                                                       + "LEFT JOIN FETCH c.commissionMembers m LEFT JOIN FETCH m.personel p LEFT JOIN FETCH p.unvan "
                                                       + "LEFT JOIN FETCH c.commissionObjects co "
                                                       + "LEFT JOIN FETCH co.object o "
                                                       + "LEFT JOIN FETCH o.deliveredItem di "
                                                       + "LEFT JOIN FETCH di.temporaryAdmissionReceipt tar "
                                                       + "LEFT JOIN FETCH tar.eserGelisSekli  "
                                                       + "LEFT JOIN FETCH c.createdBy "
                                                       + "LEFT JOIN FETCH c.documents LEFT JOIN FETCH c.commissionUpdateApprovalDocuments WHERE c.id = :id")
@NamedQuery(name = "Commission.findAll", query = "SELECT distinct c FROM Commission c LEFT JOIN FETCH c.mudurluk ORDER BY c.silinmis, c.aktif DESC")
@NamedQuery(name = "Commission.findByMudurluk", query = "SELECT distinct c FROM Commission c "
                                                        + "LEFT JOIN FETCH c.commissionObjects co "
                                                        + "LEFT JOIN FETCH co.object o "
                                                        + "LEFT JOIN FETCH o.deliveredItem di "
                                                        + "LEFT JOIN FETCH di.temporaryAdmissionReceipt tar "
                                                        + "LEFT JOIN FETCH tar.eserGelisSekli "
                                                        + "LEFT JOIN FETCH c.mudurluk m LEFT JOIN FETCH c.previousCommission WHERE m in :mudurlukList "
                                                        + "ORDER BY c.commissionState, tar.eserGelisSekli, c.id DESC, c.silinmis, c.aktif DESC")
@NamedQuery(name = "Commission.findByMudurlukAndPersonelWithCommissionType", query = "SELECT distinct c  FROM Commission c LEFT JOIN FETCH c.mudurluk m LEFT JOIN FETCH c.commissionMembers cm LEFT JOIN FETCH cm.personel p LEFT JOIN FETCH c.previousCommission "
                                                                                     + "LEFT JOIN FETCH c.commissionObjects co "
                                                                                     + "LEFT JOIN FETCH co.object o "
                                                                                     + "LEFT JOIN FETCH o.deliveredItem di "
                                                                                     + "LEFT JOIN FETCH di.temporaryAdmissionReceipt tar "
                                                                                     + "LEFT JOIN FETCH tar.eserGelisSekli  "
                                                                                     + "WHERE (m in :mudurlukList OR p.id = :id) AND c.commissionType = :type AND c.commissionState != 0 "
                                                                                     + "ORDER BY c.commissionState, tar.eserGelisSekli, c.dateUpdated DESC, c.silinmis, c.aktif DESC")
@NamedQuery(name = "Commission.findByState", query = "SELECT c FROM Commission c WHERE c.commissionState = :state")

@ValidCommission(groups = CommissionGroup.class)
public class Commission extends AbstractEntity {

    private static final long                     serialVersionUID = -1257253912454192664L;

    private static final String                   KOM              = "KOM.%s";

    private static final DecimalFormat            df               = new DecimalFormat("000,000,000");

    @Size(max = 150)
    @Column(name = "name", length = 150)
    private String                                ad;

    @Column(name = "commissionNo")
    private Integer                               commissionNo;

    @Size(max = 150)
    @Column(name = "commissionLocation", length = 150)
    private String                                commissionLocation;

    @Size(max = 20)
    @Column(name = "reportNo", length = 20)
    private String                                reportNo;

    @FilePathCheck
    @Column(name = "reportPath", length = 150)
    private String                                reportPath;

    @Size(max = 20)
    @Column(name = "acceptanceReportNo", length = 20)
    private String                                acceptanceReportNo;

    @Column(name = "acceptanceDate")
    private Date                                  acceptanceDate;

    @FilePathCheck
    @Column(name = "acceptanceReportPath", length = 150)
    private String                                acceptanceReportPath;

    @FilePathCheck
    @Column(name = "acceptedPersonelReportPath", length = 150)
    private String                                acceptedPersonelReportPath;

    @Column(name = "commissionType")
    private CommissionTypeEnum                    commissionType;

    @Column(name = "commissionState")
    private Integer                               commissionState;

    @Column(name = "kmsState")
    private KMSStateEnum                          kmsState;

    @Column(name = "commissionDate")
    private Date                                  commissionDate;

    @Column(name = "dateCreated")
    private Date                                  dateCreated;

    @Column(name = "isHybrid")
    private Boolean                               hybrid           = Boolean.FALSE;

    @Column(name = "isAuthorized")
    private Boolean                               authorized       = Boolean.FALSE;

    @Size(max = 500)
    @Column(name = "ACIKLAMA", length = 500)
    private String                                aciklama;

    @Size(max = 4000)
    @Column(name = "commissionReport", length = 4000)
    private String                                commissionReport;

    @Size(max = 500)
    @Column(name = "returnComment", length = 500)
    private String                                returnComment;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "mudurlukId", referencedColumnName = "ID")
    private Mudurluk                              mudurluk;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "previousCommissionId", referencedColumnName = "ID")
    private Commission                            previousCommission;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "commission", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID")
    private Set<CommissionDocument>               documents;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "commission", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("ID DESC")
    private Set<CommissionUpdateApprovalDocument> commissionUpdateApprovalDocuments;

    @Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "commission", fetch = FetchType.LAZY, orphanRemoval = true)
    @OrderBy("isHead DESC, ID DESC")
    private Set<CommissionMember>                 commissionMembers;

    @Column(name = "updateInProgress")
    private Boolean                               updateInProgress = true;

    @Column(name = "dateUpdated")
    @Temporal(TemporalType.TIMESTAMP)
    private Date                                  dateUpdated;

    @JoinColumn(name = "createdBy", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView                          createdBy;

    @JoinColumn(name = "updatedBy", referencedColumnName = "ID")
    @ManyToOne(fetch = FetchType.LAZY)
    private PersonelView                          updatedBy;

    @Size(max = 40)
    @Column(name = "createSessionId")
    private String                                createSessionId;
    // komisyonlarin gelis sekillerine gore gruplanmasi icin kullaniliyor.
    @OneToMany(mappedBy = "commission", fetch = FetchType.LAZY)
    @Where(clause = "commissionId in (select TOP 1 Kms_Commission_Object.commissionId from Kms_Commission_Object where Kms_Commission_Object.commissionId = commissionId) ")
    private Set<CommissionObject>                 commissionObjects;

    @Transient
    private Integer                               totalInventoryNumber;

    public Commission() {
        // blank constructor
    }

    // getters and setters ....................................................

    @Override
    public String toString() {
        return this.getAd();
    }

    @Override
    public String getTitle() {
        return this.getAd();
    }

    @Override
    public void setAciklama(final String aciklama) {
        this.aciklama = aciklama;
    }

    public CommissionStateEnum getCommissionState() {
        return CommissionStateEnum.getState(this.commissionState);
    }

    public void setCommissionState(final CommissionStateEnum stateEnum) {
        this.commissionState = stateEnum.getCode();
    }

    public Integer getCommissionNo() {
        return this.commissionNo;
    }

    public String getFormattedCommissionNo() {
        if ((this.commissionNo != null) && (this.commissionDate != null) && CommissionTypeEnum.hasGeneralType(this.commissionType)) {
            return String.format("%d-%s", DateUtil.getYearOfDate(this.commissionDate), df.format(this.commissionNo));
        } else if ((this.commissionNo != null) && (this.commissionDate != null) && (this.mudurluk != null)) {
            return String.format((this.mudurluk.getKod().concat("-%d-%s")), DateUtil.getYearOfDate(this.commissionDate), df.format(this.commissionNo));
        }
        return null;
    }

    public String getMudurlukAndYear() {
        if (CommissionTypeEnum.hasGeneralType(this.commissionType)) {
            return String.format("%d-", DateUtil.getYearOfDate(Optional.ofNullable(this.commissionDate).orElse(new Date())));
        } else if (this.mudurluk != null) {
            return String.format((this.mudurluk.getKod().concat("-%d-")), DateUtil.getYearOfDate(Optional.ofNullable(this.commissionDate).orElse(new Date())));
        }
        return null;
    }

    public String getFormattedId() {
        if (this.getId() != null) {
            return String.format(KOM, df.format(this.getId()).replace(",", "."));
        }
        return null;
    }

    public String getAd() {
        return this.ad;
    }

    public void setAd(final String name) {
        this.ad = name;
    }

    public String getReportNo() {
        return this.reportNo;
    }

    public void setReportNo(final String reportNo) {
        this.reportNo = reportNo;
    }

    public String getReportPath() {
        return this.reportPath;
    }

    public void setReportPath(final String reportPath) {
        this.reportPath = reportPath;
    }

    public CommissionTypeEnum getCommissionType() {
        return this.commissionType;
    }

    public void setCommissionType(final CommissionTypeEnum commissionType) {
        this.commissionType = commissionType;
    }

    public void setCommissionState(final Integer commissionState) {
        this.commissionState = commissionState;
    }

    public Date getCommissionDate() {
        return this.commissionDate;
    }

    public void setCommissionDate(final Date commissionDate) {
        this.commissionDate = commissionDate;
    }

    public String getAciklama() {
        return this.aciklama;
    }

    public String getCommissionReport() {
        return this.commissionReport;
    }

    public void setCommissionReport(final String commissionReport) {
        this.commissionReport = commissionReport;
    }

    public Set<CommissionDocument> getDocuments() {
        if (this.documents == null) {
            this.setDocuments(new LinkedHashSet<>());
        }
        return this.documents;
    }

    public void setDocuments(final Set<CommissionDocument> documents) {
        this.documents = documents;
    }

    public Boolean getHybrid() {
        return this.hybrid;
    }

    public void setHybrid(final Boolean hybrid) {
        this.hybrid = hybrid;
    }

    public Boolean getAuthorized() {
        return this.authorized;
    }

    public void setAuthorized(final Boolean authorized) {
        if (authorized.equals(Boolean.TRUE)) {
            this.setHybrid(Boolean.TRUE);
        }
        this.authorized = authorized;
    }

    public Date getDateCreated() {
        return this.dateCreated;
    }

    public void setDateCreated(final Date dateCreated) {
        this.dateCreated = dateCreated;
    }

    public Set<CommissionMember> getCommissionMembers() {
        if (this.commissionMembers == null) {
            this.commissionMembers = new HashSet<>();
        }
        return this.commissionMembers;
    }

    public void setCommissionMembers(final Set<CommissionMember> commissionMembers) {
        this.commissionMembers = commissionMembers;
    }

    public Mudurluk getMudurluk() {
        return this.mudurluk;
    }

    public void setMudurluk(final Mudurluk mudurluk) {
        this.mudurluk = mudurluk;
    }

    public Commission getPreviousCommission() {
        return this.previousCommission;
    }

    public void setPreviousCommission(final Commission previousCommission) {
        this.previousCommission = previousCommission;
    }

    public Date getDateUpdated() {
        return this.dateUpdated;
    }

    public void setDateUpdated(final Date dateUpdated) {
        this.dateUpdated = dateUpdated;
    }

    public PersonelView getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(final PersonelView createdBy) {
        this.createdBy = createdBy;
    }

    public PersonelView getUpdatedBy() {
        return this.updatedBy;
    }

    public void setUpdatedBy(final PersonelView updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getCreateSessionId() {
        return this.createSessionId;
    }

    public void setCreateSessionId(final String createSessionId) {
        this.createSessionId = createSessionId;
    }

    public Boolean getUpdateInProgress() {
        return this.updateInProgress;
    }

    public void setUpdateInProgress(final Boolean updateInProgress) {
        this.updateInProgress = updateInProgress;
    }

    public String getReturnComment() {
        return this.returnComment;
    }

    public void setReturnComment(final String returnComment) {
        this.returnComment = returnComment;
    }

    public String getAcceptanceReportPath() {
        return this.acceptanceReportPath;
    }

    public void setAcceptanceReportPath(final String acceptanceReportPath) {
        this.acceptanceReportPath = acceptanceReportPath;
    }

    public String getCommissionLocation() {
        return this.commissionLocation;
    }

    public void setCommissionLocation(final String commissionLocation) {
        this.commissionLocation = commissionLocation;
    }

    public KMSStateEnum getKmsState() {
        return this.kmsState;
    }

    public void setKmsState(final KMSStateEnum kmsState) {
        this.kmsState = kmsState;
    }

    public Set<CommissionUpdateApprovalDocument> getCommissionUpdateApprovalDocuments() {
        if (this.commissionUpdateApprovalDocuments == null) {
            this.setCommissionUpdateApprovalDocuments(new LinkedHashSet<>());
        }
        return this.commissionUpdateApprovalDocuments;
    }

    public void setCommissionUpdateApprovalDocuments(final Set<CommissionUpdateApprovalDocument> commissionUpdateApprovalDocuments) {
        this.commissionUpdateApprovalDocuments = commissionUpdateApprovalDocuments;
    }

    public String getAcceptedPersonelReportPath() {
        return this.acceptedPersonelReportPath;
    }

    public void setAcceptedPersonelReportPath(final String acceptedPersonelReportPath) {
        this.acceptedPersonelReportPath = acceptedPersonelReportPath;
    }

    public void setCommissionNo(final Integer commissionNo) {
        this.commissionNo = commissionNo;
    }

    public String getAcceptanceReportNo() {
        return this.acceptanceReportNo;
    }

    public void setAcceptanceReportNo(final String acceptanceReportNo) {
        this.acceptanceReportNo = acceptanceReportNo;
    }

    public Date getAcceptanceDate() {
        return this.acceptanceDate;
    }

    public void setAcceptanceDate(final Date acceptanceDate) {
        this.acceptanceDate = acceptanceDate;
    }

    public Set<CommissionObject> getCommissionObjects() {
        return this.commissionObjects;
    }

    public void setCommissionObjects(final Set<CommissionObject> commissionObjects) {
        this.commissionObjects = commissionObjects;
    }

    public String getGelisSekliAd() {
        return Optional.ofNullable(this.commissionObjects)
                       .orElse(Collections.emptySet())
                       .stream()
                       .map(x -> x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getEserGelisSekli().getAd())
                       .findFirst()
                       .orElse(SearchConstants.BLANKSTRING);
    }

    public String getTgaNo() {
        return Optional.ofNullable(this.commissionObjects)
                       .orElse(Collections.emptySet())
                       .stream()
                       .map(x -> x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getTgaNo())
                       .distinct()
                       .collect(Collectors.joining(","));
    }

    public Integer getGelisSekliRank() {
        return Optional.ofNullable(this.commissionObjects)
                       .orElse(Collections.emptySet())
                       .stream()
                       .map(x -> x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getEserGelisSekli().getRank())
                       .findFirst()
                       .orElse(0);
    }

    public Integer getTotalInventoryNumber() {
        if (this.totalInventoryNumber == null) {
            this.totalInventoryNumber = Integer.valueOf(0);
            if (this.commissionObjects != null) {
                for (final CommissionObject co : this.commissionObjects) {
                    if (co.getDecidedCost() != null) {
                        this.totalInventoryNumber = this.totalInventoryNumber + co.getObject().getItemCount();
                    }
                }
            }
        }
        return this.totalInventoryNumber;
    }

    public void setTotalInventoryNumber(final Integer totalInventoryNumber) {
        this.totalInventoryNumber = totalInventoryNumber;
    }

    public BigDecimal getTotalInventoryCost() {
        BigDecimal retVal = new BigDecimal(0);
        if (this.commissionObjects != null) {

            for (final CommissionObject co : this.commissionObjects) {
                if (co.getDecidedCost() != null) {
                    retVal = retVal.add(co.getInventoryCostForAllObjects());
                }
            }

        }
        return retVal;
    }

}