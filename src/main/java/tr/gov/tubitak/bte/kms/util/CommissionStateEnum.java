package tr.gov.tubitak.bte.kms.util;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Table;

// dikkat EnumHelper kullanilarak  kms_commissionStateEnumMapping tablosuna yeni eklenen enum degeri ile guncellemeyi unutmayniz.Bunun icin uygulama acilisinda calisan sinifi aktif edebilirsiniz. 

@Table(name = "Kms_CommissionStateEnumMapping")
public enum CommissionStateEnum {

    CANCELED(2000, "Feshedildi", "fa fa-window-close-o red"),

    COMMISSION_DRAFT(0, "Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    COMMISSION_SAVE(1, "Değerlendirme Bekliyor", "fa fa-hourglass-o"),

    COST_DRAFT_FOR_CHANGE(2, "Değişiklik Talebi ile GM'den Geri <PERSON>", "fa fa-hourglass-o"),

    // Degerlendirme -----------------------------------
    EVALUATION_DRAFT(10, "Değerlendirme Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    EVALUATION_DRAFT_CERTIFIED(11, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    EVALUATION_COMPLETED(1015, CommissionStateEnumConstants.TAMAMLANDI, CommissionStateEnumConstants.FLOPPY_GREEN),

    EVALUATION_SENDEDTOCOST(1018, "Kıymet Komisyonuna Devredildi", CommissionStateEnumConstants.EXCHANGE_GREEN),

    // Kiymet ------------------------------------------
    COST_DRAFT(20, "Kıymet Takdir Taslak", CommissionStateEnumConstants.TEXT_GRAY, "kms/kiymet-takdir-komisyonu.xhtml?c=%s"),

    COST_FROM_EVALUATION(21, "Değerlendirme Komisyonundan Aktarma", CommissionStateEnumConstants.FILE_TEXT),

    COST_DRAFT_CERTIFIED(22, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    COST_COMPLETED(1025, CommissionStateEnumConstants.TAMAMLANDI, CommissionStateEnumConstants.FLOPPY_GREEN, KMSStateEnum.TGA_MUSEUM_DIRECTOR_APPROVAL),

    // Ust Degerlendirme ------------------------------
    HIGH_EVALUATION_DRAFT(30, "Üst Değerlendirme Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    HIGH_EVALUATION_DRAFT_CERTIFIED(31, "Üst Değerlendirme Onaylı Güncelleme Geçici Kayıt", CommissionStateEnumConstants.FILE_TEXT),

    HIGH_EVALUATION_COMPLETED(1035, "Üst Değerlendirme Tamamlandı", CommissionStateEnumConstants.FLOPPY_GREEN),

    HIGH_EVALUATION_SENDEDTOCOST(1038, "Üst Kıymet Komisyonuna Devredildi", CommissionStateEnumConstants.EXCHANGE_GREEN),

    // Ust Kıymet --------------------------------------
    HIGH_COST_DRAFT(40, "Üst Kıymet Takdir Taslak", CommissionStateEnumConstants.TEXT_GRAY, "kms/ust-kiymet-takdir-komisyonu?c=%s"),

    HIGH_COST_DRAFT_CERTIFIED(41, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    HIGH_COST_COMPLETED(1045, CommissionStateEnumConstants.TAMAMLANDI, CommissionStateEnumConstants.FLOPPY_GREEN, KMSStateEnum.HIGH_TGA_MUSEUM_DIRECTOR_APPROVAL),

    // Genel Mudurluk ----------------------------------
    GENERAL_DIRECTORATE_REVIEW_DRAFT(50, "Genel Müdürlük Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    GENERAL_DIRECTORATE_REVIEW_DRAFT_CERTIFIED(51, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    GENERAL_DIRECTORATE_REVIEW_COMPLETED(1055, CommissionStateEnumConstants.TAMAMLANDI, "fa fa-file-text-o"),

    // Sayim -------------------------------------------
    COUNTING_DRAFT(60, "Sayım Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    COUNTING_DRAFT_CERTIFIED(61, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    COUNTING_COMPLETED(1065, CommissionStateEnumConstants.TAMAMLANDI, "fa fa-file-text-o"),

    // Eser Inceleme Komisyonu
    REVIEW_COMMISSION_DRAFT(80, "Eser İnceleme Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    REVIEW_COMMISSION_CERTIFIED(81, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    REVIEW_COMMISSION_COMPLETED(1085, CommissionStateEnumConstants.TAMAMLANDI, "fa fa-floppy-o"),

    // Yeniden Degerlendirme ------------------------------
    RE_EVALUATION_DRAFT(90, "Yeniden Değerlendirme Taslak", CommissionStateEnumConstants.TEXT_GRAY),

    RE_EVALUATION_DRAFT_CERTIFIED(91, CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    RE_EVALUATION_COMPLETED(1095, CommissionStateEnumConstants.TAMAMLANDI, CommissionStateEnumConstants.FLOPPY_GREEN, KMSStateEnum.RE_TGA_MUSEUM_DIRECTOR_APPROVAL),

    RE_EVALUATION_SENDEDTOCOST(1098, "Yeniden Kıymet Komisyonuna Devredildi", CommissionStateEnumConstants.EXCHANGE_GREEN),

    // Yeniden Kıymet --------------------------------------
    RE_COST_DRAFT(100, "Yeniden " + "Kıymet Takdir Taslak", CommissionStateEnumConstants.TEXT_GRAY, "kms/yeniden-kiymet-takdir-komisyonu?c=%s"),

    RE_COST_DRAFT_CERTIFIED(101, "Yeniden " + CommissionStateEnumConstants.ONAYLI_GUNCELLE, CommissionStateEnumConstants.FILE_TEXT),

    RE_COST_COMPLETED(1105, "Üst Kıymet Takdir Tamamlandı", CommissionStateEnumConstants.FLOPPY_GREEN, KMSStateEnum.RE_TGA_MUSEUM_DIRECTOR_APPROVAL),

    CANCELED_FROM_GENERAL_DIRECTORATE(1999, "Feshedildi", "fa fa-window-close-o red"),

    WAITING_FOR_OTHER_COMMISSIONS(2000, "Diğer Komisyonları Bekliyor", "fa fa-hourglass-o"),
    ;

    private final Integer                                  code;
    private final String                                   name;
    private final String                                   icon;
    private final String                                   draftLabel;
    private String                                         nextLabel;
    private final String                                   prevLabel;
    private String                                         redirectionLabel;

    private CommissionStateEnum                            next;
    private CommissionStateEnum                            prev;
    private CommissionStateEnum                            draft;
    private CommissionStateEnum                            redirection;
    private String                                         redirectUrl;
    private KMSStateEnum                                   kmsStateEnum;

    private static final Map<Integer, CommissionStateEnum> map = new HashMap<>();
    private static List<CommissionStateEnum>               listCompleted;
    private static List<CommissionStateEnum>               listEvaluationStates;
    private static List<CommissionStateEnum>               listCostStates;
    private static List<CommissionStateEnum>               listHighEvaluationStates;
    private static List<CommissionStateEnum>               listHighCostStates;
    private static List<CommissionStateEnum>               listGeneralDirectorateStates;
    private static List<CommissionStateEnum>               listCountingStates;
    private static List<CommissionStateEnum>               listReviewStates;
    private static List<CommissionStateEnum>               listReevaluationStates;
    private static List<CommissionStateEnum>               listRecostStates;

    static {

        listCompleted = Arrays.asList(CommissionStateEnum.COST_COMPLETED,
                                        CommissionStateEnum.EVALUATION_COMPLETED,
                                        CommissionStateEnum.EVALUATION_SENDEDTOCOST,
                                        CommissionStateEnum.COUNTING_COMPLETED,
                                        CommissionStateEnum.GENERAL_DIRECTORATE_REVIEW_COMPLETED,
                                        CommissionStateEnum.HIGH_EVALUATION_COMPLETED,
                                        CommissionStateEnum.HIGH_EVALUATION_SENDEDTOCOST,
                                        CommissionStateEnum.HIGH_COST_COMPLETED,
                                        CommissionStateEnum.REVIEW_COMMISSION_COMPLETED,
                                        CommissionStateEnum.RE_EVALUATION_COMPLETED,
                                        CommissionStateEnum.RE_EVALUATION_SENDEDTOCOST,
                                        CommissionStateEnum.RE_COST_COMPLETED);

        listEvaluationStates = Arrays.asList(CommissionStateEnum.EVALUATION_DRAFT,
                                                CommissionStateEnum.EVALUATION_DRAFT_CERTIFIED,
                                                CommissionStateEnum.EVALUATION_COMPLETED,
                                                CommissionStateEnum.EVALUATION_SENDEDTOCOST,
                                                CommissionStateEnum.CANCELED,
                                                CommissionStateEnum.COMMISSION_DRAFT,
                                                CommissionStateEnum.COMMISSION_SAVE);

        listCostStates = Arrays.asList(CommissionStateEnum.COST_DRAFT,
                                        CommissionStateEnum.COST_FROM_EVALUATION,
                                        CommissionStateEnum.COST_DRAFT_CERTIFIED,
                                        CommissionStateEnum.COST_COMPLETED,
                                        CommissionStateEnum.CANCELED,
                                        CommissionStateEnum.COMMISSION_DRAFT,
                                        CommissionStateEnum.COMMISSION_SAVE);

        listHighEvaluationStates = Arrays.asList(CommissionStateEnum.HIGH_EVALUATION_DRAFT,
                                                    CommissionStateEnum.HIGH_EVALUATION_DRAFT_CERTIFIED,
                                                    CommissionStateEnum.HIGH_EVALUATION_COMPLETED,
                                                    CommissionStateEnum.HIGH_EVALUATION_SENDEDTOCOST,
                                                    CommissionStateEnum.CANCELED,
                                                    CommissionStateEnum.COMMISSION_DRAFT,
                                                    CommissionStateEnum.COMMISSION_SAVE);

        listHighCostStates = Arrays.asList(CommissionStateEnum.HIGH_COST_DRAFT,
                                            CommissionStateEnum.HIGH_COST_DRAFT_CERTIFIED,
                                            CommissionStateEnum.HIGH_COST_COMPLETED,
                                            CommissionStateEnum.CANCELED,
                                            CommissionStateEnum.COMMISSION_DRAFT,
                                            CommissionStateEnum.COMMISSION_SAVE);

        listGeneralDirectorateStates = Arrays.asList(CommissionStateEnum.GENERAL_DIRECTORATE_REVIEW_DRAFT,
                                                        CommissionStateEnum.GENERAL_DIRECTORATE_REVIEW_DRAFT_CERTIFIED,
                                                        CommissionStateEnum.GENERAL_DIRECTORATE_REVIEW_COMPLETED,
                                                        CommissionStateEnum.CANCELED,
                                                        CommissionStateEnum.COMMISSION_DRAFT,
                                                        CommissionStateEnum.COMMISSION_SAVE);

        listCountingStates = Arrays.asList(CommissionStateEnum.COUNTING_DRAFT,
                                            CommissionStateEnum.COUNTING_DRAFT_CERTIFIED,
                                            CommissionStateEnum.COUNTING_COMPLETED,
                                            CommissionStateEnum.CANCELED,
                                            CommissionStateEnum.COMMISSION_DRAFT,
                                            CommissionStateEnum.COMMISSION_SAVE);

        listReviewStates = Arrays.asList(CommissionStateEnum.REVIEW_COMMISSION_DRAFT,
                                            CommissionStateEnum.REVIEW_COMMISSION_CERTIFIED,
                                            CommissionStateEnum.REVIEW_COMMISSION_COMPLETED,
                                            CommissionStateEnum.CANCELED,
                                            CommissionStateEnum.COMMISSION_DRAFT,
                                            CommissionStateEnum.COMMISSION_SAVE);

        listReevaluationStates = Arrays.asList(CommissionStateEnum.RE_EVALUATION_DRAFT,
                                                CommissionStateEnum.RE_EVALUATION_DRAFT_CERTIFIED,
                                                CommissionStateEnum.RE_EVALUATION_COMPLETED,
                                                CommissionStateEnum.RE_EVALUATION_SENDEDTOCOST,
                                                CommissionStateEnum.CANCELED,
                                                CommissionStateEnum.COMMISSION_DRAFT,
                                                CommissionStateEnum.COMMISSION_SAVE);

        listRecostStates = Arrays.asList(CommissionStateEnum.RE_COST_DRAFT,
                                            CommissionStateEnum.RE_COST_DRAFT_CERTIFIED,
                                            CommissionStateEnum.RE_COST_COMPLETED,
                                            CommissionStateEnum.CANCELED,
                                            CommissionStateEnum.COMMISSION_DRAFT,
                                            CommissionStateEnum.COMMISSION_SAVE);

        for (final CommissionStateEnum each : CommissionStateEnum.values()) {
            map.put(each.getCode(), each);
        }

        // COMMISSION
        COMMISSION_DRAFT.draft = COMMISSION_DRAFT;
        COMMISSION_DRAFT.next = COMMISSION_SAVE;
        COMMISSION_DRAFT.nextLabel = "Kaydet";

        COMMISSION_SAVE.draft = COMMISSION_SAVE;

        // EVALUATION
        EVALUATION_DRAFT.draft = EVALUATION_DRAFT;
        EVALUATION_DRAFT.next = EVALUATION_COMPLETED;

        EVALUATION_COMPLETED.redirection = COST_DRAFT;
        EVALUATION_COMPLETED.redirectionLabel = "Kıymet Takdir Komisyonuna Aktar";
        EVALUATION_COMPLETED.next = EVALUATION_SENDEDTOCOST;
        EVALUATION_COMPLETED.prev = EVALUATION_DRAFT_CERTIFIED;
        EVALUATION_SENDEDTOCOST.prev = EVALUATION_DRAFT_CERTIFIED;

        EVALUATION_DRAFT_CERTIFIED.draft = EVALUATION_DRAFT_CERTIFIED;
        EVALUATION_DRAFT_CERTIFIED.next = EVALUATION_COMPLETED;

        // COST
        COST_DRAFT.draft = COST_DRAFT;
        COST_DRAFT.next = COST_COMPLETED;

        COST_COMPLETED.prev = COST_DRAFT_CERTIFIED;
        COST_COMPLETED.redirection = COST_DRAFT_FOR_CHANGE;

        COST_DRAFT_CERTIFIED.draft = COST_DRAFT_CERTIFIED;
        COST_DRAFT_CERTIFIED.next = COST_COMPLETED;

        COST_DRAFT_FOR_CHANGE.draft = COST_DRAFT_FOR_CHANGE;
        COST_DRAFT_FOR_CHANGE.next = COST_COMPLETED;

        COST_FROM_EVALUATION.draft = COST_FROM_EVALUATION;
        COST_FROM_EVALUATION.next = COST_COMPLETED;

        // HIGH_COST
        HIGH_COST_DRAFT.draft = HIGH_COST_DRAFT;
        HIGH_COST_DRAFT.next = HIGH_COST_COMPLETED;

        HIGH_COST_COMPLETED.prev = HIGH_COST_DRAFT_CERTIFIED;

        HIGH_COST_DRAFT_CERTIFIED.draft = HIGH_COST_DRAFT_CERTIFIED;
        HIGH_COST_DRAFT_CERTIFIED.next = HIGH_COST_COMPLETED;

        // HIGH_EVALUATION
        HIGH_EVALUATION_DRAFT.draft = HIGH_EVALUATION_DRAFT;
        HIGH_EVALUATION_DRAFT.next = HIGH_EVALUATION_COMPLETED;

        HIGH_EVALUATION_COMPLETED.next = HIGH_EVALUATION_SENDEDTOCOST;

        HIGH_EVALUATION_COMPLETED.prev = HIGH_EVALUATION_DRAFT_CERTIFIED;
        HIGH_EVALUATION_SENDEDTOCOST.prev = HIGH_EVALUATION_DRAFT_CERTIFIED;

        HIGH_EVALUATION_COMPLETED.redirection = HIGH_COST_DRAFT;
        HIGH_EVALUATION_COMPLETED.redirectionLabel = "Üst Kıymet Takdir Komisyonuna Aktar";

        HIGH_EVALUATION_DRAFT_CERTIFIED.draft = HIGH_EVALUATION_DRAFT_CERTIFIED;
        HIGH_EVALUATION_DRAFT_CERTIFIED.next = HIGH_EVALUATION_COMPLETED;

        // Re- EVALUATION
        RE_EVALUATION_DRAFT.draft = RE_EVALUATION_DRAFT;
        RE_EVALUATION_DRAFT.next = RE_EVALUATION_COMPLETED;

        RE_EVALUATION_COMPLETED.prev = RE_EVALUATION_DRAFT_CERTIFIED;
        RE_EVALUATION_COMPLETED.redirection = RE_COST_DRAFT;
        RE_EVALUATION_COMPLETED.redirectionLabel = "Yeniden Kıymet Takdir Komisyonuna Aktar";
        RE_EVALUATION_COMPLETED.next = RE_EVALUATION_SENDEDTOCOST;

        RE_EVALUATION_COMPLETED.prev = RE_EVALUATION_DRAFT_CERTIFIED;
        RE_EVALUATION_SENDEDTOCOST.prev = RE_EVALUATION_DRAFT_CERTIFIED;

        RE_EVALUATION_DRAFT_CERTIFIED.draft = RE_EVALUATION_DRAFT_CERTIFIED;
        RE_EVALUATION_DRAFT_CERTIFIED.next = RE_EVALUATION_COMPLETED;

        RE_COST_DRAFT.draft = RE_COST_DRAFT;
        RE_COST_DRAFT.next = RE_COST_COMPLETED;

        RE_COST_COMPLETED.prev = RE_COST_DRAFT_CERTIFIED;

        // GENERAL_DIRECTORATE
        GENERAL_DIRECTORATE_REVIEW_DRAFT.draft = GENERAL_DIRECTORATE_REVIEW_DRAFT;
        GENERAL_DIRECTORATE_REVIEW_DRAFT.next = GENERAL_DIRECTORATE_REVIEW_COMPLETED;

        GENERAL_DIRECTORATE_REVIEW_COMPLETED.prev = GENERAL_DIRECTORATE_REVIEW_DRAFT_CERTIFIED;

        GENERAL_DIRECTORATE_REVIEW_DRAFT_CERTIFIED.draft = GENERAL_DIRECTORATE_REVIEW_DRAFT_CERTIFIED;
        GENERAL_DIRECTORATE_REVIEW_DRAFT_CERTIFIED.next = GENERAL_DIRECTORATE_REVIEW_COMPLETED;

        // COUNTING
        COUNTING_DRAFT.draft = COUNTING_DRAFT;
        COUNTING_DRAFT.next = COUNTING_COMPLETED;

        COUNTING_COMPLETED.prev = COUNTING_DRAFT_CERTIFIED;

        COUNTING_DRAFT_CERTIFIED.draft = COUNTING_DRAFT_CERTIFIED;
        COUNTING_DRAFT_CERTIFIED.next = COUNTING_COMPLETED;

        // REVİEW
        REVIEW_COMMISSION_DRAFT.draft = REVIEW_COMMISSION_DRAFT;
        REVIEW_COMMISSION_DRAFT.next = REVIEW_COMMISSION_COMPLETED;

        REVIEW_COMMISSION_COMPLETED.prev = REVIEW_COMMISSION_CERTIFIED;

        REVIEW_COMMISSION_CERTIFIED.draft = REVIEW_COMMISSION_CERTIFIED;
        REVIEW_COMMISSION_CERTIFIED.next = REVIEW_COMMISSION_COMPLETED;

    }

    private CommissionStateEnum(final Integer code, final String name, final String icon, final KMSStateEnum kmsStateEnum) {
        this(code, name, icon);
        this.kmsStateEnum = kmsStateEnum;
    }

    private CommissionStateEnum(final Integer code, final String name, final String icon) {
        this.code = code;
        this.name = name;
        this.icon = icon;
        this.draftLabel = "Geçici Kaydet";
        this.nextLabel = "Değerlendirmeyi Tamamla";
        this.prevLabel = "Geri Gönder";
        this.kmsStateEnum = KMSStateEnum.TGA_EVALUATION_AND_COST; // default state
    }

    private CommissionStateEnum(final Integer code, final String name, final String icon, final String redirectUrl) {
        this(code, name, icon);
        this.redirectUrl = redirectUrl;
    }

    public static CommissionStateEnum getState(final Integer code) {
        return map.getOrDefault(code, null);
    }

    public static Map<Integer, CommissionStateEnum> getMap() {
        return map;
    }

    public String getName() {
        return this.name;
    }

    public String getTitle() {
        return this.name;
    }

    public String getIcon() {
        return this.icon;
    }

    public CommissionStateEnum getNext() {
        return this.next;
    }

    public CommissionStateEnum getPrev() {
        return this.prev;
    }

    public CommissionStateEnum getRedirection() {
        return this.redirection;
    }

    public CommissionStateEnum getDraft() {
        return this.draft;
    }

    public String getDraftLabel() {
        return this.draftLabel;
    }

    public String getNextLabel() {
        return this.nextLabel;
    }

    public String getPrevLabel() {
        return this.prevLabel;
    }

    public String getRedirectionLabel() {
        return this.redirectionLabel;
    }

    public Integer getCode() {
        return this.code;
    }

    public static List<CommissionStateEnum> getListCompleted() {
        return listCompleted;
    }

    // gets draft state according to the commissionType
    public static CommissionStateEnum getDraftByCommissionType(final CommissionTypeEnum commissionType) {
        for (final CommissionStateEnum state : CommissionStateEnum.values()) {
            if (state.toString().startsWith(commissionType.toString()) && state.toString().contains("DRAFT")) {
                return state;
            }
        }
        return null;
    }

    public String getRedirectUrl() {
        return this.redirectUrl;
    }

    public KMSStateEnum getKmsStateEnum() {
        return this.kmsStateEnum;
    }

    static class CommissionStateEnumConstants {
        private CommissionStateEnumConstants() {
        }

        static final String         TEXT_GRAY       = "fa fa-file-text-o";
        static final String         ONAYLI_GUNCELLE = "Onaylı Güncelleme Geçici Kayıt";
        private static final String FILE_TEXT       = "fa fa-file-text-o";
        private static final String FLOPPY_GREEN    = "fa fa-floppy-o";
        private static final String TAMAMLANDI      = "Komisyon Tamamlandı";
        private static final String EXCHANGE_GREEN  = "fa fa-exchange";
    }

    public static List<CommissionStateEnum> getListEvaluationStates() {
        return listEvaluationStates;
    }

    public static List<CommissionStateEnum> getListCostStates() {
        return listCostStates;
    }

    public static List<CommissionStateEnum> getListHighEvaluationStates() {
        return listHighEvaluationStates;
    }

    public static List<CommissionStateEnum> getListHighCostStates() {
        return listHighCostStates;
    }

    public static List<CommissionStateEnum> getListGeneralDirectorateStates() {
        return listGeneralDirectorateStates;
    }

    public static List<CommissionStateEnum> getListCountingStates() {
        return listCountingStates;
    }

    public static List<CommissionStateEnum> getListReviewStates() {
        return listReviewStates;
    }

    public static List<CommissionStateEnum> getListReevaluationStates() {
        return listReevaluationStates;
    }

    public static List<CommissionStateEnum> getListRecostStates() {
        return listRecostStates;
    }
}
