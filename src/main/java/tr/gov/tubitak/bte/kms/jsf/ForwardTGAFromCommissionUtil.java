package tr.gov.tubitak.bte.kms.jsf;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import javax.enterprise.context.RequestScoped;

import javax.inject.Inject;
import javax.inject.Named;

import org.omnifaces.cdi.ViewScoped;
import tr.gov.tubitak.bte.kms.model.DeliveredItem;
import tr.gov.tubitak.bte.kms.model.TemporaryAdmissionReceipt;
import tr.gov.tubitak.bte.kms.session.TemporaryAdmissionReceiptFacade;
import tr.gov.tubitak.bte.kms.util.KMSStateEnum;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;

@Named
@ViewScoped
public class ForwardTGAFromCommissionUtil implements Serializable {

    private static final long               serialVersionUID = -6809970861300203676L;

    @Inject
    private CommissionObjectController      commissionObjectController;
    @Inject
    private TemporaryAdmissionReceiptFacade tgaFacade;

    private List<Integer>                   allDeliveredItemIds = new ArrayList<>();
    /***
     * Updates TGA(s) state if all deliveredItems are ready to get approved
     * 
     * @param kmsStateEnum
     * 
     * @param entitiesToUpdate
     */

    protected ForwardTGAFromCommissionUtil() {
        // intentially left blank

    }

    public void updateTGA(final KMSStateEnum kmsStateEnum, final List<AbstractEntity> entitiesToUpdate) {

        final List<Integer> tgaIds = this.commissionObjectController.getDistinctTgaIds();
        final List<Integer> deliveredItemIds = this.commissionObjectController.getDeliveredItemIds();
        allDeliveredItemIds.addAll(deliveredItemIds);
        final List<DeliveredItem> itemsNotInThisCommission = this.commissionObjectController.findDeliveredItemsNotAssignedToThisCommission(tgaIds, deliveredItemIds);

        if (itemsNotInThisCommission.isEmpty()) {
            // TODO: refactor this code.
            final TemporaryAdmissionReceipt tga = this.commissionObjectController.getItems().iterator().next().getObject().getDeliveredItem().getTemporaryAdmissionReceipt();
            tga.setKmsState(kmsStateEnum);
            entitiesToUpdate.add(tga);  // add TGA

        } else {
            // Control any deliveredItem has TGA contains other deliveredItems completed before
            final List<TemporaryAdmissionReceipt> tgaList = this.tgaFacade.findByIds(tgaIds);
            tgaList.forEach(tga -> this.commissionObjectController.getItems().forEach(co ->
                {
                    if ((co.getDecidedCost() != null) && tga.getDeliveredItems().contains(co.getObject().getDeliveredItem())) {
                        final List<DeliveredItem> others = tga.getDeliveredItems().stream().filter(i -> !i.getId().equals(co.getObject().getDeliveredItem().getId())).collect(Collectors.toList());
                        final boolean othersApproved = others.stream()
                                                             .allMatch(i -> allDeliveredItemIds.contains(i.getId())
                                                                            || ((i.getKmsState() != null) && (i.getKmsState().ordinal() >= kmsStateEnum.ordinal())));
                        if (othersApproved) {
                            co.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().setKmsState(kmsStateEnum);
                            entitiesToUpdate.add(co.getObject().getDeliveredItem().getTemporaryAdmissionReceipt()); // add TGA
                        }
                    }
                }));
        }

    }

}