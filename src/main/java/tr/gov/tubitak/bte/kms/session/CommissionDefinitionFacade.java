package tr.gov.tubitak.bte.kms.session;

import java.util.List;

import javax.enterprise.context.RequestScoped;

import tr.gov.tubitak.bte.kms.model.Commission;
import tr.gov.tubitak.bte.kms.util.CommissionTypeEnum;
import tr.gov.tubitak.bte.kms.util.CommissionStateEnum;
import tr.gov.tubitak.bte.mues.model.Mudurluk;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.session.AbstractFacade;

@RequestScoped
public class CommissionDefinitionFacade extends AbstractFacade<Commission> {

    public CommissionDefinitionFacade() {
        super(Commission.class);
    }

    public List<Commission> findByMudurlukAndPersonelWithCommissionType(final List<Mudurluk> mudurlukList, final CommissionTypeEnum type, final PersonelView personel) {
        return this.em.createNamedQuery("Commission.findByMudurlukAndPersonelWithCommissionType", Commission.class)
                      .setParameter("mudurlukList", mudurlukList)
                      .setParameter("id", personel.getId())
                      .setParameter("type", type)
                      .getResultList();
    }

    public List<Commission> findByMudurluk(final List<Mudurluk> mudurlukList) {
        return this.getEM().createNamedQuery("Commission.findByMudurluk", Commission.class).setParameter("mudurlukList", mudurlukList).getResultList();
    }

    // finds commission list that related current user as a commission member and commissionType
    public List<Commission> findByPersonelAndCommissionType(final PersonelView personel, final CommissionTypeEnum type) {
        return this.getEM().createNamedQuery("Commission.findByPersonelAndCommissionType", Commission.class).setParameter("id", personel.getId()).setParameter("type", type).getResultList();
    }

    public Integer findCountByTypeAndYear(final CommissionTypeEnum commissionType) {
        return (Integer) this.getEM()
                             .createNativeQuery("SELECT count(c.ID) FROM Kms_Commission c WHERE c.commissionType=:code AND YEAR(c.commissionDate)=YEAR(GETDATE())")
                             .setParameter("code", commissionType.getCode())
                             .getSingleResult();
    }

    public List<Commission> findByState(CommissionStateEnum state) {
        return this.em.createNamedQuery("Commission.findByState", Commission.class)
                .setParameter("state", state.getCode())
                .getResultList();
    }

}