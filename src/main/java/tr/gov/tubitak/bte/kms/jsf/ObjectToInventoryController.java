package tr.gov.tubitak.bte.kms.jsf;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;
import javax.transaction.Transactional;

import org.omnifaces.cdi.ViewScoped;

import tr.gov.tubitak.bte.kms.model.HeritageObject;
import tr.gov.tubitak.bte.kms.session.ObjectFacade;
import tr.gov.tubitak.bte.kms.util.KMSStateEnum;
import tr.gov.tubitak.bte.kms.util.ObjectTypeEnum;
import tr.gov.tubitak.bte.mues.jsf.AbstractController;
import tr.gov.tubitak.bte.mues.jsf.SessionBean;
import tr.gov.tubitak.bte.mues.model.Kullanici;
import tr.gov.tubitak.bte.mues.util.DBOperationResult;
import tr.gov.tubitak.bte.mues.util.MuesUtil;

@Named
@ViewScoped
public class ObjectToInventoryController extends AbstractController<HeritageObject> {

    private static final long serialVersionUID = 2217581382267101218L;

    @Inject
    private ObjectFacade      facade;

    @Inject
    private SessionBean       sessionBean;

    private Kullanici         selectedKullanici;

    private List<String>      selectedItem;

    public ObjectToInventoryController() {
        super(HeritageObject.class);
    }

    /***
     * Items to inventory by TGAState, ObjectTypeEnum and Commission
     */
    @Override
    public List<HeritageObject> getItems() {
        if (this.items == null) {
            this.items = this.facade.findByCategoryAndKMSState(Arrays.asList(ObjectTypeEnum.INVENTORY),
                                                               Arrays.asList(KMSStateEnum.TGA_REGISTER_TO_INVENTORY,
                                                                             KMSStateEnum.TGA_MADE_PAYMENT_TO_GTK,
                                                                             KMSStateEnum.TGA_PAYMENT_TRANSFORMED_TO_GRANT));
        }
        return this.items;
    }

    @Transactional
    public void transferToDraftEser(final HeritageObject object) {
        final DBOperationResult result;
        String message;
        this.setModel(object);

        Integer userId = this.sessionBean.getCurrentUser().getId();
        if (this.getSelectedKullanici() != null) {
            userId = this.getSelectedKullanici().getId();
        }

        if (object.getDeliveredItem().getOmkEserId() != null) { // coming from OMK
            result = this.facade.createEserFromOMKTransfer(object, userId);
            message = "Komisyon objesi geçici kayıt olarak MUES envanter modülüne aktarıldı";
        } else {
            result = this.facade.createEserAsBulk(object, userId);
            message = object.getItemCount() + " adet komisyon objesi geçici kayıt olarak MUES envanter modülüne aktarıldı";
        }

        this.forward();
        if ((result != null) && result.isSuccess()) {
            this.setItems(null);
            MuesUtil.showMessage(message, FacesMessage.SEVERITY_INFO);
        }
        this.selectedKullanici = null;
    }

    @Transactional
    public void transferToDraftEserByAssigningToExpert() {
        this.transferToDraftEser(this.getModel());
    }

    public void forward() {
        final List<Integer> ids = new ArrayList<>();
        ids.add(this.getModel().getDeliveredItem().getTemporaryAdmissionReceipt().getId());
        final boolean hasAllInventory = this.facade.findObjectByTGAIdList(MuesUtil.toIds(ids)).stream().allMatch(x -> (x.getObjectType() != null) && (x.getObjectType() == ObjectTypeEnum.INVENTORY));
        if (hasAllInventory) {
            this.getModel().getDeliveredItem().getTemporaryAdmissionReceipt().setKmsState(this.getModel().getDeliveredItem().getTemporaryAdmissionReceipt().getKmsState().getNext());
            this.getModel().getDeliveredItem().getTemporaryAdmissionReceipt().getDeliveredItems().forEach(x -> x.setKmsState(x.getKmsState().getNext()));
            super.update(this.getModel().getDeliveredItem().getTemporaryAdmissionReceipt());
        }
    }

    // getters and setters ....................................................

    @Override
    public ObjectFacade getFacade() {
        return this.facade;
    }

    public Kullanici getSelectedKullanici() {
        return this.selectedKullanici;
    }

    public void setSelectedKullanici(final Kullanici selectedKullanici) {
        this.selectedKullanici = selectedKullanici;
        transferToDraftEserByAssigningToExpert();
    }

    public List<String> getSelectedItem() {
        return this.selectedItem;
    }

    public void setSelectedItem(final List<String> selectedItem) {
        this.selectedItem = selectedItem;
    }

}
