package tr.gov.tubitak.bte.kms.jsf;

import java.io.UncheckedIOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.EnumMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.faces.application.FacesMessage;
import javax.inject.Inject;
import javax.inject.Named;
import javax.persistence.PersistenceException;
import javax.transaction.Transactional;

import org.omnifaces.cdi.ViewScoped;
import org.omnifaces.util.Faces;

import tr.gov.tubitak.bte.kms.model.AbstractCommissionDecisionEntity;
import tr.gov.tubitak.bte.kms.model.Commission;
import tr.gov.tubitak.bte.kms.model.CommissionEser;
import tr.gov.tubitak.bte.kms.model.CommissionEserDecision;
import tr.gov.tubitak.bte.kms.model.CommissionMember;
import tr.gov.tubitak.bte.kms.model.CommissionObject;
import tr.gov.tubitak.bte.kms.model.CommissionObjectDecision;
import tr.gov.tubitak.bte.kms.model.DeliveredItem;
import tr.gov.tubitak.bte.kms.model.HeritageObject;
import tr.gov.tubitak.bte.kms.model.ObjectDocument;
import tr.gov.tubitak.bte.kms.model.ObjectMeasure;
import tr.gov.tubitak.bte.kms.model.ObjectPhoto;
import tr.gov.tubitak.bte.kms.model.ObjectSuper;
import tr.gov.tubitak.bte.kms.session.CommissionEserFacade;
import tr.gov.tubitak.bte.kms.session.CommissionObjectDecisionFacade;
import tr.gov.tubitak.bte.kms.session.CommissionObjectFacade;
import tr.gov.tubitak.bte.kms.session.DeliveredItemFacade;
import tr.gov.tubitak.bte.kms.session.ObjectFacade;
import tr.gov.tubitak.bte.kms.util.CommissionStateEnum;
import tr.gov.tubitak.bte.kms.util.CommissionTypeEnum;
import tr.gov.tubitak.bte.kms.util.KMSStateEnum;
import tr.gov.tubitak.bte.kms.util.ObjectTypeEnum;
import tr.gov.tubitak.bte.mues.jsf.AbstractController;
import tr.gov.tubitak.bte.mues.jsf.SessionBean;
import tr.gov.tubitak.bte.mues.model.AbstractEntity;
import tr.gov.tubitak.bte.mues.model.Eser;
import tr.gov.tubitak.bte.mues.model.PersonelView;
import tr.gov.tubitak.bte.mues.search.PersonelQualifier;
import tr.gov.tubitak.bte.mues.search.PersonelSearchController;
import tr.gov.tubitak.bte.mues.search.SearchQualifier;
import tr.gov.tubitak.bte.mues.search.controller.SearchController;
import tr.gov.tubitak.bte.mues.session.EserFacade;
import tr.gov.tubitak.bte.mues.session.PersonelViewFacade;
import tr.gov.tubitak.bte.mues.solr.model.ArtifactsSolrModel;
import tr.gov.tubitak.bte.mues.solr.model.PersonelSolrModel;
import tr.gov.tubitak.bte.mues.util.MuesUtil;
import tr.gov.tubitak.bte.mues.util.NumberConverter;

/**
 */
@Named
@ViewScoped
public class CommissionObjectController extends AbstractController<CommissionObject> {

    private static final long                                          serialVersionUID = 1044043444045414040L;

    @Inject
    private CommissionObjectFacade                                     facade;

    @Inject
    private ObjectFacade                                               objectFacade;

    @Inject
    private EserFacade                                                 eserFacade;

    @Inject
    private CommissionEserFacade                                       commissionEserFacade;

    @Inject
    private CommissionObjectDecisionFacade                             decisionFacade;

    @Inject
    private DeliveredItemFacade                                        deliveredItemFacade;

    @Inject
    private PersonelViewFacade                                         personelViewFacade;

    @Inject
    private SessionBean                                                sessionBean;

    @Inject
    private ObjectController                                           objectController;

    @Inject
    @PersonelQualifier
    private PersonelSearchController                                   personelSearchController;

    @Inject
    @SearchQualifier
    private SearchController                                           searchController;

    @Inject
    private DeliveredItemSelectionController                           deliveredItemSelectionController;

    @Inject
    private CommissionDefinitionController                             commissionDefinitionController;

    private transient Map<CommissionObject, Map<PersonelView, Object>> decisionMap;

    private transient Map<CommissionEser, Map<PersonelView, Object>>   eserDecisionMap;

    private List<CommissionEser>                                       commissionEserList;

    private List<PersonelView>                                         personelList;


    public CommissionObjectController() {
        super(CommissionObject.class);
    }

    public List<CommissionObject> fetchCommissionObjectByCommissionId(final Integer id) {

        final List<CommissionObject> list = this.facade.findByCommissionId(id);

        this.decisionMap = this.decisionFacade.constructCommissionObjectDecisonMap(list);

        this.personelList = this.commissionDefinitionController.getModel().getCommissionMembers().stream().map(CommissionMember::getPersonel).collect(Collectors.toList());
        return list;
    }

    /***
     * Returns commissionEser list by commission id generating its decision map
     * 
     * @param id commission id
     * @return commissionEser list
     */
    public List<CommissionEser> fetchEserListByCommissionId(final Integer id) {
        this.commissionEserList = this.commissionEserFacade.findByCommissionId(id);
        this.eserDecisionMap = this.decisionFacade.constructCommissionEserDecisonMap(this.commissionEserList);
        return this.commissionEserList;
    }

    @Override
    public List<CommissionObject> getItems() {
        if (this.items == null) {
            if ((this.commissionDefinitionController.getModel() == null) || (this.commissionDefinitionController.getModel().getId() == null)) {
                this.items = new ArrayList<>();
            } else {
                this.items = this.fetchCommissionObjectByCommissionId(this.commissionDefinitionController.getModel().getId());
            }
        }
        return this.items;
    }

    public List<CommissionEser> getCommissionEserList() {
        if (this.commissionEserList == null) {
            this.commissionEserList = this.fetchEserListByCommissionId(this.commissionDefinitionController.getModel().getId());
        }
        return this.commissionEserList;
    }

    public void fetchObjectsFromPreviousComissionIncludeAllObjectOfTemporaryAdmissionReciept(final Commission previous, final Commission commission) {

        final List<CommissionObject> commissionObjects = this.fetchCommissionObjectByCommissionId(previous.getId());
        final List<Integer> tgaIds = commissionObjects.stream().map(x -> x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getId()).distinct().collect(Collectors.toList());
        final List<HeritageObject> objectList = this.objectFacade.findObjectByTGAIdList(tgaIds);

        objectList.forEach(x ->
            {
                final CommissionObject co = new CommissionObject();
                co.setCommission(commission);
                co.setObject(x);
                this.getItems().add(co);
            });

    }

    public void fetchObjectsFromPreviousComission(final Commission previous, final Commission commission) {

        final List<CommissionObject> commissionObjects = this.fetchCommissionObjectByCommissionId(previous.getId());

        commissionObjects.forEach(x ->
            {
                x.setId(null);
                x.setCommission(commission);
                x.setCommissionObjectDecisions(null);
                x.setDecidedCost(null);
                x.setDecidedCategory(null);

                this.getItems().add(x);
            });
    }

    public void fetchEsersFromPreviousComission(final Commission previous, final Commission commission) {
        this.fetchEserListByCommissionId(previous.getId()).forEach(x ->
            {
                x.setId(null);
                x.setCommission(commission);
                x.setCommissionEserDecisions(null);
                x.setDecidedCost(null);

                this.getCommissionEserList().add(x);
            });
    }

    public void createCommissionAndAddDeliveredItemsAsCommissionObject() {

        this.commissionDefinitionController.newRecord();
        this.commissionDefinitionController.getModel().setCommissionType(CommissionTypeEnum.EVALUATION);
        this.commissionDefinitionController.getModel().setCommissionState(CommissionStateEnum.EVALUATION_DRAFT);
        this.addDeliveredItemsAsCommissionObject();

        try {
            Faces.redirect("kms/degerlendirme-komisyonu?c=%s", this.commissionDefinitionController.getModel().getId().toString());
        } catch (final UncheckedIOException ioe) {
            this.logger.error("[EvaluationCommissionController.redirect] : Hata : {}", ioe.getMessage(), ioe);
        }
    }

    //
    public void createCostCommissionAndAddObjectAsCommissionObject() {

        final List<AbstractEntity> entities = new ArrayList<>();

        this.commissionDefinitionController.newRecord();
        this.commissionDefinitionController.getModel().setCommissionType(CommissionTypeEnum.COST);
        this.commissionDefinitionController.getModel().setCommissionState(CommissionStateEnum.COST_DRAFT);

        this.commissionDefinitionController.getModel().setDateCreated(new Date());
        this.commissionDefinitionController.getModel().setDateUpdated(new Date());
        this.commissionDefinitionController.getModel().setCreatedBy(this.sessionBean.getCurrentUser().getPersonelView());
        this.commissionDefinitionController.getModel().setUpdatedBy(this.sessionBean.getCurrentUser().getPersonelView());
        this.commissionDefinitionController.getModel().setCreateSessionId(MuesUtil.fetchSessionId());
        this.commissionDefinitionController.getModel().setMudurluk(this.deliveredItemSelectionController.getSelectedItems().get(0).getTemporaryAdmissionReceipt().getMudurluk());

        final List<Integer> ids = this.deliveredItemSelectionController.getSelectedItems().stream().map(DeliveredItem::getId).collect((Collectors.toList()));

        // this.deliveredItemSelectionController

        final List<HeritageObject> objectsList = this.objectFacade.findByDeliveredItemIds(MuesUtil.toIds(ids));
        entities.add(this.commissionDefinitionController.getModel());

        this.deliveredItemSelectionController.getSelectedItems().forEach(x ->
            {
                x.setKmsState(KMSStateEnum.TGA_MUSEUM_DIRECTOR_APPROVAL);
                entities.add(x);
            });

        objectsList.forEach(x ->
            {
                final CommissionObject commissionObject = new CommissionObject();
                commissionObject.setCommission(this.commissionDefinitionController.getModel());
                commissionObject.setObject(x);
                entities.add(commissionObject);

            });

        // persist none-existent items
        if (!entities.isEmpty()) {
            this.update("Komisyon Objeleri Oluşturuldu", entities);
            this.setItems(null);
            this.getItems();
            this.deliveredItemSelectionController.getSelectedItems().clear();
        }

        try {
            Faces.redirect("kms/kiymet-takdir-komisyonu?c=%s", this.commissionDefinitionController.getModel().getId().toString());
        } catch (final UncheckedIOException ioe) {
            this.logger.error("[EvaluationCommissionController.redirect] : Hata : {}", ioe.getMessage(), ioe);
        }
    }

    /***
     * Creates commissionObjects by selected deliveredItems that are none-existent and persists them to the DB
     */

    public void addDeliveredItemsAsCommissionObject() {
        final List<AbstractEntity> entities = new ArrayList<>();

        // newItems to prevent duplicate records
        final List<Integer> existingIds = this.getItems().stream().map(x -> x.getObject().getDeliveredItem().getId()).collect(Collectors.toList());
        final List<DeliveredItem> newItems = this.deliveredItemSelectionController.getSelectedItems().stream().filter(x -> !existingIds.contains(x.getId())).collect((Collectors.toList()));

        newItems.forEach(dItem ->
            {
                final HeritageObject object = this.createObjectByDeliveredItem(dItem);
                final CommissionObject commissionObject = new CommissionObject();
                dItem.setKmsState(KMSStateEnum.getKmsStateByGelisSekli(this.deliveredItemSelectionController.getSelectedItems().get(0).getTemporaryAdmissionReceipt().getEserGelisSekli().getRank()));
                commissionObject.setObject(object);
                commissionObject.setCommission(this.commissionDefinitionController.getModel());
                entities.add(object);
                entities.add(commissionObject);
                entities.add(dItem);
            });

        this.commissionDefinitionController.getModel().setDateCreated(new Date());
        this.commissionDefinitionController.getModel().setDateUpdated(new Date());
        this.commissionDefinitionController.getModel().setCreatedBy(this.sessionBean.getCurrentUser().getPersonelView());
        this.commissionDefinitionController.getModel().setUpdatedBy(this.sessionBean.getCurrentUser().getPersonelView());
        this.commissionDefinitionController.getModel().setCreateSessionId(MuesUtil.fetchSessionId());
        this.commissionDefinitionController.getModel().setMudurluk(this.deliveredItemSelectionController.getSelectedItems().get(0).getTemporaryAdmissionReceipt().getMudurluk());

        entities.add(this.commissionDefinitionController.getModel());
        // persist none-existent items
        if (!entities.isEmpty()) {
            this.update("Komisyon Objeleri Oluşturuldu", entities);
            this.setItems(null);
            this.getItems();
            this.deliveredItemSelectionController.getSelectedItems().clear();
        }
    }

    /***
     * Adds a new commission object copied from the incoming parent to the dataTable
     * 
     * @param commissionObject
     */
    public void addNewCommissionObject(final CommissionObject commissionObject) {
        final CommissionObject dbCommissionObject = this.facade.findEagerById(commissionObject.getId());

        // calc difference according to ancestor deliveredItem
        final int difference = this.calcDifference(dbCommissionObject.getObject().getItemCount(), commissionObject);
        if (difference == 0) {
            return;
        }

        final HeritageObject object = this.deepCopyObject(difference, dbCommissionObject.getObject());
        final CommissionObject newCommissionObject = this.deepCopyCommissionObject(object, commissionObject);

        newCommissionObject.setObject(object);

        final int index = this.getItems().indexOf(commissionObject);
        this.getItems().add(index + 1, newCommissionObject);

        this.putDecisionMap(newCommissionObject);

    }

    /***
     * Puts new decision to the decisionMap
     * 
     * @param commissionObject
     */
    public void putDecisionMap(final CommissionObject commissionObject) {
        final Set<CommissionObjectDecision> decisions = new HashSet<>();
        for (final PersonelView personel : this.personelList) {
            final CommissionObjectDecision decision = new CommissionObjectDecision();
            decision.setPersonel(personel);
            decision.setCommissionObject(commissionObject);
            decisions.add(decision);
            this.decisionFacade.getPersonelObjectDecisionMap(this.decisionMap, decision.getCommissionObject()).put(decision.getPersonel(), decision.getDecidedValue());
        }
        commissionObject.setCommissionObjectDecisions(decisions);
    }

    /***
     * Calculates the difference considering parent and children itemCount
     * 
     * @param dbCommissionObject
     * @param commissionObject from dataTable
     * @return difference
     */
    private int calcDifference(final int total, final CommissionObject commissionObject) {
        int sum = commissionObject.getObject().getItemCount();

        final List<CommissionObject> children = this.getItems()
                                                    .stream()
                                                    .filter(x -> (x.getObject().getParentId() != null) && x.getObject().getParentId().equals(commissionObject.getObject().getId()))
                                                    .collect((Collectors.toList()));

        if ((commissionObject.getObject().getParentId() == null)) {
            for (final CommissionObject c : children) {
                if (c.getId() == null) {
                    sum += c.getObject().getItemCount();
                }
            }
        } else {
            for (final CommissionObject c : children) {
                sum += c.getObject().getItemCount();

            }
        }

        return total - sum;
    }

    /***
     * Removes the child and restores the parent itemCount
     * 
     * @param commissionObject
     */
    public void removeChild(final CommissionObject commissionObject) {
        this.getItems().forEach(x ->
            {
                if ((x.getObject().getId() != null) && x.getObject().getId().equals(commissionObject.getObject().getParentId())) {
                    x.getObject().setItemCount(x.getObject().getItemCount() + commissionObject.getObject().getItemCount());
                }
            });

        this.getItems().remove(commissionObject);
    }

    /***
     * Copies the commissionObject deeply
     * 
     * @param commissionObject HeritageObject
     * @return HeritageObject
     */
    public CommissionObject deepCopyCommissionObject(final HeritageObject object, final CommissionObject commissionObject) {
        final CommissionObject copy = new CommissionObject();
        copy.setCommission(commissionObject.getCommission());
        copy.setDecidedCategory(commissionObject.getDecidedCategory());
        copy.setObject(object);
        return copy;
    }

    /***
     * Copies the deliveredItem to the object
     * 
     * @param DeliveredItem
     * @return HeritageObject
     */
    public HeritageObject createObjectByDeliveredItem(DeliveredItem objectToBeCopied) {

        objectToBeCopied = this.deliveredItemFacade.findEagerById(objectToBeCopied.getId());

        final HeritageObject copy = new HeritageObject();
        copy.setDeliveredItem(objectToBeCopied);
        copy.setItemCount(objectToBeCopied.getItemCount());
        copy.setMudurluk(this.commissionDefinitionController.getModel().getMudurluk());
        copy.setBeldeAdi(objectToBeCopied.getBeldeAdi());
        copy.setAciklama(objectToBeCopied.getAciklama());

        this.copyCommonFields(objectToBeCopied, copy);

        objectToBeCopied.getDocuments().forEach(x ->
            {
                final ObjectDocument doc = new ObjectDocument();
                doc.setAd(x.getAd());
                doc.setPdfPath(x.getPdfPath());
                doc.setObject(copy);
                doc.setAciklama(x.getAciklama());
                copy.getDocuments().add(doc);
            });

        copy.setPhotos(new HashSet<>());
        objectToBeCopied.getPhotos().forEach(x ->
            {
                final ObjectPhoto photo = new ObjectPhoto();
                photo.setFotografBasligi(x.getFotografBasligi());
                photo.setFotografPath(x.getFotografPath());
                photo.setAnaFotograf(x.getAnaFotograf());
                photo.setAciklama(x.getAciklama());
                photo.setIlgiliYuz(x.getIlgiliYuz());
                photo.setObject(copy);
                copy.getPhotos().add(photo);
            });

        copy.setMeasures(new HashSet<>());
        objectToBeCopied.getMeasures().forEach(x ->
            {
                final ObjectMeasure measure = new ObjectMeasure();
                measure.setDeger(x.getDeger());
                measure.setMeasure(x.getMeasure());
                measure.setAciklama(x.getAciklama());
                measure.setObject(copy);
                copy.getMeasures().add(measure);
            });

        return copy;
    }

    /***
     * Copies the object deeply
     * 
     * @param itemCount
     * @param objectToBeCopied HeritageObject
     * @return HeritageObject
     */
    public HeritageObject deepCopyObject(final int itemCount, final HeritageObject objectToBeCopied) {
        final HeritageObject copy = new HeritageObject();
        copy.setParentId(objectToBeCopied.getId());
        copy.setItemCount(itemCount);
        copy.setDeliveredItem(objectToBeCopied.getDeliveredItem());

        copy.setObjectType(objectToBeCopied.getObjectType());
        copy.setCategory(objectToBeCopied.getCategory());
        this.copyCommonFields(objectToBeCopied, copy);

        copy.setDocuments(new HashSet<>());
        objectToBeCopied.getDocuments().forEach(x ->
            {
                x.setId(null);
                x.setObject(copy);
                copy.getDocuments().add(x);
            });

        copy.setPhotos(new HashSet<>());
        objectToBeCopied.getPhotos().forEach(x ->
            {
                x.setId(null);
                x.setObject(copy);
                copy.getPhotos().add(x);
            });

        copy.setMeasures(new HashSet<>());
        objectToBeCopied.getMeasures().forEach(x ->
            {
                x.setId(null);
                x.setObject(copy);
                copy.getMeasures().add(x);
            });

        return copy;
    }

    public void copyCommonFields(final ObjectSuper objectToBeCopied, final HeritageObject copy) {

        copy.setCag(objectToBeCopied.getCag());
        copy.setCounty(objectToBeCopied.getCounty());
        copy.setCreatedBy(objectToBeCopied.getCreatedBy());
        copy.setCreateSessionId(objectToBeCopied.getCreateSessionId());
        copy.setDateCreated(objectToBeCopied.getDateCreated());
        copy.setDateDeleted(objectToBeCopied.getDateDeleted());
        copy.setDateDiscovery(objectToBeCopied.getDateDiscovery());
        copy.setDateUpdated(objectToBeCopied.getDateUpdated());
        copy.setDeletedBy(objectToBeCopied.getDeletedBy());
        copy.setDonem(objectToBeCopied.getDonem());
        copy.setEserAltTur(objectToBeCopied.getEserAltTur());
        copy.setEserOzelAd(objectToBeCopied.getEserOzelAd());
        copy.setHukumdar(objectToBeCopied.getHukumdar());
        copy.setKronoloji(objectToBeCopied.getKronoloji());
        copy.setKronolojiAciklama(objectToBeCopied.getKronolojiAciklama());
        copy.setMalzeme(objectToBeCopied.getMalzeme());
        copy.setProvince(objectToBeCopied.getProvince());
        copy.setTasinirMalYonKod(objectToBeCopied.getTasinirMalYonKod());
        copy.setTermEnd(objectToBeCopied.getTermEnd());
        copy.setTermStart(objectToBeCopied.getTermStart());
        copy.setUpdatedBy(objectToBeCopied.getUpdatedBy());
        copy.setUpdateInProgress(objectToBeCopied.getUpdateInProgress());
        copy.setUygarlik(objectToBeCopied.getUygarlik());
        copy.setBeldeAdi(objectToBeCopied.getBeldeAdi());
        copy.setAciklama(objectToBeCopied.getAciklama());

        copy.setDocuments(new HashSet<>());

    }

    /***
     * Removes the commissionObject and updates the parent as DB operation
     * 
     * @param commissionObject
     * @return
     */
    @Transactional
    public void removeCommissionObject(final CommissionObject commissionObject) {

        if (commissionObject.getId() != null) { // for recorded commissionObject

            try {

                if (commissionObject.getObject().getParentId() != null) { // it's a child, so the parent object itemCount must be restored
                    // restore to the parent item count
                    this.getItems().forEach(x ->
                        {
                            if ((x.getObject().getId() != null) && x.getObject().getId().equals(commissionObject.getObject().getParentId())) {
                                x.getObject().setItemCount(x.getObject().getItemCount() + commissionObject.getObject().getItemCount());
                                this.facade.getEM().merge(x.getObject());// update parent for object itemCount
                            }
                        });

                } else { // it's a parent, so remove children if exist
                    final List<CommissionObject> children = this.facade.findByParentId(commissionObject.getObject().getId());
                    children.forEach(x ->
                        {
                            if (x.getId() != null) {
                                x.getObject().setItemCount(0); // update object itemCount as 0
                                this.facade.getEM().merge(x.getObject());
                                this.facade.delete(x); // remove child commissionObject
                            }
                        });

                    commissionObject.getObject().getDeliveredItem().setKmsState(KMSStateEnum.TGA_EVALUATION_AND_COST); // set state as null, as it has
                                                                                                                       // no object anymore
                    this.facade.getEM().merge(commissionObject.getObject().getDeliveredItem());
                }
                // WARNING: DO NOT remove the object (the object may be used by another commission, so it cannot be deleted)

                commissionObject.getObject().setItemCount(0); // update object itemCount as 0
                this.facade.getEM().merge(commissionObject.getObject());

                // remove the commissionObject
                this.facade.delete(commissionObject);
                this.facade.getEM().flush();

                this.getItems().remove(commissionObject);

                // remove all children for UI dataTable
                this.getItems().removeIf(x -> (x.getObject().getParentId() != null) && x.getObject().getParentId().equals(commissionObject.getObject().getId()));

                MuesUtil.showMessage("Komisyon objesi silindi", FacesMessage.SEVERITY_INFO);
            } catch (final PersistenceException e) {
                this.logger.debug("[removeCommissionObject] : {}", e.getMessage());
                MuesUtil.showMessage("Obje silinemedi. Başka komisyonlar tarafından kullanılıyor olabilir, kontrol ediniz.", FacesMessage.SEVERITY_WARN);
            }

        } else {// remove the commissionObject that has not been recorded yet (separated object)
            this.removeChild(commissionObject);
        }
    }

    /***
     * Get commmission head personel id
     * 
     * @return Commmission head personel id
     */
    public Integer getCommissionHeadId() {
        return this.commissionDefinitionController.getModel().getCommissionMembers().stream().filter(x -> Boolean.TRUE.equals(x.getHead())).map(x -> x.getPersonel().getId()).findFirst().orElse(null);

    }

    /***
     * Checks all decisions are valid (not null)
     * 
     * @return
     */
    public boolean checkAllDecisions() {

        return !(((this.decisionMap == null)
                  || this.decisionMap.values()
                                     .stream()
                                     .filter(Objects::nonNull)
                                     .allMatch(innerMap -> innerMap.values().stream().allMatch(Objects::nonNull)))
                 && ((this.eserDecisionMap == null)
                     ||
                     this.eserDecisionMap.values()
                                         .stream()
                                         .filter(Objects::nonNull)
                                         .allMatch(innerMap -> innerMap.values().stream().allMatch(Objects::nonNull)))

                 && ((this.getItems().stream().allMatch(x -> x.getDecidedCategory() != null)
                      &&
                      this.getCommissionEserList().stream().allMatch(x -> x.getDecidedCategory() != null))

                     ||

                     (this.getItems().stream().allMatch(x -> x.getDecidedCost() != null)
                      &&
                      this.getCommissionEserList().stream().allMatch(x -> x.getDecidedCost() != null)))

        );
    }

    private BigDecimal getValue(final String b) {
        if (b.isBlank() || b.equals("null")) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(b);
    }

    /***
     * Adds commissionMember to the map & items & commissionEserList
     * 
     * @param value
     */

    public void addCommissionMember(final Object value) {
        final List<Integer> ids = this.personelSearchController.getSelectionList().stream().map(PersonelSolrModel::getId).collect(Collectors.toList());
        final List<PersonelView> selectedPersonel = this.personelViewFacade.findByIds(ids);

        selectedPersonel.forEach(prs ->
            {

                if (!this.personelList.contains(prs)) { // if not exist

                    this.personelList.add(prs);
                    // add new commissionMember to the commissionMemberList
                    final CommissionMember commissionMember = new CommissionMember();
                    commissionMember.setPersonel(prs);
                    commissionMember.setCommission(this.commissionDefinitionController.getModel());

                    // map and UI
                    Optional.ofNullable(this.decisionMap).ifPresent(z -> z.keySet().forEach(x -> this.decisionMap.get(x).put(prs, value)));
                    Optional.ofNullable(this.eserDecisionMap).ifPresent(z -> z.keySet().forEach(x -> this.eserDecisionMap.get(x).put(prs, null)));

                    // add new decision to the items and eserList
                    for (final CommissionObject commissionObject : this.getItems()) {
                        final CommissionObjectDecision decision = new CommissionObjectDecision();
                        decision.setPersonel(prs);
                        decision.setCommissionObject(commissionObject);
                        commissionObject.getCommissionObjectDecisions().add(decision);
                        this.updateDecisionMap(value, decision);

                    }

                    for (final CommissionEser commissionEser : this.getCommissionEserList()) {
                        final CommissionEserDecision decision = new CommissionEserDecision();
                        decision.setPersonel(prs);
                        decision.setCommissionEser(commissionEser);
                        commissionEser.getCommissionEserDecisions().add(decision);
                        this.updateEserDecisionMap(value, decision);
                    }

                    // commissionDefinitionController & UI
                    if (this.commissionDefinitionController.getModel().getCommissionMembers().isEmpty()) {
                        commissionMember.setHead(true);
                    }
                    this.commissionDefinitionController.getModel().getCommissionMembers().add(commissionMember);

                    // reset all decided costs
                    if (value instanceof BigDecimal) {
                        this.getItems().forEach(this::calcAverage);
                        this.getCommissionEserList().forEach(this::calcEserAverage);
                    } else {

                        this.resetAllDecidedCategories();
                    }

                }
            });

    }

    public void updateDecisionMap(final Object value, final CommissionObjectDecision decision) {

        this.decisionFacade.getPersonelObjectDecisionMap(this.decisionMap, decision.getCommissionObject()).put(decision.getPersonel(), value);

    }

    public void updateEserDecisionMap(final Object value, final CommissionEserDecision decision) {

        this.decisionFacade.getPersonelDecisionMap(this.eserDecisionMap, decision.getCommissionEser()).put(decision.getPersonel(), value);

    }

    /***
     * Changes the decidedCategory up to the majority or head
     * 
     * Applies the head decision if no majority
     * 
     * @param commissionObject
     */

    public void changeDecidedCategory(final CommissionObject commissionObject) {
        final Map<PersonelView, Object> personelMap = this.getDecisionMap().get(commissionObject);
        final EnumMap<ObjectTypeEnum, Integer> categoryRateMap = this.getCategoryRateMap(personelMap);

        if (this.isNoMajority(categoryRateMap)) {
            this.applyHeadRule(commissionObject, personelMap, categoryRateMap);
        } else {
            this.applyMajorityRule(commissionObject, categoryRateMap);
        }
    }

    private boolean isNoMajority(final EnumMap<ObjectTypeEnum, Integer> categoryRateMap) {
        final List<Integer> rates = new ArrayList<>(categoryRateMap.values());
        Collections.sort(rates, Collections.reverseOrder());
        return (rates.size() > 1) && (rates.get(0).intValue() == rates.get(1).intValue());
    }

    private void applyHeadRule(final CommissionObject commissionObject,
                               final Map<PersonelView, Object> personelMap,
                               final EnumMap<ObjectTypeEnum, Integer> categoryRateMap) {
        final CommissionObjectDecision headObjectDecision = this.getHeadObjectDecision(commissionObject);

        if (headObjectDecision != null) {
            commissionObject.setDecidedCategory(null);
            final ObjectTypeEnum headDecisionType = (ObjectTypeEnum) personelMap.get(headObjectDecision.getPersonel());
            final ObjectTypeEnum majorityDecisionType = this.getMajorityDecisionType(categoryRateMap);

            if (categoryRateMap.get(majorityDecisionType).equals(categoryRateMap.get(headDecisionType))) {
                commissionObject.setDecidedCategory(headDecisionType);
            }
        }
    }

    private CommissionObjectDecision getHeadObjectDecision(final CommissionObject commissionObject) {
        return commissionObject.getCommissionObjectDecisions()
                               .stream()
                               .filter(x -> x.getPersonel().getId().equals(this.getCommissionHeadId()))
                               .findFirst()
                               .orElse(null);
    }

    private ObjectTypeEnum getMajorityDecisionType(final EnumMap<ObjectTypeEnum, Integer> categoryRateMap) {
        return Collections.max(categoryRateMap.entrySet(), Map.Entry.comparingByValue()).getKey();
    }

    private void applyMajorityRule(final CommissionObject commissionObject, final EnumMap<ObjectTypeEnum, Integer> categoryRateMap) {
        commissionObject.setDecidedCategory(this.getMajorityDecisionType(categoryRateMap));
    }

    /***
     * Changes the decidedCategory up to the majority or head
     * 
     * Applies the head decision if no majority
     * 
     * @param commissionEser
     */
    public void changeEserDecidedCategory(final CommissionEser commissionEser) {
        final Map<PersonelView, Object> personelMap = this.getEserDecisionMap().get(commissionEser);
        final EnumMap<ObjectTypeEnum, Integer> categoryRateMap = this.getCategoryRateMap(personelMap);
        final List<Integer> rates = new ArrayList<>(categoryRateMap.values());
        Collections.sort(rates, Collections.reverseOrder()); // sort rates as desc

        // if the rate is the same among members (no majority) // e.g.: (etude=1, inventory=1, forge=1) OR (etude=2, inventory=2, forge=1)
        if ((rates.size() > 1) && (rates.get(0).intValue() == rates.get(1).intValue())) {
            // apply the head rule
            // TODO: unification
            final CommissionEserDecision headObjectDecision = commissionEser.getCommissionEserDecisions()
                                                                            .stream()
                                                                            .filter(x -> x.getPersonel().getId().equals(this.getCommissionHeadId()))
                                                                            .findFirst()
                                                                            .orElse(null);
            if (headObjectDecision != null) {
                commissionEser.setDecidedCategory(null);
                final ObjectTypeEnum headDecisionType = (ObjectTypeEnum) personelMap.get(headObjectDecision.getPersonel());
                final ObjectTypeEnum majorityDecisionType = Collections.max(categoryRateMap.entrySet(), Map.Entry.comparingByValue()).getKey();
                // check if head decision in majority
                if (categoryRateMap.get(majorityDecisionType).equals(categoryRateMap.get(headDecisionType))) {
                    commissionEser.setDecidedCategory((ObjectTypeEnum) personelMap.get(headObjectDecision.getPersonel()));
                }
            }

        } else { // if there is a majority, apply the majority rule
            commissionEser.setDecidedCategory(Collections.max(categoryRateMap.entrySet(), Map.Entry.comparingByValue()).getKey());
        }
    }

    private EnumMap<ObjectTypeEnum, Integer> getCategoryRateMap(final Map<PersonelView, Object> personelMap) {
        final EnumMap<ObjectTypeEnum, Integer> categoryRateMap = new EnumMap<>(ObjectTypeEnum.class);
        final List<Object> list = new ArrayList<>(personelMap.values());

        Arrays.stream(ObjectTypeEnum.values()).forEach(type -> categoryRateMap.put(type, Collections.frequency(list, type)));

        return categoryRateMap;
    }

    public void changeCommissionHead(final CommissionMember commissionMember) {
        this.commissionDefinitionController.changeCommissionHead(commissionMember);

        this.resetAllDecidedCategories();
    }

    // reset all decided categories
    public void resetAllDecidedCategories() {
        this.getItems().forEach(this::changeDecidedCategory);
        this.getCommissionEserList().forEach(this::changeEserDecidedCategory);
    }

    /***
     * Removes commissionMember from the map & items & commissionEserList
     */
    public void removeCommissionMember(final CommissionMember commissionMember) {
        final PersonelView personel = commissionMember.getPersonel();

        if (this.personelList.contains(personel)) {
            this.personelList.remove(personel);
            // commissionDefinitionController & UI
            this.commissionDefinitionController.removeCommissionMember(commissionMember);

            if (!this.getItems().isEmpty()) {
                // for UI
                this.decisionMap.keySet().forEach(x -> this.decisionMap.get(x).remove(personel));

                // remove the decision from the items
                for (final CommissionObject commissionObject : this.getItems()) {

                    this.removePersonelDecisions(commissionObject.getCommissionObjectDecisions(), personel);
                }
                this.getItems().forEach(this::calcAverage);
                // reset all decided costs and category
                this.getItems().forEach(this::changeDecidedCategory);
            } else {
                this.eserDecisionMap.keySet().forEach(x -> this.eserDecisionMap.get(x).remove(personel));

                for (final CommissionEser commissionEser : this.getCommissionEserList()) {
                    this.removePersonelDecisions(commissionEser.getCommissionEserDecisions(), personel);
                }
                // reset all decided costs and category
                this.getCommissionEserList().forEach(this::calcEserAverage);
                this.getCommissionEserList().forEach(this::changeEserDecidedCategory);
            }

        }
    }

    private void removePersonelDecisions(final Set<? extends AbstractCommissionDecisionEntity> decisionMap, final PersonelView personel) {
        decisionMap.removeIf(x -> x.getPersonel().equals(personel));
    }

    /***
     * Helper method to calculate average cost
     *
     * @param personelMap
     * @param decisions
     * @return BigDecimal
     */
    private BigDecimal calculateAverageCost(final Map<PersonelView, Object> personelMap, final Collection<? extends AbstractCommissionDecisionEntity> decisions) {
        BigDecimal avg = BigDecimal.ZERO;
        for (final AbstractCommissionDecisionEntity d : decisions) {
            avg = avg.add(this.getValue(String.valueOf(personelMap.get(d.getPersonel()))));
        }
        if (avg.compareTo(BigDecimal.ZERO) > 0) {
            return avg.divide(new BigDecimal(personelMap.size()), 2, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    /***
     * Calculates and sets the average of cost to the commissionObject
     * 
     * @param commissionObject
     */
    public void calcAverage(final CommissionObject commissionObject) {
        final Map<PersonelView, Object> personelMap = this.getDecisionMap().get(commissionObject);
        if ((personelMap == null) || personelMap.values().isEmpty() || (personelMap.values().iterator().next() instanceof ObjectTypeEnum)) {
            return;
        }
        commissionObject.setDecidedCost(this.calculateAverageCost(personelMap, commissionObject.getCommissionObjectDecisions()));
    }

    /***
     * Calculates and sets the average of cost to the commissionEser
     * 
     * @param commissionEser
     */
    public void calcEserAverage(final CommissionEser commissionEser) {
        final Map<PersonelView, Object> personelMap = this.eserDecisionMap.get(commissionEser);
        commissionEser.setDecidedCost(this.calculateAverageCost(personelMap, commissionEser.getCommissionEserDecisions()));
    }

    /***
     * Creates CommissionEsers by selectionList and adds them to the commissionEserList
     * 
     */
    public void addCommissionEser(final Object value) {
        final List<ArtifactsSolrModel> diffList = this.searchController.getSelectionList()
                                                                       .stream()
                                                                       .filter(x1 -> this.getCommissionEserList().stream().noneMatch(x2 -> x2.getEser().getId().equals(x1.getId())))
                                                                       .collect(Collectors.toList());

        diffList.forEach(x ->
            {

                final Eser eser = this.eserFacade.findEagerById(x.getId());
                final CommissionEser commissionEser = new CommissionEser();
                commissionEser.setEser(eser);
                commissionEser.setCommission(this.commissionDefinitionController.getModel());

                // new decisions
                for (final PersonelView personel : this.personelList) {
                    final CommissionEserDecision decision = new CommissionEserDecision();
                    decision.setPersonel(personel);
                    decision.setCommissionEser(commissionEser);
                    commissionEser.getCommissionEserDecisions().add(decision);

                    // put it to the map for UI
                    this.updateEserDecisionMap(value, decision);

                }

                this.commissionEserList.add(commissionEser);
            });

    }

    /***
     * Removes the commissionEser
     * 
     * @param commissionEser
     * @return DBOperationResult
     */
    public void removeCommissionEser(final CommissionEser commissionEser) {
        this.commissionEserList.remove(commissionEser);
        if (commissionEser.getId() != null) {
            this.commissionEserFacade.delete(commissionEser);
            MuesUtil.showMessage("Eser komisyondan silindi", FacesMessage.SEVERITY_INFO);
        }
    }

    public List<Integer> getDistinctTgaIds() {
        return this.getItems().stream().map(x -> x.getObject().getDeliveredItem().getTemporaryAdmissionReceipt().getId()).distinct().collect(Collectors.toList());
    }

    public List<Integer> getDeliveredItemIds() {
        return this.getItems().stream().map(x -> x.getObject().getDeliveredItem().getId()).collect(Collectors.toList());
    }

    public List<DeliveredItem> findDeliveredItemsNotAssignedToThisCommission(final List<Integer> tgaIds, final List<Integer> deliveredItemIds) {
        return this.deliveredItemFacade.findByTGAIdsAndExcludeIds(MuesUtil.toIds(tgaIds), MuesUtil.toIds(deliveredItemIds));
    }

    public boolean allItemsNotDecidedAsInventory() {
        return this.getItems().stream().allMatch(x -> (x.getDecidedCategory() != null) && !x.getDecidedCategory().equals(ObjectTypeEnum.INVENTORY));
    }

    /***
     * Updates eser cost (kiymet) with decided cost
     * 
     * @param event
     */
    public void updateEserCost(final List<AbstractEntity> entities) {
        this.commissionEserList.forEach(x ->
            {
                if ((x.getDecidedCost() != null) && (x.getDecidedCost().compareTo(BigDecimal.ZERO) > 0)) {
                    x.getEser().setKiymet(x.getDecidedCost());
                    entities.add(x.getEser());
                }
            });
    }

    public void updateObject() {
        this.objectController.handleCreationDetails();
        this.objectController.updateTimeRelatedFields();
        this.objectController.restoreFields();
        this.objectController.writeToPermanentFolder();
    }

    public void clearLists() {
        this.setItems(null);
        this.setCommissionEserList(null);
        this.decisionMap = null;
        this.eserDecisionMap = null;
        this.personelList = null;
    }

    // getters and setters ....................................................

    @Override
    public CommissionObjectFacade getFacade() {
        return this.facade;
    }

    public Map<CommissionObject, Map<PersonelView, Object>> getDecisionMap() {
        return this.decisionMap;
    }

    public void setDecisionMap(final Map<CommissionObject, Map<PersonelView, Object>> decisionMap) {
        this.decisionMap = decisionMap;
    }

    public List<PersonelView> getPersonelList() {
        return this.personelList;
    }

    public void setPersonelList(final List<PersonelView> personelList) {
        this.personelList = personelList;
    }

    public Map<CommissionEser, Map<PersonelView, Object>> getEserDecisionMap() {
        return this.eserDecisionMap;
    }

    public void setEserDecisionMap(final Map<CommissionEser, Map<PersonelView, Object>> eserDecisionMap) {
        this.eserDecisionMap = eserDecisionMap;
    }

    public void setCommissionEserList(final List<CommissionEser> commissionEserList) {
        this.commissionEserList = commissionEserList;
    }

    public Integer getDeliveredItemCount() {
        return (int) this.getItems().stream().filter(x -> x.getObject().getDeliveredItem() != null).map(x -> x.getObject().getDeliveredItem()).distinct().count();
    }

    public Integer getObjectItemCount() {
        return this.getItems().stream().map(CommissionObject::getObject).mapToInt(HeritageObject::getItemCount).sum();
    }

    public BigDecimal getTotalObjectsCost() {
        return this.getItems().stream()
                .filter(x -> x.getDecidedCost() != null && x.getObject() != null)
                .map(x -> x.getDecidedCost().multiply(BigDecimal.valueOf(x.getObject().getItemCount())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public String numberToWordForTL(BigDecimal number) {
        if(number != null) {
            return NumberConverter.numberToWordForTL(number);
        }
        return "";
    }

}