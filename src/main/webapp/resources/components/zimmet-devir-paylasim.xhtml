<ui:composition xmlns="http://www.w3.org/1999/xhtml"
		xmlns:h="http://java.sun.com/jsf/html"
		xmlns:p="http://primefaces.org/ui"
		xmlns:f="http://java.sun.com/jsf/core"
		xmlns:ui="http://java.sun.com/jsf/facelets"
		xmlns:composite="http://java.sun.com/jsf/composite"
		xmlns:shiro="http://shiro.apache.org/tags">

		<composite:interface>
			<composite:attribute name="naming" required="true" />
		</composite:interface>

		<composite:implementation>
	
		<p:panelGrid
					id="zimmetDevri"
					columnClasses="col-2 input-label,col-10 input-field"
					styleClass="col-9 lg:col-12 border-none px-0"
					columns="2"
					layout="flex"
					contentStyleClass="fit-in-box mx-3">
					
					<p:column>
						<p:outputLabel
							for="zimmettenDusulme"
							value="#{labels['titles.zimmettenDusulecekYer']}"  /> 
					</p:column>
					<p:column >
						<p:selectOneRadio id="zimmettenDusulme"  
								value="#{eserZimmetGroupController.operationType}"
								label="#{labels['titles.zimmettenDusulecekYer']}"  process="@this" 
								layout="responsive" 
								columns="5" 
								converter="entityConverter">
								<p:ajax update="zimmetDevri"  listener="#{eserZimmetGroupController.resetTable}"/>
								<f:selectItem  itemLabel="#{labels['titles.personeldenDevir']}" itemValue="#{true}" />
								<f:selectItem  itemLabel="#{labels['titles.alanBazliDevir']}" itemValue="#{false}" />
								
							</p:selectOneRadio>
					</p:column>
					
					<p:column rendered="#{!eserZimmetGroupController.operationType}">
						<p:outputLabel
							value="#{labels['titles.depoAlan']}"/>  
					</p:column>
					
					<p:column rendered="#{!eserZimmetGroupController.operationType }">
						 <p:panelGrid
								id="locationPanel"
								columnClasses="col-8 input-label,col-12 input-field"
								styleClass="col-12 lg:col-12 border-none px-0"
								columns="2"
								layout="flex"
								contentStyleClass="fit-in-box mx-0">
								
										<p:outputLabel for="mudurluk" value="#{labels['titles.directorate']}" />
									
										<p:autoComplete id="mudurluk"
														value="#{eserDepoController.mudurluk}"
														completeMethod="#{mudurlukController.filterByNameAndPermission}"
														var="item"
														disabled="#{sessionBean.hasDirectorate}"
														label="Eser Lokasyonu"
														queryDelay="#{sessionBean.queryDelay}"
														itemLabel="#{item.title}"
														itemValue="#{item}"
														emptyMessage="#{labels['base.record.notfound']}"
														converter="entityConverter"
														forceSelection="true"
														scrollHeight="202"
														dropdown="true"
														required="true"
														styleClass="required-input-field fit-autocomplete-in-box"
														appendTo="@this" >
											<f:attribute name="permission" value="eser:ekle" />
											<p:column>
												<span class="dropdown-item-title">#{item.title}</span>
												<span class="dropdown-item-subtitle">#{item.aciklama}</span>
											</p:column>
											<p:ajax event="itemSelect"
													listener="#{eserDepoController.handleMudurlukChange}"
													update="birim bina alan konum"
													process="@this"
													resetValues="true" global="false"/>
										</p:autoComplete>
									
										<p:outputLabel for="birim" value="#{labels['titles.bagliBirim']}" />  
									
										<p:autoComplete id="birim"
														value="#{eserDepoController.birim}"
														completeMethod="#{eserDepoController.filterByNameAndMudurluk}"
														var="item"
														label="#{labels['titles.bagliBirim']}"
														itemLabel="#{item.title}"
														itemValue="#{item}"
														queryDelay="#{sessionBean.queryDelay}"
														converter="entityConverter"
														emptyMessage="#{labels['base.record.notfound']}"
														forceSelection="true"
														styleClass="fit-autocomplete-in-box"
														dropdown="true"
														required="true"
														scrollHeight="202"
														appendTo="@this">
											<p:column>
												<span class="dropdown-item-title">#{item.title}</span>
												<span class="dropdown-item-subtitle">#{item.aciklama}</span>
											</p:column>
											<p:ajax event="itemSelect"
													listener="#{eserDepoController.handleBagliBirimChange}"
													update="bina alan konum"
													process="@this"
													resetValues="true" global="false"/>
										</p:autoComplete>
									
										<p:outputLabel for="bina" value="#{labels['titles.bina']}" />  
									
										<p:autoComplete id="bina"
														value="#{eserDepoController.bina}"
														completeMethod="#{eserDepoController.filterByNameAndBagliBirim}"
														var="item"
														converter="entityConverter"
														itemLabel="#{item.title}"
														itemValue="#{item}"
														label="#{labels['titles.bina']}"
														queryDelay="#{sessionBean.queryDelay}"
														emptyMessage="#{labels['base.record.notfound']}"
														forceSelection="true"
														dropdown="true" 
														styleClass="fit-autocomplete-in-box"
														scrollHeight="202"
														required="true"
														appendTo="@this">
											<p:column>
												<span class="dropdown-item-title">#{item.title}</span>
												<span class="dropdown-item-subtitle">#{item.aciklama}</span>
											</p:column>
											<p:ajax event="itemSelect"
													listener="#{eserDepoController.handleBinaChange}"
													update="alan konum"
													process="@this"  resetValues="true" global="false"/>
										</p:autoComplete>
								
										<p:outputLabel for="alan" value="#{labels['titles.alan']}" />  
								
										<p:autoComplete id="alan"
														value="#{eserDepoController.alan}"
														completeMethod="#{eserDepoController.filterByNameAndBina}"
														var="item"
														converter="entityConverter"
														itemLabel="#{item.title}"
														itemValue="#{item}"
														label="#{labels['titles.alan']}"
														forceSelection="true"
														emptyMessage="#{labels['base.record.notfound']}"
														queryDelay="#{sessionBean.queryDelay}"
														scrollHeight="202"
														appendTo="@this"
														styleClass="fit-autocomplete-in-box"
														dropdown="true"
														rendered="true"
														required="true"
														>
											<p:column>
												<span class="dropdown-item-title">#{item.title}</span>
												<span class="dropdown-item-subtitle">#{item.aciklama}</span>
											</p:column>
											<p:ajax event="itemSelect"
													listener="#{eserDepoController.handleAlanChange}"
													update="konum"
													process="@this"
													resetValues="true" global="false" />
										</p:autoComplete>
									
										<p:outputLabel for="konum" value="#{labels['titles.alanKonumu']}" />   
									
										<p:autoComplete id="konum"
														value="#{eserDepoController.model.alanKonumu}"
														completeMethod="#{eserDepoController.filterByNameAndAlan}"
														var="item"
														converter="entityConverter"
														forceSelection="true"
														itemLabel="#{item.title}"
														emptyMessage="#{labels['base.record.notfound']}"
														itemValue="#{item}"
														label="#{labels['titles.alanKonumu']}"
														queryDelay="#{sessionBean.queryDelay}"
														scrollHeight="202"
														dropdown="true"
														styleClass="fit-autocomplete-in-box"
														appendTo="@this">
											<p:column>
												<span class="dropdown-item-title">#{item.title}</span>
												<span class="dropdown-item-subtitle">#{item.aciklama}</span>
											</p:column>
										</p:autoComplete>
									
								<p:outputPanel styleClass="col-12 lg:col-12 unprintable">
										<p:commandButton id="btnReport"
														 value="Sorgula"
														 actionListener="#{eserZimmetGroupController.fetchEserZimmet}"
														 icon="ui-icon-check"
														 update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:zimmetliEserDatatable"
														 style="width: auto; margin: .5em;"
														 process="locationPanel">
										</p:commandButton>
									</p:outputPanel>
								
							</p:panelGrid>
					</p:column>
					
					
					<p:column rendered="#{eserZimmetGroupController.operationType}">
						<p:outputLabel
							for="zimmetSahibi"
							value="#{labels['titles.zimmetSahibi']}" />  
					</p:column>
					<p:column rendered="#{eserZimmetGroupController.operationType and !eserZimmetGroupController.permitted}">
						<h:outputText
							id="eskiZimmetSahibi"
							value="#{sessionBean.currentUser.personel.title}" />
					</p:column>
	
					<p:column rendered="#{eserZimmetGroupController.operationType and eserZimmetGroupController.permitted}">
						<p:autoComplete
							id="zimmetSahibi"
							value="#{eserZimmetGroupController.personel}"
							var="item"
							completeMethod="#{eserZimmetGroupController.filterPersonelWithoutSelectedOne}"
							itemLabel="#{item.title}"
							itemValue="#{item}"
							label="#{labels['titles.zimmetSahibi']}"
							queryDelay="#{sessionBean.queryDelay}"
							styleClass="required-input-field fit-autocomplete-in-box"
							converter="entityConverter"
							forceSelection="true"
							required="true"
							emptyMessage="#{labels['base.record.notfound']}"
							rendered="true"
							scrollHeight="202"
							dropdown="true">
							<f:attribute name="permission" value="eser:cokluzimmet" />
							<p:column>
								<span class="dropdown-item-title">#{item.title}</span>
								<span class="dropdown-item-subtitle">#{item.aciklama}</span>
							</p:column>
	
							<p:ajax
								event="itemSelect"
								listener="#{eserZimmetGroupController.fetchEserZimmet() }"
								oncomplete="fixMenuHeight();"
								update="zimmetliEserDatatable yeniZimmetSahibi"
								process="@this" />
						</p:autoComplete>
					</p:column>
					
					 
					<p:column rendered="#{eserZimmetGroupController.operationType}">
						<p:outputLabel
							for="allSelectedforEserDevir"
							value="#{labels['titles.zimmetlininEserleriniSec']}" />  
					</p:column>
					<p:column styleClass="input-field" rendered="#{eserZimmetGroupController.operationType}">
							<p:selectBooleanCheckbox
								id="allSelectedforEserDevir"
								value="#{eserZimmetGroupController.allSelected}">
								<p:ajax event="change" update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:zimmetDevri :eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton" />
							</p:selectBooleanCheckbox>
							<p:tooltip
								for="allSelectedforEserDevir"    
								value="#{labels['titles.allSelectedforEserDevir']}" />
					</p:column>
					
					<shiro:hasPermission name="eserZimmet:baskaMuzeyeDevret">
								<p:column>
									<p:outputLabel
										for="transferArtifactOtherMuzeum"
										value="Farklı Müzedeki Personele Devret" />  
								</p:column>
								<p:column styleClass="input-field">
										<p:selectBooleanCheckbox
											id="transferArtifactOtherMuzeum"
											value="#{eserZimmetGroupController.transferArtifactOtherMuzeum}">
											<p:ajax  />
										</p:selectBooleanCheckbox>
										<p:tooltip
											for="transferArtifactOtherMuzeum" 
											value="#{labels['titles.zimmetlininTaslakEserleriniSec']}" />
								</p:column>
						</shiro:hasPermission>
					
					<p:column>
						<p:outputLabel
							for="yeniZimmetSahibi"
							value="#{labels['titles.yeniZimmetSahibi']}" />  
					</p:column>
					<p:column>
						<p:autoComplete
							id="yeniZimmetSahibi"
							value="#{eserZimmetGroupController.selectedPersonel}"
							var="item"
							completeMethod="#{eserZimmetGroupController.filterPersonelWithoutSelectedList}"
							itemLabel="#{item.title}"
							itemValue="#{item}"
							label="#{labels['titles.yeniZimmetSahibi']}"
							queryDelay="#{sessionBean.queryDelay}"
							styleClass="required-input-field fit-autocomplete-in-box"
							converter="entityConverter"
							forceSelection="true"
							multiple="true"
							required="true"
							emptyMessage="#{labels['base.record.notfound']}"
							rendered="true"
							scrollHeight="202"
							dropdown="true">
							<f:attribute name="permission" value="eser:cokluzimmet"/>
							<p:column>
								<span class="dropdown-item-title">#{item.title}</span>
								<span class="dropdown-item-subtitle">#{item.aciklama}</span>
							</p:column>
							<p:ajax event="itemSelect" process="@this" update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton"/>
							<p:ajax event="itemUnselect" process="@this" immediate="true" listener="#{eserZimmetGroupController.personelUnselectListener}"/>
						</p:autoComplete>
					</p:column>
					
				<p:column rendered="#{!eserZimmetGroupController.allSelected}">
					<p:outputLabel value="#{labels['titles.zimmetliEserleri']}" />  
				</p:column>
	
				<p:column  rendered="#{!eserZimmetGroupController.allSelected}">
					<p:dataTable id="zimmetliEserDatatable"
						widgetVar="zimmetliEserTableWidget" 
						value="#{eserZimmetGroupController.lazyEserZimmetDataModel}"
						var="item" 
						rowIndexVar="row" 
						selection="#{eserZimmetGroupController.selectedEserZimmet}"					   
						paginator="true"
						rowStyleClass="table-row-hilite"
						paginatorPosition="bottom"
						paginatorTemplate="#{sessionBean.paginatorTemplate}"
						currentPageReportTemplate="#{sessionBean.currentPageReportTemplate}"
						rowsPerPageTemplate="#{sessionBean.rowsPerPageTemplate}"
						rowKey="#{item.id}"
						rows="#{sessionBean.rowsPerPageSize}" 
						sortMode="multiple"
						emptyMessage="#{labels['search.found.not']}"
						paginatorAlwaysVisible="true" 
						lazy="true" 
						scrollable="true"
						tableStyleClass="largeCheckBox">
						
						<p:ajax event="page" oncomplete="fixMenuHeight();" />
						
						<p:ajax event="rowSelectCheckbox" 
								update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton"
							    global="false"  />
						<p:ajax event="rowUnselectCheckbox" 
								update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton"
								global="false" />
						<p:ajax event="rowSelect" 
								update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton"
								global="false" />
						<p:ajax event="rowUnselect" 
								update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton"
								global="false" />
						<p:ajax event="toggleSelect" 
								update=":eserDevirForm:eserDevirView:#{cc.attrs.naming}:approveButton"
								global="false" />
										
						<p:ajax
							event="filter"
							listener="#{eserZimmetGroupController.onFilterChange}"
							global="false" />
						
						<p:column exportable="false" 
						  styleClass="to-center"
					      selectionMode="multiple" 
					      style="padding: 0px;" 
					      width="44" />
					    
					   <p:column headerText="#{labels['titles.no']}"
								  styleClass="to-right"
								  style="padding-left: 1px;"
								  width="35"
								  >
							<h:outputText value="#{row + 1}" />
						</p:column>
					      
					    <p:column headerText="#{labels['titles.directorate']}"
							filterMatchMode="contains"
							filterBy="#{item.eser.eserDepos.alanKonumu.alan.bina.bagliBirim.mudurluk.ad}" 
							sortBy="#{item.eser.eserDepos.alanKonumu.alan.bina.bagliBirim.mudurluk.ad}">
							<h:outputText value="#{item.eser.eserDepo.alanKonumu.alan.bina.bagliBirim.mudurluk.ad}" />
						</p:column>  
					      
						<p:column headerText="#{labels['artifact.id.asset']}"
							filterBy="#{item.eser.permanentId}"
							filterMatchMode="contains"
							sortBy="#{item.eser.permanentId}" width="115">
							<h:outputText value="#{item.eser.assetId}" />
						</p:column>

						<p:column headerText="#{labels['artifact.id.temp']}"
								  filterBy="#{item.eser.id}"
								  filterMatchMode="contains"
								  sortBy="#{item.eser.permanentId}">
							<h:outputText value="#{item.eser.tempId}" />
						</p:column>
						
						<p:column headerText="#{labels['titles.eserTur']}"
										filterBy="#{item.eser.eserAltTur.eserTur.ad}"
										filterMatchMode="contains"
										sortBy="#{item.eser.eserAltTur.eserTur.ad}">
										<h:outputText value="#{item.eser.eserAltTur.eserTur.title}" />
						 </p:column>
						 
									
						 <p:column headerText="#{labels['titles.eserOzelAdi']}" 
							filterBy="#{item.eser.eserOzelAdi}" 
							filterMatchMode="contains"
							sortBy="#{item.eser.eserOzelAdi}">
							<h:outputText value="#{item.eser.eserOzelAdi}" />
						</p:column>
						
						<p:column headerText="#{labels['titles.alan']}"  
							filterBy="#{item.eser.eserDepos.alanKonumu.alan.ad}" 
							filterMatchMode="contains"
							sortBy="#{item.eser.eserDepos.alanKonumu.alan.ad}">
							<h:outputText value="#{item.eser.eserDepo.alanKonumu.alan.ad}" />
						</p:column>
						
						
						<p:column headerText="#{labels['titles.alanKonumu']}"   
								filterBy="#{item.eser.eserDepos.alanKonumu.ad}" 
								filterMatchMode="contains"
								sortBy="#{item.eser.eserDepos.alanKonumu.ad}">
								<h:outputText value="#{item.eser.eserDepo.alanKonumu.ad}" />
							
					   </p:column>
					  
					  
					   <p:column headerText="#{labels['titles.zimmetTarihi']}"
							filterMatchMode="contains"
							filterBy="#{item.zimmetTarihi}"
							styleClass="filter-column" 
							style="display:none"
							exportable="true"
							>
							<h:outputText value="#{item.zimmetTarihi}" > 
								<f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
							</h:outputText>
						</p:column>
						
						
	
						<p:column headerText="#{labels['titles.zimmetTarihi']}"
								  filterBy="#{item.zimmetTarihi}"
								  styleClass="filter-column"
								  sortBy="#{item.zimmetTarihi}"
								  exportable="false"
								  width="190">
							<f:facet name="filter">
							    <h:inputHidden id="hiddenFilter"/>
						    </f:facet>
							<f:facet name="header">
							    <h:outputText value="#{labels['titles.date.range']}" style="display: block;" />
			                 	<p:datePicker id="from"
			                 				widgetVar="calendarWidgetFrom"
			                 				pattern="#{parameters.get('patern.date')}"
			                 				readonlyInput="true"
			                 				size="10"
			                 				mask="true" 
			                 				
			                 				onchange="fixDate()">
									<p:ajax event="dateSelect"
							      			onstart="$(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('hiddenFilter', view).clientId}'))[0].value = $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('from', view).clientId}_input'))[0].value + '~' + $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('to', view).clientId}_input'))[0].value"
							      			oncomplete="PF('zimmetliEserTableWidget').filter()" />
							    </p:datePicker>
							    <p:datePicker id="to"
							    			widgetVar="calendarWidgetTo"
							    			pattern="#{parameters.get('patern.date')}"
							    			readonlyInput="true"
							    			size="10"
							    			mask="true"
							    			onchange="fixDate()">
							    	<p:ajax event="dateSelect"
							    			onstart="$(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('hiddenFilter', view).clientId}'))[0].value = $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('from', view).clientId}_input'))[0].value + '~' + $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('to', view).clientId}_input'))[0].value"
							    			oncomplete="PF('zimmetliEserTableWidget').filter()" />
							    </p:datePicker>
							</f:facet>
							<h:outputText value="#{item.zimmetTarihi}">
							    <f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
						    </h:outputText>
						</p:column>
						
					
					   
					   <p:column headerText="#{labels['titles.personel']}"    
					   		sortBy="#{item.zimmetPersonel.ad}"
							filterMatchMode="contains" 
							filterBy="#{item.zimmetPersonel.title}">
							 <h:outputText value="#{item.zimmetPersonel.title}" />
						</p:column>
						
					   <p:column headerText="#{labels['titles.zimmetliPersonels']}"    
						   		 sortBy="#{item.eser.zimmetPersonelTitles}"
								 filterMatchMode="contains" 
								 filterBy="#{item.eser.zimmetPersonelTitles}"
								 width="200">

								 
							 <h:outputText value="#{item.eser.zimmetPersonelTitles}" />
						</p:column>
						
						
						<f:facet name="footer">
							<h:outputText  value="#{eserZimmetGroupController.lazyEserZimmetDataModel !=null and  eserZimmetGroupController.lazyEserZimmetDataModel.rowCount gt 0 ? 'Toplam Kayit Sayısı  '.concat(eserZimmetGroupController.lazyEserZimmetDataModel.rowCount) : ''}" />
						</f:facet>
						
					</p:dataTable>
					
				</p:column>
	
					<p:column>
						<p:outputLabel
							for="zimmetTarihi"  
							value="#{labels['titles.zimmetTarihi']}" />
					</p:column>
					<p:column>
						<p:calendar
							id="zimmetTarihi"
							value="#{eserZimmetGroupController.liabilityDate}"
							pattern="#{parameters.get('patern.date')}"
							maxdate="#{sessionBean.currentDate}"
							showOn="button"
							label="#{labels['base.eser.zimmetTarihi']}"
							styleClass="required-input-field"
							required="true"
							showButtonPanel="true"
							mask="true"
							yearRange="c-130:c+10"
							navigator="true">
							<f:convertDateTime
								type="date"
								pattern="#{parameters.get('patern.date')}" />
							<p:ajax event="dateSelect" process="@this" />
						</p:calendar>
						
					</p:column>
	
					<p:column>
						<p:outputLabel
							for="aciklama"
							value="#{labels['base.aciklama']}" />
					</p:column>
					<p:column>
						<p:inputTextarea
							id="aciklama"
							value="#{eserZimmetGroupController.description}"
							maxlength="150"
							styleClass="fit-in-box"
							rows="2"
							counter="remainingCharsAciklama"
							counterTemplate="{0} #{labels['base.char.remaining']}">
							<p:ajax event="change" process="@this" />
						</p:inputTextarea>
						<h:outputText
							id="remainingCharsAciklama"
							style="font-size: small; opacity: .6;" />
					</p:column>
					
					
					<p:outputPanel  styleClass="col-6 lg:col-6 border-none px-0"
					  layout="flex" contentStyleClass="fit-in-box mx-0" />
					  
					  
					<p:outputPanel  styleClass="col-6 lg:col-6 border-none px-0"
					  layout="flex" contentStyleClass="fit-in-box mx-0">
					 
					
					<p:commandButton
						id="approveButton"
						widgetVar="apprvBtn"
						value="#{labels['base.submit']}"
						action="#{eserZimmetGroupController.onChangeLiability(cc.attrs.isRemovedFromPerson)}"
						disabled="#{empty eserZimmetGroupController.selectedPersonel 
									or (empty eserZimmetGroupController.selectedEserZimmet and !eserZimmetGroupController.allSelected)}"
						onclick="PF('apprvBtn').disable()"
						oncomplete="PF('apprvBtn').enable()"
						icon="ui-icon-check"
						process=":eserDevirForm:eserDevirView:#{cc.attrs.tab}"
						update=":eserDevirForm :growlMessages"
						style="width: auto; "
						styleClass="to-right no-border">
						
						<p:confirm header="#{labels['titles.zimmetDevriBilgilendirme']}"  
							icon="ui-icon-info"
							message="#{eserZimmetGroupController.allSelected ? (cc.attrs.isRemovedFromPerson ? 'Zimmet sahibinin tüm eserleri devredilmek üzere onaya gönderilecek. Emin misiniz?': 'Zimmet sahibinin tüm eserleri zimmeti paylaşılmak üzere onaya gönderilecek. Emin misiniz?')
																			: (cc.attrs.isRemovedFromPerson ? 'Zimmet sahibinin seçili eserleri devredilmek üzere onaya gönderilecek. Emin misiniz?': 'Zimmet sahibinin seçili eserleri zimmeti paylaşılmak üzere onaya gönderilecek. Emin misiniz?')}"
							/>	
					</p:commandButton>
						<p:commandButton value="#{labels['search.reset']}"
										 icon="ui-icon-trash"
										 process="@this"
										 partialSubmit="true"
										 immediate="true"
										 actionListener="#{eserZimmetGroupController.resetTable}"
										 update=":eserDevirForm"
										 style="margin-left: .5em;">
											<p:resetInput target=":eserDevirForm" /> 
										</p:commandButton>
					
				</p:outputPanel>
				
				
			</p:panelGrid>
		
		</composite:implementation>
</ui:composition>