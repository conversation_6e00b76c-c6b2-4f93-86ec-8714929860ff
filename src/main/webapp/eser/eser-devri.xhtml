<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<ui:composition
	xmlns="http://www.w3.org/1999/xhtml"
	xmlns:h="http://java.sun.com/jsf/html"
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:p="http://primefaces.org/ui"
	template="/WEB-INF/template.xhtml"
	xmlns:shiro="http://shiro.apache.org/tags"
	xmlns:components="http://java.sun.com/jsf/composite/components">


	<ui:define name="content">

		<shiro:hasPermission name="eser:cokluzimmet">
			<h:form id="eserDevirForm">
				<p:tabView
					id="eserDevirView"
					activeIndex="#{eserZimmetGroupController.activeIndex}"
					styleClass="group-add-panel"
					style="border: none; margin-top: 4px;"
					cache="true"
					dynamic="true">

					<p:ajax
						event="tabChange"
						immediate="true"
						listener="#{eserZimmetGroupController.activeTabChanged}"
						global="false"
						update=":eserDevirForm:eserDevirView"
						resetValues="true" />

						<p:tab   title="#{labels['titles.cokluZimmetDevri']}"  id="zimmetDevriTab">
							<h:form id="infoFormcokluZimmetDevri">
								<p:outputPanel id="infoBoxcokluZimmetDevri" layout="block"
											   styleClass="ui-widget ui-widget-content ui-corner-all"
											   style="padding:30px; margin-bottom:10px; position:relative; background-color:#fefbd8; border-left:5px solid #ffcc00;">

									<h:outputLink value="#" onclick="$(this).closest('.ui-widget').hide(); return false;"
												  style="position:absolute; top:8px; right:10px; text-decoration:none; font-size:18px; color:#0b0a0a;">
										✖
									</h:outputLink>

									<f:verbatim>
										<div style="font-size:15px; color:#aa0404; padding:10px; border:1px solid #aa0404; background-color:#fff3f3; border-radius:4px; font-family:Arial, sans-serif;">
											⚠️ Bu alanda eser üzerindeki zimmetin bir personelden alınarak (düşülerek) bir ya da birden çok personele zimmet atanması işlemi gerçekleştirilir.
											İşlemin sistemde gerçekleşmesi için ilave olarak Müze Müdürü Onayı da gerekmektedir.
											<br/><br/>
											<strong style="font-size:16px; font-weight:bold; color:#b30000;">DİKKAT!!! Bu işlem sonucunda “Mevcut Zimmet Sahibi” üzerinden seçilen eserlere ait zimmet düşürülecektir.</strong>
										</div>
									</f:verbatim>





								</p:outputPanel>
							</h:form>
							 <components:zimmet-devir-paylasim  naming="zimmetDevir" id="zimmetDevir"
							            tab="zimmetDevriTab" isRemovedFromPerson="true" />
						</p:tab>
						
						<p:tab   title="#{labels['titles.cokluZimmetPaylasim']}"  id="zimmetPaylasimTab">
							<h:form id="infoFormcokluZimmetPaylasim">
								<p:outputPanel id="infoBoxcokluZimmetPaylasim" layout="block"
											   styleClass="ui-widget ui-widget-content ui-corner-all"
											   style="padding:30px; margin-bottom:10px; position:relative; background-color:#fefbd8; border-left:5px solid #ffcc00;">

									<h:outputLink value="#" onclick="$(this).closest('.ui-widget').hide(); return false;"
												  style="position:absolute; top:8px; right:10px; text-decoration:none; font-size:18px; color:#0b0a0a;">
										✖
									</h:outputLink>

									<f:verbatim>
										<div style="font-size:15px; color:#aa0404; padding:10px; border:1px solid #aa0404; background-color:#fff3f3; border-radius:4px; font-family:Arial, sans-serif;">
											⚠️ Bu alanda bir personele ait eser zimmetinin aynı zamanda başka personele de atanması (paylaştırılması) işlemi gerçekleştirilir. İşlemin sistemde gerçekleşmesi için ilave olarak Müze Müdürü Onayı da gerekmektedir.
										</div>
									</f:verbatim>
								</p:outputPanel>
							</h:form>
							 <components:zimmet-devir-paylasim  naming="zimmetPaylasim" id="zimmetPaylasim"
							  tab="zimmetPaylasimTab" isRemovedFromPerson="false"/>
						</p:tab>
						 
					
					<p:tab title="#{labels['titles.taslakEserDevri']}" id="eserDevirTab">
						<h:form id="infoFormtaslakEserDevri">
							<p:outputPanel id="infoBoxtaslakEserDevri" layout="block"
										   styleClass="ui-widget ui-widget-content ui-corner-all"
										   style="padding:30px; margin-bottom:10px; position:relative; background-color:#fefbd8; border-left:5px solid #ffcc00;">

								<h:outputLink value="#" onclick="$(this).closest('.ui-widget').hide(); return false;"
											  style="position:absolute; top:8px; right:10px; text-decoration:none; font-size:18px; color:#0b0a0a;">
									✖
								</h:outputLink>

								<f:verbatim>
									<div style="font-size:15px; color:#aa0404; padding:10px; border:1px solid #aa0404; background-color:#fff3f3; border-radius:4px; font-family:Arial, sans-serif;">
										⚠️ Bu alanda bir kullanıcının üstündeki Taslak Eserlerin başka bir kullanıcıya (ihtisas elemanına) devri gerçekleştirilir.” Bu alanda gerçekleştirilen işlem sisteme otomatik olarak yansır <strong style="font-size:16px; font-weight:bold; color:#b30000;"> ayrıca bir Müze Müdürü Onayı gerekmemektedir.</strong>
									</div>
								</f:verbatim>
							</p:outputPanel>
						</h:form>
						<p:panelGrid
							id="eserDevir"
							columnClasses="col-2 input-label,col-10 input-field"
							styleClass="col-9 lg:col-12 border-none px-0"
							columns="2"
							layout="flex"
							contentStyleClass="fit-in-box mx-3">

							<p:column>
								<p:outputLabel
									for="user"
									value="#{labels['titles.devirEden']}" />  
							</p:column>
							<p:column rendered="#{!eserZimmetGroupController.permitted}">
								<h:outputText
									id="user"
									value="#{sessionBean.currentUser.personel.title}" />
							</p:column>
							
							<p:column rendered="#{eserZimmetGroupController.permitted}">
								<p:autoComplete
									id="zimmetSahibi1"
									value="#{eserZimmetGroupController.user}"
									var="item"
									completeMethod="#{eserZimmetGroupController.autoCompleteKullaniciByNameAndMudurluk}"
									itemLabel="#{item.title}"
									itemValue="#{item}"
									label="#{labels['titles.zimmetSahibi']}"  
									queryDelay="#{sessionBean.queryDelay}"
									styleClass="required-input-field fit-autocomplete-in-box"
									converter="entityConverter"
									forceSelection="true"
									emptyMessage="#{labels['base.record.notfound']}"
									required="true"
									rendered="true"
									scrollHeight="202"
									dropdown="true">
									<f:attribute name="permission" value="eser:cokluzimmet" />
									<p:column>
										<span class="dropdown-item-title">#{item.title}</span>
										<span class="dropdown-item-subtitle">#{item.personelView.aciklama}</span>
									</p:column>

									<p:ajax
										event="itemSelect"
										listener="#{eserZimmetGroupController.fetchTaslakEser()}"
										oncomplete="fixMenuHeight();"
										update=":eserDevirForm:eserDevirView:taslakEserDatatable :eserDevirForm:eserDevirView:assignButton"
										process="@this" />
								</p:autoComplete>
							</p:column>
								<shiro:hasPermission name="eserZimmet:baskaMuzeyeDevret">
										<p:column>
											<p:outputLabel
												for="transferArtifactOtherMuzeum2"
												value="Farklı Müzedeki Personele Devret" />  
										</p:column>
										<p:column styleClass="input-field">
												<p:selectBooleanCheckbox
													id="transferArtifactOtherMuzeum2"
													value="#{eserZimmetGroupController.transferArtifactOtherMuzeum}">
													<p:ajax  />
												</p:selectBooleanCheckbox>
												<p:tooltip
													for="allSelectedforTaslakEserDevir" 
													value="#{labels['titles.zimmetlininTaslakEserleriniSec']}" />
										</p:column>
								</shiro:hasPermission>
							
							<p:column>
								<p:outputLabel
									for="devirAlan"
									value="#{labels['titles.devirAlan']}" />  
							</p:column>
							<p:column>
								<p:autoComplete
									id="devirAlan"
									value="#{eserZimmetGroupController.assignee}"
									var="item"
									completeMethod="#{eserZimmetGroupController.autoCompleteKullaniciByNameAndMudurlukExcludeSelectedKullanici}"
									itemLabel="#{item.title}"
									itemValue="#{item}"
									label="#{labels['titles.devirAlan']}"
									queryDelay="#{sessionBean.queryDelay}"
									styleClass="required-input-field fit-autocomplete-in-box"
									converter="entityConverter"
									forceSelection="true"
									required="true"
									emptyMessage="#{labels['base.record.notfound']}"
									rendered="true"
									scrollHeight="202"
									dropdown="true">
									<f:attribute name="permission" value="eser:cokluzimmet" />
									<p:column>
										<span class="dropdown-item-title">#{item.title}</span>
										<span class="dropdown-item-subtitle">#{item.personelView.aciklama}</span>
									</p:column>
									<p:ajax event="itemSelect" process="@this" update=":eserDevirForm:eserDevirView:assignButton"/>
								</p:autoComplete>
							</p:column>
														
							<p:column>
								<p:outputLabel
									for="allSelectedforTaslakEserDevir"
									value="#{labels['titles.zimmetlininTaslakEserleriniSec']}" />  
							</p:column>
							<p:column styleClass="input-field">
									<p:selectBooleanCheckbox
										id="allSelectedforTaslakEserDevir"
										value="#{eserZimmetGroupController.allSelected}">
										<p:ajax event="change" update=":eserDevirForm:eserDevirView:eserDevir :eserDevirForm:eserDevirView:assignButton" />
									</p:selectBooleanCheckbox>
									<p:tooltip
										for="allSelectedforTaslakEserDevir" 
										value="#{labels['titles.zimmetlininTaslakEserleriniSec']}" />
							</p:column>
							
							
							<p:column rendered="#{!eserZimmetGroupController.allSelected}">
								<p:outputLabel	value="#{labels['titles.taslakEserler']}" /> 
							</p:column>
							<p:column rendered="#{!eserZimmetGroupController.allSelected}">
								<p:dataTable id="taslakEserDatatable"
									widgetVar="taslakEserDusmeTableWidget" 
									value="#{workflowController.items}"
									filteredValue="#{workflowController.filteredValues}"
									var="item" 
									rowIndexVar="row" 
									paginator="true"
									selection="#{eserZimmetGroupController.selectedWorkFlow}"
									paginatorPosition="bottom"
									paginatorTemplate="#{sessionBean.paginatorTemplate}"
									currentPageReportTemplate="#{sessionBean.currentPageReportTemplate}"
									rowsPerPageTemplate="#{sessionBean.rowsPerPageTemplate}"
									rowKey="#{item.id}"
									rows="#{sessionBean.rowsPerPageSize}" 
									sortMode="multiple"
									emptyMessage="#{labels['search.found.not']}"
									paginatorAlwaysVisible="true" 
									scrollable="true"
									scrollHeight="100%"
									tableStyleClass="largeCheckBox">
									
									<p:ajax event="page" oncomplete="fixMenuHeight();" />
									<p:ajax event="rowSelectCheckbox" update=":eserDevirForm:eserDevirView:assignButton" global="false" process="@this" />
									<p:ajax event="rowUnselectCheckbox" update=":eserDevirForm:eserDevirView:assignButton" global="false" process="@this" />
									<p:ajax event="rowSelect" update=":eserDevirForm:eserDevirView:assignButton" process="@this" />
									<p:ajax event="rowUnselect" update=":eserDevirForm:eserDevirView:assignButton" global="false" process="@this" />
									<p:ajax event="toggleSelect" update=":eserDevirForm:eserDevirView:assignButton" global="false" process="@this" />
									<p:ajax event="filter" listener="#{workflowController.onFilterChange}" global="false" />
									
									
									<p:column exportable="false" 
									  styleClass="to-center"
								      selectionMode="multiple" 
								      style="padding: 0px;" 
								      width="44" />
								      
									<p:column headerText="#{labels['titles.no']}"
										  styleClass="to-right"
										  style="padding-left: 1px;"
										  width="35"
										  >
										<h:outputText value="#{row + 1}" />
									</p:column>
									
									<p:column headerText="#{labels['titles.directorate']}"
										filterMatchMode="contains"
										filterBy="#{item.mudurluk != null ? item.mudurluk.ad : ''}"
										sortBy="#{item.mudurluk != null ? item.mudurluk.ad : ''}">
										<h:outputText value="#{item.mudurluk.ad}" />
									</p:column>

									<p:column headerText="#{labels['artifact.id.asset']}"
											  filterBy="#{item.artifact.permanentId}"
											  filterMatchMode="contains"
											  sortBy="#{item.artifact.permanentId}">
										<h:outputText value="#{item.artifact.assetId}" />
									</p:column>
									
									<p:column headerText="#{labels['artifact.id.temp']}"
										filterBy="#{item.artifact.eserId}" 
										filterMatchMode="contains"
										sortBy="#{item.artifact.eserId}"
										sortFunction="#{muesUtil.compare}">
										<h:outputText value="#{item.artifact.tempId}" />
									</p:column>
									<p:column headerText="#{labels['titles.eserTur']}"
										filterBy="#{item.artifact.eserAltTur.eserTur.ad}"
										filterMatchMode="contains"
										sortBy="#{item.artifact.eserAltTur.eserTur.ad}">
										<h:outputText value="#{item.artifact.eserAltTur.eserTur.ad}" />
									</p:column>
									
									<p:column headerText="#{labels['titles.eserOzelAdi']}" 
										filterBy="#{item.artifact.eserOzelAdi}" 
										filterMatchMode="contains"
										sortBy="#{item.artifact.eserOzelAdi}">
										<h:outputText value="#{item.artifact.eserOzelAdi}" />
									</p:column>
									
									<p:column headerText="#{labels['titles.alan']}"  
										filterBy="#{item.artifact.eserDepo.alanKonumu.alan.ad}"
										filterMatchMode="contains"
										sortBy="#{item.artifact.eserDepo.alanKonumu.alan.ad}">
										<h:outputText value="#{item.artifact.eserDepo.alanKonumu.alan.ad}" />
									</p:column>
								
								
									<p:column headerText="#{labels['titles.alanKonumu']}"   
											filterBy="#{item.artifact.eserDepo.alanKonumu.ad}"
											filterMatchMode="contains"
											sortBy="#{item.artifact.eserDepo.alanKonumu.ad}">
											<h:outputText value="#{item.artifact.eserDepo.alanKonumu.ad}" />
										
								   </p:column>
   
								   
									<f:facet name="footer">
									<h:outputText value="#{workflowController.items !=null and  workflowController.items.size() gt 0 ? 'Toplam Kayit Sayısı  '.concat(workflowController.items.size()) : ''}" />
								</f:facet>
									
								</p:dataTable>
							</p:column>
							
							<p:outputPanel  styleClass="col-6 lg:col-6 border-none px-0"
							  layout="flex" contentStyleClass="fit-in-box mx-0"></p:outputPanel>
							<p:outputPanel  styleClass="col-6 lg:col-6 border-none px-0"
							  layout="flex" contentStyleClass="fit-in-box mx-0">
					 
							
							<p:commandButton
								id="assignButton"
								widgetVar="assignBtn"
								value="#{labels['titles.taslakEserDevret']}" 
								action="#{eserZimmetGroupController.changeDraftLiability}"
								disabled="#{eserZimmetGroupController.user == null or eserZimmetGroupController.assignee == null
									or (empty eserZimmetGroupController.selectedWorkFlow and !eserZimmetGroupController.allSelected)}"
								onclick="PF('assignBtn').disable()"
								process=":eserDevirForm:eserDevirView:eserDevirTab"
								update=":eserDevirForm :growlMessages"
								icon="ui-icon-disk"
								style="width: auto;"
								styleClass="to-center no-border">
								
								<p:confirm header="#{labels['titles.taslakEserDevirBilgilendirme']}"  
									message="#{eserZimmetGroupController.allSelected ? 'Bütün taslak eserleri devredeceksiniz. Emin misiniz ?':
																						 'Seçmiş olduğunuz taslak eserleri devredeceksiniz. Emin misiniz?'}"
									icon="ui-icon-info" />	
							</p:commandButton>
							
							<p:commandButton value="#{labels['search.reset']}"
												 icon="ui-icon-trash"
												 actionListener="#{eserZimmetGroupController.resetTable}"
												 process="@this"
												 partialSubmit="true"
												 immediate="true"
												 update=":eserDevirForm"
												 style="margin-left: .5em;">
													<p:resetInput target=":eserDevirForm" /> 
												</p:commandButton>
						</p:outputPanel>

						</p:panelGrid>
					</p:tab>
					
					
					<p:tab title="#{labels['titles.zimmetDusme']}"   id="eserDusmeTab">
						<h:form id="infoFormzimmetDusme">
							<p:outputPanel id="infoBoxzimmetDusme" layout="block"
										   styleClass="ui-widget ui-widget-content ui-corner-all"
										   style="padding:30px; margin-bottom:10px; position:relative; background-color:#fefbd8; border-left:5px solid #ffcc00;">

								<h:outputLink value="#" onclick="$(this).closest('.ui-widget').hide(); return false;"
											  style="position:absolute; top:8px; right:10px; text-decoration:none; font-size:18px; color:#0b0a0a;">
									✖
								</h:outputLink>

								<f:verbatim>
									<div style="font-size:15px; color:#aa0404; padding:10px; border:1px solid #aa0404; background-color:#fff3f3; border-radius:4px; font-family:Arial, sans-serif;">
										⚠️ Bu alan bir eserin birden çok zimmetlisinin bulunduğu durumlarda ve bu alanda düşüm işlemi yapıldıktan sonra eser üzerinde en az bir zimmet sorumlusu (ihtisas elemanı) kalacağı durumlarda kullanılır. Yani eser zimmeti sadece bir kişi üzerindeyse bu alanda zimmet düşümü yapılamaz ayrıca yeni zimmet sorumluluğu vermek için bu alan kullanılamaz. Bu tür durumlarda bu ekran kullanılmadan önce “Zimmet Paylaşım Ekranı”ndan zimmet paylaşım yapılması gerekir. İşlemin sistemde gerçekleşmesi için Müze Müdürü Onayı gerekmektedir.
										<br/><br/>
										<strong style="font-size:16px; font-weight:bold; color:#b30000;">DİKKAT!!! Bu işlem sonucunda “Mevcut Zimmet Sahibi” üzerinden seçilen eserlere ait zimmet düşürülecektir.</strong>
									</div>
								</f:verbatim>
							</p:outputPanel>
						</h:form>
						<p:panelGrid
							id="eserDusme"
							columnClasses="col-2 input-label,col-10 input-field"
							styleClass="col-9 lg:col-12 border-none px-0"
							columns="2"
							layout="flex"
							contentStyleClass="fit-in-box mx-3">

							<p:column>
								<p:outputLabel
									for="zimmetSahibiDusme"
									value="#{labels['titles.zimmetSahibi']}"  />  
							</p:column>
							<p:column rendered="#{!eserZimmetGroupController.permitted}">
								<h:outputText
									id="zimmetSahibiDusme"
									value="#{sessionBean.currentUser.personel.title}" />
							</p:column>

							<p:column rendered="#{eserZimmetGroupController.permitted}">
								<p:autoComplete
									id="zimmetSahibiDus"
									value="#{eserZimmetGroupController.personel}"
									var="item"
									completeMethod="#{eserZimmetGroupController.filterPersonelWithoutSelectedOne}"
									itemLabel="#{item.title}"
									itemValue="#{item}"
									label="#{labels['titles.zimmetSahibi']}"
									queryDelay="#{sessionBean.queryDelay}"
									styleClass="required-input-field fit-autocomplete-in-box"
									converter="entityConverter"
									forceSelection="true"
									required="true"
									emptyMessage="#{labels['base.record.notfound']}"
									rendered="true"
									scrollHeight="202"
									dropdown="true">
									<f:attribute name="permission" value="eser:cokluzimmet" />
									<p:column>
										<span class="dropdown-item-title">#{item.title}</span>
										<span class="dropdown-item-subtitle">#{item.aciklama}</span>
									</p:column>

									<p:ajax
										event="itemSelect"
										oncomplete="fixMenuHeight();"
										listener="#{eserZimmetGroupController.fetchEserZimmet}"
										update=":eserDevirForm:eserDevirView:zimmetliEserDusmeDatatable :eserDevirForm:eserDevirView:dropButton"
										process="@this" />
								</p:autoComplete>
						</p:column>
						
						<p:column>
								<p:outputLabel
									for="allSelected"  
									value="#{labels['titles.zimmetlininEserleriniSec']}" />
						</p:column>
						<p:column styleClass="input-field">
								<p:selectBooleanCheckbox
									id="allSelected"
									value="#{eserZimmetGroupController.allSelected}">
									<p:ajax event="change" update=":eserDevirForm:eserDevirView:eserDusme  :eserDevirForm:eserDevirView:dropButton" />
								</p:selectBooleanCheckbox>
								<p:tooltip
									for="allSelected"
									value="#{labels['titles.allSelectedforEserDevir']}" />
						</p:column>
						
						<p:column rendered="#{!eserZimmetGroupController.allSelected}">
							<p:outputLabel value="#{labels['titles.zimmetliEserleri']}" />  
						</p:column>
						<p:column rendered="#{!eserZimmetGroupController.allSelected}">
			
							<p:dataTable id="zimmetliEserDusmeDatatable"
								widgetVar="zimmetliEserDusmeTableWidget" 
								value="#{eserZimmetGroupController.lazyEserZimmetDataModel}"
								var="item" 
								rowIndexVar="row" 
								paginator="true"
								selection="#{eserZimmetGroupController.selectedEserZimmet}"
								paginatorPosition="bottom"
								paginatorTemplate="#{sessionBean.paginatorTemplate}"
								currentPageReportTemplate="#{sessionBean.currentPageReportTemplate}"
								rowsPerPageTemplate="#{sessionBean.rowsPerPageTemplate}"
								rowKey="#{item.id}"
								rows="#{sessionBean.rowsPerPageSize}" 
								sortMode="multiple"
								emptyMessage="#{labels['search.found.not']}"
								paginatorAlwaysVisible="true" 
								lazy="true" 
								scrollable="true"
								scrollHeight="100%"
								tableStyleClass="largeCheckBox">
								
								<p:ajax event="page" oncomplete="fixMenuHeight();" />
								<p:ajax event="rowSelectCheckbox" 
										update=":eserDevirForm:eserDevirView:dropButton"
									    global="false" 
									    process="@this" />
								<p:ajax event="rowUnselectCheckbox" 
										update=":eserDevirForm:eserDevirView:dropButton"
										global="false" 
										process="@this" />
								<p:ajax event="rowSelect" 
										update=":eserDevirForm:eserDevirView:dropButton"
										global="false"
										process="@this" />
								<p:ajax event="rowUnselect" 
								        update=":eserDevirForm:eserDevirView:dropButton"
										global="false"
										process="@this" />
								
								<p:ajax event="toggleSelect" 
										update=":eserDevirForm:eserDevirView:dropButton"
										global="false"
										process="@this" />
										
									
								<p:ajax event="filter"
										listener="#{eserZimmetGroupController.onFilterChange}"
										global="false" />

								<p:column exportable="false" 
									  styleClass="to-center"
								      selectionMode="multiple" 
								      style="padding: 0px;" 
								      width="44" />

								<p:column headerText="#{labels['titles.no']}"
										  styleClass="to-right"
										  style="padding-left: 1px;"
										  width="35"
								>
									<h:outputText value="#{row + 1}" />
								</p:column>
							  <p:column headerText="#{labels['titles.directorate']}"
									filterMatchMode="contains"
									filterBy="#{item.eser.eserDepos.alanKonumu.alan.bina.bagliBirim.mudurluk.ad}" 
									sortBy="#{item.eser.eserDepos.alanKonumu.alan.bina.bagliBirim.mudurluk.ad}">
									<h:outputText value="#{item.eser.eserDepo.alanKonumu.alan.bina.bagliBirim.mudurluk.ad}" />
								</p:column>

								
								<p:column headerText="#{labels['artifact.id.asset']}"
									filterBy="#{item.eser.permanentId}" 
									filterMatchMode="contains"
									sortBy="#{item.eser.permanentId}">
									<h:outputText value="#{item.eser.assetId}" />
								</p:column>
								<p:column headerText="#{labels['artifact.id.temp']}"
										  filterBy="#{item.eser.id}"
										  filterMatchMode="contains"
										  sortBy="#{item.eser.id}">
									<h:outputText value="#{item.eser.tempId}" />
								</p:column>
								<p:column headerText="#{labels['titles.eserTur']}"
									filterBy="#{item.eser.eserAltTur.eserTur.ad}"
									filterMatchMode="contains"
									sortBy="#{item.eser.eserAltTur.eserTur.ad}">
									<h:outputText value="#{item.eser.eserAltTur.eserTur.title}" />
								</p:column>
								<p:column headerText="#{labels['titles.eserOzelAdi']}"   
									filterBy="#{item.eser.eserOzelAdi}" 
									filterMatchMode="contains"
									sortBy="#{item.eser.eserOzelAdi}">
									<h:outputText value="#{item.eser.eserOzelAdi}" />
								</p:column>
								<p:column headerText="#{labels['titles.alan']}" 
									filterBy="#{item.eser.eserDepos.alanKonumu.alan.ad}" 
									filterMatchMode="contains"
									sortBy="#{item.eser.eserDepos.alanKonumu.alan.ad}">
									<h:outputText value="#{item.eser.eserDepo.alanKonumu.alan.ad}" />
								</p:column>
							
								<p:column headerText="#{labels['titles.alanKonumu']}"  
										filterBy="#{item.eser.eserDepos.alanKonumu.ad}" 
										filterMatchMode="contains"
										sortBy="#{item.eser.eserDepos.alanKonumu.ad}">
										<h:outputText value="#{item.eser.eserDepo.alanKonumu.ad}" />
									
							   </p:column>
                
                			   <p:column headerText="#{labels['titles.zimmetTarihi']}"
										filterMatchMode="contains"
										filterBy="#{item.zimmetTarihi}"
										styleClass="filter-column" 
										style="display:none"
										exportable="true">
										<h:outputText value="#{item.zimmetTarihi}" > 
											<f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
										</h:outputText>
									</p:column>
									
									
				
									<p:column headerText="#{labels['titles.zimmetTarihi']}"
											  filterBy="#{item.zimmetTarihi}"
											  styleClass="filter-column"
											  sortBy="#{item.zimmetTarihi}"
											  exportable="false"  width="190">
										<f:facet name="filter">
										    <h:inputHidden id="hiddenFilter"/>
									    </f:facet>
										<f:facet name="header">
										    <h:outputText value="#{labels['titles.date.range']}" style="display: block;" />
						                 	<p:datePicker id="from"
						                 				widgetVar="calendarWidgetFrom"
						                 				pattern="#{parameters.get('patern.date')}"
						                 				readonlyInput="true"
						                 				size="10"
						                 				mask="true" 
						                 				
						                 				onchange="fixDate()">
												<p:ajax event="dateSelect"
										      			onstart="$(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('hiddenFilter', view).clientId}'))[0].value = $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('from', view).clientId}_input'))[0].value + '~' + $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('to', view).clientId}_input'))[0].value"
										      			oncomplete="PF('zimmetliEserTableWidget').filter()" />
										    </p:datePicker>
										    <p:datePicker id="to"
										    			widgetVar="calendarWidgetTo"
										    			pattern="#{parameters.get('patern.date')}"
										    			readonlyInput="true"
										    			size="10"
										    			mask="true"
										    			onchange="fixDate()">
										    	<p:ajax event="dateSelect"
										    			onstart="$(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('hiddenFilter', view).clientId}'))[0].value = $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('from', view).clientId}_input'))[0].value + '~' + $(PrimeFaces.escapeClientId('#{p:resolveFirstComponentWithId('to', view).clientId}_input'))[0].value"
										    			oncomplete="PF('zimmetliEserTableWidget').filter()" />
										    </p:datePicker>
										</f:facet>
										<h:outputText value="#{item.zimmetTarihi}">
										    <f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
									    </h:outputText>
									</p:column>
							   
							   <p:column headerText="#{labels['titles.personel']}"   
							   		sortBy="#{item.zimmetPersonel.title}"
									filterMatchMode="contains" 
									filterBy="#{item.zimmetPersonel.title}">
									 <h:outputText value="#{item.zimmetPersonel.title}" />
								</p:column>
								
								<p:column headerText="#{labels['titles.zimmetliPersonel']}"    
								   		 sortBy="#{item.eser.zimmetPersonelTitles}"
										 filterMatchMode="contains" 
										 filterBy="#{item.eser.zimmetPersonelTitles}"
										 width="200">
										 
									 <h:outputText value="#{item.eser.zimmetPersonelTitles}" />
								</p:column>  
								
								      
								  <f:facet name="footer">
									<h:outputText value="#{eserZimmetGroupController.lazyEserZimmetDataModel !=null and  eserZimmetGroupController.lazyEserZimmetDataModel.rowCount gt 0 ? 'Toplam Kayit Sayısı  '.concat(eserZimmetGroupController.lazyEserZimmetDataModel.rowCount) : ''}" />
								</f:facet>
								
							</p:dataTable>
						</p:column>
							<p:column>
								<p:outputLabel
									for="description"
									value="#{labels['base.aciklama']}" />
							</p:column>
							<p:column>
								<p:inputTextarea
									id="description"
									value="#{eserZimmetGroupController.description}"
									maxlength="150"
									styleClass="fit-in-box"
									rows="2"
									counter="remainingCharsDescription"
									counterTemplate="{0} #{labels['base.char.remaining']}">
									 <p:ajax event="change" process="@this" />
								</p:inputTextarea>
								<h:outputText
									id="remainingCharsDescription"
									style="font-size: small; opacity: .6;" />
							</p:column>
							
							
							<p:outputPanel  styleClass="col-6 lg:col-6 border-none px-0"
							  layout="flex" contentStyleClass="fit-in-box mx-0" />
							  
							  
							<p:outputPanel  styleClass="col-6 lg:col-6 border-none px-0"
							  layout="flex" contentStyleClass="fit-in-box mx-0">
							 
							
								<p:commandButton
									id="dropButton"
									widgetVar="dropBtn"
									value="#{labels['base.submit']}"
									action="#{eserZimmetGroupController.removeLiability}"
									disabled="#{eserZimmetGroupController.personel == null or (empty eserZimmetGroupController.selectedEserZimmet and !eserZimmetGroupController.allSelected)}"
									onclick="PF('dropBtn').disable()"
									process=":eserDevirForm:eserDevirView:eserDusmeTab"
									update=":eserDevirForm :growlMessages :eserDevirForm:eserDevirView:zimmetliEserDusmeDatatable"
									icon="ui-icon-check"
									style="width: auto;"
									styleClass="to-center no-border" >
									
									<p:confirm header="#{labels['titles.zimmetDusmeBilgilendirme']}"  
										message="#{eserZimmetGroupController.allSelected ? 'Zimmet sahibi üzerindeki tüm eserler zimmet sahibinden üzerinden düşürülmek için onaya gönderilecek. Emin misiniz ?':
																							 'Zimmet sahibinden seçilen eserlerin zimmeti düşürülmek üzere onaya gönderilecek. Emin misiniz?'}"
										icon="ui-icon-info" />	
									
								</p:commandButton>
								
								<p:commandButton value="#{labels['search.reset']}"
												 icon="ui-icon-trash"
												 actionListener="#{eserZimmetGroupController.resetTable}"
												 process="@this"
												 partialSubmit="true"
												 immediate="true"
												 update=":eserDevirForm"
												 style="margin-left: .5em;">
													<p:resetInput target=":eserDevirForm:eserDevirView" /> 
												</p:commandButton>
							
						</p:outputPanel>
						
						</p:panelGrid>
													

					</p:tab>
				</p:tabView>
				
				<script  type="text/javascript">
					//<![CDATA[
						
						function filterAsEmpty(c) {
							if (c.value == undefined || c.value == "") {
								PF('auditViewTable').filter();
							}
			          	}
						

						function fixDate(){ 
						   var fromDate = PF('calendarWidgetFrom');
		                   var toDate = PF('calendarWidgetTo');
		                   if(fromDate.getDate() != null && toDate.getDate() != null  && fromDate.getDate() > toDate.getDate()   ){
		                       PF('growlMessageWidget').renderMessage({'summary':'Tarih Alanı Düzeltildi.', 'detail':'İlk Alan ikinciden büyük olamaz', 'severity':'Error'})
		                       toDate.setDate(fromDate.getDate());
		                   }
		                }
						
					//]]>
					</script>

			</h:form>
		</shiro:hasPermission>

		<shiro:lacksPermission name="eser:cokluzimmet">
			<ui:include src="/hata/403_i.xhtml" />
		</shiro:lacksPermission>

	</ui:define>

</ui:composition>
