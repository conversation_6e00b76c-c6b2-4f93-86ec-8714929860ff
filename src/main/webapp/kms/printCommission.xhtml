<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" 
	xmlns:h="http://java.sun.com/jsf/html" 
	xmlns:f="http://java.sun.com/jsf/core"
	xmlns:ui="http://java.sun.com/jsf/facelets"
	xmlns:o="http://omnifaces.org/ui" 
	xmlns:p="http://primefaces.org/ui"
	xmlns:components="http://java.sun.com/jsf/composite/components"
	xmlns:shiro="http://shiro.apache.org/tags"
	xmlns:c="http://java.sun.com/jsp/jstl/core"
	xmlns:fn="http://java.sun.com/jsp/jstl/functions">

	<ui:composition template="/WEB-INF/printtemplate.xhtml">

		<f:metadata>
			<f:viewParam id="commissionId" name="commissionId" value="#{commissionDefinitionController.commissionId}" />
		</f:metadata>

		<ui:define name="content">

			<o:importConstants type="tr.gov.tubitak.bte.kms.util.CommissionTypeEnum"/>
			
			<shiro:hasPermission name="kmsKomisyon:listele">

				<h:outputStylesheet library="mues" name="css/print.css" media="print" />
		
				<p:panel columns="4">
					<div style="text-align: center; width: 100%" class="ui-panel ui-panel-titlebar">
						<div class="fs-17" style="line-height: 100%">T.C.</div>
						<div>
							<p:graphicImage name="images/128x96_envanter_fis.png" library="mues"
											style="vertical-align: middle; position: absolute; top: 12px; left: 15px; width: 80px; height: 62px;" />
							<div style="width: auto;">
								<div class="fs-17">KÜLTÜR VE TURİZM BAKANLIĞI</div>
								<div class="fs-12">Kültür Varlıkları ve Müzeler Genel Müdürlüğü</div>
								<div class="fs-12">#{commissionDefinitionController.model.mudurluk.ad}</div>
								<div style="font-size: 17px; margin-bottom: 0px">#{commissionDefinitionController.model.commissionType.title} Raporu</div>
							</div>
						</div>
					</div>
				</p:panel>

				<div class="printDivContent" >
					<div class="grid">
						<div class="col-12 md:col-12">
							<div class="col-12 md:col-12"> 	
								<h:panelGrid styleClass="formTableEnvanter" columnClasses="etiket,deger,etiket,deger" columns="4">
									<f:facet name="header">
										<p:panel header="KOMİSYON BİLGİSİ" styleClass="baslik"/>
									</f:facet>
									<h:outputLabel value="Tarih" />
									<h:outputText value="#{dateUtil.getCurrentDate()}" >
										<f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
									</h:outputText>
									<h:outputLabel value="Önceki Komisyon" rendered="#{commissionDefinitionController.model.previousCommission != null}"/>
									<h:outputText value="#{commissionDefinitionController.model.previousCommission}" rendered="#{commissionDefinitionController.model.previousCommission != null}" />
									<h:outputLabel value="Müdürlük" />
									<h:outputText value="#{commissionDefinitionController.model.mudurluk}" />
									<h:outputLabel value="Komisyon Adı" />
									<h:outputText value="#{commissionDefinitionController.model.ad}" />
									<h:outputLabel value="Komisyon No" />
									<h:outputText value="#{commissionDefinitionController.model.formattedCommissionNo}" />
									<h:outputLabel value="Komisyon Türü" />
									<h:outputText value="#{commissionDefinitionController.model.commissionType.title}" />
									<h:outputLabel value="Komisyon Tarihi" />
									<h:outputText value="#{commissionDefinitionController.model.commissionDate}">
										<f:convertDateTime pattern="#{parameters.get('patern.date')}" />
									</h:outputText>
									<h:outputLabel value="Komisyon Rapor No" />
									<h:outputText value="#{not empty commissionDefinitionController.model.reportNo ? commissionDefinitionController.model.reportNo : '---------------(Doldurunuz)'}" />
									<h:outputLabel value="Açıklama" />
									<h:outputText value="#{commissionDefinitionController.model.aciklama}"/>
								</h:panelGrid>
							</div>
						</div>
						
						<p:outputPanel class="col-12 md:col-12 " rendered="#{fn:contains(commissionDefinitionController.model.commissionType, 'COST') and not empty commissionObjectController.items}">
							<div class="col-12 md:col-12 "> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMİSYONA EKLENEN OBJELER" styleClass="baslik"/>
									</f:facet>									
										<p:dataTable id="decisionTableId"
									       			 widgetVar="decisionTable"
									       			 rendered="#{not empty commissionObjectController.items}"
									                 value="#{commissionObjectController.items}"
									                 var="item"
									                 rowIndexVar="row"
									                 paginatorAlwaysVisible="true"
											         rowStyleClass="table-row-hilite"
											         style="word-break: break-word !important"
									                 emptyMessage="#{labels['base.record.notfound']}">
											 
											 <p:column headerText="#{labels['base.name']}" width="10%">
							                       <h:outputText value="#{item.object.eserOzelAd}" />
							                  </p:column>
							
							                  <p:column headerText="Adet" width="5%">
							               		  <h:outputText value="#{item.object.itemCount}" />
							                  </p:column>
							                  
							                  <p:column headerText="Malzeme" width="6%">
							                      <h:outputText value="#{item.object.malzeme}" />
							                  </p:column>
							                  
							                  <p:column headerText="Eser Tür" width="6%">
							                      <h:outputText value="#{item.object.eserAltTur.eserTur.title}" />
							                  </p:column>
							                  
							                  <p:column headerText="Dönem" width="7%">
							                      <h:outputText value="#{item.object.donem.title}" />
							                  </p:column>
							                  
							                  <p:column headerText="Maliye Kodu" width="6%">
							                      <h:outputText value="#{item.object.tasinirMalYonKod.title}" />
							                  </p:column>
							                  
							                  <p:column headerText="Geliş Şekli" width="6%">
							                      <h:outputText value="#{item.object.deliveredItem.temporaryAdmissionReceipt.eserGelisSekli}" />
							                  </p:column>
							                  
							                  <p:column headerText="TGA No" width="6%">
							                      <h:outputText value="#{item.object.deliveredItem.temporaryAdmissionReceipt.tgaNo}" />
							                  </p:column>
							
							                  <p:columns value="#{commissionObjectController.personelList}" var="personel" width="8%">
													<f:facet name="header">
														<h:outputText value="#{personel.ad} #{personel.soyad}" title="#{personel.title}" />
													</f:facet>
													<p:outputLabel value="#{commissionObjectController.decisionMap[item][personel]}"/>
											  </p:columns>
											  
											  <p:column headerText="Kıymet" width="6%">
													<p:outputLabel value="#{item.decidedCost}"/>
							                  </p:column>
							                  
							                  <p:column headerText="Karar" width="6%">
                                                <p:outputLabel value="#{item.decidedCategory.title}"/>
                                             </p:column>
							                  
							   			</p:dataTable>
								</h:panelGrid>
								<div style="padding-left: 20px; padding-right: 20px;">
									<p><br/>2863 sayılı Kültür ve Tabiat Varlıklarını Koruma Kanununun 25.maddesi gereği çıkarılan, 20.04.2009 gün ve 27206 sayılı Resmi Gazete’de yayınlanarak yürürlüğe giren  
										“Korunması gerekli Taşınır Kültür ve Tabiat Varlıklarının Tasnifi, Tescili ve Müzelere Alınması Hakkında Yönetmelik” in ilgili maddeleri gereği Müdürlüğümüzce oluşturulan  
										Kıymet Takdir  Komisyonu tarafından yukarıda <h:outputText value= "#{commissionObjectController.getDeliveredItemCount()}"></h:outputText> kalemde 
										<h:outputText value= "#{commissionObjectController.getObjectItemCount()}"></h:outputText> adet taşınır kültür ve tabiat varlığına toplam 
										<h:outputText value= "#{commissionObjectController.getTotalObjectsCost()}"></h:outputText>TL (<h:outputText value= "#{commissionObjectController.numberToWordForTL(commissionObjectController.getTotalObjectsCost())}"></h:outputText>) kıymet takdiri yapılmıştır.
					   				</p>					   				
								</div>
							</div>
						</p:outputPanel>
						
						<p:outputPanel class="col-12 md:col-12 " rendered="#{fn:contains(commissionDefinitionController.model.commissionType, 'EVALUATION') and not empty commissionObjectController.items}">
							<div class="col-12 md:col-12 "> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMİSYONA EKLENEN OBJELER" styleClass="baslik"/>
									</f:facet>									
										<p:dataTable id="decisionTableId"
									       			 widgetVar="decisionTable"
									       			 rendered="#{not empty commissionObjectController.items}"
									                 value="#{commissionObjectController.items}"
									                 var="item"
									                 rowIndexVar="row"
									                 paginatorAlwaysVisible="true"
											         rowStyleClass="table-row-hilite" 
											         style="word-break: break-word !important" 
									                 emptyMessage="#{labels['base.record.notfound']}">
											 
											 <p:column headerText="#{labels['base.name']}" width="10%">
							                       <h:outputText value="#{item.object.eserOzelAd}" />
							                  </p:column>
							
							                  <p:column headerText="Adet" width="5%">
							               		  <h:outputText value="#{item.object.itemCount}" />
							                  </p:column>
							                  
							                  <p:column headerText="Malzeme" width="6%">
							                      <h:outputText value="#{item.object.malzeme}" />
							                  </p:column>
							                  
							                  <p:column headerText="Eser Tür" width="6%">
							                      <h:outputText value="#{item.object.eserAltTur.eserTur.title}" />
							                  </p:column>
							                  
							                  <p:column headerText="Dönem" width="7%">
							                      <h:outputText value="#{item.object.donem.title}" />
							                  </p:column>
							                  
							                  <p:column headerText="Maliye Kodu" width="6%">
							                      <h:outputText value="#{item.object.tasinirMalYonKod.title}" />
							                  </p:column>
							                  
							                  <p:column headerText="Geliş Şekli" width="6%">
							                      <h:outputText value="#{item.object.deliveredItem.temporaryAdmissionReceipt.eserGelisSekli}" />
							                  </p:column>
							                  
							                  <p:column headerText="TGA No" width="6%">
							                      <h:outputText value="#{item.object.deliveredItem.temporaryAdmissionReceipt.tgaNo}" />
							                  </p:column>
							
							                  <p:columns value="#{commissionObjectController.personelList}" var="personel" width="8%">
													<f:facet name="header">
														<h:outputText value="#{personel.ad} #{personel.soyad}" title="#{personel.title}" />
													</f:facet>
													<p:outputLabel value="#{commissionObjectController.decisionMap[item][personel].title}"/>
											  </p:columns>
											  
											  <p:column headerText="Karar" width="6%">
													<p:outputLabel value="#{item.decidedCategory.title}"/>
							                  </p:column>
							                  
							   			</p:dataTable>							   				
								</h:panelGrid>
								<div style="padding-left: 20px; padding-right: 20px;">
									<p><br/>2863 sayılı Kültür ve Tabiat Varlıklarını Koruma Kanununun 25.maddesi gereği çıkarılan, 20.04.2009 gün ve 27206 sayılı Resmi Gazete’de 
						   				yayınlanarak yürürlüğe giren  “Korunması gerekli Taşınır Kültür ve Tabiat Varlıklarının Tasnifi, Tescili ve Müzelere Alınması 
						   				Hakkında Yönetmelik” in 5. maddesi gereği Müdürlüğümüzce oluşturulan  Değerlendirme  Komisyonu tarafından yukarıda 
						   				<h:outputText value= "#{commissionObjectController.getDeliveredItemCount()}"></h:outputText> kalemde 
						   				<h:outputText value= "#{commissionObjectController.getObjectItemCount()}"></h:outputText>
						   				 Kültür ve Tabiat Varlığı değerlendirilmiştir.
					   				</p>
								</div>
							</div>
						</p:outputPanel>
						
						<p:outputPanel class="col-12 md:col-12 " rendered="#{commissionDefinitionController.model.commissionType == CommissionTypeEnum.REVIEW and not empty reviewCommissionController.commissionEserList}">
							<div class="col-12 md:col-12 "> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMİSYONA EKLENEN ESERLER" styleClass="baslik"/>
									</f:facet>									
										<p:dataTable id="commissionEserTableId"
									       			 widgetVar="commissionEserTable"
									       			 rendered="#{not empty reviewCommissionController.commissionEserList}"
									                 value="#{reviewCommissionController.commissionEserList}"
									                 var="item"
									                 rowIndexVar="row"
									                 paginatorAlwaysVisible="true"
											         rowStyleClass="table-row-hilite"
											         style="word-break: break-word !important"
									                 emptyMessage="#{labels['base.record.notfound']}">
												 
											 <p:column headerText="Kültür Varlığı ID">
							                       <h:outputText value="#{item.eser.eserId}" />
							                  </p:column>							
							                  <p:column headerText="Eser Adı">
							               		  <h:outputText value="#{item.eser.eserOzelAdi}" />
							                  </p:column>
							                  <p:column headerText="Malzeme">
							               		  <h:outputText value="#{item.malzemeTitles}" />
							                  </p:column>
							                  <p:column headerText="Dönem">
							               		  <h:outputText value="#{item.eser.donem.title}" />
							                  </p:column>
							                  <p:column headerText="#{labels['titles.tespitTuru']}"  visible="true"
							                 		  sortBy="#{item.detectionType}">
							                     <h:outputText value="#{item.detectionType}" />
							                 </p:column>  
							                 <p:column headerText="#{labels['base.aciklama']} #{base.aciklama}" visible="true"  
							                 		  sortBy="#{item.aciklama}">
							                     <h:outputText value="#{item.aciklama}" />
							                 </p:column>
								          </p:dataTable>
								</h:panelGrid>
							</div>
						</p:outputPanel>
						
						<p:outputPanel class="col-12 md:col-12 " rendered="#{fn:contains(commissionDefinitionController.model.commissionType, 'EVALUATION') and not empty commissionObjectController.commissionEserList}">
							<div class="col-12 md:col-12 "> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMİSYONA EKLENEN ESERLER" styleClass="baslik"/>
									</f:facet>									
										<p:dataTable id="commissionEserTableId"
									       			 widgetVar="commissionEserTable"
									       			 rendered="#{not empty commissionObjectController.commissionEserList}"
									                 value="#{commissionObjectController.commissionEserList}"
									                 var="item"
									                 rowIndexVar="row"
									                 paginatorAlwaysVisible="true"
											         rowStyleClass="table-row-hilite"
											         style="word-break: break-word !important"
									                 emptyMessage="#{labels['base.record.notfound']}">
												 
											 <p:column headerText="Kültür Varlığı ID">
							                       <h:outputText value="#{item.eser.eserId}" />
							                  </p:column>							
							                  <p:column headerText="Eser Adı">
							               		  <h:outputText value="#{item.eser.eserOzelAdi}" />
							                  </p:column>
							                  <p:column headerText="Malzeme">
							               		  <h:outputText value="#{item.malzemeTitles}" />
							                  </p:column>
							                  <p:column headerText="Dönem">
							               		  <h:outputText value="#{item.eser.donem.title}" />
							                  </p:column>
							                   <p:columns value="#{commissionObjectController.personelList}" var="personel" width="8%">
													<f:facet name="header">
														<h:outputText value="#{personel.ad} #{personel.soyad}" title="#{personel.title}" />
													</f:facet>
													<p:outputLabel value="#{commissionObjectController.eserDecisionMap[item][personel].title}"/>
											  </p:columns>
							                   <p:column headerText="Karar" width="6%">
													<p:outputLabel value="#{item.decidedCategory.title}"/>
							                  </p:column>
								          </p:dataTable>
								</h:panelGrid>
							</div>
						</p:outputPanel>
						
						<p:outputPanel class="col-12 md:col-12 " rendered="#{fn:contains(commissionDefinitionController.model.commissionType, 'COST') and not empty commissionObjectController.commissionEserList}">
							<div class="col-12 md:col-12 "> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMİSYONA EKLENEN ESERLER" styleClass="baslik"/>
									</f:facet>									
										<p:dataTable id="commissionEserTableId"
									       			 widgetVar="commissionEserTable"
									       			 rendered="#{not empty commissionObjectController.commissionEserList}"
									                 value="#{commissionObjectController.commissionEserList}"
									                 var="item"
									                 rowIndexVar="row"
									                 paginatorAlwaysVisible="true"
											         rowStyleClass="table-row-hilite"
											         style="word-break: break-word !important"
									                 emptyMessage="#{labels['base.record.notfound']}">
												 
											 <p:column headerText="Kültür Varlığı ID">
							                       <h:outputText value="#{item.eser.eserId}" />
							                  </p:column>							
							                  <p:column headerText="Eser Adı">
							               		  <h:outputText value="#{item.eser.eserOzelAdi}" />
							                  </p:column>
							                  <p:column headerText="Malzeme">
							               		  <h:outputText value="#{item.malzemeTitles}" />
							                  </p:column>
							                  <p:column headerText="Dönem">
							               		  <h:outputText value="#{item.eser.donem.title}" />
							                  </p:column>
							                   <p:columns value="#{commissionObjectController.personelList}" var="personel" width="8%">
													<f:facet name="header">
														<h:outputText value="#{personel.ad} #{personel.soyad}" title="#{personel.title}" />
													</f:facet>
													<p:outputLabel value="#{commissionObjectController.eserDecisionMap[item][personel]}"/>
											  </p:columns>							                  
							                   <p:column headerText="Karar" width="6%">
													<p:outputLabel value="#{item.decidedCost}"/>
							                  </p:column>
								          </p:dataTable>
								</h:panelGrid>
							</div>
						</p:outputPanel>

						<p:outputPanel class="col-12 md:col-12" rendered="#{commissionDefinitionController.model.commissionType == CommissionTypeEnum.REVIEW and not empty commissionDefinitionController.model.commissionReport}">
							<div class="col-12 md:col-12"> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMISYON INCELEME RAPORU" styleClass="baslik"/>
									</f:facet>									
									<h:outputText value="#{commissionDefinitionController.model.commissionReport}" escape="false" style="word-wrap: break-word;"/>
								</h:panelGrid>
							</div>
						</p:outputPanel>
						
						<p:outputPanel class="col-12 md:col-12 " rendered="#{commissionDefinitionController.model.commissionType == CommissionTypeEnum.COUNTING}">
							<div class="col-12 md:col12 "> 
								<h:panelGrid styleClass="col-12 md:col-12 formTableEnvanter" columnClasses="etiket,deger" columns="2">
									<f:facet name="header">
										<p:panel header="KOMİSYONA EKLENEN SAYIMLAR" styleClass="baslik"/>
									</f:facet>									
										<p:dataTable id="commissionCountingTableId"
									       			 widgetVar="commissionCountingTable"
									       			 rendered="#{not empty commissionCountingController.items}"
									                 value="#{commissionCountingController.items}"
									                 var="item"
									                 rowIndexVar="row"
									                 paginatorAlwaysVisible="true"
											         rowStyleClass="table-row-hilite"
											         style="word-break: break-word !important"
									                 emptyMessage="#{labels['base.record.notfound']}">
												 
											 <p:column headerText="Müze Müdürlüğü">
							                       <h:outputText value="#{item.artifactCounting.mudurluk.ad}" />
							                  </p:column>							
							                  <p:column headerText="Bağlı Birim">
							               		  <h:outputText value="#{item.artifactCounting.alan.bina.bagliBirim.ad}" />
							                  </p:column>
							                  <p:column headerText="Bina">
							               		  <h:outputText value="#{item.artifactCounting.alan.bina.ad}" />
							                  </p:column>
							                  <p:column headerText="Alan Tür">
							               		  <h:outputText value="#{item.artifactCounting.alan.alanTur.ad}" />
							                  </p:column>
							                  <p:column headerText="Alan">
							               		  <h:outputText value="#{item.artifactCounting.alan.ad}" />
							                  </p:column>
							                  <p:column headerText="Sayım Adı">
							               		  <h:outputText value="#{item.artifactCounting.ad}" />
							                  </p:column>
							                  <p:column headerText="Sayım Türü">
							               		  <h:outputText value="#{item.artifactCounting.countingType}" />
							                  </p:column>
							                  <p:column headerText="Başlangıç Tarihi">
							               		  <h:outputText value="#{item.artifactCounting.startDate}">
							                       	<f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
							                       </h:outputText>
							                  </p:column>
							                  <p:column headerText="Bitiş Tarihi">
							               		  <h:outputText value="#{item.artifactCounting.endDate}">
							                       		<f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
							                       </h:outputText>
							                  </p:column>
							                  <p:column headerText="İşlem Sahibi">
							               		  <h:outputText value="#{item.artifactCounting.createdBy.title}" />
							                  </p:column>
								          </p:dataTable>
								</h:panelGrid>
							</div>
						</p:outputPanel>
						
						<p:outputPanel class="col-12 md:col-12 ">
							<div class="flex w-12 align-items-center justify-content-center">
								<div class="boxContainer flex-wrap column-gap-4 row-gap-8 justify-content-center">
									<c:forEach items="#{commissionDefinitionController.model.commissionMembers}" var="item">
										<div class="box w-12rem h-6rem" style="page-break-inside: avoid;">
											<div class="box1"><h:outputText value="#{item.head ? 'KOMİSYON BAŞKANI' : 'ÜYE'}" /></div>
											<div class="box2"><h:outputText value="#{item.personel.title}" /></div>
											<div class="box3"><h:outputText value="#{item.personel.unvan}" /></div>
										</div>
									</c:forEach>
								</div>
							</div>
						</p:outputPanel>
					</div>
				</div>
				
			</shiro:hasPermission>

			<shiro:lacksPermission name="kmsKomisyon:listele">
				<ui:include src="/hata/403_i.xhtml" />
			</shiro:lacksPermission>
			
		</ui:define>	
	</ui:composition>
</html>