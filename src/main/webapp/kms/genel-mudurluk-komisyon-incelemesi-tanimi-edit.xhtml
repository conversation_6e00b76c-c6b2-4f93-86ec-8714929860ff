<?xml version="1.0" encoding="UTF-8"?>
<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:p="http://primefaces.org/ui"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:o="http://omnifaces.org/ui"
				>
				
	<p:fieldset legend="Komisyon Bilgileri: #{commissionDefinitionController.model.ad}" toggleable="true">
	
	<o:importConstants type="tr.gov.tubitak.bte.kms.util.KMSStateEnum"/>
	
	<p:panelGrid id="editorDialogPanelGrid" 
					 columnClasses="col-2 input-label, col-10 input-field"
					 styleClass="col-12 border-none px-0"
					 columns="2"
					 layout="flex"
					 contentStyleClass="fit-in-box mx-0"
					 rendered="#{cc.attrs.bean.model != null}">
					 
		<ui:include src="/kms/komisyon-header-edit.xhtml" >
			<ui:param name="bean" value="#{commissionDefinitionController}" />
		</ui:include>
		
		<ui:include src="/kms/komisyon-uye-edit.xhtml">
			<ui:param name="bean" value="#{generalDirectorateCommissionController}" />
			<ui:param name="baseBean" value="#{commissionDefinitionController}" />
		</ui:include>
		
		<ui:include src="/kms/komisyon-footer-edit.xhtml" >
		 	<ui:param name="bean" value="#{commissionDefinitionController}" />	
		 	<ui:param name="baseBean" value="#{generalDirectorateCommissionController}" />				 
		 </ui:include>
		
	</p:panelGrid>
	
	</p:fieldset>


	<p:fieldset rendered="#{cc.attrs.commissionEdit and cc.attrs.bean.model.commissionState != cc.attrs.endState}">
		<ui:include src="/kms/bits/commission-reports-list-table.xhtml">
			<ui:param name="kmsState" value="#{KMSStateEnum.TGA_GENERAL_DIRECTORATE_COMMISSION_APPROVAL}"/> 
			<ui:param name="title" value="Genel Müdürlük Komisyon İnceleme"/>
			<ui:param name="renderDetail" value="false"/>
			<ui:param name="baseBean" value="#{generalDirectorateCommissionController}"/>
			<ui:param name="bean" value="#{generalDirectorateReviewController}"/>
			<ui:param name="updateDialog" value="kmsDetailDialog" />
			<ui:param name="updateContent" value=":generalDirectorateReviewCommissionDetaildialog:formEditor"/>
		</ui:include>
	</p:fieldset>
	
	<p:panel>
	
	<p:splitter gutterSize="10" />
	
	<p:outputLabel for="commissionHidden" value="Genel Müdürlük Komisyonunda İncelenen Komisyonların Listesi" style="display:none" />
	<h:inputHidden required="#{empty generalDirectorateCommissionController.reviewedCommissions}" id="commissionHidden" />
	
	<p:dataTable id="generalDirectorateReviewCommissionDetailTableId"
              			widgetVar="generalDirectorateReviewCommissionDetailTableWV"
                        value="#{generalDirectorateCommissionController.reviewedCommissions}"
                        filteredValue="#{generalDirectorateCommissionController.filteredValues}"
                        rowIndexVar="row"
                        var="item"
              			paginator="true"
              			sortMode="multiple"
              			paginatorPosition="bottom"
              			paginatorTemplate="#{sessionBean.paginatorTemplate}"
              			currentPageReportTemplate="#{sessionBean.currentPageReportTemplate}"
                        rowsPerPageTemplate="#{sessionBean.rowsPerPageTemplate}"
                        style="font-size:14px"
                        rows="#{sessionBean.rowsPerPageSize}"
                        emptyMessage="#{labels['base.record.notfound']}"
                        scrollable="true"
                        styleClass="generalDirectorateReviewCommissionDetailClass">
			      
			      <f:facet name="header">  
				  		<h:outputText value="#{labels['titles.komisyonListesi']}" />
				  </f:facet>	
                    
                    <p:column headerText="Müze Müdürülüğü"
                    		  filterBy="#{item.commission.mudurluk.ad}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.commission.mudurluk.ad}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.commission.mudurluk.ad').filterValue}">
                        <h:outputText value="#{item.commission.mudurluk.ad}" />
                    </p:column>
                    
                      <p:column headerText="#{labels['titles.komisyonId']}"  
                    		  filterBy="#{item.commission.formattedId}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.commission.formattedId}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.commission.formattedId').filterValue}">
                        <h:outputText value="#{item.commission.formattedId}" />
                    </p:column>
                    
                    <p:column headerText="#{labels['titles.komisyonNo']}"
                    		  filterBy="#{item.commission.formattedCommissionNo}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.commission.formattedCommissionNo}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.commission.formattedCommissionNo').filterValue}">
                        <h:outputText value="#{item.commission.formattedCommissionNo}" />
                    </p:column>
                    
                    <p:column headerText="#{labels['titles.komisyonIsmi']}"
                    		  filterBy="#{item.commission.ad}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.commission.ad}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.commission.ad').filterValue}">
                        <h:outputText value="#{item.commission.ad}" />
                    </p:column>
                    
                    <p:column headerText="#{labels['kms.titles.tgaNo']}"
                    		  filterBy="#{item.commission.getTgaNo()}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.commission.getTgaNo()}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.commission.getTgaNo()').filterValue}">
                        <h:outputText value="#{item.commission.getTgaNo()}" />
                    </p:column>
                    
                   
                    
                    <p:column headerText="#{labels['titles.komisyonTarihi']}"
                    		  filterBy="#{item.commission.commissionDate}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.commission.commissionDate}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.commission.commissionDate').filterValue}">
                        <h:outputText value="#{item.commission.commissionDate}">
                        	<f:convertDateTime type="date" pattern="#{parameters.get('patern.date')}" />
	                    </h:outputText>
                    </p:column>	
                    
                    <p:column headerText="Toplam Eser">
                    	<h:outputText value="#{item.commission.totalInventoryNumber}" />
                    </p:column>
                    
                    <p:column headerText="#{labels['titles.toplamKiymet']}">
                     	<h:outputText value="#{item.commission.totalInventoryCost}">
									<f:convertNumber pattern="#,##0.00 ¤" currencySymbol="TL" />
								</h:outputText>
                     </p:column>
                    <p:column headerText="#{labels['titles.yapilanIncelemeSonucu']}"
                    		  filterBy="#{item.kmsState.name}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.kmsState.name}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.kmsState').filterValue}">
                        <h:outputText value="#{item.kmsState.name}" />
                    </p:column>	
                    
                    <p:column headerText="#{labels['titles.incelemeYorumu']}"
                    		  filterBy="#{item.aciklama}"
                    		  styleClass="filter-column"
                    		  filterMatchMode="contains"
                    		  sortBy="#{item.aciklama}"
	                		  filterValue="#{generalDirectorateCommissionController.filterState('item.aciklama').filterValue}">
                        <h:outputText value="#{item.aciklama}" />
                    </p:column>	
                    
                    <p:column styleClass="to-center"
						  style="font-size:16px !important;"
						  exportable="false"
						  width="30">
			
						<f:facet name="header">
							<div style="display: none">#{labels['titles.operation.s']}</div>
							<i class="fa fa-cog" aria-hidden="true" title="#{labels['titles.operation.s']}" />
						</f:facet>
						<p:commandLink title="#{labels['titles.incele']}"
								oncomplete="openWindow('kms','#{commissionUtil.fetchCommissionPageAdressUrl(item.commission)}', '_blank')"
								update="#{updateContent}"
								process="@this"
								partialSubmit="true">
								<i class="fa fa-search-plus green action-icon-big" aria-hidden="true" />
							</p:commandLink>
							
							<p:commandLink id="viewReportDetay"
												   title="#{title} Genel Bilgiler"
												   rendered="#{renderDetail}"
												   actionListener="#{commissionReportsController.showDetail(item)}"
												   update="komisyonRaporlariOnKontrolInfo:dialogInfoContent"
												   oncomplete="PF('dialogInfo').show()"
												   process="@this"
												   style="text-decoration: none;">
									<i class="fa fa-info-circle hardblue" aria-hidden="true" />
								</p:commandLink>
					</p:column>		
		 </p:dataTable>

		<p:spacer height="10"/>
		<p:panelGrid id="editorDialogPanelGridForRapor"
					 columnClasses="col-2 input-label, col-10 input-field"
					 styleClass="col-12 border-none px-0"
					 columns="2"
					 layout="flex"
					 contentStyleClass="fit-in-box mx-0"
					 rendered="#{cc.attrs.bean.model != null}">

			<ui:include src="/kms/komisyon-footer-rapor-edit.xhtml" >
				<ui:param name="bean" value="#{commissionDefinitionController}" />
				<ui:param name="baseBean" value="#{evaluationCommissionController}" />
			</ui:include>
		</p:panelGrid>
	

	</p:panel>
</ui:composition>
