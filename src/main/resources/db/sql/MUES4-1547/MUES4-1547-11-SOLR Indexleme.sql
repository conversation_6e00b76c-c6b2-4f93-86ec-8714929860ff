-- ////////////
IF OBJECT_ID('dbo.OMK_ESER_HAREKET_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.OMK_ESER_HAREKET_VIEW;

create view OMK_ESER_HAREKET_VIEW as
SELECT Omk_ESER_HAREKET.ESER_ID                 AS ESER_HAREKET_ESER_ID,
       Omk_ESER_HAREKET.ARASTIRMA_ID            AS ESER_HAREKET_ARASTIRMA_ID,
       Omk_ESER_HAREKET.KAZI_ID                 AS ESER_HAREKET_KAZI_ID,
       Omk_ESER_HAREKET.MUZE_ID                 AS ESER_HAREKET_MUZE_ID,
       Omk_Mudurluk.AD                          AS ESER_HAREKET_MUZE_AD,
       teslimAlan.AD + ' ' + teslimAlan.SOYAD   AS ESER_HAREKET_TESLIM_ALAN_PERSONEL_AD_SOYAD,
       teslimEden.AD + ' ' + teslimEden.SOYAD   AS ESER_HAREKET_TESLIM_EDEN_PERSONEL_AD_SOYAD,
       Omk_ESER_HAREKET.ESER_GELIS_SEKLI        AS ESER_HAREKET_ESER_GELIS_SEKLI,
       Omk_ESER_HAREKET.ENVANTER_NO             AS ESER_HAREKET_ENVANTER_NO,
       Omk_ESER_HAREKET.CIKARILMA_TARIHI        AS ESER_HAREKET_CIKARILMA_TARIHI,
       Omk_ESER_HAREKET.ONAY_TARIHI             AS ESER_HAREKET_ONAY_TARIHI,
       Omk_ESER_HAREKET.ONAY_SAYISI             AS ESER_HAREKET_ONAY_SAYISI,
       Omk_ESER_HAREKET.MUZEYE_GELIS_TARIHI     AS ESER_HAREKET_MUZEYE_GELIS_TARIHI,
       Omk_ESER_HAREKET.ENVANTERE_ALINMA_TARIHI AS ESER_HAREKET_ENVANTERE_ALINMA_TARIHI,
       Omk_ESER_HAREKET.ACIKLAMA                AS ESER_HAREKET_ACIKLAMA,
       Omk_ESER_HAREKET.ELE_GECIRME_YERI        AS ESER_HAREKET_ELE_GECIRME_YERI,
       Omk_ESER_HAREKET.iadeEdenUlke            AS ESER_HAREKET_IADE_EDEN_ULKE,
       Omk_ESER_HAREKET.AKTIF                   AS ESER_HAREKET_AKTIF,
       Omk_ESER_HAREKET.SILINMIS                AS ESER_HAREKET_SILINMIS,
       KAZI_TUR.DEGER                           AS KAZI_TUR_DEGER,
       KAZI_TUR.AD                              AS KAZI_TUR_AD,
       KAZI.AD                                  AS KAZI_AD,
       KAZI.ACIKLAMA                            AS KAZI_ACIKLAMA,
       KAZI.IL_ID                               AS KAZI_IL_ID,
       KAZI.ILCE_ID                             AS KAZI_ILCE_ID,
       KAZI.ENLEM                               AS KAZI_ENLEM,
       KAZI.BOYLAM                              AS KAZI_BOYLAM,
       ARASTIRMA.ID                             AS ARASTIRMA_ID,
       ARASTIRMA_TUR.AD                         AS ARASTIRMA_TUR_AD,
       ARASTIRMA_TUR.ACIKLAMA                   AS ARASTIRMA_TUR_ACIKLAMA,
       ARASTIRMA.AD                             AS ARASTIRMA_AD,
       ARASTIRMA.ACIKLAMA                       AS ARASTIRMA_ACIKLAMA,
       KAZI.BELDE_ADI                           AS KAZI_BELDE_ADI,
       KAZI.KAZI_KODU                           AS KAZI_KAZI_KODU,
       KAZI_ILCE.AD                             AS KAZI_ILCE_AD,
       KAZI_IL.AD                               AS KAZI_IL_AD,
       Ulke.AD                                  AS Ulke_AD,
       TUZEL_KISI.TICARI_UNVAN                  AS TUZEL_KISI_TICARI_UNVAN,
       gelisSekli.[AD]                          AS ESER_HAREKET_ESER_GELIS_SEKLI_AD
FROM Omk_ESER_HAREKET
         LEFT JOIN KAZI ON KAZI.ID = Omk_ESER_HAREKET.KAZI_ID
         LEFT JOIN KAZI_TUR ON KAZI.KAZI_TUR_ID = KAZI_TUR.ID
         LEFT JOIN ARASTIRMA ON ARASTIRMA.ID = Omk_ESER_HAREKET.ARASTIRMA_ID
         LEFT JOIN ARASTIRMA_TUR ON ARASTIRMA.ARASTIRMA_TUR_ID = ARASTIRMA_TUR.ID
         LEFT JOIN Ulke ON Omk_ESER_HAREKET.iadeEdenUlke = Ulke.ID
         LEFT JOIN IL AS KAZI_IL ON KAZI.IL_ID = KAZI_IL.ID
         LEFT JOIN ILCE AS KAZI_ILCE ON KAZI.ILCE_ID = KAZI_ILCE.ID
         LEFT JOIN Pick gelisSekli ON gelisSekli.ID = Omk_ESER_HAREKET.ESER_GELIS_SEKLI
         LEFT JOIN TUZEL_KISI ON Omk_ESER_HAREKET.TESLIM_EDEN_TUZEL_KISI_ID = TUZEL_KISI.ID
         LEFT JOIN Omk_Mudurluk ON Omk_ESER_HAREKET.MUZE_ID = Omk_Mudurluk.ID
         LEFT JOIN PERSONEL teslimAlan
                   ON Omk_ESER_HAREKET.TESLIM_ALAN_PERSONEL_ID = teslimAlan.ID
         LEFT JOIN PERSONEL teslimEden
                   ON Omk_ESER_HAREKET.TESLIM_EDEN_PERSONEL_ID = teslimEden.ID;

-- ////////////
CREATE VIEW OMK_ESER_TUMBILGILER AS
WITH eser_hareket1 as (select * from OMK_ESER_HAREKET_VIEW)
SELECT dbo.Omk_ESER.ID                                                                AS ID,
--omk solr core 7 and qdrant artifact type 7
       (700000000 + Omk_ESER.ID)                                                      as uid,
       dbo.Omk_Alan.ACIKLAMA                                                          AS ALAN_ACIKLAMA_ci,
       dbo.Omk_Alan.AD                                                                AS ALAN_AD_ci,
       dbo.Omk_Alan.KOD                                                               AS ALAN_KOD_ci,
       dbo.Omk_Alan_Konumu.AD                                                         AS ALAN_KONUMU_AD_ci,
       dbo.ALAN_KONUMU_TUR.AD                                                         AS ALAN_KONUMU_TUR_AD_ci,
       dbo.ALAN_KONUMU_TUR.DEGER                                                      AS ALAN_KONUMU_TUR_DEGER_ci,
       dbo.Omk_ALAN_TUR.AD                                                            AS ALAN_TUR_AD_ci,
       dbo.Omk_ALAN_TUR.DEGER                                                         AS ALAN_TUR_DEGER_ci,
       dbo.Omk_Pack.state                                                             AS artifactState,

       dbo.MUZE_MUDURLUGU.AD                                                          as BAGLI_MUDURLUK_AD_ci,

       dbo.Omk_Mudurluk.ACIKLAMA                                                      AS MUZE_MUDURLUGU_ACIKLAMA_ci,
       dbo.Omk_Mudurluk.AD                                                            AS MUZE_MUDURLUGU_AD_ci,
       dbo.Omk_Mudurluk.BOYLAM                                                        AS MUZE_MUDURLUGU_BOYLAM,
       dbo.Omk_Mudurluk.ENLEM                                                         AS MUZE_MUDURLUGU_ENLEM,
       dbo.Omk_Mudurluk.KOD                                                           AS MUZE_MUDURLUGU_KOD_ci,

       dbo.Omk_BagliBirim.AD                                                          AS BAGLI_BIRIM_AD_ci,
       dbo.Omk_Bina.ACIKLAMA                                                          AS BINA_ACIKLAMA_ci,
       dbo.Omk_Bina.AD                                                                AS BINA_AD_ci,
       dbo.Omk_Bina.KOD                                                               AS BINA_KOD_ci,

       CASE WHEN dbo.Omk_ESER.updateInProgress = 1 THEN 0 ELSE dbo.Omk_ESER.AKTIF END AS ESER_AKTIF,

       CASE WHEN dbo.Omk_ESER.DONEM_BASLANGIC_YIL < -30000 THEN 1 ELSE 0 END          AS ESER_DONEM_BASLANGIC_GO,
       dbo.Omk_ESER.DONEM_BASLANGIC_YIL                                               AS ESER_DONEM_BASLANGIC_YIL,

       CASE WHEN dbo.Omk_ESER.DONEM_BITIS_YIL < -30000 THEN 1 ELSE 0 END              AS ESER_DONEM_BITIS_GO,
       dbo.Omk_ESER.DONEM_BITIS_YIL                                                   AS ESER_DONEM_BITIS_YIL,
       convert(varchar(25), dbo.Omk_ESER.DUZENLEME_ZAMANI, 120)                       AS ESER_DUZENLEME_ZAMANI_ci,
       dbo.Omk_ESER.ELISI_DOKUMA_SECIMI                                               AS ESER_ELISI_DOKUMA_SECIMI,
       dbo.Omk_ESER.ENVANTER_NO                                                       AS ESER_ESKI_ENVANTER_NO_ci,
       dbo.Omk_ESER.ESER_OZEL_ADI                                                     AS ESER_ESER_OZEL_ADI_ci,
       dbo.Omk_ESER.ESERI_BAGISLAYAN_ONEMLI_KISI_ID                                   AS ESER_ESERI_BAGISLAYAN_ONEMLI_KISI_ID,
       dbo.Omk_ESER.ESERI_KULLANACAK_ONEMLI_KISI_ID                                   AS ESER_ESERI_KULLANACAK_ONEMLI_KISI_ID,
       dbo.Omk_ESER.ESERI_KULLANAN_ONEMLI_KISI_ID                                     AS ESER_ESERI_KULLANAN_ONEMLI_KISI_ID,
       dbo.Omk_ESER.ESERI_YAPAN_ONEMLI_KISI_ID                                        AS ESER_ESERI_YAPAN_ONEMLI_KISI_ID,
       dbo.Omk_ESER.ESERI_YAPTIRAN_ONEMLI_KISI_ID                                     AS ESER_ESERI_YAPTIRAN_ONEMLI_KISI_ID,
       dbo.Omk_ESER.GENEL_ACIKLAMA                                                    AS ESER_GENEL_ACIKLAMA_ci,
       dbo.Omk_ESER.ISLAMI_GAYRI_SECIMI                                               AS ESER_ISLAMI_GAYRI_SECIMI,
       dbo.Omk_ESER.KIYMET                                                            AS ESER_KIYMET,
       dbo.Omk_ESER.KONDISYON_DURUMU                                                  AS ESER_KONDISYON_DURUMU,

       CASE WHEN dbo.Omk_ESER.SIKKE_DARP_YILI < -30000 THEN 1 ELSE 0 END              AS ESER_SIKKE_DARP_GO,

       dbo.Omk_ESER.SIKKE_DARP_YILI                                                   AS ESER_SIKKE_DARP_YILI,
       dbo.Omk_ESER.SIKKE_DARP_YONU                                                   AS ESER_SIKKE_DARP_YONU,
       dbo.DarpYeri.AD                                                                AS ESER_SIKKE_DARP_YERI_AD_ci,
       dbo.Omk_ESER.SILINMIS                                                          AS ESER_SILINMIS,
       dbo.Omk_ESER.SILME_ACIKLAMASI                                                  AS ESER_SILME_ACIKLAMASI_ci,
       convert(varchar(25), dbo.Omk_ESER.SILME_ZAMANI, 120)                           AS ESER_SILME_ZAMANI_ci,

       (SELECT Omk_ESER_FOTOGRAF.FOTOGRAF_PATH
        FROM Omk_ESER_FOTOGRAF
        WHERE Omk_ESER_FOTOGRAF.ESER_ID = Omk_ESER.ID
          AND Omk_ESER_FOTOGRAF.ANA_FOTOGRAF = 1)                                     AS ESER_TANIMLAYICI_FOTOGRAF_PATH_ci,

       dbo.Omk_ESER.TASINIR_ISLEM_FISI_NO                                             AS ESER_TASINIR_ISLEM_FISI_NO_ci,
       dbo.Omk_ESER.TASINIR_MAL_YON_ID                                                AS ESER_TASINIR_MAL_YON_ID,
       dbo.Omk_ESER.TORENSEL_DURUMU                                                   AS ESER_TORENSEL_DURUMU,
       dbo.Omk_ESER.UNIKLIK_DURUMU                                                    AS ESER_UNIKLIK_DURUMU,
       dbo.Omk_ESER.uretimYeriId                                                      AS ESER_URETIM_YERI_ID,
       dbo.Omk_ESER.VERSIYON                                                          AS ESER_VERSIYON,
       convert(varchar(25), dbo.Omk_ESER.YARATMA_ZAMANI, 120)                         AS ESER_YARATMA_ZAMANI_ci,
       dbo.Omk_ESER.YAZMA_BASMA_SECIMI                                                AS ESER_YAZMA_BASMA_SECIMI,
       dbo.ESER_TUR.AD                                                                AS ESER_TUR_AD_ci,
       dbo.ESER_ALT_TUR.AD                                                            AS ESER_ALT_TUR_AD_ci,
       dbo.ESER_TUR.AD + ' > ' + dbo.ESER_ALT_TUR.AD                                  AS ESER_TUR_VE_ALT_TUR_AD_ci,
       dbo.Cag.ACIKLAMA                                                               AS CAG_ACIKLAMA_ci,
       dbo.Cag.AD                                                                     AS CAG_AD_ci,
       dbo.Omk_EserDepo.ACIKLAMA                                                      AS ESER_DEPO_ACIKLAMA_ci,
       dbo.Donem.AD                                                                   AS Donem_AD_ci,
       dbo.Iliskilendirme.AD                                                          AS ILISKILENDIRME_AD_ci,
       dbo.Iliskilendirme.ACIKLAMA                                                    AS ILISKILENDIRME_ACIKLAMA_ci,
       dbo.HUKUMDAR.ACIKLAMA                                                          AS HUKUMDAR_ACIKLAMA_ci,
       dbo.HUKUMDAR.AD                                                                AS HUKUMDAR_AD_ci,

       CASE WHEN dbo.HUKUMDAR.DONEM_BASLANGIC_YIL < -30000 THEN 1 ELSE 0 END          AS HUKUMDAR_DONEM_BASLANGIC_GO,

       dbo.HUKUMDAR.DONEM_BASLANGIC_YIL                                               AS HUKUMDAR_DONEM_BASLANGIC_YIL,

       CASE WHEN dbo.HUKUMDAR.DONEM_BITIS_YIL < -30000 THEN 1 ELSE 0 END              AS HUKUMDAR_DONEM_BITIS_GO,

       dbo.HUKUMDAR.DONEM_BITIS_YIL                                                   AS HUKUMDAR_DONEM_BITIS_YIL,
       dbo.IL.AD                                                                      AS IL_AD_ci,
       dbo.IL.DEGER                                                                   AS IL_DEGER_ci,
       dbo.ILCE.AD                                                                    AS ILCE_AD_ci,
       dbo.KRONOLOJI.ACIKLAMA                                                         AS KRONOLOJI_ACIKLAMA_ci,
       dbo.KRONOLOJI.AD                                                               AS KRONOLOJI_AD_ci,
       dbo.Omk_ESER.YARATMA_KULLANICI_ID                                              AS ESER_YARATMA_KULLANICI_ID,

       dbo.Omk_Kullanici.AD + ' ' + dbo.Omk_Kullanici.SOYAD                           AS KULLANICI_KULLANICI_ADI_ci,

       dbo.PERSONEL.SICIL_NO                                                          AS IHTISAS_ELEMANI_SICIL_NO_ci,
       dbo.PERSONEL.AD                                                                AS IHTISAS_ELEMANI_AD_ci,
       dbo.PERSONEL.SOYAD                                                             AS IHTISAS_ELEMANI_SOYAD_ci,
       dbo.UretimYeri.ACIKLAMA                                                        AS UretimYeri_ACIKLAMA_ci,
       dbo.UretimYeri.AD                                                              AS UretimYeri_AD_ci,
       dbo.UYGARLIK.ACIKLAMA                                                          AS UYGARLIK_ACIKLAMA_ci,
       dbo.UYGARLIK.AD                                                                AS UYGARLIK_AD_ci,
       dbo.UNVAN.AD                                                                   AS UNVAN_AD_ci,
       dbo.UretimBolgesi.AD                                                           AS URETIM_BOLGESI_AD_ci,
       dbo.UretimBolgesi.ACIKLAMA                                                     AS URETIM_BOLGESI_ACIKLAMA_ci,
       dbo.UZMANLIK_ALANI.AD                                                          AS UZMANLIK_ALANI_AD_ci,
       dbo.UZMANLIK_ALANI.ACIKLAMA                                                    AS UZMANLIK_ALANI_ACIKLAMA_ci,
       dbo.ONEMLI_KISI.AD                                                             AS ONEMLI_KISI_AD_ci,
       dbo.ONEMLI_KISI.ACIKLAMA                                                       AS ONEMLI_KISI_ACIKLAMA_ci,
       dbo.MESLEK.AD                                                                  AS MESLEK_AD_ci,
       dbo.MESLEK.ACIKLAMA                                                            AS MESLEK_ACIKLAMA_ci,
       dbo.KADRO_DURUM.AD                                                             AS KADRO_DURUM_AD_ci,
       dbo.TASINIR_MAL_YONETMELIGI_KOD.AD                                             AS TASINIR_MAL_YONETMELIGI_KOD_AD_ci,
       FORMAT(Omk_ESER.permanentId, 'OMK\.000\.000\.000')                             AS ESER_ESER_ID_ci,
       kondisyonDurumuAd.[AD]                                                         AS ESER_KONDISYON_DURUMU_AD_ci,
       sikkeDarpYonu.[AD]                                                             AS ESER_SIKKE_DARP_YONU_AD_ci,

       CASE WHEN dbo.Omk_ESER.file3DPath is not null THEN 1 ELSE 0 END                AS ESER_3D_FILE,
       dbo.Omk_ESER.webSite                                                           As ESER_WEB_SITESI_ci,
       convert(varchar(25), dbo.Omk_Workflow.dateCompleted, 121)                      AS ESER_ONAY_ZAMANI_ci,

       CASE
           WHEN convert(varchar(25), dbo.Omk_Workflow.dateModified, 120) >
                convert(varchar(25), dbo.Omk_ESER.DUZENLEME_ZAMANI, 120)
               THEN convert(varchar(25), dbo.Omk_Workflow.dateModified, 120)
           ELSE convert(varchar(25), dbo.Omk_ESER.DUZENLEME_ZAMANI, 120) END          AS ESER_GUNCELLEME_ZAMANI_ci,


       CASE
           WHEN
               (select count(Omk_Audit_Omk_ESER.ID) from Omk_Audit_Omk_ESER where Omk_Audit_Omk_ESER.ID = Omk_ESER.ID) >
               0 THEN 1
           ELSE 0 END                                                                 AS tarihsellik,


       STUFF((SELECT ',' + REPLACE(Keyword.AD, ',', ';')
              from Omk_Eser_Keyword,
                   Keyword
              where dbo.Omk_Eser_Keyword.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_Keyword.keywordId = dbo.Keyword.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_ANAHTAR_KELIME_AD_cim,

       STUFF((SELECT ',' + REPLACE(dbo.Omk_Transcription.[translation], ',', ';')
              from Omk_Transcription
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                 for xml path(''), type).value(N'.[1]', N'nvarchar(max)'), 1, 1,
             N'')                                                                     AS ESER_TRANSKRIPSIYON_METIN_CEVIRISI_cim,

       STUFF((SELECT ',' + REPLACE(Omk_ESER_FOTOGRAF.ACIKLAMA, ',', ';')
              from Omk_ESER_FOTOGRAF
              where dbo.Omk_ESER_FOTOGRAF.ESER_ID = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_FOTOGRAF_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.ad, ',', ';')
              from Omk_Eser_YayinLiteratur,
                   Literatur
              where dbo.Omk_Eser_YayinLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_YayinLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAYIN_AD_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.aciklama, ',', ';')
              from Omk_Eser_YayinLiteratur,
                   Literatur
              where dbo.Omk_Eser_YayinLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_YayinLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAYIN_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(YAZI_TIPI.AD, ',', ';')
              from Omk_Transcription,
                   YAZI_TIPI
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                AND dbo.Omk_Transcription.yaziTipi = dbo.YAZI_TIPI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAZI_TIPI_AD_cim,

       STUFF((SELECT ',' + REPLACE(YAZI_TIPI.ACIKLAMA, ',', ';')
              from Omk_Transcription,
                   YAZI_TIPI
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                AND dbo.Omk_Transcription.yaziTipi = dbo.YAZI_TIPI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAZI_TIPI_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(Measure.AD, ',', ';')
              from Omk_Eser_Measure,
                   Measure
              where dbo.Omk_Eser_Measure.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_Measure.measureId = dbo.Measure.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS OLCU_AD_cim,

       STUFF((SELECT ',' + REPLACE(Omk_Eser_Measure.DEGER, ',', ';')
              from Omk_Eser_Measure,
                   Measure
              where dbo.Omk_Eser_Measure.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_Measure.measureId = dbo.Measure.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS OLCU_DEGER_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.ad, ',', ';')
              from Omk_Eser_KaynakLiteratur,
                   Literatur
              where dbo.Omk_Eser_KaynakLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_KaynakLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAYNAK_AD_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.aciklama, ',', ';')
              from Omk_Eser_KaynakLiteratur,
                   Literatur
              where dbo.Omk_Eser_KaynakLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_KaynakLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAYNAK_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(IliskilendirmeTurGrubu.AD, ',', ';')
              from Iliskilendirme,
                   IliskilendirmeTur,
                   IliskilendirmeTurGrubu
              where dbo.Iliskilendirme.ID = dbo.Omk_ESER.iliskilendirmeId
                AND dbo.Iliskilendirme.turID = dbo.IliskilendirmeTur.ID
                AND dbo.IliskilendirmeTur.tur = dbo.IliskilendirmeTurGrubu.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS IliskilendirmeTurGrubu_AD_cim,

       STUFF((SELECT ',' + REPLACE(Omk_EserSerh.metin, ',', ';')
              from Omk_EserSerh
              where dbo.Omk_EserSerh.eserId = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS EserSerh_METIN_cim,

       STUFF((SELECT ',' + REPLACE(Atolye.AD, ',', ';')
              from Omk_Eser_Atolye,
                   Atolye
              where dbo.Omk_Eser_Atolye.eserId = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS Atolye_AD_cim,

       STUFF((SELECT ',' + REPLACE(YapimTeknigi.AD, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   YapimTeknigi
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_YAPIM_TEKNIGI_ID = dbo.YapimTeknigi.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_YAPIM_TEKNIGI_AD_cim,

       STUFF((SELECT ',' + REPLACE(YapimTeknigi.ACIKLAMA, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   YapimTeknigi
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_YAPIM_TEKNIGI_ID = dbo.YapimTeknigi.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_YAPIM_TEKNIGI_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
              from Malzeme,
                   Omk_ESER_MALZEME_YAPIM_TEKNIGI
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAPIM_TEKNIGI_MALZEME_cim,


       STUFF((SELECT ',' + REPLACE(SUSLEME_TEKNIGI.AD, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.SUSLEME_TEKNIGI_ID = dbo.SUSLEME_TEKNIGI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_AD_cim,

       STUFF((SELECT ',' + REPLACE(SUSLEME_TEKNIGI.ACIKLAMA, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.SUSLEME_TEKNIGI_ID = dbo.SUSLEME_TEKNIGI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(ESER_MALZEME_GRUBU.AD, ',', ';')
              from ESER_MALZEME_GRUBU,
                   Malzeme,
                   Omk_ESER_MALZEME_SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.ESER_MALZEME_GRUBU.ID = dbo.Malzeme.MALZEME_GRUBU_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_MALZEME_GRUBU_AD_cim,

       STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
              from Malzeme,
                   Omk_ESER_MALZEME_SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_MALZEME_cim,

       STUFF((SELECT ',' + REPLACE(Renk.ad, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_RENK_AD_cim,

       STUFF((SELECT ',' + REPLACE(Renk.cmyk, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_RENK_CMYK_cim,

       STUFF((SELECT ',' + REPLACE(Renk.NCS, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_RENK_NCS_cim,

       STUFF((SELECT ',' + REPLACE(Renk.RGB, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_RENK_RGB_cim,

       STUFF((SELECT ',' + REPLACE(Renk.mueskod, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS SUSLEME_TEKNIGI_RENK_MUESKOD_cim,

       STUFF((SELECT ',' + REPLACE(Renk.ad, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAPIM_TEKNIGI_RENK_AD_cim,

       STUFF((SELECT ',' + REPLACE(Renk.cmyk, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAPIM_TEKNIGI_RENK_CMYK_cim,

       STUFF((SELECT ',' + REPLACE(Renk.NCS, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAPIM_TEKNIGI_RENK_NCS_cim,

       STUFF((SELECT ',' + REPLACE(Renk.RGB, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAPIM_TEKNIGI_RENK_RGB_cim,

       STUFF((SELECT ',' + REPLACE(Renk.mueskod, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS YAPIM_TEKNIGI_RENK_MUESKOD_cim,

       STUFF((SELECT ',' + REPLACE(DIL.AD, ',', ';')
              from DIL,
                   Omk_Transcription
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                AND dbo.DIL.ID = dbo.Omk_Transcription.yaziDili
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS DIL_AD_cim,


       yazmaBasma.[AD]                                                                AS ESER_YAZMA_BASMA_SECIMI_AD_ci,

       islamiGayri.[AD]                                                               AS ESER_ISLAMI_GAYRI_SECIMI_AD_ci,

       elisiDokuma.[AD]                                                               AS ESER_ELISI_DOKUMA_SECIMI_AD_ci,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ARASTIRMA_ID, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ARASTIRMA_ID_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_KAZI_ID, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_KAZI_ID_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_MUZE_ID, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_MUZE_ID_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_MUZE_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_MUZE_AD_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_TESLIM_ALAN_PERSONEL_AD_SOYAD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
             '')                                                                      AS ESER_HAREKET_TESLIM_ALAN_PERSONEL_AD_SOYAD_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_TESLIM_EDEN_PERSONEL_AD_SOYAD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
             '')                                                                      AS ESER_HAREKET_TESLIM_EDEN_PERSONEL_AD_SOYAD_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ESER_GELIS_SEKLI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ESER_GELIS_SEKLI_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ENVANTER_NO, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ENVANTER_NO_cim,
       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_CIKARILMA_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_CIKARILMA_TARIHI_cim,
       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_ONAY_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ONAY_TARIHI_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ONAY_SAYISI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ONAY_SAYISI_cim,
       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_MUZEYE_GELIS_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
             '')                                                                      AS ESER_HAREKET_MUZEYE_GELIS_TARIHI_cim,

       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_ENVANTERE_ALINMA_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
             '')                                                                      AS ESER_HAREKET_ENVANTERE_ALINMA_TARIHI_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ACIKLAMA_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ELE_GECIRME_YERI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_ELE_GECIRME_YERI_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_IADE_EDEN_ULKE, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_IADE_EDEN_ULKE_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_AKTIF, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_AKTIF_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_SILINMIS, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ESER_HAREKET_SILINMIS_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_TUR_DEGER, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_TUR_DEGER_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_TUR_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_TUR_AD_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_AD_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_ACIKLAMA_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_IL_ID, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_IL_ID_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_ILCE_ID, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_ILCE_ID_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_ENLEM, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_ENLEM_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_BOYLAM, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_BOYLAM_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_ID, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ARASTIRMA_ID_cim,


       STUFF((SELECT ',' + REPLACE(ARASTIRMA_TUR_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ARASTIRMA_TUR_AD_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_TUR_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ARASTIRMA_TUR_ACIKLAMA_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ARASTIRMA_AD_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ARASTIRMA_ACIKLAMA_cim,


       STUFF((SELECT ',' + REPLACE(KAZI_BELDE_ADI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_BELDE_ADI_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_KAZI_KODU, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_KAZI_KODU_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_ILCE_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_ILCE_AD_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_IL_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS KAZI_IL_AD_cim,

       STUFF((SELECT ',' + REPLACE(Ulke_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS Ulke_AD_cim,

       STUFF((SELECT ',' + REPLACE(TUZEL_KISI_TICARI_UNVAN, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS TUZEL_KISI_TICARI_UNVAN_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ESER_GELIS_SEKLI_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
             '')                                                                      AS ESER_HAREKET_ESER_GELIS_SEKLI_AD_cim


FROM dbo.Omk_ESER

         LEFT JOIN dbo.Omk_EserDepo
                   ON dbo.Omk_EserDepo.artifactId = dbo.Omk_ESER.ID AND dbo.Omk_EserDepo.AKTIF = 'true' AND
                      dbo.Omk_EserDepo.SILINMIS = 'false'
         LEFT JOIN dbo.Omk_Alan_Konumu ON dbo.Omk_EserDepo.ALAN_KONUMU_ID = dbo.Omk_Alan_Konumu.ID
         LEFT JOIN dbo.ALAN_KONUMU_TUR ON dbo.Omk_Alan_Konumu.ALAN_KONUMU_TUR_ID = dbo.ALAN_KONUMU_TUR.ID
         LEFT JOIN dbo.Omk_Alan ON dbo.Omk_Alan_Konumu.ALAN_ID = dbo.Omk_Alan.ID
         LEFT JOIN dbo.Omk_ALAN_TUR ON dbo.Omk_ALAN_TUR.ID = dbo.Omk_Alan.ALAN_TUR_ID

         LEFT JOIN dbo.Omk_Bina ON dbo.Omk_Alan.BINA_ID = dbo.Omk_Bina.ID
         LEFT JOIN dbo.Omk_BagliBirim ON dbo.Omk_Bina.BAGLI_BIRIM_ID = dbo.Omk_BagliBirim.ID

         LEFT JOIN dbo.Omk_Workflow ON dbo.Omk_ESER.ID = dbo.Omk_Workflow.eserId
         LEFT JOIN dbo.Omk_Mudurluk ON dbo.Omk_Workflow.mudurlukId = dbo.Omk_Mudurluk.ID

         LEFT JOIN (SELECT MAX(ID) AS MaxID, artifactId
                    FROM dbo.Omk_PackArtifact
                    GROUP BY artifactId) max_packArtifact ON max_packArtifact.artifactId = dbo.Omk_ESER.ID
         LEFT JOIN dbo.Omk_PackArtifact ON dbo.Omk_PackArtifact.ID = max_packArtifact.MaxID
         LEFT JOIN dbo.Omk_Pack ON dbo.Omk_Pack.ID = dbo.Omk_PackArtifact.packId

         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.MUZE_MUDURLUGU.ID = dbo.Omk_Mudurluk.bagliMudurlukId

         LEFT JOIN dbo.IL ON dbo.IL.ID = dbo.Omk_BagliBirim.IL_ID
         LEFT JOIN dbo.ILCE ON dbo.ILCE.ID = dbo.Omk_BagliBirim.ILCE_ID


         LEFT JOIN dbo.Donem ON dbo.Omk_ESER.CAG_DONEM_ID = dbo.Donem.ID
         LEFT JOIN dbo.Cag ON dbo.Donem.cagId = dbo.Cag.ID
         LEFT JOIN dbo.KRONOLOJI ON dbo.CAG.KRONOLOJI_ID = dbo.KRONOLOJI.ID

         LEFT JOIN dbo.UYGARLIK ON dbo.Omk_ESER.UYGARLIK_ID = dbo.UYGARLIK.ID
         LEFT JOIN dbo.HUKUMDAR ON dbo.Omk_ESER.HUKUMDAR_ID = dbo.HUKUMDAR.ID
         LEFT JOIN dbo.UretimYeri ON dbo.Omk_ESER.uretimYeriId = dbo.UretimYeri.ID
         LEFT JOIN dbo.Iliskilendirme ON dbo.Omk_ESER.iliskilendirmeId = dbo.Iliskilendirme.ID
         LEFT JOIN dbo.Omk_Kullanici ON dbo.Omk_ESER.YARATMA_KULLANICI_ID = dbo.Omk_Kullanici.ID

         LEFT JOIN dbo.ESER_ALT_TUR ON dbo.Omk_ESER.ESER_ALT_TUR_ID = dbo.ESER_ALT_TUR.ID
         LEFT JOIN dbo.ESER_TUR ON dbo.ESER_ALT_TUR.ESER_TUR_ID = dbo.ESER_TUR.ID
         LEFT JOIN dbo.TASINIR_MAL_YONETMELIGI_KOD
                   ON dbo.Omk_ESER.TASINIR_MAL_YON_ID = dbo.TASINIR_MAL_YONETMELIGI_KOD.ID
         LEFT JOIN dbo.PERSONEL ON dbo.Omk_Kullanici.PERSONEL_ID = dbo.PERSONEL.ID
         LEFT JOIN dbo.UNVAN ON dbo.PERSONEL.unvan = dbo.UNVAN.ID
         LEFT JOIN dbo.UretimBolgesi ON dbo.Omk_ESER.uretimBolgesiId = dbo.UretimBolgesi.ID
         LEFT JOIN dbo.UZMANLIK_ALANI ON dbo.PERSONEL.UZMANLIK_ALANI_ID = dbo.UZMANLIK_ALANI.ID
         LEFT JOIN dbo.ONEMLI_KISI ON dbo.Omk_ESER.ESERI_KULLANAN_ONEMLI_KISI_ID = dbo.ONEMLI_KISI.ID
         LEFT JOIN dbo.MESLEK ON dbo.PERSONEL.MESLEK_ID = dbo.MESLEK.ID
         LEFT JOIN dbo.KADRO_DURUM ON dbo.KADRO_DURUM.ID = dbo.PERSONEL.kadroDurum

         LEFT JOIN Pick islamiGayri ON islamiGayri.ID = dbo.Omk_ESER.ISLAMI_GAYRI_SECIMI
         LEFT JOIN Pick elisiDokuma ON elisiDokuma.ID = dbo.Omk_ESER.ELISI_DOKUMA_SECIMI
         LEFT JOIN Pick sikkeDarpYonu ON sikkeDarpYonu.ID = dbo.Omk_ESER.SIKKE_DARP_YONU
         LEFT JOIN Pick kondisyonDurumuAd ON dbo.Omk_ESER.KONDISYON_DURUMU = kondisyonDurumuAd.ID
         LEFT JOIN Pick yazmaBasma ON yazmaBasma.ID = dbo.Omk_ESER.YAZMA_BASMA_SECIMI

         LEFT JOIN dbo.DarpYeri ON dbo.Omk_ESER.darpYeriId = dbo.DarpYeri.ID

WHERE dbo.Omk_ESER.permanentId IS NOT NULL;