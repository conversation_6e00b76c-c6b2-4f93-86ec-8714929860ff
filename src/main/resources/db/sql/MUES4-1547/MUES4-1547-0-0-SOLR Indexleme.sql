-- BACKUP ALIYORUZ
-- ESER_Tumbilgiler
IF OBJECT_ID('dbo.ESER_Tumbilgiler_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.ESER_Tumbilgiler', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.ESER_Tumbilgiler', 'ESER_Tumbilgiler_20250418';
END

-- Kam_AbroadArtifact_VIEW
IF OBJECT_ID('dbo.Kam_AbroadArtifact_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_AbroadArtifact_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_AbroadArtifact_VIEW', 'Kam_AbroadArtifact_VIEW_20250418';
END

-- Kam_CulturalPropertySmuggling_VIEW
IF OBJECT_ID('dbo.Kam_CulturalPropertySmuggling_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_CulturalPropertySmuggling_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_CulturalPropertySmuggling_VIEW', 'Kam_CulturalPropertySmuggling_VIEW_20250418';
END

-- Kam_ForeignCountryArtifact_VIEW
IF OBJECT_ID('dbo.Kam_ForeignCountryArtifact_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_ForeignCountryArtifact_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_ForeignCountryArtifact_VIEW', 'Kam_ForeignCountryArtifact_VIEW_20250418';
END

-- Kam_IllegalExcavation_VIEW
IF OBJECT_ID('dbo.Kam_IllegalExcavation_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_IllegalExcavation_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_IllegalExcavation_VIEW', 'Kam_IllegalExcavation_VIEW_20250418';
END

-- Kam_REQUEST_VIEW
IF OBJECT_ID('dbo.Kam_REQUEST_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_REQUEST_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_REQUEST_VIEW', 'Kam_REQUEST_VIEW_20250418';
END

-- Kam_StolenArtifact_VIEW
IF OBJECT_ID('dbo.Kam_StolenArtifact_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_StolenArtifact_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_StolenArtifact_VIEW', 'Kam_StolenArtifact_VIEW_20250418';
END

-- Kam_TREASUREREQUEST_VIEW
IF OBJECT_ID('dbo.Kam_TREASUREREQUEST_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Kam_TREASUREREQUEST_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Kam_TREASUREREQUEST_VIEW', 'Kam_TREASUREREQUEST_VIEW_20250418';
END

-- LAB_ARTIFACT_VIEW
IF OBJECT_ID('dbo.LAB_ARTIFACT_VIEW_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.LAB_ARTIFACT_VIEW', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.LAB_ARTIFACT_VIEW', 'LAB_ARTIFACT_VIEW_20250418';
END

-- MBS_tumbilgiler
IF OBJECT_ID('dbo.MBS_tumbilgiler_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.MBS_tumbilgiler', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.MBS_tumbilgiler', 'MBS_tumbilgiler_20250418';
END

-- Omk_Eser_tumbilgiler
IF OBJECT_ID('dbo.Omk_Eser_tumbilgiler_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.Omk_Eser_tumbilgiler', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.Omk_Eser_tumbilgiler', 'Omk_Eser_tumbilgiler_20250418';
END

-- PERSONEL_tumbilgiler
IF OBJECT_ID('dbo.PERSONEL_tumbilgiler_20250418', 'V') IS NULL
BEGIN
    IF OBJECT_ID('dbo.PERSONEL_tumbilgiler', 'V') IS NOT NULL
        EXEC sp_rename 'dbo.PERSONEL_tumbilgiler', 'PERSONEL_tumbilgiler_20250418';
END
