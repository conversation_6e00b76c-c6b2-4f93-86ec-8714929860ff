-- ////////////
IF OBJECT_ID('dbo.KAM_ILLEGALEXCAVATION_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_ILLEGALEXCAVATION_VIEW;

CREATE VIEW KAM_ILLEGALEXCAVATION_VIEW AS
SELECT DISTINCT FORMAT(dbo.Kam_IllegalExcavation.ID, 'KK\.000\.000\.000') AS id_ci,
                dbo.Kam_IllegalExcavation.ID                              AS uid,
                dbo.Kam_IllegalExcavation.AKTIF                           AS aktif,
                dbo.Kam_IllegalExcavation.SILINMIS                        AS silinmis,
                dbo.Kam_IllegalExcavation.aciklama                        AS aciklama_ci,
                dbo.Kam_IllegalExcavation.dateCreated                     AS olusturmaZamani,
                dbo.Kam_IllegalExcavation.dateUpdated                     AS duzenlemeZamani,
                dbo.Kam_IllegalExcavation.numberOfArtifacts               AS artifactCount,


                createdBy.AD + ' ' + createdBy.SOYAD                      AS olusturanKullaniciAd_ci,
                updatedBy.AD + ' ' + updatedBy.SOYAD                      AS sonDuzenleyenKullaniciAd_ci,

                dbo.Kam_IncidentLocation.[ad]                             AS olayAdi_ci,
                dbo.Kam_IncidentLocation.[ada]                            AS olayAda,
                dbo.Kam_IncidentLocation.beldeAdi                         AS beldeAdi_ci,
                dbo.Kam_IncidentLocation.ENLEM                            AS enlem_ci,
                dbo.Kam_IncidentLocation.BOYLAM                           AS boylam_ci,
                dbo.Kam_IncidentLocation.pafta                            AS olayPafta_ci,
                dbo.Kam_IncidentLocation.parsel                           AS olayParsel,
                dbo.Kam_IncidentLocation.aciklama                         AS olayYeriAciklama_ci,

                dbo.Kam_IllegalExcavation.eventOccurrenceDate             AS olayTarihi,
                dbo.Kam_IllegalExcavation.areaName                        AS areaName_ci,
                dbo.Kam_IllegalExcavation.receiptPath                     AS raporPath_ci,
                dbo.Kam_IllegalExcavation.involvedPersNum                 AS olayaKarisanKisiSayisi,
                dbo.Kam_IllegalExcavation.rescueExcavationNeed            AS rescueExcavationNeed,


                dbo.Kam_IllegalExcavation.institutionLossExist            AS institutionLossExist,
                dbo.Kam_IllegalExcavation.protectionReportNeeded          AS protectionReportNeeded,
                dbo.Kam_IllegalExcavation.institutionLossAmount           AS institutionLossAmount,
                dbo.Kam_IllegalExcavation.excavationPitsNum               AS excavationPitsNum,
                dbo.Kam_IllegalExcavation.detectorUsage,

                dbo.Kam_IllegalExcavation.archiveData                     as archive,
                dbo.Kam_IllegalExcavation.completedProcess                as completedProcess,
                dbo.IL.AD                                                 AS ilAd_ci,
                dbo.ILCE.AD                                               AS ilceAd_ci,

                STUFF((SELECT DISTINCT SAHIS.AD + ' ' + SAHIS.SOYAD + '##'
                       from dbo.Kam_IllegalExcavation_Sahis,
                            dbo.SAHIS
                       where dbo.Kam_IllegalExcavation_Sahis.illegalExcavationId = dbo.Kam_IllegalExcavation.ID
                         AND dbo.Kam_IllegalExcavation_Sahis.sahisId = dbo.SAHIS.ID
                          for xml path(''), type)
                          .value('.', 'varchar(max)'), 1, 1, '')          AS olaySahisAds_cim,

                excavationAreaStatus.AD                                   AS excavationAreaStatusName_ci,
                eventDetectionType.AD                                     AS eventDetectionTypeName_ci,
                legalActionStatus.AD                                      AS legalActionStatusName_ci,


                'Kaçak Kazı'                                              AS incidentType_ci,
                dbo.MUZE_MUDURLUGU.AD                                     AS muzeMudurluguAd_ci

FROM dbo.Kam_IllegalExcavation
         LEFT JOIN dbo.Kam_IllegalExcavationPhoto
                   ON dbo.Kam_IllegalExcavationPhoto.illegalExcavationID = dbo.Kam_IllegalExcavation.ID
         LEFT JOIN dbo.Kam_IncidentLocation ON dbo.Kam_IllegalExcavation.incidentId = dbo.Kam_IncidentLocation.ID
         LEFT JOIN dbo.IL ON dbo.Kam_IncidentLocation.IL_ID = dbo.IL.ID
         LEFT JOIN dbo.ILCE ON dbo.Kam_IncidentLocation.ILCE_ID = dbo.ILCE.ID
         LEFT JOIN dbo.Kam_Pick AS excavationAreaStatus
                   ON dbo.Kam_IllegalExcavation.excavationAreaStatus = excavationAreaStatus.ID
         LEFT JOIN dbo.Kam_Pick AS eventDetectionType
                   ON dbo.Kam_IllegalExcavation.excavationAreaStatus = eventDetectionType.ID
         LEFT JOIN dbo.Kam_Pick AS legalActionStatus
                   ON dbo.Kam_IllegalExcavation.legalActionStatus = eventDetectionType.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Kam_IllegalExcavation.directorateId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.PERSONEL createdBy ON dbo.Kam_IllegalExcavation.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL updatedBy ON dbo.Kam_IllegalExcavation.createdBy = updatedBy.ID

WHERE dbo.Kam_IllegalExcavation.updateInProgress = 0;