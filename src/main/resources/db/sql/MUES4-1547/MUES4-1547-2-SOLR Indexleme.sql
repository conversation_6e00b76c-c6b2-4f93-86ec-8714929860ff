-- ////////////
IF OBJECT_ID('dbo.KAM_ABROADARTIFACT_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_ABROADARTIFACT_VIEW;

CREATE VIEW KAM_ABROADARTIFACT_VIEW AS
SELECT FORMAT(dbo.Kam_AbroadArtifact.ID, 'YDK\.000\.000\.000') AS id_ci,
       dbo.Kam_AbroadArtifact.ID                               AS uid,
       dbo.Kam_AbroadArtifact.dateCreated                      AS olusturmaZamani,
       dbo.Kam_AbroadArtifact.dateUpdated                      AS duzenlemeZamani,
       dbo.Kam_AbroadArtifact.detectionDate                    AS olayTarihi,
       dbo.Kam_AbroadArtifact.isReturned                       AS isReturned,
       dbo.Kam_AbroadArtifact.AKTIF                            AS aktif,
       dbo.Kam_AbroadArtifact.SILINMIS                         AS silinmis,
       dbo.Kam_AbroadArtifact.ACIKLAMA                         AS aciklama_ci,
       dbo.Kam_AbroadArtifact.archiveData                      AS archive,
       ISNULL(dbo.Kam_AbroadArtifact.completedProcess, 0)      AS completedProcess,
       dbo.MUZE_MUDURLUGU.AD                                   AS muzeMudurluguAd_ci,
       createdBy.AD + ' ' + createdBy.SOYAD                    AS olusturanKullaniciAd_ci,
       updatedBy.AD + ' ' + updatedBy.SOYAD                    AS sonDuzenleyenKullaniciAd_ci,
       gorevliPersonel.AD + ' ' + gorevliPersonel.SOYAD        AS gorevliPersonelAds_ci,
       dbo.IL.AD                                               AS ilAd_ci,
       dbo.ILCE.AD                                             AS ilceAd_ci,
       'Yurt Dışına Kaçırılmış Eser'                           AS incidentType_ci,
       dbo.Kam_AbroadArtifactPhotograph.ACIKLAMA               AS fotografAciklama_ci,
       dbo.Kam_AbroadArtifactPhotograph.fotografPath           AS fotografPath_ci,
       dbo.Kam_AbroadArtifactPhotograph.AD                     AS fotografBasligi_ci,
       dbo.Ulke.AD                                             AS ulkeAd_ci,
       dbo.Kam_AbroadArtifact.detectionDate                    AS detectionDate,
       detectionType.AD                                        AS detectionTypeAd_ci,
       returnType.AD                                           AS returnTypeAd_ci,
       detectionPlace.AD                                       AS tespitYeriAd_ci,
       Kam_AbroadMuseum.name                                   as tespitYeriAltTurAd_ci,
       dbo.Kam_AbroadArtifact.isSued,
       dbo.Kam_AbroadArtifact.isMadeExpense,
       dbo.Kam_AbroadArtifact.transportExpense,
       dbo.Kam_AbroadArtifact.attorneyExpense,
       ISNULL(dbo.Kam_AbroadArtifact.totalArtifactCount, 0)    AS totalArtifactCount,
       ISNULL(dbo.Kam_AbroadArtifact.returnedArtifactCount, 0) AS returnedArtifactCount

FROM dbo.Kam_AbroadArtifact
         LEFT JOIN dbo.PERSONEL AS createdBy ON dbo.Kam_AbroadArtifact.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL AS updatedBy ON dbo.Kam_AbroadArtifact.updatedBy = updatedBy.ID
         LEFT JOIN dbo.PERSONEL AS gorevliPersonel ON dbo.Kam_AbroadArtifact.followingPersonnel = gorevliPersonel.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Kam_AbroadArtifact.directorateId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.IL ON dbo.MUZE_MUDURLUGU.IL_ID = dbo.IL.ID
         LEFT JOIN dbo.ILCE ON dbo.MUZE_MUDURLUGU.ILCE_ID = dbo.ILCE.ID
         LEFT JOIN dbo.Kam_AbroadArtifactPhotograph
                   ON dbo.Kam_AbroadArtifactPhotograph.abroadArtifact = dbo.Kam_AbroadArtifact.ID AND
                      dbo.Kam_AbroadArtifactPhotograph.ID = (SELECT TOP 1 ID
                                                             FROM Kam_AbroadArtifactPhotograph
                                                             WHERE dbo.Kam_AbroadArtifactPhotograph.SILINMIS = 0
                                                               AND dbo.Kam_AbroadArtifactPhotograph.AKTIF = 1
                                                               AND dbo.Kam_AbroadArtifactPhotograph.abroadArtifact =
                                                                   dbo.Kam_AbroadArtifact.ID)
         LEFT JOIN dbo.Ulke ON dbo.Ulke.ID = dbo.Kam_AbroadArtifact.countryId
         LEFT JOIN dbo.Kam_Pick AS detectionType ON dbo.Kam_AbroadArtifact.detectionType = detectionType.ID
         LEFT JOIN dbo.Kam_Pick AS detectionPlace ON dbo.Kam_AbroadArtifact.detectedPlace = detectionPlace.ID
         LEFT JOIN dbo.Kam_Pick AS returnType ON dbo.Kam_AbroadArtifact.returnType = returnType.ID
         LEFT JOIN dbo.Kam_AbroadMuseum ON dbo.Kam_AbroadArtifact.abroadMuseumId = Kam_AbroadMuseum.ID
WHERE dbo.Kam_AbroadArtifact.updateInProgress = 0;