-- ////////////
IF OBJECT_ID('dbo.KAM_FOREIGNCOUNTRYARTIFACT_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_FOREIGNCOUNTRYARTIFACT_VIEW;

CREATE VIEW KAM_FOREIGNCOUNTRYARTIFACT_VIEW AS
SELECT FORMAT(dbo.Kam_ForeignCountryArtifact.ID, 'TTE\.000\.000\.000') AS id_ci,
       dbo.Kam_ForeignCountryArtifact.ID                               AS uid,
       dbo.Kam_ForeignCountryArtifact.dateCreated                      AS olusturmaZamani,
       dbo.Kam_ForeignCountryArtifact.dateUpdated                      AS duzenlemeZamani,
       dbo.Kam_ForeignCountryArtifact.reportDate                       AS olayTarihi,
       dbo.Kam_ForeignCountryArtifact.isReturned                       AS isReturned,
       dbo.Kam_ForeignCountryArtifact.AKTIF                            AS aktif,
       dbo.Kam_ForeignCountryArtifact.SILINMIS                         AS silinmis,
       dbo.Kam_ForeignCountryArtifact.ACIKLAMA                         AS aciklama_ci,
       dbo.Kam_ForeignCountryArtifact.archiveData                      AS archive,
       dbo.Kam_ForeignCountryArtifact.completedProcess                 AS completedProcess,
       dbo.Kam_ForeignCountryArtifact.returnDate                       AS geriGonderilmeTarihi,
       dbo.Kam_ForeignCountryArtifact.pdfPath                          AS raporPath_ci,
       dbo.MUZE_MUDURLUGU.AD                                           AS muzeMudurluguAd_ci,
       createdBy.AD + ' ' + createdBy.SOYAD                            AS olusturanKullaniciAd_ci,
       updatedBy.AD + ' ' + updatedBy.SOYAD                            AS sonDuzenleyenKullaniciAd_ci,
       dbo.IL.AD                                                       AS ilAd_ci,
       dbo.ILCE.AD                                                     AS ilceAd_ci,
       'Yabancı Ülke Eseri'                                            AS incidentType_ci,
       dbo.Kam_ForeignCountryArtifactPhoto.ACIKLAMA                    AS fotografAciklama_ci,
       dbo.Kam_ForeignCountryArtifactPhoto.fotografPath                AS fotografPath_ci,
       dbo.Kam_ForeignCountryArtifactPhoto.AD                          AS fotografBasligi_ci,
       (select sum(CASE
                       WHEN Kam_ForeignCountryArtifactDetail.artifactCount is null THEN 1
                       ELSE Kam_ForeignCountryArtifactDetail.artifactCount END)
        from dbo.Kam_ForeignCountryArtifactDetail
        where Kam_ForeignCountryArtifactDetail.foreignCountryArtifactId = dbo.Kam_ForeignCountryArtifact.ID
          AND Kam_ForeignCountryArtifactDetail.isForged = 0)           AS totalRealArtifactCount,
       (select sum(CASE
                       WHEN Kam_ForeignCountryArtifactDetail.artifactCount is null THEN 1
                       ELSE Kam_ForeignCountryArtifactDetail.artifactCount END)
        from dbo.Kam_ForeignCountryArtifactDetail
        where Kam_ForeignCountryArtifactDetail.foreignCountryArtifactId = dbo.Kam_ForeignCountryArtifact.ID
          AND Kam_ForeignCountryArtifactDetail.isForged = 1)           AS totalForgeArtifactCount,
       (select sum(CASE
                       WHEN Kam_ForeignCountryArtifactDetail.artifactCount is null THEN 1
                       ELSE Kam_ForeignCountryArtifactDetail.artifactCount END)
        from dbo.Kam_ForeignCountryArtifactDetail
        where Kam_ForeignCountryArtifactDetail.foreignCountryArtifactId =
              dbo.Kam_ForeignCountryArtifact.ID)                       AS artifactCount,
       dbo.Ulke.AD                                                     AS ulkeAd_ci,


       captureType.AD                                                  AS captureTypeAd_ci

FROM dbo.Kam_ForeignCountryArtifact
         LEFT JOIN dbo.PERSONEL AS createdBy ON dbo.Kam_ForeignCountryArtifact.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL AS updatedBy ON dbo.Kam_ForeignCountryArtifact.updatedBy = updatedBy.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Kam_ForeignCountryArtifact.mudurlukId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.IL ON dbo.Kam_ForeignCountryArtifact.provinceId = dbo.IL.ID
         LEFT JOIN dbo.ILCE ON dbo.Kam_ForeignCountryArtifact.countyId = dbo.ILCE.ID
         LEFT JOIN dbo.Kam_ForeignCountryArtifactPhoto ON dbo.Kam_ForeignCountryArtifactPhoto.foreignCountryArtifactId =
                                                          dbo.Kam_ForeignCountryArtifact.ID AND
                                                          dbo.Kam_ForeignCountryArtifactPhoto.ID = (SELECT TOP 1 ID
                                                                                                    FROM Kam_ForeignCountryArtifactPhoto
                                                                                                    WHERE dbo.Kam_ForeignCountryArtifactPhoto.SILINMIS = 0
                                                                                                      AND dbo.Kam_ForeignCountryArtifactPhoto.AKTIF = 1
                                                                                                      AND
                                                                                                        dbo.Kam_ForeignCountryArtifactPhoto.foreignCountryArtifactId =
                                                                                                        dbo.Kam_ForeignCountryArtifact.ID)
         LEFT JOIN dbo.Ulke ON dbo.Ulke.ID = dbo.Kam_ForeignCountryArtifact.ulkeId
         LEFT JOIN dbo.Kam_Pick AS captureType ON dbo.Kam_ForeignCountryArtifact.captureType = captureType.ID
WHERE dbo.Kam_ForeignCountryArtifact.updateInProgress = 0;