-- ////////////
IF OBJECT_ID('dbo.MBS_TUMBILGILER', 'V') IS NOT NULL
DROP VIEW dbo.MBS_TUMBILGILER;

CREATE VIEW MBS_TUMBILGILER AS
select distinct MUZE_MUDURLUGU.ID                                                    as id,
                IL.AD                                                                as muzeIlAd_ci,
                MUZE_MUDURLUGU.FOTOGRAF_PATH                                         as muzeFotograf_ci,
                MUZE_MUDURLUGU.KOD                                                   as muzeKodu_ci,
                Mbs_General_Information.ACIKLAMA                                     as muzeAciklama_ci,
                museumClosingDate                                                    as muzeKapanmaTarihi,
                museumClosingReason                                                  as muzeKapanmaSebebi_ci,
                museumCommissioningDate                                              as muzeHizmeteGirisTarihi,
                museumEstimatedOpeningDate                                           as muzeTahminiAcilisTarihi,
                museumHistory                                                        as muzeTarihcesi_ci,
                museumManagerTelephone                                               as muzeTelefonu_ci,
                museumNote                                                           as muzeNotlari_ci,
                needClosedStoreArea                                                  as muzeKapaliDepo<PERSON>lan<PERSON>htiya<PERSON>,
                needClosedStoreCount                                                 as muzeKapaliDepoIhtiyacSayisi,
                needDisplayArea                                                      as muzeTeshirAlanIhtiyaci,
                needDisplayCount                                                     as muzeTeshirAlaniIhtiyacSayisi,
                needOpenStoreArea                                                    as muzeAcikDepoAlanIhtiyaci,
                needOpenStoreCount                                                   as muzeAcikDepoIhtiyacSayisi,
                newBuildingNeedArea                                                  as muzeYeniBinaIhtiyacSayisi,
                totalBookRegistered                                                  as muzeKayitliKitapSayisi,
                totalBookSurplus                                                     as muzeIhtiyacFazlasiKitapSayisi,
                totalClosedDisplayArea                                               as muzeToplamKapaliTeshirAlani,
                totalClosedStoreArea                                                 as muzeToplamKapaliDepoAlani,
                totalClosedStoreCount                                                as muzeToplamKapaliDepoSayisi,
                totalDisplayArea                                                     as muzeToplamTeshirAlani,
                totalDisplayCount                                                    as muzeToplamTeshirAlaniSayisi,
                totalLegislationBook                                                 as muzeMevzuatKitapSayisi,
                totalMuseumArea                                                      as muzeToplamAlani,
                totalOpenDisplayArea                                                 as muzeToplamAcikTeshirAlani,
                totalOpenStoreArea                                                   as muzeToplamAcikDepoAlani,
                totalOpenStoreCount                                                  as muzeToplamAcikDepoAlaniSayisi,
                totalSpecializedBook                                                 as muzeToplamIhtisasKitapSayisi,
                totalStoreArea                                                       as muzeToplamDepoAlani,
                totalStoreCount                                                      as muzeToplamDepoAlaniSayisi,
                virtualMuseumApplicationAddress                                      as muzeSanalUygulamaAdresi_ci,

                arkeoPark.AD                                                         as muzeArkeopark_ci,
                babyCareRoom.AD                                                      as muzeBebekBakimOdasi_ci,
                brailleArtifactLabel.AD                                              as muzeBrailleEserEtiketi_ci,
                cafeteria.AD                                                         as muzeKafeterya_ci,
                disabledLift.AD                                                      as muzeEngelliAsansoru_ci,
                disabledOtopark.AD                                                   as muzeEngelliOtoparki_ci,
                disabledRamp.AD                                                      as muzeEngelliRampasi_ci,
                gamePlace.AD                                                         as muzeOyunAlani_ci,
                meetingRoom.AD                                                       as muzeToplantiOdasi_ci,
                museumGuidanceService.AD                                             as muzeRehberlikHizmeti_ci,
                otopark.AD                                                           as muzeOtopark_ci,
                parkingGarage.AD                                                     as muzeKapaliOtopark_ci,
                restaurant.AD                                                        as muzeRestorant,
                restaurant.AD                                                        as muzeRestorant_ci,
                voiceGuidanceService.AD                                              as muzeSesliRehberlik_ci,
                wc.AD                                                                as muzeWc_ci,
                lift.AD                                                              as yukAsansoru_ci,
                elevator.AD                                                          as asansor_ci,
                b.ada                                                                as muzeBinaAda,
                b.pafta                                                              as muzeBinaPafta_ci,
                b.parsel                                                             as muzeBinaParsel,
                b.enlem                                                              as muzeBinaEnlem_ci,
                b.boylam                                                             as muzeBinaBoylam_ci,

                MUZE_MUDURLUGU.AD                                                    as muzeMudurluguAd_ci,
                PERSONEL.AD + ' ' + PERSONEL.SOYAD                                   as muzeMuduruAd_ci,
                MUZE_MUDURLUGU.ID                                                    as muzeMudurluguId,
                MUZE_MUDURLUGU.AKTIF                                                 as muzeAktif,
                MUZE_MUDURLUGU.SILINMIS                                              as muzeSilinmis,
                MUZE_MUDURLUGU.Enlem + ' ' + MUZE_MUDURLUGU.Boylam                   as muzeMudurluguKoordinat_ci,
                MUZE_MUDURLUGU.totalArtifactCount                                    as muzeToplamEserSayisi,
                MUZE_MUDURLUGU.dateUpdated                                           as dateUpdated,

                STUFF((SELECT ',' + REPLACE(Mbs_Activity.AD, ',', ';')
                       from Mbs_Activity
                       where Mbs_Activity.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as aktiviteAd_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Allocation_Facility.allocationAreaName, ',', ';')
                       from Mbs_Allocation_Facility
                       where Mbs_Allocation_Facility.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as tahsisAlanAd_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Allocation_Facility,
                            Pick
                       where Mbs_Allocation_Facility.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Allocation_Facility.allocationType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as tahsisTipi_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Allocation_Facility,
                            Pick
                       where Mbs_Allocation_Facility.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Allocation_Facility.allocationStatus = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as tahsisDurumu_cim,
                STUFF((SELECT ',' + REPLACE(ALAN.AD, ',', ';')
                       from Mbs_Area_Status,
                            ALAN,
                            BINA,
                            BAGLI_BIRIM,
                            MUZE_MUDURLUGU
                       where MUZE_MUDURLUGU.ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Area_Status.ALAN_ID = ALAN.ID
                         AND BINA.ID = ALAN.BINA_ID
                         AND BAGLI_BIRIM.ID = BINA.BAGLI_BIRIM_ID
                         AND MUZE_MUDURLUGU.ID = BAGLI_BIRIM.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as alanDurumuAlanAd_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Area_Status,
                            Pick,
                            ALAN,
                            BINA,
                            BAGLI_BIRIM,
                            MUZE_MUDURLUGU
                       where MUZE_MUDURLUGU.ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Area_Status.moistureReader = Pick.ID
                         AND Mbs_Area_Status.ALAN_ID = ALAN.ID
                         AND BINA.ID = ALAN.BINA_ID
                         AND BAGLI_BIRIM.ID = BINA.BAGLI_BIRIM_ID
                         AND MUZE_MUDURLUGU.ID = BAGLI_BIRIM.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as nemOkuyucu_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Area_Status,
                            Pick,
                            ALAN,
                            BINA,
                            BAGLI_BIRIM,
                            MUZE_MUDURLUGU
                       where MUZE_MUDURLUGU.ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Area_Status.thermometerReader = Pick.ID
                         AND Mbs_Area_Status.ALAN_ID = ALAN.ID
                         AND BINA.ID = ALAN.BINA_ID
                         AND BAGLI_BIRIM.ID = BINA.BAGLI_BIRIM_ID
                         AND MUZE_MUDURLUGU.ID = BAGLI_BIRIM.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as isiOkuyucu_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Area_Status,
                            Pick,
                            ALAN,
                            BINA,
                            BAGLI_BIRIM,
                            MUZE_MUDURLUGU
                       where MUZE_MUDURLUGU.ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Area_Status.renewalNeed = Pick.ID
                         AND Mbs_Area_Status.ALAN_ID = ALAN.ID
                         AND BINA.ID = ALAN.BINA_ID
                         AND BAGLI_BIRIM.ID = BINA.BAGLI_BIRIM_ID
                         AND MUZE_MUDURLUGU.ID = BAGLI_BIRIM.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as yenilenmeIhtiyaci_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Artifact_Moulage_Information,
                            Pick
                       where Mbs_Artifact_Moulage_Information.MUZE_MUDURLUGU_ID =
                             Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Artifact_Moulage_Information.MULAJ_TEKNIGI = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as mulajTeknigi_cim,
--STUFF( (SELECT ',' +   REPLACE(Mbs_Artifact_Moulage_Information.eserId, ',', ';') from Mbs_Artifact_Moulage_Information where Mbs_Artifact_Moulage_Information.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as mulajEserId,

                STUFF((SELECT ',' + REPLACE(Mbs_Display_Information.AD, ',', ';')
                       from Mbs_Display_Information
                       where Mbs_Display_Information.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as teshirAd_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Display_Information.story, ',', ';')
                       from Mbs_Display_Information
                       where Mbs_Display_Information.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as teshirHikayesi_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Energy_Source,
                            Pick
                       where Mbs_Energy_Source.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Energy_Source.energySource = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as enerjiKaynagiAd_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Equipment_Status,
                            Pick
                       where Mbs_Equipment_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Equipment_Status.equipmentType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as techizatTipi_cim,
--STUFF( (SELECT ',' +   REPLACE(Mbs_Equipment_Status.needNumberEquipment, ',', ';') from Mbs_Equipment_Status where Mbs_Equipment_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as TECHIZAT_IHTIYAC_SAYISI,
--STUFF( (SELECT ',' +   REPLACE(Mbs_Equipment_Status.numberEquipment, ',', ';') from Mbs_Equipment_Status where Mbs_Equipment_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as TECHIZAT_SAYISI,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Museum_Expense,
                            Pick
                       where Mbs_Museum_Expense.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Museum_Expense.expenseType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as muzeGiderTipi_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Museum_Suggestion.AD, ',', ';')
                       from Mbs_Museum_Suggestion
                       where Mbs_Museum_Suggestion.generalInformation = Mbs_General_Information.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as muzeOneriAd_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Museum_Suggestion.directions, ',', ';')
                       from Mbs_Museum_Suggestion
                       where Mbs_Museum_Suggestion.generalInformation = Mbs_General_Information.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as muzeOneriTarif_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Sponsor_Volunteer.AD, ',', ';')
                       from Mbs_Sponsor_Volunteer
                       where Mbs_Sponsor_Volunteer.generalInformation = Mbs_General_Information.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as sponsorGonulluAd_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Sponsor_Volunteer,
                            Pick
                       where Mbs_Sponsor_Volunteer.generalInformation = Mbs_General_Information.ID
                         AND Mbs_Sponsor_Volunteer.svType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as sponsorGonulluTipi_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Social_Media.AD, ',', ';')
                       from Mbs_Social_Media
                       where Mbs_Social_Media.generalInformation = Mbs_General_Information.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as sosyalmedyaAd_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Social_Media.address, ',', ';')
                       from Mbs_Social_Media
                       where Mbs_Social_Media.generalInformation = Mbs_General_Information.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as sosyalmedyaAdres_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Security_Status,
                            Pick
                       where Mbs_Security_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Security_Status.securityEquipmentType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as guvenlikEkipmanTipi_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Security_Status,
                            Pick
                       where Mbs_Security_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Security_Status.securitySystemGeneralStatus = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as guvenlikGenelDurum_cim,
--STUFF( (SELECT ',' +   REPLACE(Mbs_Security_Status.count, ',', ';') from Mbs_Security_Status where Mbs_Security_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as GUVENLIK_EKIPMAN_SAYISI,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Team_List,
                            Pick
                       where Mbs_Team_List.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Team_List.teamType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as ekipTipi_cim,
                STUFF((SELECT ',' + REPLACE(PERSONEL.AD + ' ' + ISNULL(PERSONEL.SOYAD, ''), ',', ';')
                       from PERSONEL,
                            Mbs_Team_List
                       where Mbs_Team_List.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Team_List.PERSONEL_ID = PERSONEL.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as ekipPersonelAd_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Temporary_Exhibition,
                            Pick
                       where Mbs_Temporary_Exhibition.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Temporary_Exhibition.exhibitionType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as geciciSergiTipi_cim,
                STUFF((SELECT ',' + REPLACE(Mbs_Temporary_Exhibition.AD, ',', ';')
                       from Mbs_Temporary_Exhibition
                       where Mbs_Temporary_Exhibition.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as geciciSergiAd_cim,
                STUFF((SELECT ',' + REPLACE(MUZE_MUDURLUGU.AD, ',', ';')
                       from Mbs_Temporary_Exhibition,
                            Mbs_Temporary_Exhibition_Partner,
                            MUZE_MUDURLUGU
                       where Mbs_Temporary_Exhibition.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Temporary_Exhibition.ID = Mbs_Temporary_Exhibition_Partner.temporaryExhibition
                         AND Mbs_Temporary_Exhibition_Partner.MUZE_MUDURLUGU_ID = MUZE_MUDURLUGU.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as geciciSergiOrtakAd_cim,
                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from Mbs_Vehicle_Status,
                            Pick
                       where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
                         AND Mbs_Vehicle_Status.vehicleType = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as aracTipi_cim
--STUFF( (SELECT ',' +   REPLACE(Pick.AD + ':' + CAST(activeVehicleCount as VARCHAR), ',', ';') from Mbs_Vehicle_Status, Pick where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = G.MUZE_MUDURLUGU_ID AND Mbs_Vehicle_Status.vehicleType = Pick.ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as AKTIF_ARAC,
--STUFF( (SELECT ',' +   REPLACE(Pick.AD + ':' + CAST(defectiveVehicleCount as VARCHAR), ',', ';') from Mbs_Vehicle_Status, Pick where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = G.MUZE_MUDURLUGU_ID AND Mbs_Vehicle_Status.vehicleType = Pick.ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as ARIZALI_ARAC,
--STUFF( (SELECT ',' +   REPLACE(Pick.AD + ':' + CAST(needVehicleCount as VARCHAR), ',', ';') from Mbs_Vehicle_Status, Pick where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = G.MUZE_MUDURLUGU_ID AND Mbs_Vehicle_Status.vehicleType = Pick.ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as ARAC_IHTIYACI,

--STUFF( (SELECT ',' +   REPLACE(Mbs_Vehicle_Status.activeVehicleCount, ',', ';') from Mbs_Vehicle_Status where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as AKTIF_ARAC_SAYISI,
--STUFF( (SELECT ',' +   REPLACE(Mbs_Vehicle_Status.defectiveVehicleCount, ',', ';') from Mbs_Vehicle_Status where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as ARIZALI_ARAC_SAYISI,
--STUFF( (SELECT ',' +   REPLACE(Mbs_Vehicle_Status.needVehicleCount, ',', ';') from Mbs_Vehicle_Status where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as IHTIYAC_ARAC_SAYISI,
--(SELECT SUM(Mbs_Vehicle_Status.needVehicleCount) from Mbs_Vehicle_Status where Mbs_Vehicle_Status.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID) as IHTIYAC_ARAC_SAYISI_TOPLAM,
--(SELECT SUM(Mbs_Visitor_Information.visitorCount) from Mbs_Visitor_Information where Mbs_Visitor_Information.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID) as ZIYARETCI_SAYISI_TOPLAM,
--(SELECT SUM(Mbs_Museum_Expense.sum) from Mbs_Museum_Expense where Mbs_Museum_Expense.MUZE_MUDURLUGU_ID = Mbs_General_Information.MUZE_MUDURLUGU_ID) as MUZE_GIDER_TOPLAM

from MUZE_MUDURLUGU

         LEFT JOIN IL on IL.ID = MUZE_MUDURLUGU.IL_ID
         LEFT JOIN Mbs_General_Information on MUZE_MUDURLUGU.ID = Mbs_General_Information.MUZE_MUDURLUGU_ID
         LEFT JOIN PERSONEL on PERSONEL.ID = Mbs_General_Information.museumManager
         LEFT JOIN Pick arkeoPark on arkeoPark.ID = Mbs_General_Information.arkeoPark
         LEFT JOIN Pick babyCareRoom on babyCareRoom.ID = Mbs_General_Information.babyCareRoom
         LEFT JOIN Pick brailleArtifactLabel on brailleArtifactLabel.ID = Mbs_General_Information.brailleArtifactLabel
         LEFT JOIN Pick cafeteria on cafeteria.ID = Mbs_General_Information.cafeteria
         LEFT JOIN Pick disabledLift on disabledLift.ID = Mbs_General_Information.disabledLift
         LEFT JOIN Pick disabledOtopark on disabledOtopark.ID = Mbs_General_Information.disabledOtopark
         LEFT JOIN Pick disabledRamp on disabledRamp.ID = Mbs_General_Information.disabledRamp
         LEFT JOIN Pick gamePlace on gamePlace.ID = Mbs_General_Information.gamePlace
         LEFT JOIN Pick meetingRoom on meetingRoom.ID = Mbs_General_Information.meetingRoom
         LEFT JOIN Pick museumGuidanceService
                   on museumGuidanceService.ID = Mbs_General_Information.museumGuidanceService
         LEFT JOIN Pick otopark on otopark.ID = Mbs_General_Information.otopark
         LEFT JOIN Pick parkingGarage on parkingGarage.ID = Mbs_General_Information.parkingGarage
         LEFT JOIN Pick restaurant on restaurant.ID = Mbs_General_Information.restaurant
         LEFT JOIN Pick voiceGuidanceService on voiceGuidanceService.ID = Mbs_General_Information.voiceGuidanceService
         LEFT JOIN Pick wc on wc.ID = Mbs_General_Information.wc
         LEFT JOIN Pick lift on lift.ID = Mbs_General_Information.lift
         LEFT JOIN Pick elevator on elevator.ID = Mbs_General_Information.elevator
         LEFT JOIN BAGLI_BIRIM bb on bb.MUZE_MUDURLUGU_ID = MUZE_MUDURLUGU.ID
         LEFT JOIN BINA b ON bb.ID = b.BAGLI_BIRIM_ID


WHERE MUZE_MUDURLUGU.AKTIF = 1
  AND MUZE_MUDURLUGU.SILINMIS = 0;