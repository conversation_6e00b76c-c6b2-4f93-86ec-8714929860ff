-- ////////////
IF OBJECT_ID('dbo.KAM_STOLENARTIFACT_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_STOLENARTIFACT_VIEW;

CREATE VIEW KAM_STOLENARTIFACT_VIEW AS
SELECT FORMAT(dbo.Kam_StolenArtifact.ID, 'CE\.000\.000\.000')                                     AS id_ci,
       dbo.Kam_StolenArtifact.ID                                                                  AS uid,
       dbo.Kam_StolenArtifact.AKTIF                                                               as aktif,
       dbo.Kam_StolenArtifact.SILINMIS                                                            as silinmis,
       dbo.Kam_StolenArtifact.dateCreated                                                         as olusturmaZamani,
       dbo.Kam_StolenArtifact.dateUpdated                                                         as duzenlemeZamani,
       dbo.Kam_StolenArtifact.aciklama                                                            as aciklama_ci,
       dbo.Kam_StolenArtifact.completedProcess                                                    as completedProcess,
       dbo.Kam_StolenArtifact.stolenDate                                                          as o<PERSON><PERSON><PERSON><PERSON>,
       dbo.Kam_StolenArtifact.stolenDetectionDate                                                 as stolenDetectionDate,
       dbo.Kam_StolenArtifact.lastViewedDateOfArtifacts                                           as lastViewedDateOfArtifacts,
       dbo.Kam_StolenArtifact.artifactsFoundDate                                                  as artifactsFoundDate,
       dbo.Kam_StolenArtifact.archiveData                                                         AS archive,

       createdBy.AD + ' ' + createdBy.SOYAD                                                       AS olusturanKullaniciAd_ci,
       updatedBy.AD + ' ' + updatedBy.SOYAD                                                       AS sonDuzenleyenKullaniciAd_ci,

       dbo.Kam_IncidentLocation.[ada]                                                             as olayAda,
       dbo.Kam_IncidentLocation.beldeAdi                                                          as beldeAdi_ci,
       dbo.Kam_IncidentLocation.ENLEM                                                             as enlem_ci,
       dbo.Kam_IncidentLocation.BOYLAM                                                            as boylam_ci,
       dbo.Kam_IncidentLocation.pafta                                                             as olayPafta_ci,
       dbo.Kam_IncidentLocation.parsel                                                            as olayParsel,
       dbo.Kam_IncidentLocation.aciklama                                                          as olayYeriAciklama_ci,

       (case
            when lostArtifactAreaType.rank = 1 then lostMuseumIL.AD
            when lostArtifactAreaType.rank = 8 then mosqueIL.AD
            when lostArtifactAreaType.rank = 3 or lostArtifactAreaType.rank = 4 then excavationIL.AD
            else incidentIL.AD end)                                                               as ilAd_ci,

       (case
            when lostArtifactAreaType.rank = 1 then lostMuseumIce.AD
            when lostArtifactAreaType.rank = 8 then mosqueIce.AD
            when lostArtifactAreaType.rank = 3 or lostArtifactAreaType.rank = 4 then excavationIlce.AD
            else incidentIlce.AD end)                                                             AS ilceAd_ci,

       lostArtifactAreaType.AD                                                                    AS lostArtifactAreaTypeName_ci,
       captureType.AD                                                                             AS captureTypeAd_ci,
       directorate.AD                                                                             AS stolenDirectorateName_ci,
       lostDetectionType.AD                                                                       AS lostDetectionTypeName_ci,
       veriMuzeMudurlugu.AD                                                                       AS muzeMudurluguAd_ci,
       'Kayıp/Çalıntı Eser'                                                                       as incidentType_ci,

       dbo.Kam_StolenArtifactPhoto.aciklama                                                       AS fotografAciklama_ci,
       dbo.Kam_StolenArtifactPhoto.fotografPath                                                   AS fotografPath_ci,
       dbo.Kam_StolenArtifactPhoto.name                                                           AS fotografBasligi_ci,


       (select COALESCE((select sum(CASE
                                        WHEN Kam_Artifact.artifactCount is null THEN 1
                                        ELSE Kam_Artifact.artifactCount END)
                         from Kam_StolenArtifact_Kam_Artifact
                                  LEFT JOIN Kam_Artifact
                                            on Kam_Artifact.ID = Kam_StolenArtifact_Kam_Artifact.artifacts_ID
                         where Kam_StolenArtifact_Kam_Artifact.StolenArtifact_ID = dbo.Kam_StolenArtifact.ID), 0)) +
       (select COUNT(ESER.ID)
        from dbo.Kam_StolenArtifact_InventoryArtifact
                 LEFT JOIN ESER on ESER.ID = dbo.Kam_StolenArtifact_InventoryArtifact.artifacts_ID
        where Kam_StolenArtifact_InventoryArtifact.StolenArtifact_ID = dbo.Kam_StolenArtifact.ID) as artifactCount,


       (SELECT count(Kam_StolenArtifact_Sahis.sahisId)
        from dbo.Kam_StolenArtifact_Sahis
        where dbo.Kam_StolenArtifact_Sahis.stolenArtifactId = dbo.Kam_StolenArtifact.ID)
                                                                                                  AS olayaKarisanKisiSayisi,

       STUFF((SELECT DISTINCT SAHIS.AD + ' ' + SAHIS.SOYAD + '##'
              from dbo.Kam_StolenArtifact_Sahis,
                   dbo.SAHIS
              where dbo.Kam_StolenArtifact_Sahis.stolenArtifactId = dbo.Kam_StolenArtifact.ID
                AND dbo.Kam_StolenArtifact_Sahis.sahisId = dbo.SAHIS.ID
                 for xml path(''), type)
                 .value('.', 'varchar(max)'), 1, 1, '')                                           AS olaySahisAds_cim

FROM dbo.Kam_StolenArtifact
         LEFT JOIN dbo.Kam_IncidentLocation ON dbo.Kam_StolenArtifact.incidentId = dbo.Kam_IncidentLocation.ID
         LEFT JOIN dbo.IL incidentIL ON dbo.Kam_IncidentLocation.IL_ID = incidentIl.ID
         LEFT JOIN dbo.ILCE incidentIlce ON dbo.Kam_IncidentLocation.ILCE_ID = incidentIlce.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU AS directorate ON dbo.Kam_StolenArtifact.directorateId = directorate.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU AS veriMuzeMudurlugu
                   ON dbo.Kam_StolenArtifact.directorateId = veriMuzeMudurlugu.ID
         LEFT JOIN dbo.Kam_Pick AS captureType ON dbo.Kam_StolenArtifact.captureType = captureType.ID
         LEFT JOIN dbo.Kam_Pick AS lostArtifactAreaType
                   ON lostArtifactAreaType.ID = dbo.Kam_StolenArtifact.lostArtifactAreaType
         LEFT JOIN dbo.Kam_Pick AS lostDetectionType ON dbo.Kam_StolenArtifact.lostDetectionType = lostDetectionType.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU as lostMuseum ON dbo.Kam_StolenArtifact.lostMuseumId = lostMuseum.ID
         LEFT JOIN dbo.IL lostMuseumIL ON lostMuseum.IL_ID = lostMuseumIl.ID
         LEFT JOIN dbo.ILCE lostMuseumIce ON lostMuseum.ILCE_ID = lostMuseumIce.ID
         LEFT JOIN dbo.Kam_Mosque AS mosque ON dbo.Kam_StolenArtifact.mosqueId = mosque.ID
         LEFT JOIN dbo.Kam_IncidentLocation AS mosqueIncident ON mosque.incidentId = mosqueIncident.ID
         LEFT JOIN dbo.IL mosqueIL ON mosqueIncident.IL_ID = mosqueIL.ID
         LEFT JOIN dbo.ILCE mosqueIce ON mosqueIncident.ILCE_ID = mosqueIce.ID
         LEFT JOIN dbo.KAZI AS excavation ON dbo.Kam_StolenArtifact.muesExcavationId = excavation.ID
         LEFT JOIN dbo.IL excavationIL ON excavation.IL_ID = excavationIL.ID
         LEFT JOIN dbo.ILCE excavationIlce ON excavation.ILCE_ID = excavationIlce.ID
         LEFT JOIN dbo.PERSONEL createdBy ON dbo.Kam_StolenArtifact.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL updatedBy ON dbo.Kam_StolenArtifact.createdBy = updatedBy.ID

         LEFT JOIN dbo.Kam_StolenArtifactPhoto
                   ON dbo.Kam_StolenArtifactPhoto.stolenArtifactId = dbo.Kam_StolenArtifact.ID AND
                      dbo.Kam_StolenArtifactPhoto.ID = (SELECT TOP 1 ID
                                                        FROM Kam_StolenArtifactPhoto
                                                        WHERE dbo.Kam_StolenArtifactPhoto.SILINMIS = 0
                                                          AND dbo.Kam_StolenArtifactPhoto.AKTIF = 1
                                                          AND dbo.Kam_StolenArtifactPhoto.stolenArtifactId =
                                                              dbo.Kam_StolenArtifact.ID)


WHERE dbo.Kam_StolenArtifact.updateInProgress = 0;
