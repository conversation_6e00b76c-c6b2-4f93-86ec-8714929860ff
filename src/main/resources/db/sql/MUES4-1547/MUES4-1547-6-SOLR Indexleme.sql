-- ////////////
IF OBJECT_ID('dbo.KAM_REQUEST_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_REQUEST_VIEW;

CREATE VIEW KAM_REQUEST_VIEW AS
SELECT FORMAT(dbo.Kam_Request.ID, 'VB\.000\.000\.000') AS id_ci,
       dbo.Kam_Request.ID                              AS uid,
       dbo.Kam_Request.AKTIF                           AS aktif,
       dbo.Kam_Request.SILINMIS                        AS silinmis,

       dbo.Kam_Request.assignedDate                    AS havaleTarihi,
       dbo.Kam_Request.code                            AS basvuruKodu_ci,
       dbo.Kam_Request.dateCreated                     AS olusturmaZamani,
       dbo.Kam_Request.dateUpdated                     AS duzenlemeZamani,
       dbo.Kam_Request.isFalse                         AS asilliBasvuru,
       dbo.Kam_Request.hasPrivacy                      AS hasPrivacy,
       dbo.Kam_Request.archiveData                     as archive,
       dbo.Kam_Request.completedProcess                as completedProcess,
       dbo.Kam_RequestPhotograph.fotografPath          AS fotografPath_ci,
       dbo.Kam_RequestPhotograph.AD                    AS fotografBasligi_ci,
       dbo.Kam_RequestPhotograph.ACIKLAMA              AS fotografAciklama_ci,
       dbo.PERSONEL.AD + ' ' + dbo.PERSONEL.SOYAD      AS gorevliPersonelAds_ci,
       dbo.PERSONEL.SICIL_NO                           AS gorevliPersonelSicilNo_ci,
       reportedProvince.AD                             AS ilAd_ci,
       province.AD                                     AS provinceAd_ci,
       reportedCounty.AD                               AS ilceAd_ci,
       county.AD                                       AS countyAd_ci,
       dbo.MUZE_MUDURLUGU.AD                           AS muzeMudurluguAd_ci,
       reportType.AD                                   AS reportTypeName_ci,
       basvuruIcerigi.AD                               AS basvuruIcerigiAd_ci,
       requestFolder.AD                                AS requestFolderName_ci,
       operation.AD                                    AS yapilanIslemAd_ci,
       'Vatandaş Başvurusu'                            AS incidentType_ci,
       1                                               AS olayaKarisanKisiSayisi,

       createdBy.AD + ' ' + createdBy.SOYAD            AS olusturanKullaniciAd_ci,
       updatedBy.AD + ' ' + updatedBy.SOYAD            AS sonDuzenleyenKullaniciAd_ci,

------------------------- ortak alanlar
       dbo.Kam_Request.ACIKLAMA                        AS olayAciklama_ci,
       basvuruKisi.AD + ' ' + basvuruKisi.SOYAD        as olaySahisAds_ci

FROM dbo.Kam_Request
         LEFT JOIN dbo.Kam_RequestPhotograph ON dbo.Kam_RequestPhotograph.requestId = dbo.Kam_Request.ID AND
                                                dbo.Kam_RequestPhotograph.ID = (SELECT TOP 1 ID
                                                                                FROM Kam_RequestPhotograph
                                                                                WHERE dbo.Kam_RequestPhotograph.SILINMIS = 0
                                                                                  AND dbo.Kam_RequestPhotograph.AKTIF = 1
                                                                                  AND dbo.Kam_RequestPhotograph.requestId = dbo.Kam_Request.ID)
         LEFT JOIN dbo.PERSONEL ON dbo.Kam_Request.assigneeId = dbo.PERSONEL.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Kam_Request.mudurlukId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.IL AS province ON dbo.Kam_Request.provinceId = province.ID
         LEFT JOIN dbo.IL AS reportedProvince ON dbo.Kam_Request.reportedProvinceId = reportedProvince.ID
         LEFT JOIN dbo.ILCE AS county ON dbo.Kam_Request.countyId = county.ID
         LEFT JOIN dbo.ILCE AS reportedCounty ON dbo.Kam_Request.reportedCountyId = reportedCounty.ID
         LEFT JOIN dbo.Kam_Pick AS reportType ON dbo.Kam_Request.reportTypeId = reportType.ID
         LEFT JOIN dbo.Kam_Pick AS basvuruIcerigi ON dbo.Kam_Request.typeId = basvuruIcerigi.ID
         LEFT JOIN dbo.Kam_RequestFolder AS requestFolder ON dbo.Kam_Request.requestFolderId = requestFolder.ID
         LEFT JOIN dbo.Kam_Pick AS operation ON dbo.Kam_Request.operationId = operation.ID
         LEFT JOIN dbo.SAHIS as basvuruKisi ON basvuruKisi.ID = dbo.Kam_Request.reporterId
         LEFT JOIN dbo.PERSONEL createdBy ON dbo.Kam_Request.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL updatedBy ON dbo.Kam_Request.createdBy = updatedBy.ID

WHERE dbo.Kam_Request.updateInProgress = 0;