-- ////////////
IF OBJECT_ID('dbo.KAM_CULTURALPROPERTYSMUGGLING_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_CULTURALPROPERTYSMUGGLING_VIEW;

CREATE VIEW KAM_CULTURALPROPERTYSMUGGLING_VIEW AS
SELECT FORMAT(dbo.Kam_CulturalPropertySmuggling.ID, 'KVK\.000\.000\.000') AS id_ci,
       dbo.Kam_CulturalPropertySmuggling.ID                               AS uid,
       dbo.Kam_CulturalPropertySmuggling.AKTIF                            AS aktif,
       dbo.Kam_CulturalPropertySmuggling.SILINMIS                         AS silinmis,
       dbo.Kam_CulturalPropertySmuggling.dateCreated                      AS olusturmaZamani,
       dbo.Kam_CulturalPropertySmuggling.dateUpdated                      AS duzenlemeZamani,
       dbo.Kam_CulturalPropertySmuggling.aciklama                         AS aciklama_ci,
       dbo.Kam_CulturalPropertySmuggling.archiveData                      AS archive,
       dbo.Kam_CulturalPropertySmuggling.incidentDate                     AS olayTarihi,
       dbo.Kam_CulturalPropertySmuggling.receiptNo                        AS geciciAlindiNo_ci,
       dbo.Kam_CulturalPropertySmuggling.receiptPath                      AS raporPath_ci,
       dbo.Kam_CulturalPropertySmuggling.involvedPersNum                  AS olayaKarisanKisiSayisi,
       dbo.Kam_CulturalPropertySmuggling.isDismissedCase                  AS isDismissedCase,
       dbo.Kam_CulturalPropertySmuggling.reportMont                       AS reportMont,
       dbo.Kam_CulturalPropertySmuggling.completedProcess                 AS completedProcess,
       dbo.Kam_CulturalPropertySmuggling.reportYear                       AS reportYear,
       dbo.Kam_IncidentLocation.ad                                        AS olayAdi_ci,
       dbo.Kam_IncidentLocation.[ada]                                     AS olayAda,
       dbo.Kam_IncidentLocation.beldeAdi                                  AS beldeAdi_ci,
       dbo.Kam_IncidentLocation.ENLEM                                     AS enlem_ci,
       dbo.Kam_IncidentLocation.BOYLAM                                    AS boylam_ci,
       dbo.Kam_IncidentLocation.pafta                                     AS olayPafta_ci,
       dbo.Kam_IncidentLocation.parsel                                    AS olayParsel,
       dbo.Kam_IncidentLocation.aciklama                                  AS olayYeriAciklama_ci,

       dbo.IL.AD                                                          AS ilAd_ci,
       dbo.ILCE.AD                                                        AS ilceAd_ci,
       statusOfArtifacts.AD                                               AS statusOfArtifactsName_ci,
       legalActionStatus.AD                                               AS legalActionStatusName_ci,
       dbo.MUZE_MUDURLUGU.AD                                              AS muzeMudurluguAd_ci,
       'Kültür Varlığı Kaçakçılığı'                                       AS incidentType_ci,
       dbo.Kam_CulturalPropertySmugglingPhoto.aciklama                    AS fotografAciklama_ci,
       dbo.Kam_CulturalPropertySmugglingPhoto.fotografPath                AS fotografPath_ci,
       dbo.Kam_CulturalPropertySmugglingPhoto.name                        AS fotografBasligi_ci,
       createdBy.AD + ' ' + createdBy.SOYAD                               AS olusturanKullaniciAd_ci,
       updatedBy.AD + ' ' + updatedBy.SOYAD                               AS sonDuzenleyenKullaniciAd_ci,

       institution.AD                                                     AS yakalayanKurumAd_ci,

       (select COALESCE(sum(CASE WHEN Kam_Artifact.artifactCount is null THEN 1 ELSE Kam_Artifact.artifactCount END), 0)
        from dbo.Kam_CulturalPropertySmuggling_Kam_Artifact
                 LEFT JOIN Kam_Artifact on Kam_Artifact.ID = Kam_CulturalPropertySmuggling_Kam_Artifact.artifacts_ID
        where Kam_CulturalPropertySmuggling_Kam_Artifact.CulturalPropertySmuggling_ID =
              dbo.Kam_CulturalPropertySmuggling.ID) +
       (select COALESCE(sum(CASE WHEN Kam_Artifact.artifactCount is null THEN 1 ELSE Kam_Artifact.artifactCount END), 0)
        from dbo.Kam_CulturalPropertySmuggling_Kam_ForgeArtifact
                 LEFT JOIN Kam_Artifact
                           on Kam_Artifact.ID = Kam_CulturalPropertySmuggling_Kam_ForgeArtifact.artifacts_ID
        where Kam_CulturalPropertySmuggling_Kam_ForgeArtifact.CulturalPropertySmuggling_ID =
              dbo.Kam_CulturalPropertySmuggling.ID)                       as artifactCount

FROM dbo.Kam_CulturalPropertySmuggling
         LEFT JOIN dbo.Kam_Pick AS legalActionStatus
                   ON dbo.Kam_CulturalPropertySmuggling.legalActionStatus = legalActionStatus.ID
         LEFT JOIN dbo.Kam_IncidentLocation
                   ON dbo.Kam_CulturalPropertySmuggling.incidentId = dbo.Kam_IncidentLocation.ID
         LEFT JOIN dbo.IL ON dbo.Kam_IncidentLocation.IL_ID = dbo.IL.ID
         LEFT JOIN dbo.ILCE ON dbo.Kam_IncidentLocation.ILCE_ID = dbo.ILCE.ID
         LEFT JOIN dbo.Kam_Pick AS statusOfArtifacts
                   ON dbo.Kam_CulturalPropertySmuggling.statusOfArtifacts = statusOfArtifacts.ID

         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Kam_CulturalPropertySmuggling.directorateId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.PERSONEL AS createdBy ON dbo.Kam_CulturalPropertySmuggling.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL AS updatedBy ON dbo.Kam_CulturalPropertySmuggling.updatedBy = updatedBy.ID
         LEFT JOIN dbo.Institution AS institution ON dbo.Kam_CulturalPropertySmuggling.institutionId = institution.ID
         LEFT JOIN dbo.Kam_CulturalPropertySmugglingPhoto
                   ON dbo.Kam_CulturalPropertySmugglingPhoto.smugglingID = dbo.Kam_CulturalPropertySmuggling.ID AND
                      dbo.Kam_CulturalPropertySmugglingPhoto.ID = (SELECT TOP 1 ID
                                                                   FROM Kam_CulturalPropertySmugglingPhoto
                                                                   WHERE dbo.Kam_CulturalPropertySmugglingPhoto.SILINMIS = 0
                                                                     AND dbo.Kam_CulturalPropertySmugglingPhoto.AKTIF = 1
                                                                     AND
                                                                       dbo.Kam_CulturalPropertySmugglingPhoto.smugglingID =
                                                                       dbo.Kam_CulturalPropertySmuggling.ID)
where dbo.Kam_CulturalPropertySmuggling.updateInProgress = 0;