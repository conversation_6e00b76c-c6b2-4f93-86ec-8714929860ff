-- ////////////
IF OBJECT_ID('dbo.ESER_HAREKET_BASE', 'V') IS NOT NULL
DROP VIEW dbo.ESER_HAREKET_BASE;

CREATE VIEW ESER_HAREKET_BASE as
SELECT ESER_HAREKET.ESER_ID                   AS ESER_HAREKET_ESER_ID,
       ESER_HAREKET.ARASTIRMA_ID              AS ESER_HAREKET_ARASTIRMA_ID,
       ESER_HAREKET.KAZI_ID                   AS ESER_HAREKET_KAZI_ID,
       ESER_HAREKET.MUZE_ID                   AS ESER_HAREKET_MUZE_ID,
       MUZE_MUDURLUGU.AD                      AS eserHareketMuzeAd,
       teslimAlan.AD + ' ' + teslimAlan.SOYAD AS eserHareketTeslimAlanPersonelAdSoyad,
       teslimEden.AD + ' ' + teslimEden.SOYAD AS eserHareketTeslimEdenPersonelAdSoyad,
       ESER_HAREKET.ESER_GELIS_SEKLI          AS eserHareketEserGelisSekli,
       ESER_HAREKET.ENVANTER_NO               AS eserHareketEnvanterNo,
       ESER_HAREKET.CIKARILMA_TARIHI          AS eserHareketCikarilmaTarihi,
       ESER_HAREKET.ONAY_TARIHI               AS eserHareketOnayTarihi,
       ESER_HAREKET.ONAY_SAYISI               AS eserHareketOnaySayisi,
       ESER_HAREKET.MUZEYE_GELIS_TARIHI       AS eserHareketMuzeyeGelisTarihi,
       ESER_HAREKET.ENVANTERE_ALINMA_TARIHI   AS eserHareketEnvantereAlinmaTarihi,
       ESER_HAREKET.ACIKLAMA                  AS eserHareketAciklama,
       ESER_HAREKET.BULUNTU_YERI              AS eserHareketBuluntuYeri,
       ESER_HAREKET.ELE_GECIRME_YERI          AS eserHareketEleGecirmeYeri,
       ESER_HAREKET.iadeEdenUlke              AS ESER_HAREKET_IADE_EDEN_ULKE,
       ESER_HAREKET.AKTIF                     AS eserHareketAktif,
       ESER_HAREKET.SILINMIS                  AS eserHareketSilinmis,
       KAZI_TUR.DEGER                         AS kaziTurDeger,
       KAZI_TUR.AD                            AS kaziTurAd,
       KAZI.AD                                AS kaziAd,
       KAZI.ACIKLAMA                          AS kaziAciklama,
       KAZI.IL_ID                             AS kaziIlId,
       KAZI.ILCE_ID                           AS KAZI_ILCE_ID,
       KAZI.ENLEM                             AS kaziEnlem,
       KAZI.BOYLAM                            AS kaziBoylam,
       ARASTIRMA.ID                           AS ARASTIRMA_ID,
       ARASTIRMA_TUR.AD                       AS arastirmaTurAd,
       ARASTIRMA_TUR.ACIKLAMA                 AS arastirmaTurAciklama,
       ARASTIRMA.AD                           AS arastirmaAd,
       ARASTIRMA.ACIKLAMA                     AS arastirmaAciklama,
       KAZI.BELDE_ADI                         AS kaziBeldeAdi,
       KAZI.KAZI_KODU                         AS kaziKaziKodu,
       KAZI_ILCE.AD                           AS KAZI_ILCE_AD,
       KAZI_IL.AD                             AS KAZI_IL_AD,
       Ulke.AD                                AS ulkeAd,
       SAHIS.AD            									 AS sahisAd,
       SAHIS.SOYAD           								 AS sahisSoyad,
       TUZEL_KISI.TICARI_UNVAN                AS tuzelKisiTicariUnvan,
       gelisSekli.[AD]                        AS eserHareketEserGelisSekliAd
FROM ESER_HAREKET
         LEFT JOIN KAZI ON KAZI.ID = ESER_HAREKET.KAZI_ID
         LEFT JOIN KAZI_TUR ON KAZI.KAZI_TUR_ID = KAZI_TUR.ID
         LEFT JOIN ARASTIRMA ON ARASTIRMA.ID = ESER_HAREKET.ARASTIRMA_ID
         LEFT JOIN ARASTIRMA_TUR ON ARASTIRMA.ARASTIRMA_TUR_ID = ARASTIRMA_TUR.ID
         LEFT JOIN Ulke ON ESER_HAREKET.iadeEdenUlke = Ulke.ID
         LEFT JOIN SAHIS ON ESER_HAREKET.TESLIM_EDEN_SAHIS_ID = SAHIS.ID
         LEFT JOIN IL AS KAZI_IL ON KAZI.IL_ID = KAZI_IL.ID
         LEFT JOIN ILCE AS KAZI_ILCE ON KAZI.ILCE_ID = KAZI_ILCE.ID
         LEFT JOIN Pick gelisSekli ON gelisSekli.ID = ESER_HAREKET.ESER_GELIS_SEKLI
         LEFT JOIN TUZEL_KISI ON ESER_HAREKET.TESLIM_EDEN_TUZEL_KISI_ID = TUZEL_KISI.ID
         LEFT JOIN MUZE_MUDURLUGU ON ESER_HAREKET.MUZE_ID = MUZE_MUDURLUGU.ID
         LEFT JOIN PERSONEL teslimAlan
                   ON ESER_HAREKET.TESLIM_ALAN_PERSONEL_ID = teslimAlan.ID
         LEFT JOIN PERSONEL teslimEden
                   ON ESER_HAREKET.TESLIM_EDEN_PERSONEL_ID = teslimEden.ID

UNION ALL

SELECT ESER.ID                                                              AS ESER_HAREKET_ESER_ID,
       TGA.ARASTIRMA_ID                                                     AS ESER_HAREKET_ARASTIRMA_ID,
       TGA.KAZI_ID                                                          AS ESER_HAREKET_KAZI_ID,
       TGA.MUZE_ID                                                          AS ESER_HAREKET_MUZE_ID,
       MUZE_MUDURLUGU.AD                                                    AS eserHareketMuzeAd,

       STUFF((SELECT ',' + REPLACE(PERSONEL.AD + ' ' + PERSONEL.SOYAD, ',', ';')
              FROM Kms_TemporaryAdmissionReceipt_TeslimAlan,
                   PERSONEL
              WHERE Kms_TemporaryAdmissionReceipt_TeslimAlan.personelId = PERSONEL.ID
                AND TGA.ID =
                    Kms_TemporaryAdmissionReceipt_TeslimAlan.temporaryAdmissionReceiptId
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
                                            '')                                                            AS eserHareketTeslimAlanPersonelAdSoyad,

       STUFF((SELECT ',' + REPLACE(PERSONEL.AD + ' ' + PERSONEL.SOYAD, ',', ';')
              FROM Kms_TemporaryAdmissionReceipt_TeslimEden,
                   PERSONEL
              WHERE Kms_TemporaryAdmissionReceipt_TeslimEden.personelId = PERSONEL.ID
                AND TGA.ID =
                    Kms_TemporaryAdmissionReceipt_TeslimEden.temporaryAdmissionReceiptId
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
                                            '')                                                            AS eserHareketTeslimEdenPersonelAdSoyad,

       TGA.ESER_GELIS_SEKLI                                                 AS eserHareketEserGelisSekli,
       TGA.ENVANTER_NO                                                      AS eserHareketEnvanterNo,
       TGA.CIKARILMA_TARIHI                                                 AS eserHareketCikarilmaTarihi,
       TGA.ONAY_TARIHI                                                      AS eserHareketOnayTarihi,
       TGA.ONAY_SAYISI                                                      AS eserHareketOnaySayisi,
       TGA.MUZEYE_GELIS_TARIHI                                              AS eserHareketMuzeyeGelisTarihi,
       TGA.ENVANTERE_ALINMA_TARIHI                                          AS eserHareketEnvantereAlinmaTarihi,
       TGA.ACIKLAMA                                                         AS eserHareketAciklama,
       TGA.ELE_GECIRME_YERI                                                 AS BULUNTU_YERI,
       TGA.ELE_GECIRME_YERI                                                 AS eserHareketEleGecirmeYeri,
       TGA.iadeEdenUlke                                                     AS ESER_HAREKET_IADE_EDEN_ULKE,
       TGA.AKTIF                                                            AS eserHareketAktif,
       TGA.SILINMIS                                                         AS eserHareketSilinmis,
       KAZI_TUR.DEGER                                                       AS kaziTurDeger,
       KAZI_TUR.AD                                                          AS kaziTurAd,
       KAZI.AD                                                              AS kaziAd,
       KAZI.ACIKLAMA                                                        AS kaziAciklama,
       KAZI.IL_ID                                                           AS kaziIlId,
       KAZI.ILCE_ID                                                         AS KAZI_ILCE_ID,
       KAZI.ENLEM                                                           AS kaziEnlem,
       KAZI.BOYLAM                                                          AS kaziBoylam,
       ARASTIRMA.ID                                                         AS ARASTIRMA_ID,
       ARASTIRMA_TUR.AD                                                     AS arastirmaTurAd,
       ARASTIRMA_TUR.ACIKLAMA                                               AS arastirmaTurAciklama,
       ARASTIRMA.AD                                                         AS arastirmaAd,
       ARASTIRMA.ACIKLAMA                                                   AS arastirmaAciklama,
       KAZI.BELDE_ADI                                                       AS kaziBeldeAdi,
       KAZI.KAZI_KODU                                                       AS kaziKaziKodu,
       KAZI_ILCE.AD                                                         AS KAZI_ILCE_AD,
       KAZI_IL.AD                                                           AS KAZI_IL_AD,
       Ulke.AD                                                              AS ulkeAd,

       STUFF((SELECT ',' + REPLACE(SAHIS.AD, ',', ';')
              FROM Kms_TemporaryAdmissionReceipt_Sahis,
                   SAHIS
              WHERE Kms_TemporaryAdmissionReceipt_Sahis.sahisId = SAHIS.ID
                AND TGA.ID =
                    Kms_TemporaryAdmissionReceipt_Sahis.temporaryAdmissionReceiptId
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
                                            '')                                                            AS sahisAd,

       STUFF((SELECT ',' + REPLACE(SAHIS.SOYAD, ',', ';')
              FROM Kms_TemporaryAdmissionReceipt_Sahis,
                   SAHIS
              WHERE Kms_TemporaryAdmissionReceipt_Sahis.sahisId = SAHIS.ID
                AND TGA.ID =
                    Kms_TemporaryAdmissionReceipt_Sahis.temporaryAdmissionReceiptId
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
                                            '')                                                            AS sahisSoyad,

       STUFF((SELECT ',' + REPLACE(TUZEL_KISI.TICARI_UNVAN, ',', ';')
              FROM Kms_TemporaryAdmissionReceipt_TuzelKisi,
                   TUZEL_KISI
              WHERE Kms_TemporaryAdmissionReceipt_TuzelKisi.tuzelKisiId = TUZEL_KISI.ID
                AND TGA.ID =
                    Kms_TemporaryAdmissionReceipt_TuzelKisi.temporaryAdmissionReceiptId
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
                                            '')                                                            AS tuzelKisiTicariUnvan,

       gelisSekli.[AD]                                                      AS eserHareketEserGelisSekliAd

FROM Kms_Object OBJ
         LEFT JOIN ESER ON ESER.objectId = OBJ.ID
         LEFT JOIN Kms_DeliveredItem DI ON OBJ.deliveredItemId = DI.ID
         LEFT JOIN Kms_TemporaryAdmissionReceipt TGA
                   ON TGA.ID = DI.temporaryAdmissionReceiptId
         LEFT JOIN Pick gelisSekli ON gelisSekli.ID = TGA.ESER_GELIS_SEKLI
         LEFT JOIN MUZE_MUDURLUGU ON TGA.MUZE_ID = MUZE_MUDURLUGU.ID
         LEFT JOIN KAZI ON KAZI.ID = TGA.KAZI_ID
         LEFT JOIN KAZI_TUR ON KAZI.KAZI_TUR_ID = KAZI_TUR.ID
         LEFT JOIN IL AS KAZI_IL ON KAZI.IL_ID = KAZI_IL.ID
         LEFT JOIN ILCE AS KAZI_ILCE ON KAZI.ILCE_ID = KAZI_ILCE.ID
         LEFT JOIN ARASTIRMA ON ARASTIRMA.ID = TGA.ARASTIRMA_ID
         LEFT JOIN ARASTIRMA_TUR ON ARASTIRMA.ARASTIRMA_TUR_ID = ARASTIRMA_TUR.ID
         LEFT JOIN Ulke ON TGA.iadeEdenUlke = Ulke.ID
WHERE OBJ.objectType = 0;

-- ////////////
IF OBJECT_ID('dbo.ESER_HAREKET_AGG', 'V') IS NOT NULL
DROP VIEW dbo.ESER_HAREKET_AGG;

CREATE VIEW ESER_HAREKET_AGG AS
SELECT ESER_HAREKET_ESER_ID,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketMuzeAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketMuzeAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketTeslimAlanPersonelAdSoyad, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketTeslimAlanPersonelAdSoyad_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketTeslimEdenPersonelAdSoyad, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketTeslimEdenPersonelAdSoyad_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketEnvanterNo, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketEnvanterNo_cim,

       STUFF((SELECT ',' +
                     REPLACE(convert(varchar(25), e2.eserHareketCikarilmaTarihi, 120),
                             ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketCikarilmaTarihi_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketOnayTarihi, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketOnayTarihi_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketOnaySayisi, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketOnaySayisi_cim,

       STUFF(
               (SELECT ',' +
                       REPLACE(convert(varchar(25), e2.eserHareketMuzeyeGelisTarihi, 120),
                               ',', ';')
                FROM eser_hareket_base e2
                WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                   FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
               '')                                                          AS eserHareketMuzeyeGelisTarihi_cim,

       STUFF(
               (SELECT ',' + REPLACE(
                       convert(varchar(25), e2.eserHareketEnvantereAlinmaTarihi, 120),
                       ',', ';')
                FROM eser_hareket_base e2
                WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                   FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
               '')                                                          AS eserHareketEnvantereAlinmaTarihi_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketAciklama, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketAciklama_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketBuluntuYeri, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketBuluntuYeri_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketEleGecirmeYeri, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketEleGecirmeYeri_cim,

       STUFF((SELECT ',' + REPLACE(e2.ESER_HAREKET_IADE_EDEN_ULKE, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketIadeEdenUlke_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketAktif, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketAktif_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketSilinmis, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketSilinmis_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziTurDeger, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziTurDeger_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziTurAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziTurAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziAciklama, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziAciklama_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziEnlem, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziEnlem_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziBoylam, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziBoylam_cim,

       STUFF((SELECT ',' + REPLACE(e2.arastirmaTurAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS arastirmaTurAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.arastirmaTurAciklama, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS arastirmaTurAciklama_cim,

       STUFF((SELECT ',' + REPLACE(e2.arastirmaAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS arastirmaAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.arastirmaAciklama, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS arastirmaAciklama_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziBeldeAdi, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziBeldeAdi_cim,

       STUFF((SELECT ',' + REPLACE(e2.kaziKaziKodu, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziKaziKodu_cim,

       STUFF((SELECT ',' + REPLACE(e2.KAZI_ILCE_AD, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS kaziIlceAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.KAZI_IL_AD, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '') AS kaziIlAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.ulkeAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS ulkeAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.sahisAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS sahisAd_cim,

       STUFF((SELECT ',' + REPLACE(e2.sahisSoyad, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS sahisSoyad_cim,

       STUFF((SELECT ',' + REPLACE(e2.tuzelKisiTicariUnvan, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS tuzelKisiTicariUnvan_cim,

       STUFF((SELECT ',' + REPLACE(e2.eserHareketEserGelisSekliAd, ',', ';')
              FROM eser_hareket_base e2
              WHERE e2.ESER_HAREKET_ESER_ID = e1.ESER_HAREKET_ESER_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1,
             '')                                                            AS eserHareketEserGelisSekliAd_cim
FROM ESER_HAREKET_BASE e1
GROUP BY ESER_HAREKET_ESER_ID;

-- ////////////
IF OBJECT_ID('dbo.ESER_TUMBILGILER', 'V') IS NOT NULL
DROP VIEW dbo.ESER_TUMBILGILER;

CREATE VIEW [dbo].[ESER_TUMBILGILER] AS
    WITH v_eser_hareket_agg AS (select * from [dbo].ESER_HAREKET_AGG)
SELECT dbo.ESER.ID                                                            AS id,
       dbo.ALAN.ACIKLAMA                                                      AS alanAciklama_ci,
       dbo.ALAN.AD                                                            AS alanAd_ci,
       dbo.ALAN.KOD                                                           AS alanKod_ci,
       dbo.ALAN_KONUMU.AD                                                     AS alanKonumuAd_ci,
       dbo.ALAN_KONUMU_TUR.AD                                                 AS alanKonumuTurAd_ci,
       dbo.ALAN_KONUMU_TUR.DEGER                                              AS alanKonumuTurDeger_ci,
       dbo.ALAN_TUR.AD                                                        AS alanTurAd_ci,
       dbo.ALAN_TUR.DEGER                                                     AS alanTurDeger_ci,
       dbo.BAGLI_BIRIM.ACIKLAMA                                               AS bagliBirimAciklama_ci,
       dbo.BAGLI_BIRIM.AD                                                     AS bagliBirimAd_ci,
       dbo.BAGLI_BIRIM.ADRES                                                  AS bagliBirimAdres_ci,
       dbo.BAGLI_BIRIM.BOYLAM                                                 AS bagliBirimBoylam_ci,
       dbo.BAGLI_BIRIM.ENLEM                                                  AS bagliBirimEnlem_ci,
       dbo.BAGLI_BIRIM.EPOSTA_KURUMSAL                                        AS bagliBirimEpostaKurumsal_ci,
       dbo.BAGLI_BIRIM.FAKS_1                                                 AS bagliBirimFaks1_ci,
       dbo.BAGLI_BIRIM.KOD                                                    AS bagliBirimKod_ci,
       dbo.BAGLI_BIRIM.TELEFON_NO_1                                           AS bagliBirimTelefonNo1_ci,
       dbo.BAGLI_BIRIM.WEB_SAYFASI                                            AS bagliBirimWebSayfasi_ci,
       dbo.BINA.ACIKLAMA                                                      AS binaAciklama_ci,
       dbo.BINA.AD                                                            AS binaAd_ci,
       dbo.BINA.KOD                                                           AS binaKod_ci,
       CASE WHEN dbo.ESER.updateInProgress = 1 THEN 0 ELSE dbo.ESER.AKTIF END AS aktif,
       CASE WHEN dbo.ESER.DONEM_BASLANGIC_YIL < -30000 THEN 1 ELSE 0 END      AS eserDonemBaslangicGo,
       dbo.ESER.DONEM_BASLANGIC_YIL                                           AS eserDonemBaslangicYil,
       CASE WHEN dbo.ESER.DONEM_BITIS_YIL < -30000 THEN 1 ELSE 0 END          AS eserDonemBitisGo,
       dbo.ESER.DONEM_BITIS_YIL                                               AS eserDonemBitisYil,
       dbo.ESER.ENVANTER_NO                                                   AS eserEskiEnvanterNo_ci,
       dbo.ESER.ESER_OZEL_ADI                                                 AS eserEserOzelAdi_ci,
       dbo.ESER.ESERI_BAGISLAYAN_ONEMLI_KISI_ID                               AS eserEseriBagislayanOnemliKisiId_ci,
       dbo.ESER.ESERI_KULLANACAK_ONEMLI_KISI_ID                               AS eserEseriKullanacakOnemliKisiId_ci,
       dbo.ESER.ESERI_KULLANAN_ONEMLI_KISI_ID                                 AS eserEseriKullananOnemliKisiId_ci,
       dbo.ESER.ESERI_YAPAN_ONEMLI_KISI_ID                                    AS eserEseriYapanOnemliKisiId_ci,
       dbo.ESER.ESERI_YAPTIRAN_ONEMLI_KISI_ID                                 AS eserEseriYaptiranOnemliKisiId_ci,
       dbo.ESER.GENEL_ACIKLAMA                                                AS eserGenelAciklama_ci,
       dbo.ESER.ISLAMI_GAYRI_SECIMI                                           AS eserIslamiGayriSecimi_ci,
       dbo.ESER.KIYMET                                                        AS eserKiymet_ci,
       dbo.ESER.KONDISYON_DURUMU                                              AS eserKondisyonDurumu_ci,
       CASE WHEN dbo.ESER.SIKKE_DARP_YILI < -30000 THEN 1 ELSE 0 END          AS eserSikkeDarpGo,
       dbo.ESER.SIKKE_DARP_YILI                                               AS eserSikkeDarpYili,
       dbo.ESER.SIKKE_DARP_YONU                                               AS eserSikkeDarpYonu,
       dbo.DarpYeri.AD                                                        AS eserSikkeDarpYeriAd_ci,
       dbo.ESER.SILINMIS                                                      AS eserSilinmis,
       dbo.ESER.SILME_ACIKLAMASI                                              AS eserSilmeAciklamasi_ci,
       dbo.ESER.SILME_ZAMANI                                                  AS eserSilmeZamani,
       (SELECT ESER_FOTOGRAF.FOTOGRAF_PATH
        FROM ESER_FOTOGRAF
        WHERE ESER_FOTOGRAF.ESER_ID = ESER.ID
          AND ESER_FOTOGRAF.ANA_FOTOGRAF = 1)                                 AS eserTanimlayiciFotografPath_ci,
       dbo.ESER.TASINIR_ISLEM_FISI_NO                                         AS eserTasinirIslemFisiNo_ci,
       dbo.ESER.TASINIR_MAL_YON_ID                                            AS eserTasinirMalYonId,
       dbo.ESER.TORENSEL_DURUMU                                               AS eserTorenselDurumu,
       dbo.ESER.UNIKLIK_DURUMU                                                AS eserUniklikDurumu,
       dbo.ESER.VERSIYON                                                      AS eserVersiyon,
       dbo.ESER.YARATMA_ZAMANI                                                AS eserYaratmaZamani,
       dbo.ESER.YAZMA_BASMA_SECIMI                                            AS eserYazmaBasmaSecimi,
       dbo.ESER_TUR.AD                                                        AS eserTurAd_ci,
       dbo.ESER_ALT_TUR.AD                                                    AS eserAltTurAd_ci,
       dbo.ESER_TUR.AD + ' > ' + dbo.ESER_ALT_TUR.AD                          AS eserTurveAltTurAd_ci,
       dbo.Cag.ACIKLAMA                                                       AS cagAciklama_ci,
       dbo.Cag.AD                                                             AS cagAd_ci,
       dbo.ESER_DEPO.ACIKLAMA                                                 AS eserDepoAciklama,
       dbo.Donem.AD                                                           AS donemAd_ci,
       dbo.Iliskilendirme.AD                                                  AS iliskilendirmeAd_ci,
       dbo.Iliskilendirme.ACIKLAMA                                            AS iliskilendirmeAciklama_ci,
       dbo.HUKUMDAR.ACIKLAMA                                                  AS hukumdarAciklama_ci,
       dbo.HUKUMDAR.AD                                                        AS hukumdarAd_ci,
       CASE WHEN dbo.HUKUMDAR.DONEM_BASLANGIC_YIL < -30000 THEN 1 ELSE 0 END  AS hukumdarDonemBaslangicGo,
       dbo.HUKUMDAR.DONEM_BASLANGIC_YIL                                       AS hukumdarDonemBaslangicYil,
       CASE WHEN dbo.HUKUMDAR.DONEM_BITIS_YIL < -30000 THEN 1 ELSE 0 END      AS hukumdarDonemBitisGo,
       dbo.HUKUMDAR.DONEM_BITIS_YIL                                           AS hukumdarDonemBitisYil,
       dbo.IL.AD                                                              AS ilAd_ci,
       dbo.IL.DEGER                                                           AS ilDeger_ci,
       dbo.ILCE.AD                                                            AS ilceAd_ci,
       dbo.KRONOLOJI.ACIKLAMA                                                 AS kronolojiAciklama_ci,
       dbo.KRONOLOJI.AD                                                       AS kronolojiAd_ci,
       dbo.ESER.YARATMA_KULLANICI_ID                                          AS eserYaratmaKullaniciId,
       dbo.KULLANICI.AD + ' ' + dbo.KULLANICI.SOYAD                           AS kullaniciKullaniciAdi_ci,
       dbo.PERSONEL.SICIL_NO                                                  AS ihtisasElemaniSicilNo_ci,
       dbo.PERSONEL.AD                                                        AS ihtisasElemaniAd_ci,
       dbo.PERSONEL.SOYAD                                                     AS ihtisasElemaniSoyad_ci,
       dbo.MUZE_MUDURLUGU.ACIKLAMA                                            AS muzeMudurluguAciklama_ci,
       dbo.MUZE_MUDURLUGU.AD                                                  AS muzeMudurluguAd_ci,
       dbo.MUZE_MUDURLUGU.BOYLAM                                              AS muzeMudurluguBoylam_ci,
       dbo.MUZE_MUDURLUGU.ENLEM                                               AS muzeMudurluguEnlem_ci,
       dbo.MUZE_MUDURLUGU.KOD                                                 AS muzeMudurluguKod_ci,
       dbo.UretimYeri.ACIKLAMA                                                AS uretimyeriAciklama_ci,
       dbo.UretimYeri.AD                                                      AS uretimyeriAd_ci,
       dbo.UYGARLIK.ACIKLAMA                                                  AS uygarlikAciklama_ci,
       dbo.UYGARLIK.AD                                                        AS uygarlikAd_ci,
       dbo.UNVAN.AD                                                           AS unvanAd_ci,
       dbo.UretimBolgesi.AD                                                   AS uretimbolgesiAd_ci,
       dbo.UretimBolgesi.ACIKLAMA                                             AS uretimbolgesiAciklama_ci,
       dbo.UZMANLIK_ALANI.AD                                                  AS uzmanlikAlaniAd_cim,
       dbo.UZMANLIK_ALANI.ACIKLAMA                                            AS uzmanlikAlaniAciklama_ci,
       dbo.ONEMLI_KISI.AD                                                     AS onemliKisiAd_ci,
       dbo.ONEMLI_KISI.ACIKLAMA                                               AS onemliKisiAciklama_ci,
       dbo.MESLEK.AD                                                          AS meslekAd_ci,
       dbo.MESLEK.ACIKLAMA                                                    AS meslekAciklama_ci,
       dbo.KADRO_DURUM.AD                                                     AS kadroDurumAd_ci,
       dbo.TASINIR_MAL_YONETMELIGI_KOD.AD                                     AS tasinirMalYonetmeligiKodAd_ci,
       FORMAT(ESER.permanentId, 'TR\.M\.000\.000\.000')                       AS eserEserId_ci,
       kondisyonDurumuAd.[AD]                                                 AS eserKondisyonDurumuAd_ci,
       sikkeDarpYonu.[AD]                                                     AS eserSikkeDarpYonuAd_ci,
       dbo.ESER.birlestirilmisEser                                            AS eserBirlestirilmisEser_ci,
       CASE WHEN dbo.ESER.file3DPath is not null THEN 1 ELSE 0 END            AS eser3DFile,
       dbo.ESER.webSite                                                       AS eserWebSitesi_ci,
       dbo.Workflow.dateCompleted                                             AS eserOnayZamani,
       CASE
           WHEN convert(varchar(25), dbo.Workflow.dateModified, 120) >
                convert(varchar(25), dbo.ESER.DUZENLEME_ZAMANI, 120) THEN dbo.Workflow.dateModified
           ELSE dbo.ESER.DUZENLEME_ZAMANI END                                 AS eserDuzenlemeZamani,
       CASE
           WHEN (select count(audit_ESER.ID) from audit_ESER where audit_ESER.ID = ESER.ID) > 0 THEN 1
           ELSE 0 END                                                         AS tarihsellik,
       STUFF((SELECT ',' + REPLACE(Keyword.AD, ',', ';')
              FROM Eser_Keyword,
                   Keyword
              WHERE dbo.Eser_Keyword.eserId = dbo.ESER.ID
                AND dbo.Eser_Keyword.keywordId = dbo.Keyword.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserAnahtarKelimeAd_cim,
       STUFF((SELECT ',' + REPLACE(dbo.Transcription.[translation], ',', ';')
              FROM Transcription
              WHERE dbo.Transcription.eser = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value(N'.[1]', N'nvarchar(max)'), 1, 1,
             N'')                                                             AS eserTranskripsiyonMetinCevirisi_cim,
       CASE
           WHEN EXISTS(SELECT dbo.Monogram.[fotografPath]
                       FROM Transcription,
                            Monogram
                       WHERE dbo.Transcription.eser = dbo.ESER.ID
                         AND dbo.Transcription.monogram = dbo.Monogram.ID
                         AND dbo.Monogram.[fotografPath] IS NOT NULL) THEN 1
           ELSE 0 END                                                         AS eserMonogramExist,

       STUFF((SELECT ',' + REPLACE(ESER_FOTOGRAF.ACIKLAMA, ',', ';')
              FROM ESER_FOTOGRAF
              WHERE dbo.ESER_FOTOGRAF.ESER_ID = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserFotografAciklama_cim,
       STUFF((SELECT ',' + REPLACE(Literatur.ad, ',', ';')
              FROM Eser_YayinLiteratur,
                   Literatur
              WHERE dbo.Eser_YayinLiteratur.eserId = dbo.ESER.ID
                AND dbo.Eser_YayinLiteratur.literaturId = dbo.Literatur.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yayinAd_cim,
       STUFF((SELECT ',' + REPLACE(Literatur.aciklama, ',', ';')
              FROM Eser_YayinLiteratur,
                   Literatur
              WHERE dbo.Eser_YayinLiteratur.eserId = dbo.ESER.ID
                AND dbo.Eser_YayinLiteratur.literaturId = dbo.Literatur.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yayinAciklama_cim,
       STUFF((SELECT ',' + REPLACE(YAZI_TIPI.AD, ',', ';')
              FROM Transcription,
                   YAZI_TIPI
              WHERE dbo.Transcription.eser = dbo.ESER.ID
                AND dbo.Transcription.yaziTipi = dbo.YAZI_TIPI.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yaziTipiAd_cim,
       STUFF((SELECT ',' + REPLACE(YAZI_TIPI.ACIKLAMA, ',', ';')
              FROM Transcription,
                   YAZI_TIPI
              WHERE dbo.Transcription.eser = dbo.ESER.ID
                AND dbo.Transcription.yaziTipi = dbo.YAZI_TIPI.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yaziTipiAciklama_cim,
       STUFF((SELECT ',' + REPLACE(Measure.AD, ',', ';')
              FROM Eser_Measure,
                   Measure
              WHERE dbo.Eser_Measure.eserId = dbo.ESER.ID
                AND dbo.Eser_Measure.measureId = dbo.Measure.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS olcuAd_cim,
       STUFF((SELECT ',' + REPLACE(Eser_Measure.DEGER, ',', ';')
              FROM Eser_Measure,
                   Measure
              WHERE dbo.Eser_Measure.eserId = dbo.ESER.ID
                AND dbo.Eser_Measure.measureId = dbo.Measure.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS olcuDeger_cim,
       STUFF((SELECT ',' + REPLACE(mt.AD, ',', ';')
              FROM dbo.Measure m
                       INNER JOIN dbo.MeasureType mt ON m.measureType = mt.ID
                       INNER JOIN dbo.Eser_Measure em ON em.measureId = m.ID
              WHERE em.eserId = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS olcuType_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.ad, ',', ';')
              FROM Eser_KaynakLiteratur,
                   Literatur
              WHERE dbo.Eser_KaynakLiteratur.eserId = dbo.ESER.ID
                AND dbo.Eser_KaynakLiteratur.literaturId = dbo.Literatur.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS kaynakAd_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.aciklama, ',', ';')
              FROM Eser_KaynakLiteratur,
                   Literatur
              WHERE dbo.Eser_KaynakLiteratur.eserId = dbo.ESER.ID
                AND dbo.Eser_KaynakLiteratur.literaturId = dbo.Literatur.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS kaynakAciklama_cim,

       STUFF((SELECT ',' + REPLACE(IliskilendirmeTurGrubu.AD, ',', ';')
              FROM Iliskilendirme,
                   IliskilendirmeTur,
                   IliskilendirmeTurGrubu
              WHERE dbo.Iliskilendirme.ID = dbo.ESER.iliskilendirmeId
                AND dbo.Iliskilendirme.turID = dbo.IliskilendirmeTur.ID
                AND dbo.IliskilendirmeTur.tur = dbo.IliskilendirmeTurGrubu.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS iliskilendirmeTurGrubuAd_cim,

       STUFF((SELECT ',' + REPLACE(EserSerh.metin, ',', ';')
              FROM EserSerh
              WHERE dbo.EserSerh.eserId = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserserhMetin_cim,

       STUFF((SELECT ',' + REPLACE(Atolye.AD, ',', ';')
              FROM Eser_Atolye,
                   Atolye
              WHERE dbo.Eser_Atolye.eserId = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS atolyeAd_cim,

       STUFF((SELECT ',' + REPLACE(YapimTeknigi.AD, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   YapimTeknigi
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_YAPIM_TEKNIGI_ID = dbo.YapimTeknigi.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserYapimTeknigiAd_cim,

       STUFF((SELECT ',' + REPLACE(YapimTeknigi.ACIKLAMA, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   YapimTeknigi
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_YAPIM_TEKNIGI_ID = dbo.YapimTeknigi.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserYapimTeknigiAciklama_cim,

       STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
              FROM Malzeme,
                   ESER_MALZEME_YAPIM_TEKNIGI
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yapimTeknigiMalzeme_cim,

       STUFF((SELECT ',' + REPLACE(p.AD, ',', ';')
              FROM ESER_ZIMMET ez
                       LEFT JOIN PERSONEL p on ez.ZIMMET_PERSONEL_ID = p.ID
              WHERE ez.ESER_ID = dbo.ESER.ID
                AND ez.AKTIF = 1
                AND ez.SILINMIS = 0
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserZimmetPersonelAd_cim,

       STUFF((SELECT ',' + REPLACE(p.SOYAD, ',', ';')
              FROM ESER_ZIMMET ez
                       LEFT JOIN PERSONEL p on ez.ZIMMET_PERSONEL_ID = p.ID
              WHERE ez.ESER_ID = dbo.ESER.ID
                AND ez.AKTIF = 1
                AND ez.SILINMIS = 0
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserZimmetPersonelSoyad_cim,

       STUFF((SELECT ',' + REPLACE(p.AD + ' ' + p.SOYAD, ',', ';')
              FROM ESER_ZIMMET ez
                       LEFT JOIN PERSONEL p on ez.ZIMMET_PERSONEL_ID = p.ID
              WHERE ez.ESER_ID = dbo.ESER.ID
                AND ez.AKTIF = 1
                AND ez.SILINMIS = 0
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserZimmetPersonelAdSoyad_cim,

       STUFF((SELECT ',' + REPLACE(dbo.ESER_ZIMMET.ZIMMET_TARIHI, ',', ';')
              FROM ESER_ZIMMET
              WHERE dbo.ESER_ZIMMET.ESER_ID = dbo.ESER.ID
                AND dbo.ESER_ZIMMET.AKTIF = 1
                AND dbo.ESER_ZIMMET.SILINMIS = 0
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserZimmetZimmetTarihi_cim,

       STUFF((SELECT ',' + REPLACE(SUSLEME_TEKNIGI.AD, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   SUSLEME_TEKNIGI
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.ESER_MALZEME_SUSLEME_TEKNIGI.SUSLEME_TEKNIGI_ID = dbo.SUSLEME_TEKNIGI.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiAd_cim,

       STUFF((SELECT ',' + REPLACE(SUSLEME_TEKNIGI.ACIKLAMA, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   SUSLEME_TEKNIGI
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.ESER_MALZEME_SUSLEME_TEKNIGI.SUSLEME_TEKNIGI_ID = dbo.SUSLEME_TEKNIGI.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiAciklama_cim,

       STUFF((SELECT ',' + REPLACE(ESER_MALZEME_GRUBU.AD, ',', ';')
              FROM ESER_MALZEME_GRUBU,
                   Malzeme,
                   ESER_MALZEME_SUSLEME_TEKNIGI
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.ESER_MALZEME_GRUBU.ID = dbo.Malzeme.MALZEME_GRUBU_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserMalzemeGrubuAd_cim,

       STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
              FROM Malzeme,
                   ESER_MALZEME_SUSLEME_TEKNIGI
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiMalzeme_cim,

       STUFF((SELECT ',' + REPLACE(Renk.ad, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiRenkAd_cim,

       STUFF((SELECT ',' + REPLACE(Renk.cmyk, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiRenkCMYK_cim,

       STUFF((SELECT ',' + REPLACE(Renk.NCS, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiRenkNCS_cim,

       STUFF((SELECT ',' + REPLACE(Renk.RGB, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiRenkRGB_cim,

       STUFF((SELECT ',' + REPLACE(Renk.mueskod, ',', ';')
              FROM ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS suslemeTeknigiRenkMueskod_cim,

       STUFF((SELECT ',' + REPLACE(Renk.ad, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yapimTeknigiRenkAd_cim,

       STUFF((SELECT ',' + REPLACE(Renk.cmyk, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yapimTeknigiRenkCMYK_cim,

       STUFF((SELECT ',' + REPLACE(Renk.NCS, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yapimTeknigiRenkNCS_cim,

       STUFF((SELECT ',' + REPLACE(Renk.RGB, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yapimTeknigiRenkRGB_cim,

       STUFF((SELECT ',' + REPLACE(Renk.mueskod, ',', ';')
              FROM ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              WHERE dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                AND dbo.Renk.ID = dbo.ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS yapimTeknigiRenkMueskod_cim,

       STUFF((SELECT ',' + REPLACE(DIL.AD, ',', ';')
              FROM DIL,
                   Transcription
              WHERE dbo.Transcription.eser = dbo.ESER.ID
                AND dbo.DIL.ID = dbo.Transcription.yaziDili
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS dilAd_cim,

       yazmaBasma.[AD]                                                        AS eserYazmaBasmaSecimiAd_ci,

       islamiGayri.[AD]                                                       AS eserIslamiGayriSecimiAd_ci,

       elisiDokuma.[AD]                                                       AS eserElisiDokumaSecimiAd_ci,

       eh.eserHareketMuzeAd_cim,
       eh.eserHareketTeslimAlanPersonelAdSoyad_cim,
       eh.eserHareketTeslimEdenPersonelAdSoyad_cim,
       eh.eserHareketEnvanterNo_cim,
       eh.eserHareketCikarilmaTarihi_cim,
       eh.eserHareketOnayTarihi_cim,
       eh.eserHareketOnaySayisi_cim,
       eh.eserHareketMuzeyeGelisTarihi_cim,
       eh.eserHareketEnvantereAlinmaTarihi_cim,
       eh.eserHareketAciklama_cim,
       eh.eserHareketBuluntuYeri_cim,
       eh.eserHareketEleGecirmeYeri_cim,
       eh.eserHareketIadeEdenUlke_cim,
       eh.eserHareketAktif_cim,
       eh.eserHareketSilinmis_cim,
       eh.kaziTurDeger_cim,
       eh.kaziTurAd_cim,
       eh.kaziAd_cim,
       eh.kaziAciklama_cim,
       eh.kaziEnlem_cim,
       eh.kaziBoylam_cim,
       eh.arastirmaTurAd_cim,
       eh.arastirmaTurAciklama_cim,
       eh.arastirmaAd_cim,
       eh.arastirmaAciklama_cim,
       eh.kaziBeldeAdi_cim,
       eh.kaziKaziKodu_cim,
       eh.kaziIlceAd_cim,
       eh.kaziIlAd_cim,
       eh.ulkeAd_cim,
       eh.sahisAd_cim,
       eh.sahisSoyad_cim,
       eh.sahisAd_cim + ' ' + eh.sahisSoyad_cim                               AS sahisAdSoyad_cim,
       eh.tuzelKisiTicariUnvan_cim,
       eh.eserHareketEserGelisSekliAd_cim,

       STUFF((SELECT DISTINCT ',' + REPLACE(ENVANTER_NO, ',', ';')
              FROM audit_ESER
              WHERE audit_ESER.ID = eser.ID
                 FOR XML PATH(''), TYPE).value('.', 'varchar(max)'), 1, 1, '')   AS eserEnvanterNoHistory_cim

FROM dbo.ESER
         LEFT JOIN dbo.ESER_DEPO ON dbo.ESER_DEPO.ESER_ID = dbo.ESER.ID AND dbo.ESER_DEPO.AKTIF = 'true' AND
                                    dbo.ESER_DEPO.SILINMIS = 'false'
         LEFT JOIN dbo.ALAN_KONUMU ON dbo.ESER_DEPO.ALAN_KONUMU_ID = dbo.ALAN_KONUMU.ID
         LEFT JOIN dbo.ALAN_KONUMU_TUR ON dbo.ALAN_KONUMU.ALAN_KONUMU_TUR_ID = dbo.ALAN_KONUMU_TUR.ID
         LEFT JOIN dbo.ALAN ON dbo.ALAN_KONUMU.ALAN_ID = dbo.ALAN.ID
         LEFT JOIN dbo.ALAN_TUR ON dbo.ALAN_TUR.ID = dbo.ALAN.ALAN_TUR_ID
         LEFT JOIN dbo.BINA ON dbo.ALAN.BINA_ID = dbo.BINA.ID
         LEFT JOIN dbo.BAGLI_BIRIM ON dbo.BINA.BAGLI_BIRIM_ID = dbo.BAGLI_BIRIM.ID
         LEFT JOIN dbo.Workflow ON dbo.ESER.ID = dbo.Workflow.eserId
         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Workflow.mudurlukId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.IL ON dbo.IL.ID = dbo.BAGLI_BIRIM.IL_ID
         LEFT JOIN dbo.ILCE ON dbo.ILCE.ID = dbo.BAGLI_BIRIM.ILCE_ID
         LEFT JOIN dbo.Donem ON dbo.ESER.CAG_DONEM_ID = dbo.Donem.ID
         LEFT JOIN dbo.Cag ON dbo.Donem.cagId = dbo.Cag.ID
         LEFT JOIN dbo.KRONOLOJI ON dbo.CAG.KRONOLOJI_ID = dbo.KRONOLOJI.ID
         LEFT JOIN dbo.UYGARLIK ON dbo.ESER.UYGARLIK_ID = dbo.UYGARLIK.ID
         LEFT JOIN dbo.HUKUMDAR ON dbo.ESER.HUKUMDAR_ID = dbo.HUKUMDAR.ID
         LEFT JOIN dbo.UretimYeri ON dbo.ESER.uretimYeriId = dbo.UretimYeri.ID
         LEFT JOIN dbo.Iliskilendirme ON dbo.ESER.iliskilendirmeId = dbo.Iliskilendirme.ID
         LEFT JOIN dbo.KULLANICI ON dbo.ESER.YARATMA_KULLANICI_ID = dbo.KULLANICI.ID
         LEFT JOIN dbo.ESER_ALT_TUR ON dbo.ESER.ESER_ALT_TUR_ID = dbo.ESER_ALT_TUR.ID
         LEFT JOIN dbo.ESER_TUR ON dbo.ESER_ALT_TUR.ESER_TUR_ID = dbo.ESER_TUR.ID
         LEFT JOIN dbo.TASINIR_MAL_YONETMELIGI_KOD
                   ON dbo.ESER.TASINIR_MAL_YON_ID = dbo.TASINIR_MAL_YONETMELIGI_KOD.ID
         LEFT JOIN dbo.PERSONEL ON dbo.KULLANICI.PERSONEL_ID = dbo.PERSONEL.ID
         LEFT JOIN dbo.UNVAN ON dbo.PERSONEL.unvan = dbo.UNVAN.ID
         LEFT JOIN dbo.UretimBolgesi ON dbo.ESER.uretimBolgesiId = dbo.UretimBolgesi.ID
         LEFT JOIN dbo.UZMANLIK_ALANI ON dbo.PERSONEL.UZMANLIK_ALANI_ID = dbo.UZMANLIK_ALANI.ID
         LEFT JOIN dbo.ONEMLI_KISI ON dbo.ESER.ESERI_KULLANAN_ONEMLI_KISI_ID = dbo.ONEMLI_KISI.ID
         LEFT JOIN dbo.MESLEK ON dbo.PERSONEL.MESLEK_ID = dbo.MESLEK.ID
         LEFT JOIN dbo.KADRO_DURUM ON dbo.KADRO_DURUM.ID = dbo.PERSONEL.kadroDurum
         LEFT JOIN Pick islamiGayri ON islamiGayri.ID = dbo.ESER.ISLAMI_GAYRI_SECIMI
         LEFT JOIN Pick elisiDokuma ON elisiDokuma.ID = dbo.ESER.ELISI_DOKUMA_SECIMI
         LEFT JOIN Pick sikkeDarpYonu ON sikkeDarpYonu.ID = dbo.ESER.SIKKE_DARP_YONU
         LEFT JOIN Pick kondisyonDurumuAd ON dbo.ESER.KONDISYON_DURUMU = kondisyonDurumuAd.ID
         LEFT JOIN Pick yazmaBasma ON yazmaBasma.ID = dbo.ESER.YAZMA_BASMA_SECIMI
         LEFT JOIN dbo.Birlestirme ON dbo.ESER.ID = dbo.Birlestirme.anaEserId
         LEFT JOIN dbo.DarpYeri ON dbo.ESER.darpYeriId = dbo.DarpYeri.ID
         LEFT JOIN v_eser_hareket_agg eh ON eh.ESER_HAREKET_ESER_ID = dbo.ESER.ID

WHERE dbo.ESER.permanentId IS NOT NULL;
