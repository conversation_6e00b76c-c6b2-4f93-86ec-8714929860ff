-- ////////////
IF OBJECT_ID('dbo.LAB_ARTIFACT_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.LAB_ARTIFACT_VIEW;

CREATE VIEW LAB_ARTIFACT_VIEW AS
SELECT DISTINCT dbo.Lab_Artifact.ID                                                            AS id,
                dbo.Lab_Artifact.eserId                                                        AS labArtifactEserid,
                dbo.ESER.ESER_OZEL_ADI                                                         as eserEserOzelAdi_ci,

                dbo.ESER.ENVANTER_NO                                                           AS eserEskiEnvanterNo_ci,

                dbo.ESER_TUR.AD                                                                AS eserTurAd_ci,
                dbo.ESER_ALT_TUR.AD                                                            AS eserAltTurAd_ci,
                dbo.ESER_TUR.AD + ' > ' + dbo.ESER_ALT_TUR.AD                                  AS eserTurVeAltTurAd_ci,
                dbo.TASINIR_MAL_YONETMELIGI_KOD.AD                                             AS tasinirMalYonetmeligiKodAd_ci,
                dbo.Donem.AD                                                                   as donemAd_ci,
                dbo.Lab_Artifact.quintessence                                                  as quintessence,
                dbo.Lab_Artifact.changeTime                                                    as changeTime,

                dbo.Lab_Artifact.pieceNumber                                                   AS labArtifactPiecenumber,
                CASE
                    WHEN Lab_Request.ID IS NULL THEN NULL
                    ELSE CONCAT(DATEPART(year, dbo.Lab_Request.dateCreated), '-',
                                dbo.Lab_Request.ID) END                                        AS labRequestId_ci,
                (CASE
                     WHEN dbo.Lab_Request.dateCreated IS NULL THEN dbo.Lab_ExternalProcess.dateCreated
                     ELSE dbo.Lab_Request.dateCreated END)                                     AS labRequestDatecreated,
                (CASE
                     WHEN dbo.MUZE_MUDURLUGU.AD IS NULL THEN m2.AD
                     ELSE dbo.MUZE_MUDURLUGU.AD END)                                           AS muzeMudurluguAd_ci,
                --dbo.Omk_Mudurluk.AD                                                                       AS Lab_PrivateMuseum_name,
                (CASE
                     WHEN dbo.Lab_LaboratoryDirectorate.AD IS NULL THEN l2.AD
                     ELSE dbo.Lab_LaboratoryDirectorate.AD END)                                AS mudurlukAd_ci,
                dbo.Lab_Task.dateStarted                                                       AS labTaskDatestarted,
                dbo.Lab_Task.dateFinished                                                      AS labTaskDatefinished,
                dbo.Lab_Task.ID                                                                AS labTaskId,
                dbo.Material.name                                                              AS labMaterialName_ci,
                FORMAT(ESER.permanentId, 'TR\.M\.000\.000\.000')                               AS eserEserId_ci,

                (SELECT ESER_FOTOGRAF.FOTOGRAF_PATH
                 FROM ESER_FOTOGRAF
                 WHERE ESER_FOTOGRAF.ESER_ID = ESER.ID
                   AND ESER_FOTOGRAF.ANA_FOTOGRAF = 1)                                         AS eserTanimlayiciFotografPath_ci,
                CASE
                    WHEN dbo.Lab_Artifact.state > 1200 AND dbo.Lab_Artifact.state < 1300 THEN '1'
                    ELSE CASE WHEN dbo.Lab_ExternalProcess.state = 4 THEN '1' ELSE '0' END END AS operationsCompleted,

                ISNULL(dbo.Omk_Mudurluk.AD, '') + '##' + ISNULL(dbo.ARASTIRMA.AD, '') + '##' +
                ISNULL(Lab_Collectioner.name, '') + '##' + ISNULL(KAZI.AD, '') + '##' +
                ISNULL(TUZEL_KISI.TICARI_UNVAN, '')
                                                                                               as aitOlduguBirim,

                STUFF((SELECT DISTINCT ',' + dbo.Lab_Commission.description
                       from dbo.Lab_Commission
                       where dbo.Lab_Commission.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS labCommissionDescription_cim,
                STUFF((SELECT DISTINCT ',' + dbo.PERSONEL.AD + ' ' + dbo.PERSONEL.SOYAD
                       from dbo.PERSONEL,
                            dbo.Lab_CommissionPersonel,
                            dbo.Lab_Commission
                       where dbo.PERSONEL.ID = dbo.Lab_CommissionPersonel.personelId
                         AND dbo.Lab_Commission.ID = dbo.Lab_CommissionPersonel.commissionId
                         AND dbo.Lab_Commission.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS commissionPersonelAdsoyad_cim,
                STUFF((SELECT DISTINCT ',' + Lab_ArtifactComment.comment
                       from dbo.Lab_ArtifactComment
                       where dbo.Lab_Artifact.ID = dbo.Lab_ArtifactComment.artifactId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS artifactComment_cim,
                STUFF((SELECT DISTINCT ',' + Lab_RequestComment.comment
                       from dbo.Lab_RequestComment
                       where dbo.Lab_Request.ID = dbo.Lab_RequestComment.requestId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS requestComment_cim,
                STUFF((SELECT DISTINCT ',' + Lab_Determination.description
                       from dbo.Lab_Determination
                       where dbo.Lab_Artifact.ID = dbo.Lab_Determination.artifactId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS determDescription_cim,
                STUFF((SELECT DISTINCT ',' + Lab_Problem.directorComment
                       from dbo.Lab_Problem
                       where dbo.Lab_Artifact.ID = dbo.Lab_Problem.artifactId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS problemDirectorComment_cim,
                STUFF((SELECT ',' + Lab_Analysis.description
                       from dbo.Lab_Analysis
                       where dbo.Lab_Artifact.ID = dbo.Lab_Analysis.artifactId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS analysisDescription_cim,
                STUFF((SELECT DISTINCT ',' + Lab_AnalysisType.name
                       from dbo.Lab_AnalysisType,
                            dbo.Lab_Analysis,
                            dbo.Lab_Analysis_AnalysisType
                       WHERE dbo.Lab_Artifact.ID = dbo.Lab_Analysis.artifactId
                         AND Lab_Analysis_AnalysisType.analysis = dbo.Lab_Analysis.ID
                         AND dbo.Lab_Analysis_AnalysisType.analysisType = dbo.Lab_AnalysisType.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS analysisTypeName_cim,
                STUFF((SELECT DISTINCT ',' + dbo.Lab_Tool.name
                       from dbo.Lab_Tool,
                            dbo.Lab_AppliedWorkTool,
                            dbo.Lab_AppliedWork
                       where dbo.Lab_Tool.ID = dbo.Lab_AppliedWorkTool.toolId
                         AND dbo.Lab_AppliedWorkTool.appliedWorkId = dbo.Lab_AppliedWork.ID
                         AND dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedworkTool_cim,
                STUFF((SELECT DISTINCT ',' + dbo.Lab_Substance.name
                       from dbo.Lab_Substance,
                            dbo.Lab_AppliedWorkSubstance,
                            dbo.Lab_AppliedWork
                       where dbo.Lab_Substance.ID = dbo.Lab_AppliedWorkSubstance.substanceId
                         AND dbo.Lab_AppliedWorkSubstance.appliedWorkId = dbo.Lab_AppliedWork.ID
                         AND dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedworkSubstance_cim,
                STUFF((SELECT DISTINCT ',' + dbo.Lab_Chemical.name
                       from dbo.Lab_Chemical,
                            dbo.Lab_AppliedWorkChemical,
                            dbo.Lab_AppliedWork
                       where dbo.Lab_Chemical.ID = dbo.Lab_AppliedWorkChemical.chemicalId
                         AND dbo.Lab_AppliedWorkChemical.appliedWorkId = dbo.Lab_AppliedWork.ID
                         AND dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedworkChemical_cim,
                STUFF((SELECT DISTINCT ',' + dbo.Lab_Kullanici.AD + ' ' + dbo.Lab_Kullanici.SOYAD
                       from dbo.Lab_Kullanici,
                            dbo.Lab_TaskUser
                       where dbo.Lab_Kullanici.ID = dbo.Lab_TaskUser.userId
                         AND dbo.Lab_TaskUser.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS taskUserName_cim,
                STUFF((SELECT DISTINCT ',' + FORMAT(dbo.Lab_Analysis.ID, 'ANZ\.000\.000\.000')
                       from dbo.Lab_Analysis
                       where dbo.Lab_Artifact.ID = dbo.Lab_Analysis.artifactId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS analysisId_cim,
                STUFF(
                        (SELECT DISTINCT ', ' + FORMAT(dbo.Lab_Analysis.dateAnalyzed, 'yyyy-MM-ddTHH:mm:ssZ') -- ISO 8601 formatı
                         FROM dbo.Lab_Analysis
                         WHERE dbo.Lab_Artifact.ID = dbo.Lab_Analysis.artifactId
                            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, ''
                )                                                                              AS dateAnalyzed_cim,

                STUFF((SELECT DISTINCT ',' + dbo.Lab_AnalysisPlace.name
                       from dbo.Lab_AnalysisPlace,
                            dbo.Lab_Analysis
                       where dbo.Lab_Artifact.ID = dbo.Lab_Analysis.artifactId
                         AND Lab_Analysis.analysisPlaceId = Lab_AnalysisPlace.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS analysisPlaceName_cim,
                STUFF((SELECT DISTINCT ',' + dbo.Lab_AnalysisDevice.name
                       from dbo.Lab_AnalysisDevice,
                            dbo.Lab_Analysis,
                            dbo.Lab_Analysis_AnalysisDevice
                       where dbo.Lab_Artifact.ID = dbo.Lab_Analysis.artifactId
                         AND dbo.Lab_Analysis_AnalysisDevice.analysis = dbo.Lab_Analysis.ID
                         AND Lab_Analysis_AnalysisDevice.analysisDevice = Lab_AnalysisDevice.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS analysisDeviceName_cim,
                STUFF((SELECT DISTINCT ',' + (CASE WHEN (dbo.Lab_Commission.type = 1) then 'İç' else 'Dış' end)
                       from dbo.Lab_Commission
                       where dbo.Lab_Commission.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS commissionType_cim,
                STUFF((SELECT DISTINCT ',' + dbo.Lab_Commission.meetingPlace
                       from dbo.Lab_Commission
                       where dbo.Lab_Commission.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS meetingPlace_cim,
                STUFF(
                        (SELECT DISTINCT ',' + FORMAT(dbo.Lab_Commission.dateMeeting, 'yyyy-MM-ddTHH:mm:ssZ') -- ISO 8601 formatına çevir
                         FROM dbo.Lab_Commission
                         WHERE dbo.Lab_Task.ID = dbo.Lab_Commission.taskId
                            FOR XML PATH(''), TYPE).value('.', 'NVARCHAR(MAX)'), 1, 1, ''
                )                                                                              AS dateMeeting_cim,


                STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
                       from Malzeme,
                            ESER_MALZEME_YAPIM_TEKNIGI
                       where dbo.ESER_MALZEME_YAPIM_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                         AND dbo.ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.ESER.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS malzemeAds_cim,
                STUFF((SELECT ',' + REPLACE(Lab_DecayType.name, ',', ';')
                       from Lab_DecayType,
                            Lab_DeterminationDecayType,
                            Lab_Determination,
                            Lab_Material_DecayType
                       where dbo.Lab_DecayType.ID = dbo.Lab_Material_DecayType.decayType
                         AND dbo.Lab_DeterminationDecayType.decayTypeId = Lab_Material_DecayType.ID
                         AND dbo.Lab_DeterminationDecayType.determinationId = dbo.Lab_Determination.ID
                         AND dbo.Lab_Determination.artifactId = dbo.Lab_Artifact.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS decayNames_cim,


                STUFF((SELECT ',' + Lab_TechnicType.name
                       from dbo.Lab_TechnicType,
                            Lab_AppliedWork,
                            Lab_Technic,
                            dbo.Lab_AppliedProcedure,
                            Lab_AppliedWorkAppliedProcedure
                       WHERE dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                         AND Lab_AppliedWork.ID = Lab_AppliedWorkAppliedProcedure.appliedWorkId
                         AND Lab_AppliedWorkAppliedProcedure.appliedProcedureId = Lab_AppliedProcedure.ID
                         AND Lab_Technic.ID = Lab_AppliedProcedure.technicId
                         AND Lab_TechnicType.ID = Lab_Technic.technicTypeId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedWorkTechnicType_cim,

                STUFF((SELECT ',' + Lab_Technic.name
                       from dbo.Lab_Technic,
                            Lab_AppliedWork,
                            dbo.Lab_AppliedWorkAppliedProcedure,
                            Lab_AppliedProcedure
                       WHERE dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                         AND Lab_AppliedWork.ID = Lab_AppliedWorkAppliedProcedure.appliedWorkId
                         AND Lab_AppliedWorkAppliedProcedure.appliedProcedureId = Lab_AppliedProcedure.ID
                         AND Lab_Technic.ID = Lab_AppliedProcedure.technicId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedWorkTechnic_cim,

                STUFF((SELECT ',' + Lab_AppliedProcedure.AD
                       from dbo.Lab_AppliedWork,
                            Lab_AppliedProcedure,
                            Lab_AppliedWorkAppliedProcedure
                       WHERE dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                         AND Lab_AppliedWork.ID = dbo.Lab_AppliedWorkAppliedProcedure.appliedWorkId
                         AND dbo.Lab_AppliedWorkAppliedProcedure.appliedProcedureId = dbo.Lab_AppliedProcedure.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedWorkappliedProcedure_cim,

                STUFF((SELECT ',' + Lab_AppliedWork.description
                       from dbo.Lab_AppliedWork
                       WHERE dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                      '')                                                                      AS appliedWorkDescription_cim,

                CASE
                    WHEN Lab_ExternalProcess_Artifact.artifactId IS NOT NULL THEN CAST('1' AS BIT)
                    ELSE CAST('0' AS BIT) END                                                  AS artifactProcessType,
                dbo.Lab_ExternalProcess.description                                            AS externalProcessDescription_ci,
                dbo.Lab_ExternalProcess.name                                                   AS externalProcessName_ci,
                p1.AD                                                                          AS externalProcessRequestedFrom_ci,
                p2.AD                                                                          AS externalProcessRequestedMethod_ci,
                STUFF(
                        (SELECT ',' + Lab_TechnicType.name + ' > ' + Lab_Technic.name + ' > ' +
                                Lab_AppliedProcedure.AD
                         FROM dbo.Lab_AppliedWork,
                              Lab_AppliedWorkAppliedProcedure,
                              Lab_AppliedProcedure,
                              Lab_Technic,
                              Lab_TechnicType
                         WHERE dbo.Lab_AppliedWork.taskId = dbo.Lab_Task.ID
                           AND Lab_AppliedWork.ID = Lab_AppliedWorkAppliedProcedure.appliedWorkId
                           AND Lab_AppliedWorkAppliedProcedure.appliedProcedureId = Lab_AppliedProcedure.ID
                           AND Lab_Technic.ID = Lab_AppliedProcedure.technicId
                           AND Lab_TechnicType.ID = Lab_Technic.technicTypeId
                            for xml path(''), type).value('.', 'varchar(max)'), 1, 2,
                        '')                                                                    AS technicTypeTechnicAppliedProcedure_cim

FROM dbo.Lab_Artifact
         LEFT JOIN dbo.Lab_Task ON dbo.Lab_Task.artifactId = dbo.Lab_Artifact.ID
         LEFT JOIN dbo.Material ON dbo.Lab_Artifact.materialId = dbo.Material.ID
         LEFT JOIN dbo.Lab_Request ON dbo.Lab_Artifact.requestId = dbo.Lab_Request.ID
         LEFT JOIN dbo.Lab_Movement ON dbo.Lab_Request.movementId = dbo.Lab_Movement.ID
         LEFT JOIN dbo.Lab_LaboratoryDirectorate
                   ON dbo.Lab_Movement.laboratoryDirectorateId = dbo.Lab_LaboratoryDirectorate.ID
         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.Lab_Movement.museumDirectorateId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.ESER ON dbo.Lab_Artifact.eserId = dbo.ESER.ID
         LEFT JOIN dbo.ESER_ALT_TUR ON dbo.ESER.ESER_ALT_TUR_ID = dbo.ESER_ALT_TUR.ID
         LEFT JOIN dbo.ESER_TUR ON dbo.ESER_ALT_TUR.ESER_TUR_ID = dbo.ESER_TUR.ID
         LEFT JOIN dbo.Donem ON dbo.Donem.ID = dbo.ESER.CAG_DONEM_ID
         LEFT JOIN dbo.TASINIR_MAL_YONETMELIGI_KOD
                   ON dbo.ESER.TASINIR_MAL_YON_ID = dbo.TASINIR_MAL_YONETMELIGI_KOD.ID
         LEFT JOIN dbo.Omk_Mudurluk ON dbo.Lab_Movement.privateMuseumId = dbo.Omk_Mudurluk.ID
         LEFT JOIN dbo.ARASTIRMA ON dbo.Lab_Movement.explorationId = dbo.ARASTIRMA.ID
         LEFT JOIN dbo.Lab_Collectioner ON dbo.Lab_Movement.collectionerId = dbo.Lab_Collectioner.ID
         LEFT JOIN dbo.KAZI ON dbo.Lab_Movement.excavationId = dbo.KAZI.ID
         LEFT JOIN dbo.TUZEL_KISI ON dbo.Lab_Movement.legalEntityId = dbo.TUZEL_KISI.ID

         LEFT JOIN dbo.Lab_ExternalProcess_Artifact
                   ON dbo.Lab_ExternalProcess_Artifact.artifactId = dbo.Lab_Artifact.ID
         LEFT JOIN dbo.Lab_ExternalProcess
                   ON dbo.Lab_ExternalProcess.ID = dbo.Lab_ExternalProcess_Artifact.externalProcessId
         LEFT JOIN dbo.MUZE_MUDURLUGU m2 ON m2.ID = dbo.Lab_ExternalProcess.museumDirectorateId
         LEFT JOIN dbo.Lab_LaboratoryDirectorate l2 ON l2.ID = dbo.Lab_ExternalProcess.labDirectorateId
         LEFT JOIN dbo.Lab_Pick p1 ON p1.ID = Lab_ExternalProcess.requestedFrom
         LEFT JOIN dbo.Lab_Pick p2 ON p2.ID = Lab_ExternalProcess.requestedMethod

WHERE dbo.Lab_Artifact.state NOT IN (8888, 1300);