-- ////////////
IF OBJECT_ID('dbo.KAM_TREASUREREQUEST_VIEW', 'V') IS NOT NULL
DROP VIEW dbo.KAM_TREASUREREQUEST_VIEW;

CREATE VIEW KAM_TREASUREREQUEST_VIEW AS
SELECT FORMAT(dbo.Kam_TreasureRequest.ID, 'DB\.000\.000\.000')              AS id_ci,
       dbo.Kam_TreasureRequest.ID                                           AS uid,
       dbo.Kam_TreasureRequest.AKTIF                                        AS aktif,
       dbo.Kam_TreasureRequest.SILINMIS                                     AS silinmis,
       dbo.Kam_TreasureRequest.ACIKLAMA                                     AS aciklama_ci,
       dbo.Kam_TreasureRequest.beginningDate                                AS baslangicTarihi,
       dbo.Kam_TreasureRequest.endingDate                                   AS bitisTarihi,
       dbo.Kam_TreasureRequest.isNotificationRequired                       AS isNotificationRequired,
       dbo.Kam_TreasureRequest.isPortableArtifactFound                      AS isPortableArtifactFound,


       dbo.Kam_TreasureRequest.isUnportableArtifactFound                    AS isUnportableArtifactFound,
       dbo.MUZE_MUDURLUGU.AD                                                AS muzeMudurluguAd_ci,
       dbo.Kam_TreasureRequest.archiveData                                  AS archive,
       dbo.Kam_TreasureRequest.completedProcess                             AS completedProcess,
       dbo.Kam_TreasurePermit.newPermitNumber                               AS newPermitNumber,
       dbo.Kam_TreasurePermit.oldPermitNumber                               AS oldPermitNumber,
       dbo.Kam_TreasurePermit.pdfPath                                       AS permitPath_ci,
       dbo.Kam_TreasurePermit.permitDate                                    AS izinTarihi,

       dbo.Kam_TreasurePermit.isExperienced                                 AS isExperienced,

       landowningState.AD                                                   AS landowningStateName_ci,
       treasureRequestState.AD                                              AS treasureRequestStateName_ci,

       dbo.Kam_TreasureLand.[ada]                                           AS olayAda,

-- ortak alanlar ----
       dbo.Kam_TreasureLand.longitude                                       as enlem,
       dbo.Kam_TreasureLand.derinlik                                        as derinlik,
       dbo.Kam_TreasureLand.latitude                                        as boylam,
       dbo.Kam_TreasureLand.genislik                                        as genislik,
       dbo.Kam_TreasureLand.pafta                                           as olayPafta_ci,
       dbo.Kam_TreasureLand.parsel                                          as olayParsel,
       dbo.Kam_TreasureLand.uzunluk                                         as uzunluk,

       dbo.Kam_TreasureRequest.requestDate                                  AS olayTarihi,
       dbo.Kam_TreasurePermit.ACIKLAMA                                      AS olayAciklama_ci,
       dbo.Kam_TreasureLand.adres                                           AS olayYeriAciklama_ci,
       dbo.IL.AD                                                            AS ilAd_ci,
       dbo.ILCE.AD                                                          AS ilceAd_ci,

       createdBy.AD + ' ' + createdBy.SOYAD                                 AS olusturanKullaniciAd_ci,
       updatedBy.AD + ' ' + updatedBy.SOYAD                                 AS sonDuzenleyenKullaniciAd_ci,

       dbo.Kam_TreasureRequest.dateCreated                                  AS olusturmaZamani,
       dbo.Kam_TreasureRequest.dateUpdated                                  AS duzenlemeZamani,

       dbo.Kam_TreasureRequestPhotograph.ACIKLAMA                           AS fotografAciklama_ci,
       dbo.Kam_TreasureRequestPhotograph.fotografPath                       AS fotografPath_ci,
       dbo.Kam_TreasureRequestPhotograph.AD                                 AS fotografBasligi_ci,

       'Define Başvurusu'                                                   AS incidentType_ci,

       1                                                                    AS olayaKarisanKisiSayisi,


       STUFF((SELECT REPLACE(pp.AD + ' ' + pp.SOYAD, ',', ';') + '##'
              from dbo.Kam_TreasureRequest_Personnel sas,
                   dbo.PERSONEL pp
              where sas.treasureRequestId = dbo.Kam_TreasureRequest.ID
                AND sas.personelId = pp.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') AS gorevliPersonelAds_cim,

       STUFF((SELECT REPLACE(pp.name, ',', ';') + '##'
              from dbo.Kam_TreasureRequest_Equipment sas,
                   dbo.Kam_Tool pp
              where sas.treasureRequestId = dbo.Kam_TreasureRequest.ID
                AND sas.equipment = pp.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') AS ekipmanAds_cim,

       STUFF((SELECT REPLACE(pp.SICIL_NO, ',', ';') + '##'
              from dbo.Kam_TreasureRequest_Personnel sas,
                   dbo.PERSONEL pp
              where sas.treasureRequestId = dbo.Kam_TreasureRequest.ID
                AND sas.personelId = pp.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') AS gorevliPersonelSicilNo_cim,


       dbo.Kam_TreasureSahis.AD + ' ' + dbo.Kam_TreasureSahis.AD            as olaySahisAds_ci

FROM dbo.Kam_TreasureRequest
         INNER JOIN dbo.Kam_TreasurePermit ON dbo.Kam_TreasurePermit.treasureRequestId = dbo.Kam_TreasureRequest.ID
         INNER JOIN dbo.Kam_TreasureLand ON dbo.Kam_TreasureLand.treasureRequestId = dbo.Kam_TreasureRequest.ID

         LEFT JOIN dbo.Kam_TreasureRequestPhotograph
                   ON dbo.Kam_TreasureRequestPhotograph.treasureRequest = dbo.Kam_TreasureRequest.ID AND
                      dbo.Kam_TreasureRequestPhotograph.ID = (SELECT TOP 1 ID
                                                              FROM Kam_TreasureRequestPhotograph
                                                              WHERE dbo.Kam_TreasureRequestPhotograph.SILINMIS = 0
                                                                AND dbo.Kam_TreasureRequestPhotograph.AKTIF = 1
                                                                AND dbo.Kam_TreasureRequestPhotograph.treasureRequest =
                                                                    dbo.Kam_TreasureRequest.ID)

         INNER JOIN dbo.Kam_TreasureSahis ON dbo.Kam_TreasurePermit.permitteeId = dbo.Kam_TreasureSahis.ID

         INNER JOIN dbo.MUZE_MUDURLUGU ON dbo.Kam_TreasureRequest.mudurlukId = dbo.MUZE_MUDURLUGU.ID
         LEFT JOIN dbo.Kam_Pick as landowningState ON dbo.Kam_TreasurePermit.landowningState = landowningState.ID
         LEFT JOIN dbo.Kam_Pick AS treasureRequestState
                   ON dbo.Kam_TreasureRequest.treasureRequestState = treasureRequestState.ID

         LEFT JOIN dbo.IL ON dbo.Kam_TreasureLand.il = dbo.IL.ID
         LEFT JOIN dbo.ILCE ON dbo.Kam_TreasureLand.ilce = dbo.ILCE.ID
         LEFT JOIN dbo.PERSONEL createdBy ON dbo.Kam_TreasureRequest.createdBy = createdBy.ID
         LEFT JOIN dbo.PERSONEL updatedBy ON dbo.Kam_TreasureRequest.createdBy = updatedBy.ID
WHERE Kam_TreasureRequest.updateInProgress = 0;