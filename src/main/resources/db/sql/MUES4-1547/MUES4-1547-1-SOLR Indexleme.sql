-- ////////////
IF OBJECT_ID('dbo.PERSONEL_TUMBILGILER', 'V') IS NOT NULL
DROP VIEW dbo.PERSONEL_TUMBILGILER;

CREATE VIEW PERSONEL_TUMBILGILER AS
select distinct PERSONEL.ID                                                          as id,
                PERSONEL.AKTIF                                                       as personelAktif,
                PERSONEL.SILINMIS                                                    as personelSilinmis,
                PERSONEL.ACIKLAMA                                                    as personelAciklama_ci,
                PERSONEL.SICIL_NO                                                    as personelSicilNo_ci,
                PERSONEL.TC_KIMLIK_NO                                                as personelTcKimlikNo_ci,
                PERSONEL.PASAPORT_NO                                                 as personelPasaportNo_ci,
                PERSONEL.YABANCI_KIMLIK_NO                                           as personelYabanciKimlikNo_ci,
                PERSONEL.AD + ' ' + PERSONEL.SOYAD                                   as personelAdSoyad_ci,
                PERSONEL.DOGUM_YERI                                                  as personelDogum<PERSON>eri_ci,
                PERSONEL.DOGUM_TARIHI                                                as personelDogumTarihi_ci,
                PERSONEL.TELEFON_NO_IS                                               as personelTelefonNoIs_ci,
                PERSONEL.TELEFON_NO_CEP                                              as personelTelefonNoCep_ci,
                PERSONEL.CALISMA_DURUMU                                              as personelCalismaDurumu_ci,
                PERSONEL.EPOSTA_KURUMSAL                                             as personelEpostaKurumsal_ci,
                PERSONEL.EPOSTA_KISISEL                                              as personelEpostaKisisel_ci,
                PERSONEL.dateUpdated                                                 as dateUpdated,

--Artifact_Counting.ID as eserSayimId,
--Artifact_Counting.ID as eserSayimId_ci,
--Artifact_Counting.AD as eserSayimAd,
--Artifact_Counting.AD as eserSayimAd_ci,
--Artifact_Counting.startDate as eserSayimBaslamaTarihi,
--Artifact_Counting.startDate as eserSayimBaslamaTarihi_ci,
--Artifact_Counting.endDate as eserSayimBitisTarihi,
--Artifact_Counting.endDate as eserSayimBitisTarihi_ci,
--Artifact_Counting.countingType as eserSayimTipi,
--Artifact_Counting.countingType as eserSayimTipi_ci,
--Artifact_Counting.ACIKLAMA as eserSayimAciklama,
--Artifact_Counting.ACIKLAMA as eserSayimAciklama_ci,

--ESER_ZIMMET.ID as eserZimmetId,
--ESER_ZIMMET.ID as eserZimmetId_ci,
--ESER_ZIMMET.ZIMMET_TARIHI as eserZimmetZimmetTarihi,
--ESER_ZIMMET.ZIMMET_TARIHI as eserZimmetZimmetTarihi_ci,
--ESER_ZIMMET.ACIKLAMA as eserZimmetAciklama,
--ESER_ZIMMET.ACIKLAMA as eserZimmetAciklama_ci,

                KULLANICI.ID                                                         as kullaniciId_ci,
                KULLANICI.KULLANICI_ADI                                              as kullaniciTcNo_ci,
                KULLANICI.AKTIF                                                      as kullaniciAktif_ci,
                KULLANICI.SILINMIS                                                   as kullaniciSilinmis,

                MESLEK.AD                                                            as meslekAd_ci,
                UNVAN.AD                                                             as unvanAd_ci,
                KADRO_DURUM.AD                                                       as kadroDurumAd_ci,
                MUZE_MUDURLUGU.AD                                                    as muzeMudurluguAd_ci,

                MUZE_MUDURLUGU.ACIKLAMA                                              AS muzeAciklama_ci,
                MUZE_MUDURLUGU.AKTIF                                                 AS muzeAktif_ci,
                MUZE_MUDURLUGU.SILINMIS                                              AS muzeSilinmis,

-- audit_PERSONEL.AD as audit_PERSONEL_AD,

                (SELECT count(ESER_ZIMMET.ID)
                 from ESER_ZIMMET
                 where ESER_ZIMMET.ZIMMET_PERSONEL_ID = PERSONEL.ID
                   and ESER_ZIMMET.AKTIF = 1
                   and ESER_ZIMMET.SILINMIS = 0)                                     as ZIMMET_ESER_SAYISI,

                STUFF((SELECT ',' + REPLACE(UZMANLIK_ALANI.AD, ',', ';')
                       from PersonelUzmanlikAlani,
                            UZMANLIK_ALANI
                       where PersonelUzmanlikAlani.PERSONEL_ID = PERSONEL.ID
                         and PersonelUzmanlikAlani.UZMANLIK_ALANI_ID = UZMANLIK_ALANI.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as personelUzmanlikAlaniAd_cim,

                STUFF((SELECT ',' + REPLACE(DIL.AD, ',', ';')
                       from PersonelYabanciDil,
                            DIL
                       where PersonelYabanciDil.PERSONEL_ID = PERSONEL.ID
                         and PersonelYabanciDil.DIL_ID = DIL.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as personelYabanciDilAd_cim,

                STUFF((SELECT ',' + REPLACE(Pick.AD, ',', ';')
                       from PersonelYabanciDil,
                            Pick
                       where PersonelYabanciDil.PERSONEL_ID = PERSONEL.ID
                         and PersonelYabanciDil.languageLevel = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as personelYabanciDilSeviye_cim,


-- STUFF( (SELECT ',' +   REPLACE(UNVAN.AD, ',', ';') from PERSONEL_GOREV, UNVAN where PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
-- AND PERSONEL_GOREV.UNVAN_ID = UNVAN.ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as PERSONEL_GOREV_UNVAN_AD,

-- STUFF( (SELECT ',' +   REPLACE(KADRO_DURUM.AD, ',', ';') from PERSONEL_GOREV, KADRO_DURUM where PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
-- AND PERSONEL_GOREV.KADRO_DURUMU_ID = KADRO_DURUM.ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as PERSONEL_GOREV_KADRO_DURUM,

-- STUFF( (SELECT ',' +   REPLACE(Pick.AD, ',', ';') from PERSONEL_GOREV, Pick where PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
-- AND PERSONEL_GOREV.GOREVLENDIRME_TIPI = Pick.ID for xml path(''), type).value('.', 'varchar(max)') , 1, 1, '') as PERSONEL_GOREV_GOREVLENDIRME_TURU,


-- AUDIT LER

                STUFF((SELECT DISTINCT ',' + REPLACE(MUZE_MUDURLUGU.AD, ',', ';')
                       from MUZE_MUDURLUGU,
                            audit_PERSONEL
                       where audit_PERSONEL.ID = PERSONEL.ID
                         AND MUZE_MUDURLUGU.ID = audit_PERSONEL.mudurlukId
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as gecmisMuzeMudurluguAd_cim,

                STUFF((SELECT DISTINCT ',' + REPLACE(MUZE_MUDURLUGU.AD, ',', ';')
                       from audit_PERSONEL_GOREV,
                            MUZE_MUDURLUGU,
                            audit_PERSONEL
                       where audit_PERSONEL.ID = PERSONEL.ID
                         AND audit_PERSONEL_GOREV.MUZE_MUDURLUGU_ID = MUZE_MUDURLUGU.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as gecmisGorevMuzeMudurluguAd_cim,

                STUFF((SELECT DISTINCT ',' + REPLACE(UNVAN.AD, ',', ';')
                       from UNVAN,
                            audit_PERSONEL_GOREV
                       where audit_PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
                         AND audit_PERSONEL_GOREV.UNVAN_ID = UNVAN.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '') as gecmisUnvanAd_cim,

                STUFF((SELECT DISTINCT ',' + REPLACE(Pick.AD, ',', ';')
                       from audit_PERSONEL_GOREV,
                            Pick
                       where audit_PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
                         AND audit_PERSONEL_GOREV.GOREVLENDIRME_TIPI = Pick.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                      '')                                                            as gecmisPersonelGorevGorevlendirmeTuru_cim,

                STUFF((SELECT ',' + REPLACE(audit_PERSONEL_GOREV.GOREV_BASLAMA_TARIHI, ',', ';')
                       from audit_PERSONEL_GOREV
                       where audit_PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                      '')                                                            as gecmisPersonelGorevBaslamaTarihi_cim,

                STUFF((SELECT ',' + REPLACE(audit_PERSONEL_GOREV.GOREV_BITIS_TARIHI, ',', ';')
                       from audit_PERSONEL_GOREV
                       where audit_PERSONEL_GOREV.PERSONEL_ID = PERSONEL.ID
                          for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                      '')                                                            as gecmisPersonelGorevBitisTarihi_cim

from PERSONEL

         -- LEFT JOIN Artifact_Counting on Artifact_Counting.PERSONEL_ID = PERSONEL.ID
-- LEFT JOIN ESER_HAREKET teslimAlan on ESER_HAREKET.TESLIM_ALAN_PERSONEL_ID = PERSONEL.ID
-- LEFT JOIN ESER_HAREKET teslimEden on ESER_HAREKET.TESLIM_EDEN_PERSONEL_ID = PERSONEL.ID
-- LEFT JOIN ESER_ZIMMET on ESER_ZIMMET.ZIMMET_PERSONEL_ID = PERSONEL.ID
         LEFT JOIN KULLANICI on KULLANICI.PERSONEL_ID = PERSONEL.ID
         LEFT JOIN MESLEK on MESLEK.ID = PERSONEL.MESLEK_ID
         LEFT JOIN UNVAN on UNVAN.ID = PERSONEL.unvan
         LEFT JOIN KADRO_DURUM on KADRO_DURUM.ID = PERSONEL.kadroDurum
         LEFT JOIN MUZE_MUDURLUGU on MUZE_MUDURLUGU.ID = PERSONEL.mudurlukId

-- LEFT JOIN audit_PERSONEL on audit_PERSONEL.ID = PERSONEL.ID
-- LEFT JOIN audit_PERSONEL_GOREV ON audit_PERSONEL_GOREV.PERSONEL_ID = audit_PERSONEL.ID

WHERE PERSONEL.AKTIF = 1
  AND PERSONEL.SILINMIS = 0
  AND PERSONEL.inspector = 0;