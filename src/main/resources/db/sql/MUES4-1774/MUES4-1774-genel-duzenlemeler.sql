--MUES KMS indexleme
INSERT INTO dbo.IZIN (AD, KOD, ACIKLAMA, AKTIF, SILINMIS, menuId) SELECT N'Komisyon Tga sorgu indexleme', N'kmsTgaSorgu:Indeksleme', null, 1, 0, ID FROM MENU WHERE AD = 'Komisyon TGA Sorgulama'
INSERT INTO dbo.IZIN (AD, KOD, ACIKLAMA, AKTIF, SILINMIS, menuId)  SELECT N'Komisyon Obje sorgu indexleme', N'kmsObjeSorgu:Indeksleme', null, 1, 0,  ID FROM MENU WHERE AD ='Komisyon Obje Sorgulama'
INSERT INTO dbo.IZIN (AD, KOD, ACIKLAMA, AKTIF, SILINMIS, menuId) SELECT N'Komisyon tga sorgu indexleme', N'komisyonSorgu:Indeksleme', null, 1, 0, ID FROM MENU WHERE AD = 'Komisyon Sorgulama'


--ÖMK Eser indexleme
INSERT INTO dbo.Omk_Izin (AKTIF, SILINMIS, ACIKLAMA, AD, KOD, menuId) SELECT 1, 0, N'Ömk Eser Sorgu İndeksleme', N'Ömk Eser Sorgu İndeksleme', N'eserSorgu:Indeksleme', ID FROM MENU WHERE AD = 'Sorgular'

  --LAB indexkleme
INSERT INTO dbo.Lab_Izin (AKTIF, SILINMIS, ACIKLAMA, AD, KOD, menuId) SELECT 1, 0, null, N'Restorasyon-Konservasyon Sorgulama', N'eserSorgu:Indeksleme', ID FROM MENU WHERE ad = 'Restorasyon-Konservasyon Sorgulama'

    EXEC sp_rename 'KMS_COMMISSION_VIEs', 'KMS_COMMISSION_VIEW';
