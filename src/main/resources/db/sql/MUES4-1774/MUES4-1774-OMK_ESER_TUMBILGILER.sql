-- ////////////
IF OBJECT_ID('dbo.OMK_ESER_TUMBILGILER', 'V') IS NOT NULL
DROP VIEW dbo.OMK_ESER_TUMBILGILER;

CREATE VIEW OMK_ESER_TUMBILGILER AS

WITH eser_hareket1 as (select * from OMK_ESER_HAREKET_VIEW)
SELECT dbo.Omk_ESER.ID                                                                AS id,
--omk solr core 7 and qdrant artifact type 7
       (700000000 + Omk_ESER.ID)                                                      as uid,
       dbo.Omk_Alan.ACIKLAMA                                                          AS alanAciklama_ci,
       dbo.Omk_Alan.AD                                                                AS alanAd_ci,
       dbo.Omk_Alan.KOD                                                               AS alanKod_ci,
       dbo.Omk_Alan_Konumu.AD                                                         AS alanKonumuAd_ci,
       dbo.ALAN_KONUMU_TUR.AD                                                         AS alanKonumuTurAd_ci,
       dbo.ALAN_KONUMU_TUR.DEGER                                                      AS alanKonumuTurDeger_ci,
       dbo.Omk_ALAN_TUR.AD                                                            AS alanturAd_ci,
       dbo.Omk_ALAN_TUR.DEGER                                                         AS alanturDeger_ci,
       dbo.Omk_Pack.state                                                             AS artifactState,

       dbo.MUZE_MUDURLUGU.AD                                                          as bagliMudurlukAd_ci,

       dbo.Omk_Mudurluk.ACIKLAMA                                                      AS muzeMudurluguAciklama_ci,
       dbo.Omk_Mudurluk.AD                                                            AS muzeMudurluguAd_ci,
       dbo.Omk_Mudurluk.BOYLAM                                                        AS muzeMudurluguBoylam,
       dbo.Omk_Mudurluk.ENLEM                                                         AS muzeMudurluguEnlem,
       dbo.Omk_Mudurluk.KOD                                                           AS muzeMudurluguKod_ci,

       dbo.Omk_BagliBirim.AD                                                          AS bagliBirimAd_ci,
       dbo.Omk_Bina.ACIKLAMA                                                          AS binaAciklama_ci,
       dbo.Omk_Bina.AD                                                                AS binaAd_ci,
       dbo.Omk_Bina.KOD                                                               AS binaKod_ci,

       CASE WHEN dbo.Omk_ESER.updateInProgress = 1 THEN 0 ELSE dbo.Omk_ESER.AKTIF END AS aktif,
       CASE WHEN dbo.Omk_ESER.file3DPath is not null THEN 1 ELSE 0 END                AS eser3DFile,

       CASE WHEN dbo.Omk_ESER.DONEM_BASLANGIC_YIL < -30000 THEN 1 ELSE 0 END          AS eserDonemBaslangicGo,
       dbo.Omk_ESER.DONEM_BASLANGIC_YIL                                               AS eserDonemBaslangicYil,

       CASE WHEN dbo.Omk_ESER.DONEM_BITIS_YIL < -30000 THEN 1 ELSE 0 END              AS eserDonemBitisGo,
       dbo.Omk_ESER.DONEM_BITIS_YIL                                                   AS eserDonemBitisYil,
       --convert(varchar(25), dbo.Omk_ESER.DUZENLEME_ZAMANI, 120)                       AS eserDuzenlemeZamani,
       --dbo.Omk_ESER.ELISI_DOKUMA_SECIMI                                               AS eserElisiDokumaSecimiAd,
       dbo.Omk_ESER.ENVANTER_NO                                                       AS eserEskiEnvanterNo_ci,
       dbo.Omk_ESER.ESER_OZEL_ADI                                                     AS eserEserOzelAdi,
       dbo.Omk_ESER.ESERI_BAGISLAYAN_ONEMLI_KISI_ID                                   AS eserEseriBagislayanOnemliKisiId,
       dbo.Omk_ESER.ESERI_KULLANACAK_ONEMLI_KISI_ID                                   AS eserEseriKullanacakOnemliKisiId,
       dbo.Omk_ESER.ESERI_KULLANAN_ONEMLI_KISI_ID                                     AS eserEseriKullananOnemliKisiId,
       dbo.Omk_ESER.ESERI_YAPAN_ONEMLI_KISI_ID                                        AS eserEseriYapanOnemliKisiId,
       dbo.Omk_ESER.ESERI_YAPTIRAN_ONEMLI_KISI_ID                                     AS eserEseriYaptiranOnemliKisiId,
       dbo.Omk_ESER.GENEL_ACIKLAMA                                                    AS eserGenelAciklama_ci,
       dbo.Omk_ESER.ISLAMI_GAYRI_SECIMI                                               AS eserIslamiGayriSecimi,
       dbo.Omk_ESER.KIYMET                                                            AS eserKiymet,

       FORMAT(Omk_ESER.permanentId, 'OMK\.000\.000\.000')                             AS eserEserId_ci,

       dbo.Omk_ESER.KONDISYON_DURUMU                                                  AS eserKondisyonDurumu,

       CASE WHEN dbo.Omk_ESER.SIKKE_DARP_YILI < -30000 THEN 1 ELSE 0 END              AS eserSikkeDarpGo,
       dbo.DarpYeri.AD                                                                AS eserSikkeDarpYeriAd_ci,

       dbo.Omk_ESER.SIKKE_DARP_YILI                                                   AS eserSikkeDarpYili,
       dbo.Omk_ESER.SIKKE_DARP_YONU                                                   AS eserSikkeDarpYonu,
       sikkeDarpYonu.[AD]                                                             AS eserSikkeDarpYonuAd_ci,

       dbo.Omk_ESER.SILINMIS                                                          AS eserSilinmis,
       dbo.Omk_ESER.SILME_ACIKLAMASI                                                  AS eserSilmeAciklamasi_ci,
       convert(varchar(25), dbo.Omk_ESER.SILME_ZAMANI, 120)                           AS eserSilmeZamani,


       dbo.Omk_ESER.TASINIR_ISLEM_FISI_NO                                             AS eserTasinirIslemFisiNo,
       dbo.Omk_ESER.TASINIR_MAL_YON_ID                                                AS eserTasinirMalYonId,
       dbo.Omk_ESER.TORENSEL_DURUMU                                                   AS eserTorenselDurumu,
       dbo.Omk_ESER.UNIKLIK_DURUMU                                                    AS eserUniklikDurumu,
       --dbo.Omk_ESER.uretimYeriId                                                      AS ESER_URETIM_YERI_ID,
       dbo.Omk_ESER.VERSIYON                                                          AS eserVersiyon,
       dbo.Omk_ESER.YARATMA_KULLANICI_ID                                              AS eserYaratmaKullaniciId,
       dbo.Omk_ESER.YARATMA_ZAMANI                                                    AS eserYaratmaZamani,
       dbo.Omk_Workflow.dateCompleted                                                 AS eserOnayZamani,
       dbo.Omk_ESER.YAZMA_BASMA_SECIMI                                                AS eserYazmaBasmaSecimi,
       dbo.Cag.ACIKLAMA                                                               AS cagAciklama_ci,
       dbo.Cag.AD                                                                     AS cagAd_ci,
       dbo.Donem.AD                                                                   AS donemAd_ci,
       dbo.Iliskilendirme.AD                                                          AS iliskilendirmeAd_ci,
       dbo.Iliskilendirme.ACIKLAMA                                                    AS iliskilendirmeAciklama_ci,
       dbo.HUKUMDAR.ACIKLAMA                                                          AS hukumdarAciklama_ci,
       dbo.HUKUMDAR.AD                                                                AS hukumdarAd_ci,

       CASE WHEN dbo.HUKUMDAR.DONEM_BASLANGIC_YIL < -30000 THEN 1 ELSE 0 END          AS hukumdarDonemBaslangicGo,
       dbo.HUKUMDAR.DONEM_BASLANGIC_YIL                                               AS hukumdarDonemBaslangicYil,
       CASE WHEN dbo.HUKUMDAR.DONEM_BITIS_YIL < -30000 THEN 1 ELSE 0 END              AS hukumdarDonemBitisGo,
       dbo.HUKUMDAR.DONEM_BITIS_YIL                                                   AS hukumdarDonemBitisYil,

       dbo.IL.AD                                                                      AS ilAd_ci,
       dbo.IL.DEGER                                                                   AS ilDeger_ci,
       dbo.ILCE.AD                                                                    AS ilceAd_ci,
       dbo.KRONOLOJI.ACIKLAMA                                                         AS kronolojiAciklama_ci,
       dbo.KRONOLOJI.AD                                                               AS kronolojiAd_ci,

       dbo.Omk_Kullanici.AD + ' ' + dbo.Omk_Kullanici.SOYAD                           AS kullaniciKullaniciAdi_ci,
       dbo.UYGARLIK.ACIKLAMA                                                          AS uygarlikAciklama_ci,
       dbo.UYGARLIK.AD                                                                AS uygarlikAd_ci,

       dbo.TASINIR_MAL_YONETMELIGI_KOD.AD                                             AS tasinirMalYonetmeligiKodAd_ci,
       kondisyonDurumuAd.[AD]                                                         AS eserKondisyonDurumuAd_ci,
       yazmaBasma.[AD]                                                                AS eserYazmaBasmaSecimiAd_ci,
       islamiGayri.[AD]                                                               AS eserIslamiGayriSecimiAd_ci,
       dbo.UretimYeri.AD                                                              AS uretimyeriAd_ci,
       dbo.UretimYeri.ACIKLAMA                                                        AS uretimyeriAciklama_ci,

       dbo.Omk_ESER.webSite                                                           As webSite_ci,


       dbo.ESER_TUR.AD                                                                AS eserTurAd_ci,
       dbo.ESER_ALT_TUR.AD                                                            AS eserAltTurAd_ci,
       dbo.ESER_TUR.AD + ' > ' + dbo.ESER_ALT_TUR.AD                                  AS eserTurveAltTurAd_ci,

       dbo.UNVAN.AD                                                                   AS unvanAd_ci,
       dbo.ONEMLI_KISI.AD                                                             AS onemliKisiAd_ci,
       dbo.ONEMLI_KISI.ACIKLAMA                                                       AS onemliKisiAciklama_ci,
       dbo.MESLEK.AD                                                                  AS meslekAd_ci,
       dbo.MESLEK.ACIKLAMA                                                            AS meslekAciklama_ci,
       dbo.KADRO_DURUM.AD                                                             AS kadroDurumAd_ci,
       dbo.UretimBolgesi.AD                                                           AS uretimbolgesiAd_ci,
       dbo.UretimBolgesi.ACIKLAMA                                                     AS uretimbolgesiAciklama_ci,

       dbo.PERSONEL.SICIL_NO                                                          AS ihtisasElemaniSicilNo_ci,
       dbo.PERSONEL.AD                                                                AS ihtisasElemaniAd_ci,
       dbo.PERSONEL.SOYAD                                                             AS ihtisasElemaniSoyad_ci,
       dbo.PERSONEL.AD + ' ' + dbo.PERSONEL.SOYAD                                     AS ihtisasElemaniAdSoyad_ci,

       (SELECT Omk_ESER_FOTOGRAF.FOTOGRAF_PATH
        FROM Omk_ESER_FOTOGRAF
        WHERE Omk_ESER_FOTOGRAF.ESER_ID = Omk_ESER.ID
          AND Omk_ESER_FOTOGRAF.ANA_FOTOGRAF = 1)                                     AS eserTanimlayiciFotografPath,

       CASE
           WHEN
               (select count(Omk_Audit_Omk_ESER.ID)
                from Omk_Audit_Omk_ESER
                where Omk_Audit_Omk_ESER.ID = Omk_ESER.ID) >
               0 THEN 1
           ELSE 0 END                                                                 AS tarihsellik,

       dbo.UZMANLIK_ALANI.AD                                                          AS uzmanlikAlaniAd_ci,
       dbo.UZMANLIK_ALANI.ACIKLAMA                                                    AS uzmanlikAlaniAciklama_ci,

       CASE
           WHEN convert(varchar(25), dbo.Omk_Workflow.dateModified, 120) >
                convert(varchar(25), dbo.Omk_ESER.DUZENLEME_ZAMANI, 120)
               THEN dbo.Omk_Workflow.dateModified
           ELSE dbo.Omk_ESER.DUZENLEME_ZAMANI END                                     AS eserDuzenlemeZamani,


       STUFF((SELECT ',' + REPLACE(Keyword.AD, ',', ';')
              from Omk_Eser_Keyword,
                   Keyword
              where dbo.Omk_Eser_Keyword.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_Keyword.keywordId = dbo.Keyword.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserAnahtarKelimeAd_cim,

       STUFF((SELECT ',' + REPLACE(dbo.Omk_Transcription.[translation], ',', ';')
              from Omk_Transcription
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                 for xml path(''), type).value(N'.[1]', N'nvarchar(max)'), 1, 1,
                 N'')                                                                     AS eserTranskripsiyonMetinCevirisi_cim,

       STUFF((SELECT ',' + REPLACE(Omk_ESER_FOTOGRAF.ACIKLAMA, ',', ';')
              from Omk_ESER_FOTOGRAF
              where dbo.Omk_ESER_FOTOGRAF.ESER_ID = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserFotografAciklama_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.ad, ',', ';')
              from Omk_Eser_YayinLiteratur,
                   Literatur
              where dbo.Omk_Eser_YayinLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_YayinLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yayinAd_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.aciklama, ',', ';')
              from Omk_Eser_YayinLiteratur,
                   Literatur
              where dbo.Omk_Eser_YayinLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_YayinLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yayinAciklama_cim,

       STUFF((SELECT ',' + REPLACE(YAZI_TIPI.AD, ',', ';')
              from Omk_Transcription,
                   YAZI_TIPI
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                AND dbo.Omk_Transcription.yaziTipi = dbo.YAZI_TIPI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yaziTipiAd_cim,

       STUFF((SELECT ',' + REPLACE(YAZI_TIPI.ACIKLAMA, ',', ';')
              from Omk_Transcription,
                   YAZI_TIPI
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                AND dbo.Omk_Transcription.yaziTipi = dbo.YAZI_TIPI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yaziTipiAciklama_cim,

       STUFF((SELECT ',' + REPLACE(Measure.AD, ',', ';')
              from Omk_Eser_Measure,
                   Measure
              where dbo.Omk_Eser_Measure.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_Measure.measureId = dbo.Measure.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS olcuAd_cim,

       STUFF((SELECT ',' + REPLACE(Omk_Eser_Measure.DEGER, ',', ';')
              from Omk_Eser_Measure,
                   Measure
              where dbo.Omk_Eser_Measure.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_Measure.measureId = dbo.Measure.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS olcuDeger_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.ad, ',', ';')
              from Omk_Eser_KaynakLiteratur,
                   Literatur
              where dbo.Omk_Eser_KaynakLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_KaynakLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaynakAd_cim,

       STUFF((SELECT ',' + REPLACE(Literatur.aciklama, ',', ';')
              from Omk_Eser_KaynakLiteratur,
                   Literatur
              where dbo.Omk_Eser_KaynakLiteratur.eserId = dbo.Omk_ESER.ID
                AND dbo.Omk_Eser_KaynakLiteratur.literaturId = dbo.Literatur.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaynakAciklama_cim,

       STUFF((SELECT ',' + REPLACE(IliskilendirmeTurGrubu.AD, ',', ';')
              from Iliskilendirme,
                   IliskilendirmeTur,
                   IliskilendirmeTurGrubu
              where dbo.Iliskilendirme.ID = dbo.Omk_ESER.iliskilendirmeId
                AND dbo.Iliskilendirme.turID = dbo.IliskilendirmeTur.ID
                AND dbo.IliskilendirmeTur.tur = dbo.IliskilendirmeTurGrubu.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS iliskilendirmeturgrubuAd_cim,

       STUFF((SELECT ',' + REPLACE(Omk_EserSerh.metin, ',', ';')
              from Omk_EserSerh
              where dbo.Omk_EserSerh.eserId = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserserhMetin_cim,

       STUFF((SELECT ',' + REPLACE(Atolye.AD, ',', ';')
              from Omk_Eser_Atolye,
                   Atolye
              where dbo.Omk_Eser_Atolye.eserId = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS atolyeAd_cim,

       STUFF((SELECT ',' + REPLACE(YapimTeknigi.AD, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   YapimTeknigi
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_YAPIM_TEKNIGI_ID = dbo.YapimTeknigi.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserYapimTeknigiAd_cim,

       STUFF((SELECT ',' + REPLACE(YapimTeknigi.ACIKLAMA, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   YapimTeknigi
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_YAPIM_TEKNIGI_ID = dbo.YapimTeknigi.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserYapimTeknigiAciklama_cim,

       STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
              from Malzeme,
                   Omk_ESER_MALZEME_YAPIM_TEKNIGI
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yapimTeknigiMalzeme_cim,


       STUFF((SELECT ',' + REPLACE(SUSLEME_TEKNIGI.AD, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.SUSLEME_TEKNIGI_ID = dbo.SUSLEME_TEKNIGI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS suslemeTeknigiAd_cim,

       STUFF((SELECT ',' + REPLACE(SUSLEME_TEKNIGI.ACIKLAMA, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.SUSLEME_TEKNIGI_ID = dbo.SUSLEME_TEKNIGI.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiAciklama_cim,

       STUFF((SELECT ',' + REPLACE(ESER_MALZEME_GRUBU.AD, ',', ';')
              from ESER_MALZEME_GRUBU,
                   Malzeme,
                   Omk_ESER_MALZEME_SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.ESER_MALZEME_GRUBU.ID = dbo.Malzeme.MALZEME_GRUBU_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserMalzemeGrubuAd_cim,

       STUFF((SELECT ',' + REPLACE(Malzeme.AD, ',', ';')
              from Malzeme,
                   Omk_ESER_MALZEME_SUSLEME_TEKNIGI
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.MALZEME_ID = dbo.Malzeme.ID
                AND dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiMalzeme_cim,

       STUFF((SELECT ',' + REPLACE(Renk.ad, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiRenkAd_cim,

       STUFF((SELECT ',' + REPLACE(Renk.cmyk, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiRenkCMYK_cim,

       STUFF((SELECT ',' + REPLACE(Renk.NCS, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiRenkNCS_cim,

       STUFF((SELECT ',' + REPLACE(Renk.RGB, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiRenkRGB_cim,

       STUFF((SELECT ',' + REPLACE(Renk.mueskod, ',', ';')
              from Omk_ESER_MALZEME_SUSLEME_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_SUSLEME_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS suslemeTeknigiRenkMueskod_cim,

       STUFF((SELECT ',' + REPLACE(Renk.ad, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yapimTeknigiRenkAd_cim,

       STUFF((SELECT ',' + REPLACE(Renk.cmyk, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS yapimTeknigiRenkCMYK_cim,

       STUFF((SELECT ',' + REPLACE(Renk.NCS, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yapimTeknigiRenkNCS_cim,

       STUFF((SELECT ',' + REPLACE(Renk.RGB, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS yapimTeknigiRenkRGB_cim,

       STUFF((SELECT ',' + REPLACE(Renk.mueskod, ',', ';')
              from Omk_ESER_MALZEME_YAPIM_TEKNIGI,
                   Renk
              where dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.ESER_ID = dbo.Omk_ESER.ID
                AND dbo.Renk.ID = dbo.Omk_ESER_MALZEME_YAPIM_TEKNIGI.RENK_ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS yapimTeknigiRenkMueskod_cim,

       STUFF((SELECT ',' + REPLACE(DIL.AD, ',', ';')
              from DIL,
                   Omk_Transcription
              where dbo.Omk_Transcription.eser = dbo.Omk_ESER.ID
                AND dbo.DIL.ID = dbo.Omk_Transcription.yaziDili
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS dilAd_cim,


       elisiDokuma.[AD]                                                               AS eserElisiDokumaSecimiAd_ci,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_MUZE_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserHareketMuzeAd_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_TESLIM_ALAN_PERSONEL_AD_SOYAD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketTeslimAlanPersonelAdSoyad_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_TESLIM_EDEN_PERSONEL_AD_SOYAD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketTeslimEdenPersonelAdSoyad_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ESER_GELIS_SEKLI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketEserGelisSekli_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ENVANTER_NO, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketEnvanterNo_cim,
       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_CIKARILMA_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketCikarilmaTarihi,
       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_ONAY_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketOnayTarihi,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ONAY_SAYISI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketOnaySayisi,
       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_MUZEYE_GELIS_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketMuzeyeGelisTarihi,

       STUFF((SELECT ',' + REPLACE(convert(varchar(25), ESER_HAREKET_ENVANTERE_ALINMA_TARIHI, 120), ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketEnvantereAlinmaTarihi,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserHareketAciklama_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ELE_GECIRME_YERI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketEleGecirmeYeri_cim,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_AKTIF, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserHareketAktif,
       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_SILINMIS, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS eserHareketSilinmis,

       STUFF((SELECT ',' + REPLACE(KAZI_TUR_DEGER, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziTurDeger,
       STUFF((SELECT ',' + REPLACE(KAZI_TUR_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziTurAd_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziAd_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziAciklama_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_ENLEM, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziEnlem_cim,

       STUFF((SELECT ',' + REPLACE(KAZI_BOYLAM, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziBoylam_cim,


       STUFF((SELECT ',' + REPLACE(ARASTIRMA_TUR_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS arastirmaTurAd_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_TUR_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS arastirmaTurAciklama_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS arastirmaAd_cim,

       STUFF((SELECT ',' + REPLACE(ARASTIRMA_ACIKLAMA, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS arastirmaAciklama_cim,


       STUFF((SELECT ',' + REPLACE(KAZI_BELDE_ADI, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziBeldeAdi_cim,
       STUFF((SELECT ',' + REPLACE(KAZI_KAZI_KODU, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS kaziKaziKodu_cim,

       STUFF((SELECT ',' + REPLACE(Ulke_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1, '')           AS ulkeAd_cim,

       STUFF((SELECT ',' + REPLACE(TUZEL_KISI_TICARI_UNVAN, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS tuzelKisiTicariUnvan_cim,

       STUFF((SELECT ',' + REPLACE(ESER_HAREKET_ESER_GELIS_SEKLI_AD, ',', ';')
              from eser_hareket1
              where eser_hareket1.ESER_HAREKET_ESER_ID = Omk_ESER.ID
                 for xml path(''), type).value('.', 'varchar(max)'), 1, 1,
                 '')                                                                      AS eserHareketEserGelisSekliAd_cim


FROM dbo.Omk_ESER

         LEFT JOIN dbo.Omk_EserDepo
                   ON dbo.Omk_EserDepo.artifactId = dbo.Omk_ESER.ID AND dbo.Omk_EserDepo.AKTIF = 'true' AND
                      dbo.Omk_EserDepo.SILINMIS = 'false'
         LEFT JOIN dbo.Omk_Alan_Konumu ON dbo.Omk_EserDepo.ALAN_KONUMU_ID = dbo.Omk_Alan_Konumu.ID
         LEFT JOIN dbo.ALAN_KONUMU_TUR ON dbo.Omk_Alan_Konumu.ALAN_KONUMU_TUR_ID = dbo.ALAN_KONUMU_TUR.ID
         LEFT JOIN dbo.Omk_Alan ON dbo.Omk_Alan_Konumu.ALAN_ID = dbo.Omk_Alan.ID
         LEFT JOIN dbo.Omk_ALAN_TUR ON dbo.Omk_ALAN_TUR.ID = dbo.Omk_Alan.ALAN_TUR_ID

         LEFT JOIN dbo.Omk_Bina ON dbo.Omk_Alan.BINA_ID = dbo.Omk_Bina.ID
         LEFT JOIN dbo.Omk_BagliBirim ON dbo.Omk_Bina.BAGLI_BIRIM_ID = dbo.Omk_BagliBirim.ID

         LEFT JOIN dbo.Omk_Workflow ON dbo.Omk_ESER.ID = dbo.Omk_Workflow.eserId
         LEFT JOIN dbo.Omk_Mudurluk ON dbo.Omk_Workflow.mudurlukId = dbo.Omk_Mudurluk.ID

         LEFT JOIN (SELECT MAX(ID) AS MaxID, artifactId
                    FROM dbo.Omk_PackArtifact
                    GROUP BY artifactId) max_packArtifact ON max_packArtifact.artifactId = dbo.Omk_ESER.ID
         LEFT JOIN dbo.Omk_PackArtifact ON dbo.Omk_PackArtifact.ID = max_packArtifact.MaxID
         LEFT JOIN dbo.Omk_Pack ON dbo.Omk_Pack.ID = dbo.Omk_PackArtifact.packId

         LEFT JOIN dbo.MUZE_MUDURLUGU ON dbo.MUZE_MUDURLUGU.ID = dbo.Omk_Mudurluk.bagliMudurlukId

         LEFT JOIN dbo.IL ON dbo.IL.ID = dbo.Omk_BagliBirim.IL_ID
         LEFT JOIN dbo.ILCE ON dbo.ILCE.ID = dbo.Omk_BagliBirim.ILCE_ID


         LEFT JOIN dbo.Donem ON dbo.Omk_ESER.CAG_DONEM_ID = dbo.Donem.ID
         LEFT JOIN dbo.Cag ON dbo.Donem.cagId = dbo.Cag.ID
         LEFT JOIN dbo.KRONOLOJI ON dbo.CAG.KRONOLOJI_ID = dbo.KRONOLOJI.ID

         LEFT JOIN dbo.UYGARLIK ON dbo.Omk_ESER.UYGARLIK_ID = dbo.UYGARLIK.ID
         LEFT JOIN dbo.HUKUMDAR ON dbo.Omk_ESER.HUKUMDAR_ID = dbo.HUKUMDAR.ID
         LEFT JOIN dbo.UretimYeri ON dbo.Omk_ESER.uretimYeriId = dbo.UretimYeri.ID
         LEFT JOIN dbo.Iliskilendirme ON dbo.Omk_ESER.iliskilendirmeId = dbo.Iliskilendirme.ID
         LEFT JOIN dbo.Omk_Kullanici ON dbo.Omk_ESER.YARATMA_KULLANICI_ID = dbo.Omk_Kullanici.ID

         LEFT JOIN dbo.ESER_ALT_TUR ON dbo.Omk_ESER.ESER_ALT_TUR_ID = dbo.ESER_ALT_TUR.ID
         LEFT JOIN dbo.ESER_TUR ON dbo.ESER_ALT_TUR.ESER_TUR_ID = dbo.ESER_TUR.ID
         LEFT JOIN dbo.TASINIR_MAL_YONETMELIGI_KOD
                   ON dbo.Omk_ESER.TASINIR_MAL_YON_ID = dbo.TASINIR_MAL_YONETMELIGI_KOD.ID
         LEFT JOIN dbo.PERSONEL ON dbo.Omk_Kullanici.PERSONEL_ID = dbo.PERSONEL.ID
         LEFT JOIN dbo.UNVAN ON dbo.PERSONEL.unvan = dbo.UNVAN.ID
         LEFT JOIN dbo.UretimBolgesi ON dbo.Omk_ESER.uretimBolgesiId = dbo.UretimBolgesi.ID
         LEFT JOIN dbo.UZMANLIK_ALANI ON dbo.PERSONEL.UZMANLIK_ALANI_ID = dbo.UZMANLIK_ALANI.ID
         LEFT JOIN dbo.ONEMLI_KISI ON dbo.Omk_ESER.ESERI_KULLANAN_ONEMLI_KISI_ID = dbo.ONEMLI_KISI.ID
         LEFT JOIN dbo.MESLEK ON dbo.PERSONEL.MESLEK_ID = dbo.MESLEK.ID
         LEFT JOIN dbo.KADRO_DURUM ON dbo.KADRO_DURUM.ID = dbo.PERSONEL.kadroDurum

         LEFT JOIN Pick islamiGayri ON islamiGayri.ID = dbo.Omk_ESER.ISLAMI_GAYRI_SECIMI
         LEFT JOIN Pick elisiDokuma ON elisiDokuma.ID = dbo.Omk_ESER.ELISI_DOKUMA_SECIMI
         LEFT JOIN Pick sikkeDarpYonu ON sikkeDarpYonu.ID = dbo.Omk_ESER.SIKKE_DARP_YONU
         LEFT JOIN Pick kondisyonDurumuAd ON dbo.Omk_ESER.KONDISYON_DURUMU = kondisyonDurumuAd.ID
         LEFT JOIN Pick yazmaBasma ON yazmaBasma.ID = dbo.Omk_ESER.YAZMA_BASMA_SECIMI

         LEFT JOIN dbo.DarpYeri ON dbo.Omk_ESER.darpYeriId = dbo.DarpYeri.ID

WHERE dbo.Omk_ESER.permanentId IS NOT NULL