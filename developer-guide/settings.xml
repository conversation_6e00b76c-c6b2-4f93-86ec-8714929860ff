<!--    This file is used to configure the Maven settings for the project.-->
<!--    It includes server credentials for uploading artifacts to the MUES repository.-->
<!--    Make sure to replace 'tubitak-username' and 'tubitak-password' with your actual credentials. PUT THIS FILE AS ~/.m2/settings.xml -->
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">
    <servers>
        <server>
            <id>mues-releases</id>
            <username>tubitak-username</username>
            <password>tubitak-password</password>
            <configuration>
              <httpConfiguration>
                <all>
                    <usePreemptive>true</usePreemptive>
                </all>
              </httpConfiguration>
            </configuration>
        </server>
        <server>
            <id>mues-snapshots</id>
           <username>tubitak-username</username>
            <password>tubitak-password</password>
            <configuration>
              <httpConfiguration>
                <all>
                    <usePreemptive>true</usePreemptive>
                </all>
              </httpConfiguration>
            </configuration>
        </server>
    </servers>
</settings>